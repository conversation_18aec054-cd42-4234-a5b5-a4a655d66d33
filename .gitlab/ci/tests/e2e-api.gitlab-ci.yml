include:
  - local: .gitlab/ci/tests/e2e-api-base.gitlab-ci.yml

.if-dev-commit-pipeline: &if-dev-commit-pipeline
  if: '$CI_COMMIT_BRANCH == $DEV_BRANCH'

.if-api-scheduled-regression-test: &if-api-scheduled-regression-test
  if: '$CI_PIPELINE_SOURCE == "schedule" && $ICT_TEST_API'

.if-api-web-triggered-test: &if-api-web-triggered-test
  if: '$CI_PIPELINE_SOURCE == "web" && $SUITE && $ICT_TEST_API'

api:e2e:smoke:
  stage: verify
  extends:
    - .run-api-smoke-test
  rules:
    - <<: *if-api-scheduled-regression-test

api:e2e:regression:post-deploy:dev:
  stage: verify
  extends:
    - .run-api-regression-test
  allow_failure: true
  needs:
    - job: api:e2e:build
      artifacts: true
    - job: infrastructure:deploy
      artifacts: false
      optional: true
  rules:
    - <<: *if-dev-commit-pipeline

api:e2e:regression:schedule:
  stage: verify
  extends:
    - .run-api-regression-test
  allow_failure: true
  rules:
    - <<: *if-api-scheduled-regression-test
      when: always

api:e2e:test-suite:web:
  stage: verify
  extends:
    - .run-api-test-base
    - .depend-on-api-e2e-build
  allow_failure: true
  script:
    - yarn test:${SUITE}
  rules:
    - <<: *if-api-web-triggered-test
      when: always
