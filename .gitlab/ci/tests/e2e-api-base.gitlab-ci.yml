variables:
  TEST_DOCKER_IMAGE: mcr.microsoft.com/playwright:v1.45.1-jammy
  API_E2E_PROJECT_ROOT: "$CI_PROJECT_DIR/services/api-e2e"

.if-dev-commit-pipeline: &if-dev-commit-pipeline
  if: '$CI_COMMIT_BRANCH == $DEV_BRANCH'

.if-api-scheduled-regression-test: &if-api-scheduled-regression-test
  if: '$CI_PIPELINE_SOURCE == "schedule" && $ICT_TEST_API'

.if-api-web-triggered-test: &if-api-web-triggered-test
  if: '$CI_PIPELINE_SOURCE == "web" && $SUITE && $ICT_TEST_API'

.playwright-test-artifacts: &playwright-test-artifacts
  when: always
  paths:
    - ${API_E2E_PROJECT_ROOT}/results
  reports:
    junit: ${API_E2E_PROJECT_ROOT}/results/junit/ict-test-results.xml

.depend-on-api-e2e-build:
  needs:
    - job: api:e2e:build
      artifacts: true

.configure-api-test-job:
  image: $TEST_DOCKER_IMAGE
  before_script:
    - cd $API_E2E_PROJECT_ROOT
    - yarn install
    - yarn setup

.run-api-test-base:
  stage: verify
  extends:
    - .configure-api-test-job
  artifacts:
    <<: *playwright-test-artifacts
  variables:
    ICT_ENV: $TARGET_ENVIRONMENT
    GITLAB_ACCESS_TOKEN: $GROUP_ACCESS_TOKEN

.run-api-smoke-test:
  stage: verify
  extends:
    - .run-api-test-base
    - .depend-on-api-e2e-build
  allow_failure: true
  script:
    - yarn test:smoke

.run-api-regression-test:
  stage: verify
  extends:
    - .run-api-test-base
    - .depend-on-api-e2e-build
  allow_failure: true
  script:
    - yarn test:regression

api:e2e:build:
  stage: build
  extends:
    - .configure-api-test-job
  needs: []
  allow_failure: false
  artifacts:
    untracked: true
  variables:
    SECURE_FILES_DOWNLOAD_PATH: "./"
  script:
    - curl --silent "https://gitlab.com/gitlab-org/incubation-engineering/mobile-devops/download-secure-files/-/raw/main/installer" | bash
    - yarn run build
  rules:
    - <<: *if-dev-commit-pipeline
    - <<: *if-api-scheduled-regression-test
    - <<: *if-api-web-triggered-test
