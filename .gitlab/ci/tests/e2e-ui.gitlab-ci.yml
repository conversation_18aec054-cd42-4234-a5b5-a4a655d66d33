include:
  - local: .gitlab/ci/tests/e2e-ui-base.gitlab-ci.yml

.if-dev-commit-pipeline: &if-dev-commit-pipeline
  if: '$CI_COMMIT_BRANCH == $DEV_BRANCH'

.if-ui-scheduled-regression-test: &if-ui-scheduled-regression-test
  if: '$CI_PIPELINE_SOURCE == "schedule" && $ICT_TEST_UI'

.if-ui-web-triggered-test: &if-ui-web-triggered-test
  if: '$CI_PIPELINE_SOURCE == "web" && $SUITE && $ICT_TEST_UI'

ui:e2e:smoke:
  stage: verify
  extends:
    - .run-ui-smoke-test
  allow_failure: true
  rules:
    - <<: *if-ui-scheduled-regression-test

ui:e2e:regression:post-deploy:dev:
  stage: verify
  extends:
    - .run-ui-regression-test
  allow_failure: true
  needs:
    - job: ui:e2e:playwright-setup
      artifacts: true
    - job: infrastructure:deploy
      artifacts: false
      optional: true
    - job: ui:publish
      artifacts: false
      optional: true
  rules:
    - <<: *if-dev-commit-pipeline

ui:e2e:regression:schedule:live-data:
  stage: verify
  extends:
    - .run-ui-test-base
    - .depend-on-ui-build
    - .depend-on-ui-playwright-setup
  allow_failure: true
  script:
    - mise run test:regression
  variables:
    USE_MOCK_API: false
  rules:
    - <<: *if-ui-scheduled-regression-test
      when: always

ui:e2e:regression:schedule:full:
  stage: verify
  extends:
    - .run-ui-test-base
    - .depend-on-ui-build
    - .depend-on-ui-playwright-setup
  allow_failure: true
  script:
    - mise run test:regression-full
  rules:
    - <<: *if-ui-scheduled-regression-test
      when: always

ui:e2e:test-suite:web:
  stage: verify
  extends:
    - .run-ui-test-base
    - .depend-on-ui-build
    - .depend-on-ui-playwright-setup
  allow_failure: true
  script:
    - mise run test:suite
  rules:
    - <<: *if-ui-web-triggered-test
      when: always
