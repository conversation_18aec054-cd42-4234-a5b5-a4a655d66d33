include:
  - local: .gitlab/ci/includes/shared.gitlab-ci.yml

variables:
  TEST_ROOT: "$CI_PROJECT_DIR/services/ui-e2e"
  TEST_DOCKER_IMAGE: mcr.microsoft.com/playwright:v1.45.1-jammy

.if-dev-commit-pipeline: &if-dev-commit-pipeline
  if: '$CI_COMMIT_BRANCH == $DEV_BRANCH'

.if-ui-scheduled-regression-test: &if-ui-scheduled-regression-test
  if: '$CI_PIPELINE_SOURCE == "schedule" && $ICT_TEST_UI'

.if-ui-web-triggered-test: &if-ui-web-triggered-test
  if: '$CI_PIPELINE_SOURCE == "web" && $SUITE && $ICT_TEST_UI'

.navigate-to-mise-directory: &navigate-to-mise-directory
  - cd $UI_PROJECT_ROOT

.playwright-check-test-result: &playwright-check-test-result
  - mise run test:check
  - echo "Testing Finished..."

.playwright-test-artifacts: &playwright-test-artifacts
  when: always
  paths:
    - ${TEST_ROOT}/results
  reports:
    junit: ${TEST_ROOT}/results/junit/ict-test-results.xml

.depend-on-ui-build:
  needs:
    - job: ui:e2e:build
      artifacts: true

.depend-on-ui-playwright-setup:
  needs:
    - job: ui:e2e:playwright-setup
      artifacts: true

.configure-ui-test-job:
  extends:
    - .common-base
  before_script:
    - *navigate-to-mise-directory
    - !reference [.common-base, before_script]

.run-ui-test-base:
  stage: verify
  extends:
    - .configure-ui-test-job
  variables:
    ICT_ENV: $TARGET_ENVIRONMENT
    ICT_BASE_URL: $TEST_SUITE_UI_URL
    ICT_API_URL: $TEST_SUITE_API_URL
    ICT_MOCK_API_URL: $TEST_SUITE_MOCK_API_URL
    GITLAB_ACCESS_TOKEN: $GROUP_ACCESS_TOKEN
    PLAYWRIGHT_BROWSERS_PATH: $UI_E2E_PROJECT_ROOT/node_modules/.cache/playwright
  after_script:
    - *navigate-to-mise-directory
    - *playwright-check-test-result
  artifacts:
    <<: *playwright-test-artifacts

.run-ui-smoke-test:
  stage: verify
  extends:
    - .run-ui-test-base
    - .depend-on-ui-build
    - .depend-on-ui-playwright-setup
  allow_failure: true
  script:
    - mise run test:smoke

.run-ui-regression-test:
  stage: verify
  extends:
    - .run-ui-test-base
    - .depend-on-ui-build
    - .depend-on-ui-playwright-setup
  allow_failure: true
  script:
    - mise run test:regression

ui:e2e:build:
  stage: build
  extends:
    - .configure-ui-test-job
  needs: []
  allow_failure: false
  variables:
    SECURE_FILES_DOWNLOAD_PATH: "./"
  artifacts:
    untracked: true
  script:
    - mise run test:build
  rules:
    - <<: *if-dev-commit-pipeline
    - <<: *if-ui-scheduled-regression-test
    - <<: *if-ui-web-triggered-test

ui:e2e:playwright-setup:
  stage: verify
  extends:
    - .configure-ui-test-job
    - .depend-on-ui-build
  allow_failure: false
  variables:
    PLAYWRIGHT_BROWSERS_PATH: $UI_E2E_PROJECT_ROOT/node_modules/.cache/playwright
  artifacts:
    paths:
      - $UI_E2E_PROJECT_ROOT/node_modules/
    expire_in: 1 hour
  script:
    - mise run test:playwright-setup
  rules:
    - <<: *if-dev-commit-pipeline
    - <<: *if-ui-scheduled-regression-test
    - <<: *if-ui-web-triggered-test
# keeping for future ref on how to shard the job once it gets to long.
# .run-regression-test:
#   stage: verify
#   extends:
#     - .run-test-base
#     - .depend-on-ui-build
#   parallel: 10
#   allow_failure: false
#   script:
#     - mise run test:regression --shard=$CI_NODE_INDEX/$CI_NODE_TOTAL
#   artifacts:
#     <<: *playwright-test-artifacts
