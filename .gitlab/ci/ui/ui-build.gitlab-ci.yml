include:
  - local: .gitlab/ci/includes/shared.gitlab-ci.yml
  # - local: .gitlab/ci/tests/e2e-ui.gitlab-ci.yml

.if-dev-mr-pipeline: &if-dev-mr-pipeline
  if: "$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $DEV_BRANCH"

.ui-base:
  extends: .common-base
  allow_failure: false
  before_script:
    - cd $UI_PROJECT_ROOT
    - !reference [.common-base, before_script]

ui:build:
  stage: build
  extends: .ui-base
  inherit:
    default: true
  needs: []
  script:
    - mise run build:${TARGET_ENVIRONMENT}
  artifacts:
    paths:
      - $UI_PROJECT_ROOT/dist/

# Parallel test jobs
ui:typecheck:
  stage: test
  extends: .ui-base
  needs: [ui:build]
  script:
    - mise run typecheck
  rules:
    - <<: *if-dev-mr-pipeline

ui:lint:
  stage: test
  extends: .ui-base
  needs: [ui:build]
  script:
    - mise run lint
  rules:
    - <<: *if-dev-mr-pipeline

ui:test:
  stage: test
  extends: .ui-base
  needs: [ui:build]
  script:
    - mise run test-coverage
  coverage: /All\sfiles.*?\s+(\d+.\d+)/
  rules:
    - <<: *if-dev-mr-pipeline

ui:publish:
  stage: deploy
  extends: .gcp-auth-base
  dependencies: [ui:build]
  variables:
    SERVICE_ACCOUNT_KEY: ${TARGET_LANDING_ZONE}-cicd-sa.json
  script:
    - cd $UI_PROJECT_ROOT
    - TIMESTAMP=$(date +%Y%m%d-%H%M%S)
    - ZIP_NAME=ui-assets-$CI_COMMIT_BRANCH.zip
    - |
      # Zip the dist folder
      cd dist
      zip -r ../$ZIP_NAME .  # Name to be updated by https://jira.dematic.net/browse/ICT-6233
      cd ..
      
      # Upload to Artifact Registry
      gcloud artifacts generic upload \
        --project=$ARTIFACT_REGISTRY_PROJECT_ID \
        --location=$ARTIFACT_REGISTRY_LOCATION \
        --repository=${UI_ARTIFACT_REGISTRY} \
        --source=$ZIP_NAME \
        --package=$ZIP_NAME \
        --version=${TIMESTAMP}

      # Clean up
      rm -f $ZIP_NAME
  rules:
    - if: '$CI_COMMIT_REF_PROTECTED == "true"'