# =====================================
# Stage Environment Variables
# =====================================

locals {
  lz_config                      = read_terragrunt_config(find_in_parent_folders("lz.hcl"))
  cicd_service_account_principal = local.lz_config.locals.cicd_service_account_principal
}

inputs = {
  environment_folder_id     = "*************" # DematicNonProd/ControlTower/stage
  environment_folder_name   = "stage"
  environment_friendly_name = "Stage"
  environment_name          = "stage"

  alert_auto_close_duration = "3600s"
  
  auth0_config = {
    domain   = "control-tower-stage.us.auth0.com"
    audience = "https://stage.api.ict.dematic.dev"
  }

  availability_config = {
    url        = "https://system-availability-api-************.us-east1.run.app"
    project_id = "edp-s-us-east1-etl"
  }

  edp_config = {
    project_id      = "edp-s-us-east1-etl"
    pubsub_topic_id = "adi-data"
  }

  enable_uptime_alert_policy = true

  metric_processor_ingress_config = "INGRESS_TRAFFIC_ALL" # Creating a variable so we can test ingress config individual environments. When we're ready, we'll make it `INGRESS_TRAFFIC_INTERNAL_ONLY`
  
  ignition_upstream_url = "stage-dematic-software.ignition.ict.dematic.dev"

  metric_processor_url = "https://stage-ict-etl-metric-processor-296194495382.us-east1.run.app"

  aiml_config = {
    agent_search_url            = "https://genai-stage.ops.dematic.dev"
    agent_search_audience_url   = "https://dematic-chat-650845477849.us-central1.run.app"
    gold_questions_audience_url = "https://ragengine-650845477849.us-central1.run.app"
  }

  postgres_databases = [
    "dematic",
    "superior_uniform",
    "qa_manual",
    "ict_development",
    "qa_automation",
    "drt_automation",
    "stark_industries",
    "qa_manual_1",
    "verification_and_validation",
    "integration_test",
    "tti",
    "acehardware"
  ]

  postgres_monitoring_notification_email = "<EMAIL>"

}