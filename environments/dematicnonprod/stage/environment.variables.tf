# =====================================
# Stage Environment Variables
# =====================================

variable "auth0_config" {
  description = <<EOT
    The Auth0 configuration for the environment, including the domain and audience.
  EOT

  type = object({
    domain   = string
    audience = string
  })
}

variable "availability_config" {
  description = <<EOT
    The Availability configuration for the environment, including the url and projectId.
  EOT

  type = object({
    url        = string
    project_id = string
  })
}

variable "edp_config" {
  description = "The EDP configuration for the environment."
  type = object({
    project_id      = string
    pubsub_topic_id = string
  })
}

variable "enable_uptime_alert_policy" {
  description = <<EOF
    Whether to enable alerting for the uptime check. This should be set to true if you want to
    receive alerts when the monitored resource is down. If enabled, the alert will be wired up to
    the full uptime check.
  EOF

  type = bool
}

variable "environment_folder_id" {
  description = "The ID of the folder that instances for this environment should be created in."
  type        = string
}

variable "environment_folder_name" {
  description = "The name of the folder that instances for this environment should be created in."
  type        = string
}

variable "environment_friendly_name" {
  description = "A friendly name for the environment, used for display purposes."
  type        = string
}

variable "environment_iap_members" {
  description = <<EOT
    An optional list of members that should be granted access to the IAP for the environment. The 
    members should be prefixed with one of: ["serviceAccount:", "user:", "group:"].
  EOT

  type    = list(string)
  default = []

  validation {
    error_message = "All entries must be prefixed with 'serviceAccount:', 'user:', or 'group:'."

    condition = alltrue([
      for member in var.environment_iap_members : (
        startswith(member, "serviceAccount:") ||
        startswith(member, "user:") ||
        startswith(member, "group:")
      )
    ])
  }
}

variable "environment_name" {
  description = <<EOT
    The name of the environment that the resources are being deployed to. This value will be 
    combined with the `naming_root` to create a unique prefix for any resources requiring a 
    globally-unique identifier. Therefore, it must be unique among Control Tower environments but 
    should be as short as possible to comply with GCP resource name length restrictions.
  EOT

  type = string
}

variable "ignition_upstream_url" {
  description = "The URL of the Ignition upstream service that the proxy should connect to."
  type        = string
}

variable "metric_processor_ingress_config" {
  description = "The ingress configuration for the metric processor Cloud Run service"
  type        = string
  validation {
    condition = contains([
      "INGRESS_TRAFFIC_ALL",
      "INGRESS_TRAFFIC_INTERNAL_ONLY",
      "INGRESS_TRAFFIC_INTERNAL_LOAD_BALANCER"
    ], var.metric_processor_ingress_config)
    error_message = "Must be a valid Cloud Run ingress traffic setting"
  }
}


variable "metric_processor_url" {
  description = "The URL for the metric processor that instances in this environment should use."
  type        = string
}

variable "aiml_config" {
  description = <<EOT
    The AIML configuration for the environment.
  EOT

  type = object({
    gold_questions_audience_url = string
    agent_search_url            = string
    agent_search_audience_url   = string
  })
}

variable "postgres_databases" {
  description = "A list of PostgreSQL databases to be created in the environment."
  type        = list(string)
}

variable "postgres_monitoring_notification_email" {
  description = <<EOT
    The email address for sending alerts when a monitoring
    threshold is crossed.
    EOT
  type        = string
}
variable "postgres_alerting_config" {

  description = <<EOT
    Default PostgreSQL alerting configs for stage. Defaults to false but we can override from `environment.hcl` when we're testing. 
    Duration and evaluation periods are lower because if we ever do enable in Staging we'll want to make alerts easy to triggert.
  EOT

  type = object({
    enable_connection_alerts       = optional(bool, false)
    enable_cpu_alerts              = optional(bool, false)
    enable_memory_alerts           = optional(bool, false)
    enable_connection_count_alerts = optional(bool, false)
    enable_tps_alerts              = optional(bool, false)
    enable_deadlock_alerts         = optional(bool, false)
    alert_duration_threshold       = optional(string, "60s")
    alert_evaluation_period        = optional(string, "60s")
    alert_auto_close_duration      = optional(string, "3600s")
  })
  default = {}
}

variable "postgres_performance_monitoring_config" {
  description = "Configuration for PostgreSQL performance monitoring thresholds. Set very low for staging for testing."
  type = object({
    cpu_threshold_percent     = optional(number, 5)
    memory_threshold_percent  = optional(number, 10)
    max_connections_threshold = optional(number, 5)
    deadlock_threshold_count  = optional(number, 0)
  })
  default = {}
}

