# =====================================
# Dev Environment Variables
# =====================================

locals {
  lz_config                      = read_terragrunt_config(find_in_parent_folders("lz.hcl"))
  cicd_service_account_principal = local.lz_config.locals.cicd_service_account_principal
}

inputs = {
  environment_folder_id     = "************" #DematicNonProd/ControlTower/dev
  environment_folder_name   = "dev"
  environment_friendly_name = "Dev"
  environment_name          = "dev"

  auth0_config = {
    domain   = "dev-zmogq9vd.us.auth0.com"
    audience = "https://dev.api.ict.dematic.dev"
  }

  availability_config = {
    url        = "https://system-availability-api-************.us-east1.run.app"
    project_id = "edp-d-us-east2-etl"
  }

  edp_config = {
    project_id      = "edp-d-us-east2-etl"
    pubsub_topic_id = "adi-data"
  }

  enable_uptime_alert_policy = true
  environment_iap_members = [
    "group:<EMAIL>"
  ]

  ignition_upstream_url = "stage-dematic-software.ignition.ict.dematic.dev"

  metric_processor_ingress_config = "INGRESS_TRAFFIC_ALL"

  metric_processor_url = "https://dev-ict-etl-metric-processor-************.us-east1.run.app"

  aiml_config = {
    agent_search_url            = "https://genai.ops.dematic.dev"
    agent_search_audience_url   = "https://dematic-chat-833546053256.us-central1.run.app"
    gold_questions_audience_url = "https://ragengine-833546053256.us-central1.run.app"
  }

  postgres_databases = [
    "dematic",
    "dematic_qa",
    "drt_automation",
    "ict_development",
    "qa_automation",
    "superior_uniform",
    "tti",
    "acehardware"
  ]

  
  # postgres_monitoring_notification_email = "<EMAIL>"
  postgres_monitoring_notification_email = "<EMAIL>"

 # Everything toggled on for testing. We can remove this later if we want to stop alerting in Dev.
  postgres_alerting_config = {
    enable_connection_alerts = true
    enable_connection_count_alerts = true
    enable_cpu_alerts = true
    enable_deadlock_alerts = true
    enable_tps_alerts = true
    enable_memory_alerts = true
}
  
  iam_bindings = {}
}
