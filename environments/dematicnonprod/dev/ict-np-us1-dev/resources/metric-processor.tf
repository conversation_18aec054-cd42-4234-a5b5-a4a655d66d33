module "metric-processor" {
  source = "../../../../../infrastructure/modules/instance/metric-processor"


  project_id = local.project_id
  region     = var.global_default_region

  artifact_registry_repository = data.terraform_remote_state.common.outputs.docker_repository

  cloud_run_config  = var.lz_default_cloud_run_config
  cloud_run_ingress = var.metric_processor_ingress_config

  common_resource_labels = local.common_resource_labels

  container_image = {
    image_name = "metric-processor"
    image_tag  = "latest"
  }

  edp_project_id = var.edp_config.project_id

  enforce_unique_naming = var.enforce_unique_naming

  environment = var.environment_name

  environment_variables = merge(
    local.cloud_run_environment_variables,
    local.api_redis_environment_variables,
    local.api_postgresql_environment_variables,
    {
      PROJECT_ID = local.project_id
    }
  )

  required_roles = var.metricprocessor_service_account_roles

  naming_root = var.global_naming_root

  network_config = {
    enable_flow_logs  = true
    flow_log_config   = var.lz_network_flow_log_config
    network_self_link = module.scaffolding.network_self_link
    subnet_ip_range   = var.global_default_ip_ranges.metric_processor
  }

  vpc_access_connector_config = var.global_default_vpc_access_connector_config
  vpc_access_connector_id     = module.scaffolding.shared_vpc_connection.vpc_access_connector_id

  zone = var.global_default_zone
}
