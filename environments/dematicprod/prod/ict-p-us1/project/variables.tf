# Generated by Terragrunt. Sig: nIlQXj57tbuaRZEa
# =====================================
# US1 Prod Instance Variables
# =====================================

variable "admin_api_config" {
  description = <<EOT
    The configuration for the Admin API Cloud Run service. This is used to set environment 
    variables in the Cloud Run container.
  EOT

  type = object({
    secret_name = string
  })
}

variable "alias_parent_domain_to_self" {
  description = <<EOT
    Set to true to alias the parent domain to the instance's domain. For example, this would make 
    `us1.ict.dematic.cloud` resolve to the same IP address as `ict.dematic.cloud`.
  EOT

  type    = bool
  default = false
}

variable "api_security_roles" {
  description = <<EOT
    The security roles to grant to all API Cloud Run Service Accounts in this instance.
  EOT

  type = list(string)
}

variable "config_api_config_file_path" {
  description = <<EOT
    The path to the API configuration file to use for the environment. This is used to set the 
    configuration for the API Cloud Run service.

    NOTE: This path is relative to the project and resources directories, not the location of 
    this file. It will need to navigate one additional directory up to reach the `config/` 
    directory where the API configuration files are stored.
  EOT

  type = string
}

variable "data_explorer_config" {
  description = <<EOT
    The Data Explorer configuration for this instance of Control Tower. These are applied as 
    environment variables in the Cloud Run container.

    Note: the tables are currently defined in the global configuration.
  EOT

  type = object({
    secret_name = string
    url         = string
  })
}

variable "enforce_unique_naming" {
  description = <<EOT
    Set this to true to use the provided 'environment` value to create a unique prefix for all 
    resources in the module. This is required to allow multiple instances of the module to be 
    deployed in the same GCP project. Used by Review Apps to generate the preview environments.
  EOT

  type = bool
}

variable "instance_friendly_name" {
  description = <<EOT
    A friendly name for the instance, used for display purposes with the environment_name. 
    E.g., `US1`.
  EOT

  type = string
}

variable "instance_name" {
  description = <<EOT
    The name of the instance being that the resources are being deployed to. This value will be 
    combined with the `environment_name` to create the unique identier for this instance. E.g., 
    `us1`.
  EOT

  type = string
}

variable "simulation_api_key_secret_name" {
  description = "The name of the secret containing the API key for the simulation service."
  type        = string
}

variable "subnet_cidr_range" {
  description = "The IP CIDR range to assign to the instance's subnet in the shared VPC."
  type        = string
}

variable "tenant_config_file" {
  description = <<EOT
    The path to the tenant configuration file to use for the environment.

    NOTE: This path is relative to the project and resources directories, not the location of 
    this file. It will need to navigate one additional directory up to reach the `config/` 
    directory where the tenant configuration files are stored.
  EOT

  type = string
}

variable "ui_app_artifact_folder" {
  description = <<EOT
    The path to the UI application build artifact folder (dist) to use for the environment.
  EOT

  type = string
}

variable "use_environment_name_for_instance_name" {
  description = <<EOT
    Set this to true to use the `environment_name` instead of the `instance_name` for the 
    instance's name. For example, in the US1 Dev instance, this would result in the DNS name 
    `dev.ict.dematic.dev`, and the resource naming prefix would be `dev`.

    Note: This will not apply to the project name, because the project name has the option of 
    including both values.
  EOT

  type = bool
}

variable "use_environment_name_in_project_name" {
  description = <<EOT
    Set this to true to use the `environment_name` with the `instance_name` in the project name.
    For example, in the US1 Dev instance, this would result in the project name `ict-np-us1-dev`. 
    In prod, we'd set this to false, so the project name would be `ict-np-us1`.
  EOT

  type = bool
}

variable "use_parent_dns_name" {
  description = <<EOT
    When set to true and passed to the instance modules, the DNS name will not include the instance
    name as a prefix.

    Note: this can only be done for one instance per DNS managed zone.
  EOT

  type    = bool
  default = false
}

# =====================================
# Prod Environment Variables
# =====================================

variable "auth0_config" {
  description = <<EOT
    The Auth0 configuration for the environment, including the domain and audience.
  EOT

  type = object({
    domain   = string
    audience = string
  })
}

variable "availability_config" {
  description = <<EOT
    The Availability configuration for the environment, including the url and projectId.
  EOT

  type = object({
    url        = string
    project_id = string
  })
}

variable "edp_config" {
  description = "The EDP configuration for the environment."
  type = object({
    project_id      = string
    pubsub_topic_id = string
  })
}

variable "enable_uptime_alert_policy" {
  description = <<EOF
    Whether to enable alerting for the uptime check. This should be set to true if you want to
    receive alerts when the monitored resource is down. If enabled, the alert will be wired up to
    the full uptime check.
  EOF

  type = bool
}

variable "environment_folder_id" {
  description = "The ID of the folder that instances for this environment should be created in."
  type        = string
}

variable "environment_folder_name" {
  description = "The name of the folder that instances for this environment should be created in."
  type        = string
}

variable "environment_friendly_name" {
  description = "A friendly name for the environment, used for display purposes."
  type        = string
}

variable "environment_iap_members" {
  description = <<EOT
    An optional list of members that should be granted access to the IAP for the environment. The 
    members should be prefixed with one of: ["serviceAccount:", "user:", "group:"].
  EOT

  type    = list(string)
  default = []

  validation {
    error_message = "All entries must be prefixed with 'serviceAccount:', 'user:', or 'group:'."

    condition = alltrue([
      for member in var.environment_iap_members : (
        startswith(member, "serviceAccount:") ||
        startswith(member, "user:") ||
        startswith(member, "group:")
      )
    ])
  }
}

variable "environment_name" {
  description = <<EOT
    The name of the environment that the resources are being deployed to. This value will be 
    combined with the `naming_root` to create a unique prefix for any resources requiring a 
    globally-unique identifier. Therefore, it must be unique among Control Tower environments but 
    should be as short as possible to comply with GCP resource name length restrictions.
  EOT

  type = string
}

variable "ignition_upstream_url" {
  description = "The URL of the Ignition upstream service that the proxy should connect to."
  type        = string
}

variable "metric_processor_ingress_config" {
  description = "The ingress configuration for the metric processor Cloud Run service"
  type        = string

  validation {
    condition = contains([
      "INGRESS_TRAFFIC_ALL",
      "INGRESS_TRAFFIC_INTERNAL_ONLY",
      "INGRESS_TRAFFIC_INTERNAL_LOAD_BALANCER"
    ], var.metric_processor_ingress_config)
    error_message = "Must be a valid Cloud Run ingress traffic setting"
  }
}

variable "metric_processor_url" {
  description = "The URL for the metric processor that instances in this environment should use."
  type        = string
}

variable "aiml_config" {
  description = <<EOT
    The AIML configuration for the environment.
  EOT

  type = object({
    gold_questions_audience_url = string
    agent_search_url            = string
    agent_search_audience_url   = string
  })
}

variable "postgres_databases" {
  description = "A list of PostgreSQL databases to be created in the environment."
  type        = list(string)
}

variable "postgres_monitoring_notification_email" {
  description = <<EOT
    The email address for sending alerts when a monitoring
    threshold is crossed.
    EOT
  type        = string
}

variable "postgres_performance_monitoring_config" {
  description = "Configuration for PostgreSQL performance monitoring thresholds. In general keep defaults for production."
  type = object({
    cpu_threshold_percent     = optional(number, 80)
    memory_threshold_percent  = optional(number, 85)
    max_connections_threshold = optional(number, 50)
    deadlock_threshold_count  = optional(number, 0)
    transactions_per_second   = optional(number, 50)
  })
  default = {}
}

variable "postgres_alerting_config" {
  description = "Production configuration for PostgreSQL performance alerting. Everything toggled on in production"
  type = object({
    enable_cpu_alerts              = optional(bool, true)
    enable_memory_alerts           = optional(bool, true)
    enable_connection_count_alerts = optional(bool, true)
    enable_tps_alerts              = optional(bool, true)
    alert_duration_threshold       = optional(string, "300s")
    alert_evaluation_period        = optional(string, "60s")
  })
  default = {}
}

# =====================================
# DematicProd Landing Zone Variables
# =====================================

variable "lz_abbreviation" {
  description = "The abbreviation of the target landing zone."
  type        = string
}

variable "lz_api_security_roles" {
  description = <<EOT
    The API security roles for all Control Tower API Cloud Runs in the target landing zone."
  EOT

  type = list(string)
}

variable "lz_cicd_project_id" {
  description = <<EOT
    The ID of the GCP project that contains the CI/CD resources for the target landing zone, 
    including the service account that is used by the GitLab runners to apply Terraform resources. 
    The project can be used by providers in each environment to deploy and manage the environment's 
    project. Other resources should be deployed by a provider scoped to the environment's project.
  EOT

  type = string
}

variable "lz_cloud_run_environment_variables" {
  description = <<EOT
    Any environment variables that should be applied to all Cloud Runs in the target landing zone.
  EOT

  type = map(string)
}

variable "lz_cors_origin" {
  description = <<EOT
    The CORS origin that should be used for all Cloud Run services in the target landing zone. 
    This is used to allow cross-origin requests from the web application to the API.
  EOT

  type = string
}
variable "lz_default_cloud_run_ingress_config" {
  description = "The default Cloud Run ingress configuration for Cloud Runs in the target landing zone."
  type        = string
}
variable "lz_default_cloud_run_config" {
  description = "The default Cloud Run configuration for Cloud Runs in the target landing zone."

  type = object({
    enable_binary_auth = bool

    cpu_limit    = number
    memory_limit = string

    cpu_idle          = bool
    startup_cpu_boost = bool

    min_instances = number
    max_instances = number
  })
}

variable "lz_folder_id" {
  description = "The ID of the GCP folder that hosts the Control Tower target landing zone."
  type        = string
}

variable "lz_folder_name" {
  description = "The name of the target landing zone GCP folder."
  type        = string
}

variable "lz_network_flow_log_config" {
  description = <<EOT
    The default network flow log configuration that should be used by each VPC subnet in the 
    target landing zone. 
  EOT

  type = object({
    aggregation_interval = optional(string)
    flow_sampling        = optional(number)
    metadata             = optional(string)
    metadata_fields      = optional(list(string))
    filter_expr          = optional(string)
  })
}

variable "lz_project_apis" {
  description = <<EOF
    The list of APIs that should be enabled on all Control Tower projects in the target landing 
    zone. This is the default set of APIs that are required for Control Tower to function properly.
  EOF

  type = list(string)
}

variable "lz_project_api_identities" {
  description = <<EOF
    The list of APIs that use service agents that should be enabled on all Control Tower projects 
    in the target landing zone. This will also force the initialization of the service agent so 
    that they can be immediately used by other Terraform resources.
  EOF

  type = list(object({
    api   = string
    roles = list(string)
  }))
}

variable "lz_project_labels" {
  description = "The labels that should be applied to all projects in the target landing zone."
  type        = map(string)
}

variable "lz_resource_annotations" {
  description = <<EOT
    The annotations that should be applied to all compatible resources in the target landing zone.
  EOT

  type = map(string)
}

variable "lz_resource_labels" {
  description = <<EOT
    The labels that should be applied to all target landing zone resources that can be labeled.
  EOT

  type = map(string)
}

variable "lz_shared_vpc_network" {
  description = <<EOT
    The name of the shared VPC network and host project for this landing zone. This VPC is connected 
    to the Dematic NCC hub, providing access to other app resources over private networking.
  EOT

  type = object({
    project_id   = string
    network_name = string
  })
}

variable "lz_short_name" {
  description = "The short name of the target landing zone."
  type        = string
}

variable "lz_simulation_api_url" {
  description = "URL for the simulation canvas API"
}

# =====================================
# Global variables
# =====================================

variable "billing_account_id" {
  description = "The ID of the billing account to use for Control Tower."
  type        = string
}

variable "cost_center" {
  description = "The cost center to use for Control Tower."
  type        = string
}

variable "default_postgresql_username" {
  description = "The default username to use for the PostgreSQL database."
  type        = string
}

variable "gitlab_runner_permissions" {
  description = "The GitLab Runner service account, and the roles that should be granted to it."
  type = object({
    service_account = string
    roles           = list(string)
  })
}

variable "iam_bindings" {
  description = <<EOT
    The IAM bindings to apply to the project. This is a map of role names to lists of members.
  EOT

  type    = map(list(string))
  default = {}
}

variable "global_api_security_roles" {
  description = "The API security roles for all Control Tower API Cloud Runs."
  type        = list(string)
}

variable "global_cloud_run_environment_variables" {
  description = "The environment variables that should be applied to all Control Tower Cloud Runs."
  type        = map(string)
}

variable "global_data_explorer_config" {
  description = <<EOT
    The Data Explorer configuration that is common to all Control Tower environments.
  EOT

  type = object({
    tables = string
  })
}

variable "global_default_alternate_zone" {
  description = "The default alternate zone to use for zonal Control Tower resources."
  type        = string
}

variable "global_default_ip_ranges" {
  description = <<EOT
    The default IP ranges to use for Control Tower subnets, serverless connectors, and the private 
    service connection for Cloud SQL and Memorystore.
  EOT

  type = object({
    api                     = string
    ignition_proxy          = string
    metric_processor        = string
    private_service_connect = string
    typeorm_migrations      = string
  })
}

variable "global_default_backend_cloud_armor_config" {
  description = <<EOT
    The default Cloud Armor configuration for Control Tower load balancer backends.
  EOT

  type = object({
    module_version = string
    layer_7_ddos_config = object({
      enable              = bool
      default_rule_action = string
      rule_visibility     = string
      json_parsing        = string
      log_level           = string
    })
  })
}

variable "global_default_load_balancing_scheme" {
  description = "The default load balancing scheme to use for Control Tower load balancers."
  type        = string
}

variable "global_default_region" {
  description = "The default region to use for regional Control Tower resources."
  type        = string
}

variable "global_default_vpc_access_connector_config" {
  description = <<EOT
    The default VPC Access Connector configuration for Control Tower Cloud Run services.
    This is used to connect Cloud Run services to the VPC network.
  EOT

  type = object({
    machine_type  = string
    min_instances = string
    max_instances = string
  })
}

variable "global_default_zone" {
  description = "The default zone to use for zonal Control Tower resources."
  type        = string
}

variable "global_dns_config" {

  type = object({
    default_a_record_ttl     = number
    default_cname_record_ttl = number
    default_ns_record_ttl    = number

    nonprod_root_zone = object({
      name       = string
      project_id = string
    })

    prod_root_zone = object({
      name       = string
      project_id = string
    })
  })
}

variable "google_health_check_source_ip_ranges" {
  description = "The source IP ranges for Google health checks."
  type        = list(string)
}

variable "google_iap_source_ip_ranges" {
  description = "The source IP ranges for Google IAP."
  type        = list(string)
}

variable "global_naming_root" {
  description = <<EOF
    The root identifier for all Control Tower resources. This value will be combined with the
    `environment` to create a unique prefix for any resources requiring a globally-unique name. 
    This value is intended to be shared across all Control Tower deployments. As of module 
    creation, this value is "ict".
  EOF

  type = string
}

variable "global_oauth_brand_configuration" {
  description = <<EOF
    The OAuth brand configuration for Control Tower environments. This is a map containing the 
    following keys:
      - name: The name of the OAuth brand.
      - support_email: The support email for the OAuth brand.
      - application_title: The title of the application for the OAuth brand.
  EOF

  type = object({
    support_email     = string
    application_title = string
  })
}

variable "global_project_apis" {
  description = <<EOF
    The list of APIs that should be enabled on all Control Tower projects. This includes the 
    default set of APIs that are required for Control Tower to function properly.
  EOF

  type = list(string)
}

variable "global_project_api_identities" {
  description = <<EOF
    The list of APIs that use service agents that should be enabled on all Control Tower projects. 
    This will also force the initialization of the service agent so that they can be immediately 
    used by other Terraform resources.
  EOF

  type = list(object({
    api   = string
    roles = list(string)
  }))
}

variable "global_project_labels" {
  description = "The labels that should be applied to all Control Tower projects."
  type        = map(string)
}

variable "global_resource_labels" {
  description = "The labels that should be applied to all resources that can be labeled."
  type        = map(string)
}

variable "global_shared_vpc_config" {
  description = <<EOT
    The common shared VPC configuration for all Control Tower environments.

    This includes:
    - required_roles: The roles to assign to the Cloud SQL agent service
        account in the shared VPC host project.
  EOT

  type = object({
    required_roles = list(string)
  })
}

variable "metricprocessor_service_account_roles" {
  description = "The roles that should be applied to the metricprocessor service account."
  type        = list(string)
}

variable "minimum_tls_version" {
  description = "The minimum TLS version to use for all Control Tower resources."
  type        = string
}

variable "organization_id" {
  description = "The ID of the GCP organization to create the project in."
  type        = string
}

