# =====================================
# Prod Environment Variables
# =====================================

locals {
  lz_config                      = read_terragrunt_config(find_in_parent_folders("lz.hcl"))
  cicd_service_account_principal = local.lz_config.locals.cicd_service_account_principal
}

inputs = {
  environment_folder_id     = "************" # DematicProd/ControlTower/prod
  environment_folder_name   = "prod"
  environment_friendly_name = "Prod"
  environment_name          = "prod"

  auth0_config = {
    domain   = "control-tower-prod.us.auth0.com"
    audience = "https://api.ict.dematic.cloud"
  }

  availability_config = {
    url        = "https://system-availability-api-************.us-east1.run.app"
    project_id = "edp-p-us-east1-etl"
  }

  edp_config = {
    project_id      = "edp-p-us-east1-etl"
    pubsub_topic_id = "adi-data"
  }

  enable_uptime_alert_policy = true

  ignition_upstream_url = "prod-dematic-software.ignition.ict.dematic.cloud"

  metric_processor_ingress_config = "INGRESS_TRAFFIC_ALL" # Creating a variable so we can test individual environments. When we're ready, we'll make it `INGRESS_TRAFFIC_INTERNAL_ONLY`
  
  metric_processor_url = "https://prod-ict-etl-metric-processor-************.us-east1.run.app"

  aiml_config = {
    agent_search_url            = "https://genai.aiml.dematic.cloud"
    agent_search_audience_url   = "https://dematic-chat-68486065123.us-central1.run.app"
    gold_questions_audience_url = "https://ragengine-68486065123.us-central1.run.app"
  }

  postgres_databases = [
    "dematic_software",
    "dematic",
    "superior_uniform",
    "ict_development",
    "tti",
    "acehardware"
  ]

  postgres_monitoring_notification_email = "<EMAIL>"
  
  # In general we shouldn't need to set any values here because production will use defaults from the module, but we can override here if things get too noisy.
  postgres_alerting_config = {
    enable_connection_alerts = true
    enable_connection_count_alerts = true
    enable_cpu_alerts = true
    enable_deadlock_alerts = true
    enable_slow_query_alerts = true
    enable_memory_alerts = true
    
  }

}
