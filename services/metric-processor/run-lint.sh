#!/bin/bash

# Get the absolute path to the script's directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

# Change to the script's directory
cd "$SCRIPT_DIR"

# Run the linting commands through mise
if [ "$1" = "check" ]; then
    mise x -- poetry run ruff check --fix "$2"
elif [ "$1" = "format" ]; then
    mise x -- poetry run ruff format "$2"
elif [ "$1" = "poetry-check" ]; then
    mise x -- poetry check
elif [ "$1" = "test" ]; then
    mise x -- poetry run test
fi
