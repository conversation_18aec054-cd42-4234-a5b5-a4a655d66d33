"""
Unit tests for the RedisService class.
"""

import unittest
from unittest.mock import patch, MagicMock
import time
from src.services.redis_service import RedisService


class TestRedisService(unittest.TestCase):
    """Unit tests for the RedisService class."""

    def setUp(self):
        """Set up test fixtures."""
        # Reset the singleton instance before each test
        RedisService._instance = None

        # Mock Redis client
        self.mock_redis_client = MagicMock()

        # Patch the redis.StrictRedis to return our mock
        self.redis_patcher = patch('redis.StrictRedis', return_value=self.mock_redis_client)
        self.mock_redis_class = self.redis_patcher.start()

        # Patch environment variables
        self.env_patcher = patch.dict(
            'os.environ',
            {'REDISHOST': 'test-host', 'REDISPORT': '6379', 'REDISDB': '0', 'REDISAUTHSTRING': 'test-auth'},
        )
        self.env_patcher.start()

    def tearDown(self):
        """Clean up after tests."""
        self.redis_patcher.stop()
        self.env_patcher.stop()
        # Reset singleton
        RedisService._instance = None

    def test_singleton_pattern(self):
        """Test that RedisService follows singleton pattern."""
        service1 = RedisService()
        service2 = RedisService()
        service3 = RedisService.get_instance()

        self.assertIs(service1, service2)
        self.assertIs(service1, service3)
        self.assertIs(service2, service3)

    def test_initialization_with_env_vars(self):
        """Test Redis client initialization with environment variables."""
        service = RedisService()

        self.mock_redis_class.assert_called_once_with(host='test-host', port=6379, db=0, password='test-auth')

    @patch.dict('os.environ', {}, clear=True)
    def test_initialization_with_defaults(self):
        """Test Redis client initialization with default values."""
        # Reset singleton to test new initialization
        RedisService._instance = None

        service = RedisService()

        self.mock_redis_class.assert_called_once_with(host='localhost', port=6379, db=0, password='')

    def test_boolean_toggle_true_to_false(self):
        """Test boolean_toggle method when current value is True."""
        service = RedisService()
        self.mock_redis_client.get.return_value = True

        service.boolean_toggle('test_metric', ttl=1800)

        self.mock_redis_client.get.assert_called_once_with('test_metric', False)
        self.mock_redis_client.set.assert_called_once_with('test_metric', False)
        self.mock_redis_client.expire.assert_called_once_with('test_metric', 1800)

    def test_boolean_toggle_false_to_true(self):
        """Test boolean_toggle method when current value is False."""
        service = RedisService()
        self.mock_redis_client.get.return_value = False

        service.boolean_toggle('test_metric')

        self.mock_redis_client.get.assert_called_once_with('test_metric', False)
        self.mock_redis_client.set.assert_called_once_with('test_metric', True)
        self.mock_redis_client.expire.assert_called_once_with('test_metric', 3600)  # default TTL

    def test_boolean_toggle_default_ttl(self):
        """Test boolean_toggle method uses default TTL when not specified."""
        service = RedisService()
        self.mock_redis_client.get.return_value = False

        service.boolean_toggle('test_metric')

        self.mock_redis_client.expire.assert_called_once_with('test_metric', 3600)

    def test_cycle_time_start(self):
        """Test cycle_time_start method stores start time with TTL."""
        service = RedisService()
        event_timestamp = 1234567890.0
        instance_id = 'test_instance'

        service.cycle_time_start('test_metric', event_timestamp=event_timestamp, instance_id=instance_id, ttl=1800)

        self.mock_redis_client.hset.assert_called_once_with('test_metric:start', instance_id, event_timestamp)
        self.mock_redis_client.expire.assert_called_once_with('test_metric:start', 1800)

    def test_cycle_time_start_default_ttl(self):
        """Test cycle_time_start method uses default TTL when not specified."""
        service = RedisService()
        event_timestamp = 1234567890.0
        instance_id = 'test_instance'

        service.cycle_time_start('test_metric', event_timestamp=event_timestamp, instance_id=instance_id)

        self.mock_redis_client.expire.assert_called_once_with('test_metric:start', 3600)

    def test_cycle_time_stop_with_start_time(self):
        """Test cycle_time_stop method when start time exists."""
        service = RedisService()
        event_timestamp = 1234567950.0  # 60 seconds later
        start_timestamp = 1234567890.0
        instance_id = 'test_instance'
        row_hash = 'test_hash'

        # Mock that start time exists
        self.mock_redis_client.hget.return_value = str(start_timestamp)

        service.cycle_time_stop(
            'test_metric', event_timestamp=event_timestamp, instance_id=instance_id, row_hash=row_hash, ttl=1800
        )

        # Verify start time lookup
        self.mock_redis_client.hget.assert_called_once_with('test_metric:start', instance_id)

        # Calculate expected cycle time (60 seconds / 60 = 1 minute)
        expected_cycle_time = (event_timestamp - start_timestamp) / 60
        expected_event_key = f'{row_hash}-{instance_id}_{expected_cycle_time}'

        # Verify zadd call
        self.mock_redis_client.zadd.assert_called_once_with('test_metric', {expected_event_key: event_timestamp})

        # Verify cleanup calls
        self.mock_redis_client.zremrangebyscore.assert_called_once_with('test_metric', 0, event_timestamp - 1800)
        self.mock_redis_client.hdel.assert_called_once_with('test_metric:start', instance_id)

        # Verify TTL is set
        self.mock_redis_client.expire.assert_called_once_with('test_metric', 1800)

    def test_cycle_time_stop_without_start_time(self):
        """Test cycle_time_stop method when start time doesn't exist."""
        service = RedisService()
        event_timestamp = 1234567950.0
        instance_id = 'test_instance'
        row_hash = 'test_hash'

        # Mock that start time doesn't exist
        self.mock_redis_client.hget.return_value = None

        service.cycle_time_stop(
            'test_metric', event_timestamp=event_timestamp, instance_id=instance_id, row_hash=row_hash
        )

        # Verify start time lookup
        self.mock_redis_client.hget.assert_called_once_with('test_metric:start', instance_id)

        # Verify no other Redis operations are called
        self.mock_redis_client.zadd.assert_not_called()
        self.mock_redis_client.zremrangebyscore.assert_not_called()
        self.mock_redis_client.hdel.assert_not_called()

    def test_incr_with_ttl(self):
        """Test incr method increments counter and sets TTL."""
        service = RedisService()

        service.incr('test_metric', ttl=1800)

        self.mock_redis_client.incr.assert_called_once_with('test_metric', 1)
        self.mock_redis_client.expire.assert_called_once_with('test_metric', 1800)

    def test_incr_default_ttl(self):
        """Test incr method uses default TTL when not specified."""
        service = RedisService()

        service.incr('test_metric')

        self.mock_redis_client.incr.assert_called_once_with('test_metric', 1)
        self.mock_redis_client.expire.assert_called_once_with('test_metric', 3600)

    def test_decr_with_ttl(self):
        """Test decr method decrements counter and sets TTL."""
        service = RedisService()

        service.decr('test_metric', ttl=1800)

        self.mock_redis_client.decr.assert_called_once_with('test_metric', 1)
        self.mock_redis_client.expire.assert_called_once_with('test_metric', 1800)

    def test_decr_default_ttl(self):
        """Test decr method uses default TTL when not specified."""
        service = RedisService()

        service.decr('test_metric')

        self.mock_redis_client.decr.assert_called_once_with('test_metric', 1)
        self.mock_redis_client.expire.assert_called_once_with('test_metric', 3600)

    def test_distinct_item_count_with_identifier(self):
        """Test distinct_item_count method with valid identifier."""
        service = RedisService()
        event_timestamp = 1234567890.0
        identifier = 'test_item_123'

        service.distinct_item_count('test_metric', event_timestamp=event_timestamp, identifier=identifier, ttl=1800)

        # Verify zadd call
        self.mock_redis_client.zadd.assert_called_once_with('test_metric', {identifier: event_timestamp})

        # Verify cleanup call
        self.mock_redis_client.zremrangebyscore.assert_called_once_with('test_metric', 0, event_timestamp - 1800)

        # Verify TTL is set
        self.mock_redis_client.expire.assert_called_once_with('test_metric', 1800)

    def test_distinct_item_count_empty_identifier(self):
        """Test distinct_item_count method with empty identifier."""
        service = RedisService()
        event_timestamp = 1234567890.0

        service.distinct_item_count('test_metric', event_timestamp=event_timestamp, identifier='', ttl=1800)

        # Verify no Redis operations are called
        self.mock_redis_client.zadd.assert_not_called()
        self.mock_redis_client.zremrangebyscore.assert_not_called()

    def test_distinct_item_count_default_ttl(self):
        """Test distinct_item_count method uses default TTL when not specified."""
        service = RedisService()
        event_timestamp = 1234567890.0
        identifier = 'test_item_123'

        service.distinct_item_count('test_metric', event_timestamp=event_timestamp, identifier=identifier)

        # Verify cleanup call with default TTL
        self.mock_redis_client.zremrangebyscore.assert_called_once_with('test_metric', 0, event_timestamp - 3600)

        # Verify TTL is set with default value
        self.mock_redis_client.expire.assert_called_once_with('test_metric', 3600)

    def test_distinct_item_subtract_with_identifier(self):
        """Test distinct_item_subtract method with valid identifier."""
        service = RedisService()
        event_timestamp = 1234567890.0
        identifier = 'test_item_123'

        service.distinct_item_subtract('test_metric', event_timestamp=event_timestamp, identifier=identifier, ttl=1800)

        # Verify zrem call
        self.mock_redis_client.zrem.assert_called_once_with('test_metric', identifier)

        # Verify cleanup call
        self.mock_redis_client.zremrangebyscore.assert_called_once_with('test_metric', 0, event_timestamp - 1800)

        # Verify TTL is set
        self.mock_redis_client.expire.assert_called_once_with('test_metric', 1800)

    def test_distinct_item_subtract_empty_identifier(self):
        """Test distinct_item_subtract method with empty identifier."""
        service = RedisService()
        event_timestamp = 1234567890.0

        service.distinct_item_subtract('test_metric', event_timestamp=event_timestamp, identifier='', ttl=1800)

        # Verify no Redis operations are called
        self.mock_redis_client.zrem.assert_not_called()
        self.mock_redis_client.zremrangebyscore.assert_not_called()

    def test_event_set(self):
        """Test event_set method stores event data with TTL."""
        service = RedisService()
        event_timestamp = 1234567890.0
        row_hash = 'test_hash'
        value = 42.5

        service.event_set('test_metric', event_timestamp=event_timestamp, row_hash=row_hash, value=value, ttl=1800)

        expected_event_key = f'{row_hash}_{value}'

        # Verify zadd call
        self.mock_redis_client.zadd.assert_called_once_with('test_metric', {expected_event_key: event_timestamp})

        # Verify cleanup call
        self.mock_redis_client.zremrangebyscore.assert_called_once_with('test_metric', 0, event_timestamp - 1800)

        # Verify TTL is set
        self.mock_redis_client.expire.assert_called_once_with('test_metric', 1800)

    def test_event_set_no_value(self):
        """Test event_set method when value is None or 0."""
        service = RedisService()
        event_timestamp = 1234567890.0
        row_hash = 'test_hash'

        service.event_set('test_metric', event_timestamp=event_timestamp, row_hash=row_hash, value=None, ttl=1800)

        expected_event_key = f'{row_hash}_0'

        # Verify zadd call with 0 as default value
        self.mock_redis_client.zadd.assert_called_once_with('test_metric', {expected_event_key: event_timestamp})

        # Verify cleanup and TTL calls
        self.mock_redis_client.zremrangebyscore.assert_called_once_with('test_metric', 0, event_timestamp - 1800)
        self.mock_redis_client.expire.assert_called_once_with('test_metric', 1800)

    def test_event_set_default_ttl(self):
        """Test event_set method uses default TTL when not specified."""
        service = RedisService()
        event_timestamp = 1234567890.0
        row_hash = 'test_hash'

        service.event_set('test_metric', event_timestamp=event_timestamp, row_hash=row_hash)

        # Verify cleanup call with default TTL
        self.mock_redis_client.zremrangebyscore.assert_called_once_with('test_metric', 0, event_timestamp - 3600)

        # Verify TTL is set with default value
        self.mock_redis_client.expire.assert_called_once_with('test_metric', 3600)

    @patch('time.time')
    def test_set_last_processed_time(self, mock_time):
        """Test set_last_processed_time method stores current time."""
        mock_time.return_value = 1234567890.0
        service = RedisService()
        tenant = 'test_tenant'

        service.set_last_processed_time(tenant)

        expected_metric_name = f'{tenant}:last_processed_time'
        self.mock_redis_client.set.assert_called_once_with(expected_metric_name, 1234567890.0)
        # Note: This method doesn't call expire - it only sets the value

    def test_storage_location_distribution_available(self):
        """Test storage_location_distribution_available method stores count with TTL."""
        service = RedisService()
        empty_count = 150.0

        service.storage_location_distribution_available(
            'test_metric', empty_location_position_count=empty_count, ttl=1800
        )

        self.mock_redis_client.set.assert_called_once_with('test_metric', empty_count)
        self.mock_redis_client.expire.assert_called_once_with('test_metric', 1800)

    def test_storage_location_distribution_available_default_ttl(self):
        """Test storage_location_distribution_available method uses default TTL."""
        service = RedisService()
        empty_count = 150.0

        service.storage_location_distribution_available('test_metric', empty_location_position_count=empty_count)

        self.mock_redis_client.expire.assert_called_once_with('test_metric', 3600)

    def test_storage_location_distribution_occupied(self):
        """Test storage_location_distribution_occupied method calculates and stores occupied count."""
        service = RedisService()
        empty_count = 50.0
        total_count = 200.0

        service.storage_location_distribution_occupied('test_metric', empty_count, total_count, ttl=1800)

        # Calculate expected occupied: 200 - 50 = 150
        expected_occupied = 150.0

        self.mock_redis_client.set.assert_called_once_with('test_metric', expected_occupied)
        self.mock_redis_client.expire.assert_called_once_with('test_metric', 1800)

    def test_storage_location_distribution_occupied_default_ttl(self):
        """Test storage_location_distribution_occupied method uses default TTL."""
        service = RedisService()
        empty_count = 50.0
        total_count = 200.0

        service.storage_location_distribution_occupied('test_metric', empty_count, total_count)

        self.mock_redis_client.expire.assert_called_once_with('test_metric', 3600)

    def test_store_static_value_with_value(self):
        """Test store_static_value method stores value with TTL."""
        service = RedisService()
        test_value = 'test_static_value'

        service.store_static_value('test_metric', value=test_value, ttl=1800)

        self.mock_redis_client.set.assert_called_once_with('test_metric', test_value)
        self.mock_redis_client.expire.assert_called_once_with('test_metric', 1800)

    def test_store_static_value_no_value(self):
        """Test store_static_value method does nothing when value is None."""
        service = RedisService()

        service.store_static_value('test_metric', value=None, ttl=1800)

        # Verify no Redis operations are called
        self.mock_redis_client.set.assert_not_called()
        self.mock_redis_client.expire.assert_not_called()

    def test_store_static_value_default_ttl(self):
        """Test store_static_value method uses default TTL when not specified."""
        service = RedisService()
        test_value = 'test_static_value'

        service.store_static_value('test_metric', value=test_value)

        self.mock_redis_client.expire.assert_called_once_with('test_metric', 86400)

    def test_store_handling_unit_for_edge(self):
        """Test store_handling_unit_for_edge method stores data with TTL."""
        service = RedisService()
        handling_unit_key = 'test_handling_unit'
        leave_time = '2023-01-01T12:00:00Z'
        outbound_area = 'test_area'
        units = 5
        outbound_node_label = 'test_label'

        service.store_handling_unit_for_edge(
            handling_unit_key, leave_time, outbound_area, units, outbound_node_label, ttl=1800
        )

        expected_value = {
            'leave_time': leave_time,
            'outbound_area': outbound_area,
            'units': units,
            'outbound_node_label': outbound_node_label,
        }

        self.mock_redis_client.hset.assert_called_once_with(handling_unit_key, mapping=expected_value)
        self.mock_redis_client.expire.assert_called_once_with(handling_unit_key, 1800)

    def test_store_handling_unit_for_edge_with_parent_nodes(self):
        """Test store_handling_unit_for_edge method with outbound_parent_nodes."""
        service = RedisService()
        handling_unit_key = 'test_handling_unit'
        leave_time = '2023-01-01T12:00:00Z'
        outbound_area = 'test_area'
        units = 5
        outbound_node_label = 'test_label'
        outbound_parent_nodes = ['node1', 'node2']

        service.store_handling_unit_for_edge(
            handling_unit_key,
            leave_time,
            outbound_area,
            units,
            outbound_node_label,
            outbound_parent_nodes=outbound_parent_nodes,
            ttl=1800,
        )

        expected_value = {
            'leave_time': leave_time,
            'outbound_area': outbound_area,
            'units': units,
            'outbound_node_label': outbound_node_label,
            'outbound_parent_nodes': str(outbound_parent_nodes),
        }

        self.mock_redis_client.hset.assert_called_once_with(handling_unit_key, mapping=expected_value)
        self.mock_redis_client.expire.assert_called_once_with(handling_unit_key, 1800)

    def test_store_handling_unit_for_edge_default_ttl(self):
        """Test store_handling_unit_for_edge method uses default TTL when not specified."""
        service = RedisService()
        handling_unit_key = 'test_handling_unit'
        leave_time = '2023-01-01T12:00:00Z'
        outbound_area = 'test_area'
        units = 5
        outbound_node_label = 'test_label'

        service.store_handling_unit_for_edge(handling_unit_key, leave_time, outbound_area, units, outbound_node_label)

        self.mock_redis_client.expire.assert_called_once_with(handling_unit_key, 3600)
