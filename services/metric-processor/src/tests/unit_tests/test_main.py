"""
Unit tests for the main Flask application endpoints.
"""

import base64
import json
import unittest
from unittest.mock import patch, MagicMock
from src.main import app, process_facts, get_metrics, log_message, publish_to_dead_letter_topic


class TestMainEndpoints(unittest.TestCase):
    """Unit tests for the main Flask application endpoints."""

    def setUp(self):
        """Set up test fixtures."""
        self.app = app
        self.app.config['TESTING'] = True
        self.client = self.app.test_client()

        # Sample test data
        self.sample_fact_data = {'equipment_code': 'M11-GTP-01', 'timestamp': '2023-10-01T10:00:00Z', 'value': 42}

        self.sample_cloud_event = {
            'message': {
                'data': base64.b64encode(json.dumps(self.sample_fact_data).encode()).decode(),
                'attributes': {
                    'tenant_id': 'test-tenant',
                    'facility_id': 'test-facility',
                    'event_type': 'nok_interval',
                    'row_hash': 'test-hash-123',
                },
            }
        }

        self.sample_metrics_request = {
            'tenant_id': 'test-tenant',
            'facility_id': 'test-facility',
            'metrics': [
                {
                    'id': 'test-tenant:test-facility:workstations:arrivals_per_fault_denominator:60m_set:ratio',
                    'fact_type': 'nok_interval',
                    'created_by': 'workstations_arrivals_per_fault_denominator',
                }
            ],
        }

    def test_process_message_endpoint_success(self):
        """Test successful processing of a message through POST / endpoint."""
        with patch('src.main.process_facts') as mock_process_facts:
            mock_process_facts.return_value = ({'status': 'success'}, 200)

            response = self.client.post('/', data=json.dumps(self.sample_cloud_event), content_type='application/json')

            self.assertEqual(response.status_code, 200)
            data = json.loads(response.data)
            self.assertEqual(data['status'], 'success')
            mock_process_facts.assert_called_once()

    def test_process_message_endpoint_no_message(self):
        """Test POST / endpoint with no message data."""
        response = self.client.post('/', data=json.dumps({'invalid': 'data'}), content_type='application/json')

        self.assertEqual(response.status_code, 400)
        data = json.loads(response.data)
        self.assertIn('error', data)
        self.assertIn('No valid message data', data['error'])

    def test_process_message_endpoint_invalid_json(self):
        """Test POST / endpoint with invalid JSON."""
        response = self.client.post('/', data='invalid json', content_type='application/json')

        self.assertEqual(response.status_code, 400)

    def test_metrics_endpoint_success(self):
        """Test successful metrics retrieval through POST /metrics/ endpoint."""
        with patch('src.main.get_metrics') as mock_get_metrics:
            mock_response = {
                'status': 'success',
                'metrics': [
                    {
                        'id': 'test-metric',
                        'value': 0.75,
                        'status': 'ok',  # This is what we want to test!
                    }
                ],
                'last_processed_time': '2023-10-01T10:00:00Z',
            }
            mock_get_metrics.return_value = (mock_response, 200)

            response = self.client.post(
                '/metrics/', data=json.dumps(self.sample_metrics_request), content_type='application/json'
            )

            self.assertEqual(response.status_code, 200)
            data = json.loads(response.data)
            self.assertEqual(data['status'], 'success')
            self.assertIn('metrics', data)
            self.assertIn('status', data['metrics'][0])  # Verify status is included
            mock_get_metrics.assert_called_once()

    def test_metrics_endpoint_missing_fields(self):
        """Test POST /metrics/ endpoint with missing required fields."""
        # Missing tenant_id
        invalid_request = {'facility_id': 'test-facility', 'metrics': []}
        response = self.client.post('/metrics/', data=json.dumps(invalid_request), content_type='application/json')

        # Endpoint returns 400 for invalid request format
        self.assertEqual(response.status_code, 400)
        data = json.loads(response.data)
        self.assertIn('error', data)

    def test_metrics_endpoint_no_metrics(self):
        """Test POST /metrics/ endpoint with no metrics."""
        empty_request = {'tenant_id': 'test-tenant', 'facility_id': 'test-facility', 'metrics': []}
        response = self.client.post('/metrics/', data=json.dumps(empty_request), content_type='application/json')

        self.assertEqual(response.status_code, 422)


class TestProcessFacts(unittest.TestCase):
    """Unit tests for the process_facts function."""

    def setUp(self):
        """Set up test fixtures."""
        self.sample_fact_data = {'equipment_code': 'M11-GTP-01', 'timestamp': '2023-10-01T10:00:00Z', 'value': 42}

        self.valid_cloud_event = {
            'message': {
                'data': base64.b64encode(json.dumps(self.sample_fact_data).encode()).decode(),
                'attributes': {
                    'tenant_id': 'test-tenant',
                    'facility_id': 'test-facility',
                    'event_type': 'nok_interval',
                    'row_hash': 'test-hash-123',
                },
            }
        }

    @patch('src.main.METRIC_PROCESSOR_FACILITY_ALLOW_LIST', ['test-facility'])
    @patch('src.main.MetricProcessor')
    @patch('src.main.config_loader')
    @patch('src.main.log_message')
    def test_process_facts_success(self, mock_log_message, mock_config_loader, mock_metric_processor):
        """Test successful fact processing."""
        # Mock config loader
        mock_config_loader.get_config_by_fact_type.return_value = {'test_config': 'data'}

        # Mock metric processor
        mock_processor_instance = MagicMock()
        mock_metric_processor.return_value = mock_processor_instance

        result, status_code = process_facts(self.valid_cloud_event)

        self.assertEqual(status_code, 200)
        self.assertEqual(result['status'], 'success')
        mock_config_loader.get_config_by_fact_type.assert_called_once_with(
            'test-tenant', 'test-facility', 'nok_interval'
        )
        mock_processor_instance.process_metrics.assert_called_once()

    def test_process_facts_no_data(self):
        """Test process_facts with no data in message."""
        cloud_event = {'message': {'data': base64.b64encode(b'').decode(), 'attributes': {}}}

        result, status_code = process_facts(cloud_event)

        self.assertEqual(status_code, 422)
        self.assertIn('error', result)
        self.assertIn('No data found', result['error'])

    def test_process_facts_missing_attributes(self):
        """Test process_facts with missing required attributes."""
        cloud_event = {
            'message': {
                'data': base64.b64encode(json.dumps(self.sample_fact_data).encode()).decode(),
                'attributes': {
                    'tenant_id': 'test-tenant',
                    # Missing facility_id, event_type, row_hash
                },
            }
        }

        result, status_code = process_facts(cloud_event)

        self.assertEqual(status_code, 200)
        self.assertEqual(result['status'], 'ignored')

    @patch('src.main.METRIC_PROCESSOR_FACILITY_ALLOW_LIST', ['allowed-facility'])
    def test_process_facts_facility_not_allowed(self):
        """Test process_facts with facility not in allow list."""
        cloud_event = {
            'message': {
                'data': base64.b64encode(json.dumps(self.sample_fact_data).encode()).decode(),
                'attributes': {
                    'tenant_id': 'test-tenant',
                    'facility_id': 'not-allowed-facility',
                    'event_type': 'nok_interval',
                    'row_hash': 'test-hash-123',
                },
            }
        }

        result, status_code = process_facts(cloud_event)

        self.assertEqual(status_code, 200)
        self.assertEqual(result['status'], 'ignored')
        self.assertIn('not in the allow list', result['message'])

    @patch('src.main.METRIC_PROCESSOR_FACILITY_ALLOW_LIST', ['test-facility'])
    def test_process_facts_invalid_json(self):
        """Test process_facts with invalid JSON data."""
        cloud_event = {
            'message': {
                'data': base64.b64encode(b'invalid json').decode(),
                'attributes': {
                    'tenant_id': 'test-tenant',
                    'facility_id': 'test-facility',
                    'event_type': 'nok_interval',
                    'row_hash': 'test-hash-123',
                },
            }
        }

        result, status_code = process_facts(cloud_event)

        self.assertEqual(status_code, 400)
        self.assertIn('error', result)
        self.assertIn('Invalid JSON format', result['error'])

    @patch('src.main.METRIC_PROCESSOR_FACILITY_ALLOW_LIST', ['test-facility'])
    @patch('src.main.config_loader')
    def test_process_facts_config_loading_error(self, mock_config_loader):
        """Test process_facts with config loading error."""
        mock_config_loader.get_config_by_fact_type.side_effect = ConnectionError('Database connection failed')

        result, status_code = process_facts(self.valid_cloud_event)

        self.assertEqual(status_code, 503)
        self.assertIn('error', result)
        self.assertIn('Database connection failed', result['error'])

    @patch('src.main.METRIC_PROCESSOR_FACILITY_ALLOW_LIST', ['test-facility'])
    @patch('src.main.config_loader')
    def test_process_facts_no_config(self, mock_config_loader):
        """Test process_facts when no config is found."""
        mock_config_loader.get_config_by_fact_type.return_value = None

        result, status_code = process_facts(self.valid_cloud_event)

        self.assertEqual(status_code, 200)
        self.assertEqual(result['status'], 'ignored')


class TestGetMetrics(unittest.TestCase):
    """Unit tests for the get_metrics function."""

    def setUp(self):
        """Set up test fixtures."""
        self.valid_request = {
            'tenant_id': 'test-tenant',
            'facility_id': 'test-facility',
            'metric_ids': ['test-tenant:test-facility:workstations:arrivals_per_fault_denominator:60m_set:ratio'],
        }

    @patch('src.main.MetricService')
    def test_get_metrics_success_with_status(self, mock_metric_service_class):
        """Test successful metrics retrieval (status calculation moved to inventory-api)."""
        # Mock MetricService instance
        mock_service = MagicMock()
        mock_metric_service_class.return_value = mock_service

        # Mock the response - status is now null (calculated in inventory-api)
        mock_service.get_metric_values.return_value = {
            'last_processed_time': b'2023-10-01T10:00:00Z',
            'metrics': [
                {
                    'id': 'test-metric',
                    'value': 0.75,
                    'status': None,  # Status calculation moved to inventory-api
                    'type': 'ratio',
                    'fact_type': 'nok_interval',
                    'created_by': 'workstations_arrivals_per_fault_denominator',
                }
            ],
        }

        result, status_code = get_metrics(self.valid_request)

        self.assertEqual(status_code, 200)
        self.assertEqual(result['status'], 'success')
        self.assertIn('metrics', result)
        self.assertEqual(len(result['metrics']), 1)

        # Verify status is null (will be calculated by inventory-api)
        metric = result['metrics'][0]
        self.assertIn('status', metric)
        self.assertIsNone(metric['status'])
        self.assertEqual(metric['value'], 0.75)

        # Verify MetricService was called correctly
        mock_service.get_metric_values.assert_called_once_with(self.valid_request['metric_ids'], 'test-tenant')

    def test_get_metrics_no_metrics(self):
        """Test get_metrics with no metrics in request."""
        request = {'tenant_id': 'test-tenant', 'facility_id': 'test-facility', 'metric_ids': []}

        result, status_code = get_metrics(request)

        self.assertEqual(status_code, 422)
        self.assertIn('error', result)
        self.assertIn('No metric_ids found', result['error'])

    def test_get_metrics_missing_tenant_id(self):
        """Test get_metrics with missing tenant_id."""
        request = {'facility_id': 'test-facility', 'metric_ids': ['test-metric']}

        result, status_code = get_metrics(request)

        self.assertEqual(status_code, 422)
        self.assertIn('error', result)
        self.assertIn('No tenant_id found', result['error'])

    def test_get_metrics_missing_facility_id(self):
        """Test get_metrics with missing facility_id - not required by get_metrics."""
        request = {'tenant_id': 'test-tenant', 'metric_ids': ['test-metric']}

        result, status_code = get_metrics(request)

        # facility_id is not required for get_metrics, so this should succeed or fail differently
        # The test should check the actual behavior
        self.assertIn(status_code, [200, 422, 500])

    def test_get_metrics_empty_metrics_list(self):
        """Test get_metrics with empty metrics list."""
        request = {'tenant_id': 'test-tenant', 'facility_id': 'test-facility', 'metric_ids': []}

        result, status_code = get_metrics(request)

        self.assertEqual(status_code, 422)
        self.assertIn('error', result)
        self.assertIn('No metric_ids found', result['error'])

    @patch('src.main.MetricService')
    def test_get_metrics_service_exception(self, mock_metric_service_class):
        """Test get_metrics when MetricService raises an exception."""
        mock_service = MagicMock()
        mock_metric_service_class.return_value = mock_service
        mock_service.get_metric_values.side_effect = Exception('Redis connection failed')

        result, status_code = get_metrics(self.valid_request)

        self.assertEqual(status_code, 500)
        self.assertIn('error', result)
        self.assertIn('error occurred while processing', result['error'])

    @patch('src.main.MetricService')
    def test_get_metrics_multiple_metrics_with_different_statuses(self, mock_metric_service_class):
        """Test get_metrics with multiple metrics (status is null, calculated in inventory-api)."""
        mock_service = MagicMock()
        mock_metric_service_class.return_value = mock_service

        # Mock response with multiple metrics - all have null status
        mock_service.get_metric_values.return_value = {
            'last_processed_time': b'2023-10-01T10:00:00Z',
            'metrics': [
                {'id': 'metric-1', 'value': 0.35, 'status': None, 'type': 'ratio'},
                {'id': 'metric-2', 'value': 0.15, 'status': None, 'type': 'ratio'},
                {'id': 'metric-3', 'value': 0.25, 'status': None, 'type': 'ratio'},
                {'id': 'metric-4', 'value': 42, 'status': None, 'type': 'count'},
            ],
        }

        request = {
            'tenant_id': 'test-tenant',
            'facility_id': 'test-facility',
            'metric_ids': ['metric-1', 'metric-2', 'metric-3', 'metric-4'],
        }

        result, status_code = get_metrics(request)

        self.assertEqual(status_code, 200)
        self.assertEqual(result['status'], 'success')
        self.assertEqual(len(result['metrics']), 4)

        # Verify all metrics have null status (calculated in inventory-api)
        metrics = result['metrics']
        self.assertIsNone(metrics[0]['status'])
        self.assertIsNone(metrics[1]['status'])
        self.assertIsNone(metrics[2]['status'])
        self.assertIsNone(metrics[3]['status'])


class TestUtilityFunctions(unittest.TestCase):
    """Unit tests for utility functions."""

    @patch('src.main.logger')
    def test_log_message_basic(self, mock_logger):
        """Test basic log_message functionality."""
        fact_type = 'nok_interval'
        fact_obj = {'equipment_code': 'M11-GTP-01', 'value': 42}
        attributes = {'tenant_id': 'test-tenant', 'facility_id': 'test-facility'}

        log_message(fact_type, fact_obj, attributes)

        mock_logger.info.assert_called_once()
        call_args = mock_logger.info.call_args
        self.assertEqual(call_args[0][0], 'Processing message')
        self.assertEqual(call_args[1]['fact_type'], fact_type)
        self.assertEqual(call_args[1]['fact_obj'], fact_obj)

    @patch('src.main.pubsub_v1.PublisherClient')
    @patch('src.main.os.getenv')
    def test_publish_to_dead_letter_topic_success(self, mock_getenv, mock_publisher_client):
        """Test successful publishing to dead letter topic."""
        mock_getenv.return_value = 'test-project'
        mock_publisher = MagicMock()
        mock_publisher_client.return_value = mock_publisher
        mock_future = MagicMock()
        mock_publisher.publish.return_value = mock_future

        message = {'test': 'data'}
        attributes = {'tenant_id': 'test-tenant'}

        publish_to_dead_letter_topic(message, attributes)

        mock_publisher.publish.assert_called_once()
        mock_future.result.assert_called_once()

    @patch('src.main.logger')
    def test_publish_to_dead_letter_topic_no_topic(self, mock_logger):
        """Test publish_to_dead_letter_topic when no topic is configured."""
        with patch('src.main.DEAD_LETTER_TOPIC', ''):
            publish_to_dead_letter_topic({'test': 'data'}, {})

            mock_logger.error.assert_called_once_with('Dead letter topic is not configured.')


class TestRequestLifecycle(unittest.TestCase):
    """Unit tests for request lifecycle management."""

    def setUp(self):
        """Set up test fixtures."""
        self.app = app
        self.app.config['TESTING'] = True
        self.client = self.app.test_client()

    @patch('src.main.structlog.threadlocal.bind_threadlocal')
    @patch('src.main.logger')
    def test_request_uuid_generation(self, mock_logger, mock_bind_threadlocal):
        """Test that request UUID is generated and bound for each request."""
        with patch('src.main.process_facts', return_value=({'status': 'success'}, 200)):
            cloud_event = {'message': {'data': base64.b64encode(b'{"test": "data"}').decode(), 'attributes': {}}}

            self.client.post('/', data=json.dumps(cloud_event), content_type='application/json')

            # Verify UUID was bound to thread local
            mock_bind_threadlocal.assert_called_once()
            call_args = mock_bind_threadlocal.call_args[1]
            self.assertIn('request_id', call_args)

            # Verify request started log
            mock_logger.info.assert_called()

    @patch('src.main.structlog.threadlocal.clear_threadlocal')
    @patch('src.main.logger')
    def test_request_cleanup(self, mock_logger, mock_clear_threadlocal):
        """Test that request context is cleared after each request."""
        with patch('src.main.process_facts', return_value=({'status': 'success'}, 200)):
            cloud_event = {'message': {'data': base64.b64encode(b'{"test": "data"}').decode(), 'attributes': {}}}

            self.client.post('/', data=json.dumps(cloud_event), content_type='application/json')

            # Verify thread local was cleared
            mock_clear_threadlocal.assert_called_once()


if __name__ == '__main__':
    unittest.main()
