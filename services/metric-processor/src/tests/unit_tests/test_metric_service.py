"""
Unit tests for the MetricService class.
"""

import unittest
from unittest.mock import patch
from src.services.metric_service import MetricService


class TestMetricService(unittest.TestCase):
    """Unit tests for the MetricService class."""

    def setUp(self):
        """Set up test fixtures."""
        # Mock Redis connection to avoid actual Redis dependency
        with patch('redis.StrictRedis'):
            self.service = MetricService()

    def test_service_initialization(self):
        """Test that MetricService can be initialized."""
        self.assertIsNotNone(self.service)
        self.assertIsInstance(self.service, MetricService)

    # TODO: Add tests for other MetricService methods as needed
    # (e.g., get_metric_values, build_redis_command, process_results, build_response)


if __name__ == '__main__':
    unittest.main()
