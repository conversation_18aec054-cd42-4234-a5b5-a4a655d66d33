import ast
import datetime
import json
import os
import re
from typing import Any, Dict, List, Optional
import structlog
import psycopg2
from .services.neo4j_factory import Neo4jFactory
from .services.redis_service import RedisService
from .services.postgres_factory import PostgresFactory
from .utils import (
    EVENT_TIME_EXPECTED_DELAY_MINUTES,
    EVENT_TIME_PROCESSING_WINDOW_MINUTES,
    EXCLUDED_EDGES,
    get_event_time,
)
import src.schemas.config_schema as config_schema
from .config_loader import ConfigLoader

logging = structlog.get_logger()


class MetricProcessor:
    """
    Processes metrics based on configurations for tenants, facilities, and fact types.

    This class handles loading configurations, validating metric conditions,
    and updating systems such as Redis and Neo4j based on the processed metrics.
    The four types of metric configs drive the core logic of the processor. They are:
    - outbound-edge
    - inbound-edge
    - complete-edge
    - node

    Each metric config is processed for each of the views in the configs 'views' list.

    Attributes:
        config_cache (dict): Class-level cache for storing loaded configurations.
        tenant (str): The tenant identifier.
        facility (str): The facility identifier.
        fact_type (str): The type of fact to process.
        data (dict): The data used for metric processing.
        config (dict): The loaded configuration for the processor.
        redis_service (RedisService): Redis service for storing metrics.
        neo4j_service (Neo4jService): Neo4j service for graph operations.
        is_local_dev (bool): Indicates if the processor is running in local development mode.
    """

    # Shared class-level dictionary to hold configurations
    config_cache = {}

    def __init__(
        self,
        tenant: str,
        facility: str,
        fact_type: str,
        row_hash: str,
        data: Dict[str, Any],
        config: dict,
    ):
        """
        Initializes MetricProcessor only if a valid configuration is provided.

        Args:
            tenant (str): The tenant identifier (e.g., "tenant_a").
            facility (str): The facility identifier (e.g., "facility_1").
            fact_type (str): The type of fact or data configuration to process (e.g., "inventory").
            data (dict): The input data containing event details.
            config (dict): The preloaded and validated metric configuration.
        """
        self.tenant = tenant
        self.facility = facility
        self.fact_type = fact_type
        self.row_hash = row_hash
        self.data = data
        self.config = config
        self.request_id = structlog.threadlocal.get_threadlocal().get('request_id', 'unknown')
        self.redis_service = RedisService()
        self.neo4j_service = Neo4jFactory.get_instance(tenant, facility, self.redis_service)
        self.is_local_dev = os.getenv('LOCALDEV', 'false')

    def process_metrics(self):
        """
        Processes metrics based on the loaded configuration, applying condition checks and updating systems.

        For each metric, this method:
        - Validates the metric configuration.
        - Checks match conditions against the input data.
        - Executes Redis operations and graph updates as required.
        """
        if not self.config:
            logging.debug('No configuration loaded, skipping metric processing.')
            return

        for metric_config_name, metric_config in self.config.items():
            try:
                if not self._is_match(metric_config.match_conditions):
                    continue  # Skip processing this metric if not matched against any metric configs

                # Mark this config as actively used for this facility
                self._update_config_active_status(metric_config)

                # Process each view for the metric config
                self._process_metric_config_views(metric_config, metric_config_name)

            except Exception as e:
                logging.exception(
                    'Error processing metric',
                    metric_config_name=metric_config_name,
                    fact_type=self.fact_type,
                    tenant=self.tenant,
                    facility=self.facility,
                    error=str(e),
                    request_id=self.request_id,
                )
        logging.info(
            'Metric processing completed',
            fact_type=self.fact_type,
            tenant=self.tenant,
            facility=self.facility,
            request_id=self.request_id,
        )

    def _process_metric_with_resolved_metric_name(
        self,
        resolved_name: str,
        metric_config: Dict[str, Any],
        view: str,
        metric_config_name: str,
        **kwargs,
    ) -> None:
        """
        Processes an individual metric by updating Redis and optionally the graph.
        This method is invoked after determining the complete qualified metric name.
        It is used by metric configurations for nodes, inbound edges, and complete edges.

        Args:
            resolved_name (str): The name of the resolved metric.
            metric_config (dict): Configuration details for the metric.
            view (str): The graph view that contains the metric.
            metric_config_name (str): The key of the metric config being processed.
        """

        # Load the excluded nodes from the config loader
        config_loader = ConfigLoader.get_instance()
        excluded_nodes = config_loader.load_excluded_nodes()

        # Check if the metric name or view contains any excluded nodes
        for excluded_node in excluded_nodes:
            if excluded_node in resolved_name or excluded_node in view:
                logging.info(
                    'Skipping metric due to excluded node',
                    view=view,
                    metric=metric_config_name,
                    resolved_name=resolved_name,
                    excluded_node=excluded_node,
                )
                return

        # Extract the event timestamp from the event data to use in Redis.
        event_timestamp = get_event_time(self.fact_type, self.data)

        # Only process events for which we can determine the time they occurred.
        if event_timestamp is not None:
            now = datetime.datetime.now(datetime.timezone.utc)
            processing_window = int(EVENT_TIME_PROCESSING_WINDOW_MINUTES) + int(EVENT_TIME_EXPECTED_DELAY_MINUTES)

            # Only process metrics that are less than one hour plus expected delay.
            if event_timestamp > now - datetime.timedelta(minutes=processing_window):
                # Always update the graph to ensure views are up to date, regardless of Redis cache status
                graph_operation = metric_config.graph_operation
                if graph_operation:
                    # Add parent nodes to kwargs if they exist
                    parent_node_types = [
                        'parent_nodes',
                        'inbound_parent_nodes',
                        'outbound_parent_nodes',
                    ]
                    # Only add parent nodes from metric_config if they're not already in kwargs
                    # Depending on the metric type, the parent_nodes might already be in kwargs,
                    # and might have been dynamically resolved.
                    for node_type in parent_node_types:
                        if node_type not in kwargs and getattr(metric_config, node_type, None):
                            kwargs[node_type] = getattr(metric_config, node_type)
                    kwargs['metric_units'] = metric_config.metric_units if metric_config.metric_units else ''
                    kwargs['metric_description'] = metric_config.description if metric_config.description else ''
                    kwargs['metric_config_name'] = metric_config_name
                    self._update_graph(
                        self._format_metric_name_for_neo4j(resolved_name),
                        graph_operation,
                        view,
                        **kwargs,
                    )

                # Add more params to redis_params.
                redis_params = metric_config.redis_params if metric_config.redis_params else {}
                redis_params['row_hash'] = self.row_hash
                # We want any param contained in a match_condition to be added to redis_params.
                # So get each key from match_condition and from all or_conditions,
                # and get value of that key from the message data.
                for key in metric_config.match_conditions:
                    if key == 'or_condition':
                        for or_condition in metric_config.match_conditions['or_condition']:
                            or_condition_key = next(iter(or_condition))
                            redis_params[or_condition_key] = self.data.get(or_condition_key, '')
                    else:
                        redis_params[key] = self.data.get(key, '')

                self._update_redis(
                    resolved_name,
                    metric_config.redis_operation,
                    event_timestamp.timestamp(),
                    params=redis_params,
                )

                logging.debug(
                    'Metric processed successfully',
                    tenant=self.tenant,
                    facility=self.facility,
                    fact_type=self.fact_type,
                    metric_name=resolved_name,
                    request_id=self.request_id,
                )
            else:
                # This event occurred outside the specified processing window and should not be processed.
                try:
                    readable_timestamp = event_timestamp.strftime('%Y-%m-%d %H:%M:%S')
                except (AttributeError, ValueError):
                    readable_timestamp = 'None'
                logging.error(f'Not processed, the event is too old: {readable_timestamp}')
        else:
            logging.error(
                'Event missing timestamp',
                tenant=self.tenant,
                facility=self.facility,
                fact_type=self.fact_type,
                data=self.data,
            )

    # ============================================================================
    # CORE PROCESSING FLOW
    # ============================================================================

    def _process_metric_config_views(self, metric_config, metric_config_name):
        for raw_view in metric_config.views:
            view = self._resolve_dynamic_string(raw_view, event_data=self.data)
            config_type = metric_config.config_type
            hu_id = None
            logging.debug(
                'Processing metric',
                metric_config_name=metric_config_name,
                fact_type=self.fact_type,
                tenant=self.tenant,
                facility=self.facility,
                view=view,
                request_id=self.request_id,
            )
            if config_type in ['outbound-edge', 'inbound-edge']:
                hu_id = self.data.get(metric_config.hu_id, self.data.get('transportUnitId'))

            if config_type == 'outbound-edge':
                self._process_outgoing_edge(
                    hu_id,
                    datetime.datetime.now().isoformat(),
                    metric_config,
                    metric_config_name,
                    view,
                )

            elif config_type == 'inbound-edge':
                self._process_inbound_edge(hu_id, metric_config, metric_config_name, view)

            elif config_type == 'complete-edge':
                self._process_complete_edge(metric_config, metric_config_name, view)

            else:  # node metric type
                self._process_node(metric_config, metric_config_name, view)

    # ============================================================================
    # OUTBOUND EDGE FLOW
    # ============================================================================

    def _process_outgoing_edge(
        self,
        hu_id: str,
        current_time: str,
        metric_config: Dict[str, Any],
        created_by: str,
        view: str,
    ) -> None:
        """
        Processes outgoing edges by storing new handling units.

        Args:
            hu_id (str): The handling unit identifier.
            current_time (str): The current timestamp in ISO 8601 format.
            metric_config (dict): Configuration details for the metric.
            created_by (str): The name of the metric config that created this node.
            view (str): The graph view of the edge.
        """

        logging.debug(f'Processing outgoing edge for handling unit: {hu_id}, metricconfig: {metric_config}')

        try:
            handling_unit_key = f'{self.tenant}:{self.facility}:handling_unit:{hu_id}:edge:{view}'
            self._store_new_handling_unit_for_edge(handling_unit_key, current_time, metric_config, view, created_by)
        except Exception:
            logging.exception(f'Error processing outgoing edge for {hu_id}')
            return None

    def _store_new_handling_unit_for_edge(
        self,
        handling_unit_key: str,
        current_time: str,
        metric_config: Dict[str, Any],
        view: str,
        created_by: str,
    ) -> None:
        """
        Stores a new handling unit in Redis.

        Args:
            handling_unit_key (str): The Redis key for the handling unit.
            current_time (str): The current timestamp in ISO 8601 format.
            metric_config (dict): Configuration details for the metric.
            view (str): The graph view of the edge.
            created_by (str): The name of the metric config that created this node.
        """
        # Store units (ex: handling_unit), current_time and outbound area in redis
        units = metric_config.units

        outbound_area = metric_config.outbound_area
        outbound_node_label = metric_config.label
        outbound_parent_nodes = self._normalize_parent_nodes(metric_config.outbound_parent_nodes)

        if metric_config.name_formula:
            outbound_area = self._resolve_dynamic_name_value(metric_config.name_formula)

        # Ensure the outbound area node exists with the correct view
        with self.neo4j_service.driver.session() as session:
            session.write_transaction(
                self.neo4j_service._create_area_node,
                view,
                outbound_area,
                outbound_node_label,
                outbound_parent_nodes,
                created_by=created_by,
            )

        self.redis_service.store_handling_unit_for_edge(
            handling_unit_key,
            current_time,
            outbound_area,
            units,
            outbound_node_label=outbound_node_label,
            outbound_parent_nodes=outbound_parent_nodes,
        )
        return None

    # ============================================================================
    # INBOUND EDGE FLOW
    # ============================================================================

    def _process_inbound_edge(self, hu_id: str, metric_config: dict, metric_config_name: str, view: str) -> None:
        """
        Processes an inbound edge by handling the incoming edge, retrieving Redis data,
        checking exclusions, and processing resolved metrics.

        Args:
            hu_id (str): The handling unit identifier.
            metric_config (dict): Configuration details for the metric.
            metric_config_name (str): The name of the metric config being processed.
            view (str): The graph view of the edge.
        """

        # Check for excluded edges first - get Redis data to check outbound node label
        handling_unit_key = f'{self.tenant}:{self.facility}:handling_unit:{hu_id}:edge:{view}'
        handling_data = self.redis_service.redis_client.hgetall(handling_unit_key)

        outbound_node_label = handling_data.get(b'outbound_node_label')
        if outbound_node_label:
            outbound_node_label = outbound_node_label.decode('utf-8')

            logging.debug(
                'Outbound and inbound node labels',
                outbound_node_label=outbound_node_label,
                inbound_node_label=metric_config.label,
                excluded_edges=EXCLUDED_EDGES,
                metric_config_name=metric_config_name,
            )
            # Exit early if inbound and outbound edge labels are in excluded edges
            if outbound_node_label and (outbound_node_label.lower(), metric_config.label.lower()) in EXCLUDED_EDGES:
                logging.debug(
                    'Skipping metric due to excluded edge',
                    metric_config_name=metric_config_name,
                    fact_type=self.fact_type,
                    tenant=self.tenant,
                    facility=self.facility,
                    outbound_node_label=outbound_node_label,
                    inbound_node_label=metric_config.label,
                    excluded_edges=EXCLUDED_EDGES,
                )
                return

        # If not excluded, proceed with normal processing
        resolved_names = self._handle_incoming_edge(hu_id, metric_config, metric_config_name, view)

        outbound_parent_nodes = handling_data.get(b'outbound_parent_nodes')
        if outbound_parent_nodes:
            # Decode the string and parse it back into a list
            outbound_parent_nodes = outbound_parent_nodes.decode('utf-8')
            # If it's a string representation of a list, parse it safely
            if outbound_parent_nodes.startswith('[') and outbound_parent_nodes.endswith(']'):
                try:
                    outbound_parent_nodes = ast.literal_eval(outbound_parent_nodes)
                except (ValueError, SyntaxError):
                    # If parsing fails, treat as a single string
                    outbound_parent_nodes = [outbound_parent_nodes]
            else:
                outbound_parent_nodes = [outbound_parent_nodes]

        # Delete handling unit key from redis after retrieving outbound node label
        self.redis_service.redis_client.delete(handling_unit_key)
        logging.info(
            'Successfully processed and deleted handling unit',
            tenant=self.tenant,
            facility=self.facility,
            fact_type=self.fact_type,
            handling_unit_key=handling_unit_key,
            request_id=self.request_id,
        )

        if resolved_names:
            for resolved_name in resolved_names:
                logging.debug(
                    'Resolved edge metric name',
                    resolved_name=resolved_name,
                    request_id=self.request_id,
                    tenant=self.tenant,
                    facility=self.facility,
                )
                kwargs = {
                    'label': metric_config.label,
                    'outbound_node_label': outbound_node_label,
                    'inbound_parent_nodes': self._normalize_parent_nodes(metric_config.inbound_parent_nodes),
                    'outbound_parent_nodes': self._normalize_parent_nodes(outbound_parent_nodes),
                }
                # Resolve any dynamic values, leave regular values as is.
                kwargs['inbound_parent_nodes'] = [
                    self._resolve_dynamic_string(node, self.data) for node in kwargs['inbound_parent_nodes']
                ]
                kwargs['outbound_parent_nodes'] = [
                    self._resolve_dynamic_string(node, self.data) for node in kwargs['outbound_parent_nodes']
                ]
                self._process_metric_with_resolved_metric_name(
                    resolved_name,
                    metric_config,
                    view,
                    metric_config_name,
                    **kwargs,
                )

    def _handle_incoming_edge(self, hu_id: str, metric_config: dict, created_by: str, view: str) -> list[str] | None:
        """
        Handles the logic for processing incoming edges.

        Args:
            hu_id (str): The handling unit identifier.
            metric_config (dict): Configuration details for the metric.
            created_by (str): The name of the metric config that created this node.
            view (str): The graph view of the edge.

        Returns:
            list: Resolved names of metrics.
        """

        try:
            handling_unit_key = f'{self.tenant}:{self.facility}:handling_unit:{hu_id}:edge:{view}'
            # If handling unit exists, process it
            if self.redis_service.redis_client.hexists(handling_unit_key, 'leave_time'):
                logging.debug(
                    'Handling unit found in Redis, processing incoming edge',
                    fact_type=self.fact_type,
                    metric_name=created_by,
                    hu_id=hu_id,
                    view=view,
                    redis_key=handling_unit_key,
                    tenant=self.tenant,
                    facility=self.facility,
                    request_id=self.request_id,
                )
                return self._process_existing_handling_unit_for_edge(handling_unit_key, metric_config)
            else:
                logging.debug(
                    'Handling unit key does not exist in Redis, skipping edge processing',
                    metric_name=created_by,
                    fact_type=self.fact_type,
                    hu_id=hu_id,
                    view=view,
                    tenant=self.tenant,
                    facility=self.facility,
                    redis_key=handling_unit_key,
                    request_id=self.request_id,
                )
                return None

        except Exception as e:
            logging.exception(
                'Error handling incoming edge',
                metric_name=created_by,
                fact_type=self.fact_type,
                hu_id=hu_id,
                view=view,
                redis_key=handling_unit_key,
                tenant=self.tenant,
                facility=self.facility,
                error=str(e),
                request_id=self.request_id,
            )
            return None

    def _process_existing_handling_unit_for_edge(
        self, handling_unit_key: str, metric_config: Dict[str, Any]
    ) -> List[str]:
        """
        Processes an existing handling unit in Redis.

        Args:
            handling_unit_key (str): The Redis key for the handling unit.
            metric_config (dict): Configuration details for the metric.

        Returns:
            list: Resolved names of metrics based on the processed handling unit.
        """
        logging.debug(
            'Processing existing handling unit for edge',
            tenant=self.tenant,
            facility=self.facility,
            fact_type=self.fact_type,
            handling_unit_key=handling_unit_key,
            request_id=self.request_id,
        )

        resolved_names = []
        handling_data = self.redis_service.redis_client.hgetall(handling_unit_key)

        if not handling_data:
            logging.error(
                'No data found in Redis for handling unit',
                tenant=self.tenant,
                facility=self.facility,
                fact_type=self.fact_type,
                handling_unit_key=handling_unit_key,
                request_id=self.request_id,
            )
            return []

        outbound_area = handling_data.get(b'outbound_area')

        if not outbound_area:
            logging.error(
                'No outbound_area found in Redis data',
                tenant=self.tenant,
                facility=self.facility,
                fact_type=self.fact_type,
                handling_unit_key=handling_unit_key,
                request_id=self.request_id,
            )
            return []

        outbound_area = outbound_area.decode('utf-8')
        inbound_area = metric_config.inbound_area

        if metric_config.name_formula:
            inbound_area = self._resolve_dynamic_name_value(metric_config.name_formula)

        if not inbound_area:
            logging.error(
                'No inbound_area found in metric configuration',
                tenant=self.tenant,
                facility=self.facility,
                fact_type=self.fact_type,
                handling_unit_key=handling_unit_key,
                metric_config=metric_config,
                request_id=self.request_id,
            )
            return []

        resolved_name = f'{self.tenant}:{self.facility}:{outbound_area}:{inbound_area}:15m_set:hourly_rate'
        resolved_names.append(resolved_name)

        return resolved_names

    # ============================================================================
    # COMPLETE EDGE FLOW
    # ============================================================================

    def _process_complete_edge(self, metric_config: dict, metric_config_name: str, view: str) -> None:
        """
        Processes a complete edge by resolving areas, creating the metric name,
        normalizing parent nodes, and processing the metric.

        Args:
            metric_config (dict): Configuration details for the metric.
            metric_config_name (str): The name of the metric config being processed.
            view (str): The graph view of the edge.
        """
        inbound_area = self._resolve_dynamic_config_item_value(metric_config.inbound_area)
        outbound_area = self._resolve_dynamic_config_item_value(metric_config.outbound_area)

        resolved_name = f'{self.tenant}:{self.facility}:{outbound_area}:{inbound_area}:15m_set:hourly_rate'
        logging.debug(
            'Resolved complete-edge metric name',
            resolved_name=resolved_name,
            request_id=self.request_id,
            tenant=self.tenant,
            facility=self.facility,
        )

        # Normalize parent nodes
        inbound_parent_nodes = self._normalize_parent_nodes(metric_config.inbound_parent_nodes)
        outbound_parent_nodes = self._normalize_parent_nodes(metric_config.outbound_parent_nodes)

        # Resolve any dynamic values, leave regular values as is.
        inbound_parent_nodes = [self._resolve_dynamic_string(node, self.data) for node in inbound_parent_nodes]
        outbound_parent_nodes = [self._resolve_dynamic_string(node, self.data) for node in outbound_parent_nodes]

        kwargs = {
            'label': metric_config.label,
            'outbound_node_label': metric_config.outbound_node_label,
            'inbound_parent_nodes': inbound_parent_nodes,
            'outbound_parent_nodes': outbound_parent_nodes,
        }
        self._process_metric_with_resolved_metric_name(
            resolved_name,
            metric_config,
            view,
            metric_config_name,
            **kwargs,
        )

    # ============================================================================
    # NODE FLOW
    # ============================================================================

    def _process_node(self, metric_config: dict, metric_config_name: str, view: str) -> None:
        """
        Processes a node metric by resolving the metric name, normalizing parent nodes,
        and processing the metric.

        Args:
            metric_config (dict): Configuration details for the metric.
            metric_config_name (str): The name of the metric config being processed.
            view (str): The graph view of the node.
        """
        resolved_name = self._get_name(metric_config)
        logging.debug(
            'Resolved node metric name',
            resolved_name=resolved_name,
            request_id=self.request_id,
            tenant=self.tenant,
            facility=self.facility,
        )

        kwargs = {
            'label': metric_config.label,
            'parent_nodes': self._normalize_parent_nodes(metric_config.parent_nodes),
        }
        # Resolve any dynamic values, leave regular values as is.
        kwargs['parent_nodes'] = [self._resolve_dynamic_string(node, self.data) for node in kwargs['parent_nodes']]
        self._process_metric_with_resolved_metric_name(
            resolved_name,
            metric_config,
            view,
            metric_config_name,
            **kwargs,
        )

    # ============================================================================
    # HELPER METHODS
    # ============================================================================

    def _create_default_config(self) -> Dict[str, Any]:
        """
        Creates a default configuration when no specific configuration is found.

        Returns:
            dict: A default configuration dictionary.
        """
        logging.debug('Creating default configuration that does nothing.')
        return {}

    def _format_metric_name_for_neo4j(self, resolved_name: str) -> str:
        """
        Updates the name of complex metrics that gets stored in Neo4j.
        This is used for metrics which use more than one redis key to calculate a metric value such as ratio metrics.

        Args:
            resolved_name (str): The full name of the metric as defined in the metric config's name_formula field.

        Returns:
            str: The updated metric name that will be stored in Neo4j.
        """
        config_type = resolved_name.split(':')[-1]
        if config_type == 'ratio':
            return resolved_name.replace('_numerator', '').replace('_denominator', '')
        return resolved_name

    def _normalize_parent_nodes(self, parent_nodes):
        """
        Normalizes parent nodes to ensure they are a list.
        This is the single source of truth for parent node normalization.

        Args:
            parent_nodes: The parent nodes to normalize (can be None, string, or list)
            facility_id: The facility ID (kept for backward compatibility but not used)

        Returns:
            list: Normalized list of parent nodes
        """
        if not parent_nodes:
            return []
        if isinstance(parent_nodes, str):
            return [parent_nodes]
        return parent_nodes

    def _resolve_dynamic_string(self, template: str, event_data: dict) -> str:
        """
        Resolves a string with placeholders by replacing them with values from event_data.

        Args:
            template (str): The string template which could be static (e.g., "multishuttle") or dynamic (e.g., "{aisle_code}").
            event_data (dict): The event data containing potential replacements for dynamic placeholders.

        Returns:
            str: The resolved string with placeholders replaced.
        """
        if '{' in template and '}' in template:  # Detects placeholder format
            try:
                resolved_string = template.format(**event_data)
                return resolved_string
            except KeyError as e:
                missing_key = str(e).strip("'")
                logging.warning(
                    'Missing key in event data; using original string',
                    tenant=self.tenant,
                    facility=self.facility,
                    fact_type=self.fact_type,
                    template=template,
                    missing_key=missing_key,
                    event_data=event_data,
                    request_id=self.request_id,
                )
                return template  # Return original if placeholder is missing
        return template  # If no placeholders, return as is

    def _resolve_dynamic_config_item_value(self, config_item_value: str) -> str:
        """
        Resolves dynamic values in any configuration item.

        Args:
            config_item_value (str): The key of the configuration item that needs to be resolved.

        Returns:
            str: The resolved dynamic value, or the original value if does not contain any vars.
        """
        if isinstance(config_item_value, str):
            if '{' in config_item_value and '}' in config_item_value:
                # Parse out the string that is between the braces. ex: `{foo}`
                match = re.search(r'\{(.*?)\}', config_item_value)
                param_name = match.group(1)
                # Get the value from the fact data for this param specified in edge config.
                param_value = self.data.get(param_name)
                params = {param_name: param_value}
                return config_item_value.format(**params)
            return config_item_value
        return ''

    def _resolve_dynamic_name_value(self, config: config_schema.NameConfig) -> str:
        """
        Resolves dynamic values for a metric configuration.

        Args:
            config (NameConfig): The metric name configuration containing `source`, `pattern`, and `template`.
            view (str): The graph view that contains the metric.

        Returns:
            str: The resolved dynamic value, or None if pattern match fails.

        Raises:
            ValueError: If the input code does not match the expected pattern.
        """
        # First resolve the source template to get the actual value
        input_code = self._resolve_dynamic_string(config.source, self.data)

        # Then apply the pattern and template
        pattern = config.pattern
        template = config.template

        # Match the input code against the provided pattern
        match = re.match(pattern, input_code)
        if match:
            # Extract dynamic groups based on regex named groups
            extracted_values = match.groupdict()
            # Format the templates using the extracted values
            resolved_name = template.format(**extracted_values)
            return resolved_name
        else:
            logging.error(
                'Name config pattern match failed',
                fact_type=self.fact_type,
                input_code=input_code,
                pattern=pattern,
                template=template,
                tenant=self.tenant,
                facility=self.facility,
                request_id=self.request_id,
            )
            return None

    def _get_name(self, metric_config: Dict[str, Any]) -> str:
        """
        Generates the name of the metric based on the provided metric configuration.

        Args:
            metric_config (dict): The metric configuration containing node_name, metric_type, time_window, and aggregation.

        Returns:
            str: The generated metric name.
        """
        try:
            # First try to use name property if it exists
            if hasattr(metric_config, 'name_formula') and metric_config.name_formula:
                node_name = self._resolve_dynamic_name_value(metric_config.name_formula)
            else:
                # Fall back to node_name if name property doesn't exist
                node_name = self._resolve_dynamic_string(metric_config.node_name, self.data)

            # Use resolve_dynamic_string to handle dynamic metric types
            metric_type = self._resolve_dynamic_string(metric_config.metric_type, self.data)
            return f'{self.tenant}:{self.facility}:{node_name}:{metric_type}:{metric_config.time_window}:{metric_config.aggregation}'
        except Exception as e:
            logging.warning(
                'Error generating metric name',
                metric_config=metric_config,
                fact_type=self.fact_type,
                tenant=self.tenant,
                facility=self.facility,
                error=str(e),
                request_id=self.request_id,
            )
            return 'unknown_metric'

    def _is_match(self, conditions: dict) -> bool:
        """
        Evaluates whether the provided data matches the specified conditions.

        Args:
            conditions (dict): A dictionary of match conditions to evaluate.
                            Conditions can include 'or_condition' for complex OR logic.

        Returns:
            bool: True if all conditions are met, False otherwise.
        """
        for key, condition in conditions.items():
            if key == 'or_condition':
                for or_group in condition:
                    matched = False
                    for sub_key, sub_pattern in or_group.items():
                        value = self.data.get(sub_key)

                        if value is None or value == 'None':
                            continue  # Skip missing values

                        if isinstance(value, (int, float, bool)):
                            value = str(value)

                        # Allow empty values only if the regex explicitly allows it
                        if value == '' and not re.fullmatch(sub_pattern, ''):
                            continue  # Skip this condition if empty values aren't allowed

                        if isinstance(value, str) and re.search(sub_pattern, value, re.IGNORECASE):
                            matched = True
                            break

                    if matched:
                        break  # Match found, continue to the next condition since the OR group succeeded

                if not matched:
                    return False  # All OR groups failed to match

            else:
                value = self.data.get(key)

                # fail match when value is None, unless we are specifically checking for it
                if value is None or value == 'None':
                    if condition == '^None$':
                        continue
                    else:
                        return False

                if isinstance(value, (int, float, bool)):
                    value = str(value)

                if value == '' and not re.fullmatch(condition, ''):
                    return False

                # If the condition contains dynamic values, resolve them
                if '{' in condition and '}' in condition:
                    # condition = self._resolve_dynamic_string(condition, self.data)
                    match = re.search(r'\{(.*?)\}', condition)
                    param_name = match.group(1)
                    if param_name in self.data:
                        # Get the value from the fact data for this param specified in edge config.
                        param_value = self.data.get(param_name)
                        params = {param_name: param_value}
                        condition = condition.format(**params)

                if isinstance(value, str) and not re.search(condition, value, re.IGNORECASE):
                    return False

        return True  # All conditions passed

    # ============================================================================
    # DATABASE/EXTERNAL SERVICE METHODS
    # ============================================================================

    def _update_config_active_status(self, metric_config: config_schema.BaseMetricConfig) -> None:
        """
        Updates the active status of a metric config in the database.

        This is called when a config is matched and used, to track which
        configs are actively being used by each facility. The cache operates
        as our source of truth for which configs are active. The logic operates
        as follows:

        - If the config is already active in the cache, we don't need to update
          the database or the cache since it's already active. Return early.

        - If the config is not already active in the cache, we call the postgres
          service to update the config active status. We then update the cache
          to reflect the new active status.

        Args:
            metric_config: The metric configuration object
        """
        try:
            # Get postgres service from factory if available
            postgres_service = PostgresFactory.get_instance(self.tenant)

            if postgres_service:
                # Get ConfigLoader instance for cache operations
                config_loader = ConfigLoader.get_instance()

                # Check if config is already active for this facility to avoid unnecessary database updates
                # Pass the metric config object directly
                if config_loader.is_config_active_for_facility(metric_config, self.facility):
                    logging.debug(
                        'Config already active in cache, skipping database update',
                        metric_config_name=metric_config.metric_config_name,
                        facility=self.facility,
                        fact_type=metric_config.fact_type,
                        tenant=self.tenant,
                    )
                    # Return early - we don't need to update our cache or database
                    return

                # Update the database since we know the config is not active in the cache
                postgres_service.update_metric_config_active(
                    metric_config=metric_config,
                    facility_id=self.facility,
                )

                # Update cache to reflect the new active status
                config_loader.mark_config_active_in_cache(
                    self.tenant, self.facility, metric_config.fact_type, metric_config.metric_config_name
                )
            else:
                logging.warning(
                    'PostgreSQL service not available, skipping config status update',
                    metric_config_name=metric_config.metric_config_name,
                    facility=self.facility,
                    tenant=self.tenant,
                )
        except (psycopg2.Error, ConnectionError, ValueError, json.JSONDecodeError) as e:
            # Don't let database errors break metric processing
            logging.error(
                'Failed to update config active status',
                metric_config_name=metric_config.metric_config_name,
                facility=self.facility,
                tenant=self.tenant,
                error=str(e),
                exc_info=True,
            )

    def _update_redis(
        self,
        metric_name: str,
        operation: str,
        event_timestamp: float,
        params: Optional[Dict[str, Any]] = None,
    ) -> None:
        """
        Updates the Redis database based on the operation specified for the metric.

        Args:
            metric_name (str): The name of the metric to update.
            operation (str): The Redis operation to perform.
            event_timestamp (float): The timestamp of the event.
            params (dict, optional): Additional parameters for the operation.
        """
        # Extract parameters if params object is provided, otherwise use scalar value.
        if params:
            extracted_params = {}
            for key, value in params.items():
                if isinstance(value, str) and '{' in value:
                    # Strip curly braces and get the value from self.data
                    extracted_params[key] = self.data.get(value.strip('{}'))
                else:
                    # If it's not a string, use the value directly
                    extracted_params[key] = value
        else:
            extracted_params = {}

        logging.debug(
            'Updating Redis',
            metric_name=metric_name,
            operation=operation,
            tenant=self.tenant,
            facility=self.facility,
            request_id=self.request_id,
        )

        method = getattr(self.redis_service, operation, None)
        if method:
            method(metric_name, event_timestamp=event_timestamp, **extracted_params)

            # Update the timestamp for last time a metric was processed.
            self.redis_service.set_last_processed_time(self.tenant)
        else:
            logging.error(
                'Redis operation not found',
                tenant=self.tenant,
                facility=self.facility,
                metric_name=metric_name,
                operation=operation,
                request_id=self.request_id,
            )

    def _update_graph(
        self,
        metric_name: str,
        operation: str,
        view: str,
        **kwargs,
    ) -> None:
        """
        Updates the graph database for the specified metric.

        Args:
            metric_name (str): The name of the metric to update.
            operation (str): The graph operation to perform.
            view (str): The graph view to update.
        """
        if not self.neo4j_service:
            logging.error(
                'No Neo4j instance found, skipping graph update',
                tenant=self.tenant,
                facility=self.facility,
                metric_name=metric_name,
                request_id=self.request_id,
            )
            return

        logging.debug(
            'Updating Neo4j graph',
            tenant=self.tenant,
            facility=self.facility,
            metric_name=metric_name,
            operation=operation,
            view=view,
            request_id=self.request_id,
        )
        method = getattr(self.neo4j_service, operation)
        if method:
            method(metric_name, view, **kwargs)
        else:
            logging.error(
                'Neo4j graph_operation not found',
                tenant=self.tenant,
                facility=self.facility,
                config_name=metric_name,
                operation=operation,
                request_id=self.request_id,
            )
