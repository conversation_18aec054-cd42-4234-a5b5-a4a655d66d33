#!/bin/bash

# Pre-commit hook for metric-processor service
# This script runs tests before allowing commits

set -e  # Exit on any error

echo "🧪 Running metric-processor tests..."

# Run the tests using poetry
if poetry run test; then
    echo "✅ All metric-processor tests passed!"
else
    echo "❌ Metric-processor tests failed. Please fix the issues before committing."
    exit 1
fi

echo "🎉 Pre-commit checks completed successfully!"
