{"Active Operators": "Active Operators", "Add View": "Add View", "addViewModal": {"nameLabel": "View Name", "namePlaceholder": "Enter view name", "nameRequired": "View name is required", "nameTaken": "This view name is already in use", "typeLabel": "View Type"}, "Advice Cycle Time": "Advice Cycle Time", "adviceDetailsDrawer": {"adviceLine": "Advice Line", "closeButton": "Close", "errorLoading": "Error loading advice details", "handlingUnit": "Handling Unit", "handlingUnitType": "Handling Unit Type", "itemsReceivedHeading": "Items Received", "linesLabel": "Lines:", "loading": "Loading advice details...", "noItemsReceived": "No items received", "noValue": "--", "packagingLevel": "Packaging Level", "quantity": "Quantity", "sku": "SKU", "statusLabel": "Status:", "supplierIdLabel": "Supplier ID:", "typeLabel": "Type:"}, "Are you sure you want to delete this menu item?": "Are you sure you want to delete this menu item?", "accessRequestForm": {"organizationLabel": "Organization (Required)", "organizationPlaceholder": "Enter organization name (e.g., Dematic)", "organizationRequired": "Organization is required", "sitesLabel": "Sites", "sitesPlaceholder": "Enter facility locations (e.g., New York, Los Angeles)", "emailLabel": "<PERSON><PERSON> Address (Required)", "emailPlaceholder": "Enter your email address", "emailRequired": "Email address is required", "emailInvalid": "Please enter a valid email address", "additionalInfoLabel": "Additional Information (Optional)", "additionalInfoPlaceholder": "Any additional information that would help with your request", "submit": "Submit Access Request", "submitSuccess": "Your access request has been submitted successfully", "submitError": "Failed to submit access request. Please try again."}, "authErrorPage": {"contactAdministrator": "Contact Administrator", "submitRequest": "Submit Request", "logout": "Log Out", "noOrganisations": "No Organizations", "noOrganisationsDescription": "Your account isn't part of any organization yet. Please provide the organization and sites you need access to.", "noFacilityAccess": "No Facility Access", "noFacilityAccessDescription": "Your account does not have access to any facilities. Please contact your administrator to get access.", "refresh": "Refresh", "unknownError": "Unknown Error", "unknownErrorDescription": "An unknown error occurred. Please contact your administrator.", "unverifiedEmail": "Email Not Verified", "unverifiedEmailDescription": "Please verify your email address to continue. Check your inbox for a verification link.", "unverifiedEmailResend": "Resend Verification Email"}, "barChartWidgetOptions": {"barColor": "Bar Color", "chartTitle": "Chart Title", "chartType": "Chart Type", "dataTitle": "Data", "displayTitle": "Display", "groupBy": "Group By", "orientation": "Orientation", "orientationHorizontal": "Horizontal (Bars)", "orientationVertical": "Vertical (Columns)", "showAverageLine": "Show Average Line", "showTargetLine": "Show Target Line", "sortAscAZ": "Ascending (A to Z)", "sortAscLowHigh": "Ascending (Low to High)", "sortByName": "Sort by Name", "sortByValue": "Sort by Value", "sortDescHighLow": "Descending (High to Low)", "sortDescZA": "Descending (Z to A)", "sortingTitle": "Sorting", "sortNone": "None", "targetValue": "Target Value"}, "Cancel": "Cancel", "Changed By": "Changed By", "chartComponent": {"noChartData": "No Chart Data Available"}, "chartWidgetOptions": {"activeTime": "Active Time", "blockedTime": "Blocked Time", "chartStyle": "Chart Style", "chartTitle": "Chart Title", "chartType": "Chart Type", "dataTitle": "Data", "displayTitle": "Display", "donorTotesPerHour": "<PERSON><PERSON>/Hour", "idleTime": "Idle Time", "linesPerHour": "Lines/Hour", "operatorId": "Operator ID", "orderTotesPerHour": "Order Totes/Hour", "quantityPerHour": "Quantity/Hour", "showAverageLine": "Show Average Line", "showTargetLine": "Show Target Line", "starvedTime": "Starved Time", "status": "Status", "styleArea": "Area", "styleColumn": "Column", "styleLine": "Line", "styleStackedArea": "Stacked Area", "styleStackedColumn": "Stacked Column", "styleStackedRow": "Stacked Row", "targetValue": "Target Value", "weightedLinesPerHour": "Weighted Lines/Hour", "weightedQuantityPerHour": "Weighted Quantity/Hour", "workflowStatus": "Workflow Status", "workMode": "Work Mode", "workstation": "Workstation", "workstationListTitle": "Workstation List"}, "Collapse All": "Collapse All", "configurationManagement": {"title": "Configuration Management", "tabs": {"facts": "Facts", "metrics": "Metrics", "nodes": "Nodes"}}, "configErrorBoundary": {"localhostOverrideError": "It looks like you've set the config API to localhost, which might be causing this error.", "logout": "Log Out", "refreshPage": "Refresh Page", "resetApiOverrides": "Reset API Overrides", "title": "Something Went Wrong", "unexpectedError": "An unexpected error has occurred while loading this page."}, "configuredAlerts": {"frequency": "Email sent (at most)", "owner": "Owner", "recipients": "Recipients", "subject": "Subject", "suspended": "Status", "title": "Configured <PERSON><PERSON>s", "view": "View", "viewAll": "View all", "visibility": "Visibility"}, "containerDetail": {"avgDailyCycleCount": "Avg Daily Cycle Count", "avgDailyPickEvents": "Avg Daily Pick Events", "configError": "Configuration Error", "configLoadError": "Failed to load container list configuration: {{error}}", "containerList": "Container List", "cycleCountEventsToday": "Cycle Count Events Today", "pickEventsToday": "Pick Events Today"}, "containerList": {"lastUpdated": "Inventory Data Updated: {{formattedLastUpdated}}", "loading": "Loading..."}, "containerListTable": {"containerId": "Container ID", "dataUpdated": "Data Updated", "freeCycleCount": "Free Cycle Count", "lastActivity": "Last Activity", "lastCycleCount": "Last Cycle Count", "locationId": "Location ID", "notAvailable": "N/A", "quantity": "Quantity", "sku": "SKU", "zone": "Zone"}, "curatedDataTable": {"errorLoadingData": "No data available"}, "dataView": {"table": {"title": "Data Configuration", "actions": {"newDataQuery": "New Data Query"}, "columns": {"name": "Name", "description": "Description", "draft": "Draft", "source": "Source", "type": "Type"}, "values": {"yes": "Yes", "no": "No"}, "errors": {"error": "Error", "failedToCreateDataQuery": "Failed to create data query", "errorLoadingDataConfigSettings": "Error loading data config settings: {{error}}"}}, "newDataQueryModal": {"title": "New Data Query", "create": "Create", "creating": "Creating...", "cancel": "Cancel", "nameLabel": "Name", "namePlaceholder": "Enter data query name", "typeLabel": "Type"}, "bottomPanel": {"tabs": {"ariaLabel": "Query result tabs", "request": "Request", "response": "Response", "resultView": "Result View", "metadata": "<PERSON><PERSON><PERSON>"}, "buttons": {"execute": "Execute", "executing": "Executing...", "copyQuery": "Copy Query", "copying": "Copying..."}, "request": {"dateRangeLabel": "Date Range", "filterTitle": "Filter: {{filterId}}", "filterPlaceholder": "Select {{filterId}}..."}, "response": {"errorExecuting": "Error executing query: {{error}}", "executing": "Executing query...", "validResult": "<PERSON><PERSON>", "invalidResult": "<PERSON><PERSON><PERSON>", "noDataRows": "No data rows to display.", "noDataToDisplay": "No data to display. Execute a query to see results."}, "resultView": {"errorExecuting": "Error executing query: {{error}}", "executing": "Executing query...", "noResultToDisplay": "No result to display. Execute a query to see the result."}, "metadata": {"property": "Property", "value": "Value", "errorExecuting": "Error executing query: {{error}}", "executing": "Executing query...", "noMetadataToDisplay": "No metadata to display. Execute a query to see metadata."}}}, "Customer Cycle Time": "Customer Cycle Time", "Customer Line Progress": "Customer Line Progress", "Customer Line Throughput Rate": "Customer Line Throughput Rate", "Customer Order Progress": "Customer Order Progress", "Customer Orders Shipped": "Customer Orders Shipped", "Cycle Counts": "Cycle Counts", "dailyPerformanceTable": {"asrsRetrieval": "ASRS Retrieval", "asrsStorage": "ASRS Storage", "date": "Date", "dmsRetrieval": "DMS Retrieval", "dmsStorage": "DMS Storage", "donorContainers": "Donor Containers", "firstShiftPercent": "1st Shift %", "gtpContainers": "GTP Containers", "idlePercent": "Idle %", "linesPerHour": "Lines/Hour", "linesPicked": "Lines Picked", "pickLineQty": "Pick Line Qty", "qtyPerLine": "Qty/Line", "secondShiftPercent": "2nd Shift %", "starvedHours": "Starved Hours", "starvedPercent": "Starved %", "totalHours": "Total Hours"}, "dashboard": {"addWidget": "Add Widget", "settingSavedErrorMessage": "Your dashboard was not saved successfully", "settingSavedSuccessMessage": "Your dashboard has been saved"}, "dashboardSummary": {"errorLoadingDashboardSummary": "Failed to load dashboard summary. Please try again later."}, "datagrid": {"ascending": "Ascending", "descending": "Descending", "errorLoadingData": "Error loading data: {{error}}", "loadingData": "Loading data...", "noDataAvailable": "No data available"}, "datagridHeader": {"export": "Export", "exportOptions": "Export Options", "searchTable": "Search Table"}, "Default Route": "Default Route", "Delete": "Delete", "Delete item": "Delete item", "Delete Menu Item": "Delete Menu Item", "deleteSettings": {"successSingle": "Setting deleted successfully", "successMultiple": "{{count}} settings deleted successfully", "errorSingle": "Failed to delete setting: {{name}}", "errorMultiple": "Failed to delete {{count}} settings: {{names}}", "modalTitle": "Delete Setting", "modalTitlePlural": "Delete Settings", "modalConfirmation": "Are you sure you want to delete", "modalConfirmationSingle": "this setting", "modalConfirmationPlural": "these settings", "modalWarning": "This will remove the setting(s) from the database for this tenant in this environment! All facility specific and user specific settings will be removed as well.", "tableHeaders": {"name": "Name"}, "unexpectedError": "An unexpected error occurred during deletion"}, "dmsLevelView": {"binLocation": "Bin Location", "containerType": "Container Type", "heading": "Bin Location", "skuAndQuantity": "SKU and Quantity", "status": "Status"}, "duration": {"hours": {"plural": "Hours", "singular": "Hour"}, "minutes": {"plural": "Minutes", "singular": "Minute"}, "seconds": {"plural": "Seconds", "singular": "Second"}}, "dynamicRouteView": {"interpolatedPageTitle": "{{menu<PERSON><PERSON><PERSON>abel}} - {{CTTitle}}"}, "Edit item": "Edit item", "emailVerification": {"authenticationError": "Authentication Error", "authErrorMessage": "Authentication error. Please contact your administrator.", "emailAlreadyVerified": "Email Already Verified", "emailSent": "<PERSON><PERSON>", "emailSentSuccessfully": "Email verification resent successfully", "error": "Error", "genericError": "Failed to resend email verification. Please try again or contact support.", "rateLimitExceeded": "Rate Limit Exceeded", "rateLimitMessage": "Too many verification email requests. Please try again in 24 hours.", "userNotFound": "User Not Found", "userNotFoundMessage": "User not found. Please contact your administrator."}, "Enable All": "Enable All", "Error loading menu configuration": "Error loading menu configuration", "Estimated Completion": "Estimated Completion", "Expand All": "Expand All", "exportDialog": {"unknownError": "An unknown error occurred during export."}, "Facility Estimated Completion": "Facility Estimated Completion", "Facility Order Cycle Time": "Facility Order Cycle Time", "Facility Order Lines Progress": "Facility Order Lines Progress", "Facility Order Progress": "Facility Order Progress", "Facility Orders Outstanding": "Facility Orders Outstanding", "Facility Orders Shipped": "Facility Orders Shipped", "Facility Throughput Rate": "Facility Throughput Rate", "facilityGuard": {"errorTitle": "Facility Access Error", "loadingFacilities": "Loading facilities...", "logout": "Log Out", "noFacilitiesError": "You do not have access to any facilities. Please contact your administrator.", "settingUpFacility": "Setting up facility access..."}, "facilityProcessFlowDataStalenessIndicator": {"pollingErrorMessage": "The last data refresh failed. Showing the most recent available data.", "pollingErrorTitle": "Data Refresh Failed"}, "facilityProcessFlowDetailPanel": {"noMetricsAvailable": "No metrics available for this element"}, "faultTracking": {"addManualEntry": "Add Manual Entry", "alarmDescription": "Alarm Description", "cancel": "Cancel", "clearSelection": "Clear selection", "comments": "Alarm Comments", "duration": "Duration", "editAlarmTimings": "Edit Al<PERSON>", "editAlarmTimingsSavedSuccessfully": "Edit alarm timings saved successfully", "editAlarmTimingsSubmissionFailed": "Failed to save alarm timing changes. Please try again.", "editTimingsSubtitle": "Adjust the date range, start and end time, the duration will update automatically.", "endDate": "End Date", "endTime": "End Time", "enterComments": "Enter comments", "enterDescription": "Enter description", "enterTimesToCalculate": "Enter times to calculate", "equipment": "Equipment", "equipmentRequired": "Equipment is required", "errorLoadingData": "Error loading data: {{error}}", "excluded": "Excluded", "included": "Included", "initialValue": "Initial Value", "loadingSection": "Loading section...", "loadingSectionsEquipment": "Loading sections and equipment...", "manualEntry": "Create a Manual Alarm", "manualEntrySavedSuccessfully": "Manual entry saved successfully", "manualEntrySubmissionFailed": "Failed to save manual entry. Please try again.", "manualEntrySubtitle": "Adjust the date range, start and end time, the duration will update automatically.", "manualEntryUnavailable": "Manual entry is currently unavailable", "noAlarmSelected": "No alarm selected", "noSectionFound": "No section found", "notAvailable": "N/A", "restoreToInitialValue": "Restore to Initial Value", "save": "Save", "searchEquipment": "Search equipment...", "searchSections": "Search sections...", "secondsMilliseconds": "Seconds & Milliseconds", "section": "Section", "sectionRequired": "Section is required", "selectRowsToEdit": "Select rows to edit timings", "startDate": "Start Date", "startTime": "Start Time", "systemAvailabilityCalculation": "System Availability Calculation", "timezoneConfigLoading": "Loading timezone configuration...", "timezoneConfigMissing": "Timezone configuration is required for manual entries", "title": "Fault Tracking", "updateCalculationStatus": "Update Calculation Status", "validation": {"commentsRequired": "Comments are required.", "descriptionRequired": "Description is required.", "endDateBeforeStart": "End date cannot be before start date", "equipmentRequired": "Equipment is required.", "sectionRequired": "Section is required.", "endDateRequired": "End date is required.", "endSecondsBeforeStart": "End seconds:milliseconds must be after start seconds:milliseconds", "endSecondsRequired": "End seconds:milliseconds required when start seconds:milliseconds is specified", "endTimeBeforeStart": "End time cannot be before start time", "endTimeEqualsStart": "End time must be after start time (add seconds:milliseconds for precision)", "endTimeRequired": "End time is required.", "invalidSecondsFormat": "Format should be ss:ms - use 30:500", "invalidTimeFormat": "Invalid time format - use HH:MM", "startDateRequired": "Start date is required.", "startDateTooOld": "Start date cannot be more than 30 days ago", "startDateInFuture": "Start date cannot be in the future", "endDateInFuture": "End date cannot be in the future", "startTimeInFuture": "Start time cannot be in the future", "endTimeInFuture": "End time cannot be in the future", "startTimeRequired": "Start time is required."}}, "alarmAuditLog": {"title": "Alarm Activity Log", "columns": {"alarmIdentifier": "Alarm ID", "modifiedComment": "Comments", "timestamp": "Timestamp", "modifiedUser": "User ID", "newValues": "New Value", "operationType": "Operation Type", "originalValues": "Previous Value", "processIdentifier": "Process ID"}, "emptyState": "N/A", "searchPlaceholder": "Search for alarm ID", "popover": {"title": "Alarm Details", "previousValue": "Previous Value", "newValue": "New Value", "comments": "Comments", "details": "Details", "emptyState": "N/A"}}, "fileUpload": {"chooseDocuments": "Choose documents to upload", "date": "Date", "deleteFileDescription": "Delete file", "fileSelected": "File selected", "fileUploadHistory": "File Upload History", "knownOrderCount": "Known Order Line Count", "knownOrderLineCount": "Known Order Line Count", "replaceFile": "Replace file", "selectFile": "Select file", "step1": "Step 1: SAP Order Manager File", "step2": "Step 2: SAP Order Details File", "supportedFileFormats": "Supported file formats: .xlsx, .xls", "uploadFailed": "Upload failed", "uploadFilesButton": "Upload Files", "uploading": "Uploading...", "uploadSuccessful": "Upload successful"}, "filterWidget": {"errorLoadingFilters": "Failed to load filter data. Please try again later."}, "Finished Advices": "Finished Advices", "formatAvg": {"defaultUnitString": "Avg: {{avg}} {{unit}}", "noUnit": "Avg: {{avg}}", "percentage": "Avg: {{avg}}%"}, "formatTarget": {"defaultUnitString": "Target: {{target}} {{unit}}", "noUnit": "Target: {{target}}", "percentage": "Target: {{target}}%"}, "formatTime": {"hasHours": "{{hours}} hrs {{minutes}} mins", "hasHoursNoMinutes": "{{hours}} hrs", "hasMinutes": "{{minutes}} mins {{seconds}} seconds", "hasMinutesNoSeconds": "{{minutes}} mins", "hasSeconds": "{{seconds}} secs", "nan": "0 {{unit}}"}, "formatDuration": {"invalid": "0s", "subSecond": "{{seconds}}s", "daysHours": "{{days}}d {{hours}}hr", "daysOnly": "{{days}}d", "hoursMinutes": "{{hours}}hr {{minutes}}min", "hoursOnly": "{{hours}}hr", "minutesSeconds": "{{minutes}}min {{seconds}}s", "minutesOnly": "{{minutes}}min", "secondsOnly": "{{seconds}}s"}, "formatTotal": {"defaultUnitString": "{{totalFormatted}} {{defaultUnitString}}", "percentage": "{{totalFormatted}}%"}, "Fulfillment Orders Outstanding": "Fulfillment Orders Outstanding", "Go Home": "Go Home", "In Progress Advices": "In Progress Advices", "Inventory Accuracy": "Inventory Accuracy", "inventoryForecastTimestamp": {"inventoryDataUpdated": "Inventory Data Updated: {{dataUpdateTimestamp}}", "loadingTimestampData": "Loading timestamp data...", "noDataAvailable": "No data available", "skuForecastPerformed": "SKU Forecast Performed: {{analysisPerformedTimestamp}}"}, "inventoryList": {"lastUpdated": "Inventory Data Updated: {{formattedLastUpdated}}", "loading": "Loading..."}, "inventoryListTable": {"avgDailyOrders": "Avg Daily Orders", "avgDailyQty": "Avg Daily Qty", "contOverage": "Overage", "daysOnHand": "Days On Hand", "description": "Description", "latestActivity": "Latest Activity", "latestCycleCount": "Latest Cycle Count", "maxContainers": "Max Containers", "qtyAllocated": "Qty Allocated", "qtyAvailable": "Qty Available", "sku": "SKU", "skuPositions": "SKU Positions", "targetMultiplicity": "Target Multiplicity", "velocityClassification": "Velocity Classification"}, "itemDetails": {"description": "This is a detail view for item {{itemId}}", "heading": "Item {{itemId}} Details", "item1": "Item 1", "routableView": "Routable View", "title": "<PERSON><PERSON>"}, "itemList": {"heading": "Item List", "routableExampleView": "Routable Example View", "viewItem1": "View Item 1"}, "kpiChartWidgetOptions": {"chartStyle": "Chart Style", "chartTitle": "Chart Title", "chartType": "Chart Type", "color": "Color", "dataTitle": "Data", "displayId": "display", "displayTitle": "Display", "kpiValueLabel": "KPI Value Label", "kpiValuePlaceholder": "Label displayed below the KPI value", "precisionLabel": "Precision", "showAverageKpi": "Show Average KPI", "showAverageLine": "Show Average Line", "showCurrentKpi": "Show Current KPI", "showMaxKpi": "Show Max KPI", "showMinKpi": "Show Min KPI", "showTargetLine": "Show Target Line", "showTotalKpi": "Show Total KPI", "showUnit": "Show Unit", "styleArea": "Area", "styleColumn": "Column", "styleLine": "Line", "stylingTitle": "Styl<PERSON>", "targetValue": "Target Value"}, "Last 14 Days": "Last 14 Days", "Last 30 Days": "Last 30 Days", "Last 7 Days": "Last 7 Days", "Last Month": "Last Month", "Last Week": "Last Week", "last30Days": "Last 30 days", "last60Days": "Last 60 days", "last7Days": "Last 7 days", "Level": "Level", "Loading": {"": "Loading..."}, "loggedInWidgetOperators": {"actualLabel": "Actual", "expectedLabel": "Expected", "lineCount": "Line Count", "title": "Logged in Operators", "totalLinesPerHour": "Total lines per hour"}, "menu": {"administration": "Administration", "advancedOrchestration": "Advanced Orchestration", "aiChat": "AI Chat", "alarmActivityLog": "Alarm Activity Log", "alarms": "Alarms", "applicationConfig": "Application Config", "applicationHealth": "Application Health", "automationOverview": "Automation Overview", "automationVisualization": "Automation Visualization", "configuredAlerts": "Configured <PERSON><PERSON>s", "containerList": "Container List", "controls": "Controls", "curatedData": "Curated Data", "dailyPerformanceReport": "Daily Performance Report", "dashboardManager": "Dashboard Manager", "dataExplorer": "Data Explorer", "debugInfo": "Debug Info", "dematicChat": "<PERSON><PERSON>", "dematicInternal": "Dematic Internal", "dematicSearch": "Dematic Search", "devices": "Devices", "equipment": "Equipment", "equipmentHierarchy": "Equipment Hierarchy", "events": "Events", "examples": "Examples", "facilityOverview": "Facility Overview", "facilityProcessFlow": "Facility Process Flow", "facilityProcessFlowDataStalenessIndicator": {"pollingErrorMessage": "The last data refresh failed. Showing the most recent available data.", "pollingErrorTitle": "Data Refresh Failed"}, "faultTracking": "Fault Tracking", "featureFlags": "Feature Flags", "fileUpload": "File Upload", "functionalAreas": "Functional Areas", "groups": "Groups", "inboundOverview": "Inbound Overview", "inventory": "Inventory", "inventoryForecast": "Inventory Forecast", "inventoryList": "Inventory List", "inventoryOverview": "Inventory Overview", "kpis": "KPIs", "locations": "Locations", "menuManager": "Menu Manager", "mfeManager": "MFE Manager", "myDashboards": "My Dashboards", "operationalAlerting": "Operational Alerting", "operationsVisibility": "Operations Visibility", "optionsView": "Options View", "outboundOverview": "Outbound Overview", "performanceAnalysis": "Performance Analysis", "permissions": "Permissions", "pickingBufferAreaDetails": "Picking Buffer Area Details", "playground": "Playground", "processFlowVisualization": "Process Flow Visualization", "replenishmentDetails": "Replenishment Details", "routableView": "Routable View", "rules": "Rules", "scenarioModeling": "Scenario Modeling", "simulation": "Simulation", "staticView": "Static View", "systemAvailability": "System Availability", "systemHealth": "System Health", "userManagement": "User Management", "users": "Users", "workstation": "Workstation", "workstationActiveOrders": "Active Orders", "workstationOverview": "Workstation Overview"}, "Menu configuration has been saved successfully": "Menu configuration has been saved successfully", "Menu configuration not found": "Menu configuration not found", "Menu Manager": "Menu Manager", "Menu Manager Tree": "Menu Manager Tree", "Menu Name": "<PERSON>u Name", "menuManager": {"failedToSaveConfiguration": "Failed to save menu configuration: {{error}}", "failedToSaveConfigurationValidation": "Failed to save menu configuration due to failed menu validation: {{error}}"}, "New Value": "New Value", "No menu items found": "No menu items found", "No menu setting found in the configuration": "No menu setting found in the configuration", "notFound": {"message": "The page you're looking for doesn't exist or may have been moved.", "title": "Page Not Found"}, "Old Value": "Old Value", "Order Cycle Time": "Order Cycle Time", "Order Lines Progress": "Order Lines Progress", "Outstanding Advices": "Outstanding Advices", "Overall Outbound Rate": "Overall Outbound Rate", "pdfGenerator": {"analysis": "Analysis", "caseQuantity": "Case Quantity", "casesRequired": "Cases Required", "conditionCode": "Condition Code", "containers": "Containers", "createdDate": "Created {{createdDate}}", "data": "Data", "eaches": "{{quantity}} eaches", "forecastLabel": "Forecast Forward Pick Tomorrow", "locationId": "Location ID", "noData": "No data available", "noReserveLocationsError": "No locations found under Reserve Storage for the selected SKU(s). PDF cannot be generated.", "pageOfTotal": "Page {{pageNumber}} of {{totalPages}}", "performed": "Performed {{performedDate}}", "skuOfTotal": "SKU {{currentSku}} of {{totalSkus}}", "skuQuantity": "SKU Quantity", "skus": "SKUs", "taskDescription": "Create new Generate Move tasks to move the included SKUs from Reserve Storage locations to the Forward Pick ASRS.", "taskLabel": "Task: Generate Move to ASRS", "updated": "Updated {{updatedDate}}"}, "pieChartWidgetOptions": {"chartTitle": "Chart Title", "chartType": "Chart Type", "dataTitle": "Data", "displayTitle": "Display", "groupBy": "Group By", "pieType": "Pie Type", "pieTypeDonut": "Donut", "pieTypePie": "Pie", "showPercentage": "Show Percentage"}, "progress": {"format": "{{current}} of {{total}}"}, "Projected Customer Order Fulfillment": "Projected Customer Order Fulfillment", "Projected Facility Order Fulfillment": "Projected Facility Order Fulfillment", "Property Updated": "Property Updated", "recentSearchesView": {"lastSevenDays": "Last 7 Days", "lastThirtyDays": "Last 30 Days", "today": "Today", "view": "View", "zeroRecentSearches": "You have 0 recent searches."}, "searchResultsView": {"noResults": "No results"}, "Select workstations": "Select workstations", "siteTimeText": {"siteTime": "{{value, formatDate}}"}, "stationHealthWidget": {"downtime": "Downtime", "title": "Station Health"}, "stationPerformanceWidget": {"title": "Station Performance", "totalActiveTime": "Total Active Time", "totalStarvedTime": "Total Starved Time"}, "Storage Utilization": "Storage Utilization", "tenantFacilityDisplay": {"facility": "Facility"}, "This Month": "This Month", "This Week": "This Week", "Throughput Rate": "Throughput Rate", "Time Changed": "Time Changed", "Today": "Today", "totalStationsWidgetProps": {"modeHeading": "Mode", "statusHeading": "Status", "title": "Total Stations"}, "Units per Hour": "Units per Hour", "Units Remaining": "Units Remaining", "Unsaved Changes": "Unsaved Changes", "userManagement": {"assign": "Assign", "assignedRoles": "Assigned Roles", "assignedRolesDescription": "Assign the roles that {{userName}} has while accessing your application in the {{organization}} organization.", "assignRoles": "Assign Roles", "assignRolesDescription": "Select the roles you want to assign to this user.", "cancel": "Cancel", "deleteRole": "Delete role", "email": "Email", "errorLoadingRoles": "Error loading available roles.", "lastLogin": "Last Login", "noAvailableRoles": "No additional roles available to assign.", "noDataAvailable": "No user data is currently available.", "noDescription": "No description available", "noRoles": "No roles assigned to this user.", "roleDescription": "Description", "roleName": "Name", "title": "User Management", "userId": "User ID", "userNotFound": "User not found or could not be loaded."}, "userManagementTable": {"createdAt": "Created At", "email": "Email", "emailVerified": "<PERSON><PERSON>", "isSocialAuth": "Social Auth", "lastLogin": "Last Login", "loginCount": "Login <PERSON>", "name": "Name"}, "useViewOptions": {"defaultErrorMessage": "Your settings were not saved.", "defaultSuccessMessaage": "Your settings have been saved.", "finalErrorMessageWithDetails": "{{errorMessage}} Error: {{errorDetails}}"}, "viewHistory": {"apply": "Apply", "changedBy": "Changed by", "defaultValue": "Default Value", "defaultValueDescription": "The default value for the view", "noHistoryAvailable": "No history available", "noHistoryEntriesFound": "No history entries found.", "searchHistory": "Search history", "title": "View History", "viewHistoryEntries": "View history entries"}, "widget": {"delete": "Delete", "loadingOptions": "Loading options", "loadingWidget": "Loading widget", "settings": "Settings"}, "widgetContainer": {"errorTitle": "Error", "genericErrorOccurred": "An error occurred.", "initializingSubtitle": "Click the three dots to configure the widget.", "initializingTitle": "Get started", "loadingData": "Loading data", "noDataSubtitle": "No data available for the current date range. Please check back later.", "noDataTitle": "No Data Available"}, "workstation": {"orderStatusWidget": {"activeOrder": {"singular": "1 active order"}, "activeOrders": {"plural": "{{amount}} active orders"}, "notice": {"subtitle": {"plural": "{{amount}} active orders have been open for over 45 minutes", "singular": "1 active order has been open for over 45 minutes"}, "title": "Notice"}, "status": {"delayed": "{{amount}} Delayed", "healthy": "Healthy"}, "title": "Order Status"}}, "workstationOrderDetail": {"deliveryNumber": "Delivery Number", "orderStatus": "Order Status", "pickLines": "Pick Lines", "workstationOverview": "Workstation Overview"}, "workstationOrderPicks": {"container": "Container", "containerHeader": "Container", "containers": "Containers", "eventTimeHeader": "Event Time", "lastLocationHeader": "Last Location", "loading": "Loading...", "noData": "No data available", "orderLine": "Order Line", "qtyOrdered": "Qty Ordered", "qtyPicked": "<PERSON><PERSON> Picked", "quantityHeader": "Quantity", "size": "Size", "status": "Status", "statusHeader": "Status", "style": "Style", "transportHeader": "Transport"}, "workstationOrderStatus": {"activeOrders": "Active Orders", "arrivalTime": "Arrival Time", "completedPicks": "Completed Picks", "container": "Container", "dwellTime": "Dwell Time", "orderId": "Order ID", "orderStatus": "Order Status", "pickTask": "Pick Task", "position": "Position", "station": "Station", "title": "Order Status", "totalPicks": "Total Picks", "workstationOverview": "Workstation Overview"}, "workstationOrderStatusTable": {"arrivalTime": "Arrival Time", "completedPicks": "Completed Picks", "container": "Container", "dwellTime": "Dwell Time", "orderId": "Order ID", "orderStatus": "Order Status", "pickTask": "Pick Task", "position": "Position", "station": "Station"}, "Yesterday": "Yesterday"}