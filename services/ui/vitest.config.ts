import { defineConfig } from "vitest/config";
import path from "path";

/**
 * Exclude paths from test coverage - these includes internal code and configuration files
 */
const excludedTestingPaths = [
  "**/*.config.*",
  "**/*.d.ts",
  "**/*.spec.*",
  "**/*.test.*",
  ".yarn/**",
  "./src/app/components/datagrid/examples/**",
  "./src/app/mfe/**",
  "./src/app/views/debug-info",
  "./src/app/views/examples/**",
  "./src/app/views/mfe-manager",
  "./src/app/views/playground",
  "./src/app/widgets/examples/**",
  "./src/test-utils/**",
  "build/**",
  "dist/**",
  "eslint.config.js",
  "node_modules/**",
  "public/**",
  "vite.config.ts",
  "**/index.ts",
];

export default defineConfig({
  test: {
    watch: false,
    globals: true,
    environment: "jsdom",
    include: ["src/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}"],
    reporters: ["default"],
    coverage: {
      reportsDirectory: "./coverage/apps/dashboard",
      provider: "v8",
      exclude: excludedTestingPaths,
    },
  },
  resolve: {
    alias: {
      "@ict/sdk/*": path.resolve(
        __dirname,
        "../../libs/shared/ict-sdk/generated/*",
      ),
      "@ict/sdk": path.resolve(
        __dirname,
        "../../libs/shared/ict-sdk/generated",
      ),
    },
  },
});
