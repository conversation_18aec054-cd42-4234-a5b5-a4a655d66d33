[tools]
opentofu = "1.8.4"
node = "22"

[env]
ICT_TEST_ROOT="../ui-e2e"

[settings]
experimental = true

[tasks."prep"]
description = "build project"
run = [
    "corepack enable"
]

[tasks."setup"]
description = "build project"
depends = [ "prep"]
run = [
   "yarn install",
]

[tasks.build]
description = "build project"
depends = ["setup"]
run = [
   "yarn run build"
]

[tasks."test:build"]
description = "build test project"
depends = [ "prep" ]
run = """
    cd ${ICT_TEST_ROOT}
    yarn install
    curl --silent "https://gitlab.com/gitlab-org/incubation-engineering/mobile-devops/download-secure-files/-/raw/main/installer" | bash
    yarn build
"""

[tasks."test:playwright-setup"]
description = "install playwright"
depends = [ "setup" ]
run = """
    cd $ICT_TEST_ROOT
    mkdir -p node_modules/.cache/playwright/
    PLAYWRIGHT_BROWSERS_PATH=./node_modules/.cache/playwright yarn playwright install --with-deps
"""

[tasks."build:dev"]
description = "build project for dev"
depends = ["setup"]
run = [
   "yarn run build:dev"
]

[tasks."build:stage"]
description = "build project for stage"
depends = ["setup"]
run = [
   "yarn run build:stage"
]

[tasks."build:prod"]
description = "build project for prod"
depends = ["setup"]
run = [
   "yarn run build:prod"
]

[tasks.start]
description = "run locally using dev endpoints"
depends = ["build"]
run = "yarn start"

[tasks.cicd-test]
description = "Run all tests in the CI pipeline, including typecheck, lint, and test coverage"
depends = ["setup", "typecheck", "lint", "test-coverage"]

[tasks.typecheck]
description = "typecheck"
depends = ["setup"]
run = [
   "yarn run typecheck",
]

[tasks.lint]
description = "lint"
depends = ["setup"]
run = [
   "yarn run lint",  
]

[tasks.test-coverage]
description = "test coverage"
depends = ["setup"]
run = [
   "yarn run test:coverage"
]

[tasks."test:regression"]
description = "runs regression tests"
depends = [ "prep" ]
run = "cd $ICT_TEST_ROOT && yarn test:regression:ci"

[tasks."test:regression-full"]
description = "runs full regression"
depends = [ "prep" ]
run = "cd $ICT_TEST_ROOT && yarn test:regression:full:ci"

[tasks."test:smoke"]
description = "runs smoke tests"
depends = [ "prep" ]
run = "cd $ICT_TEST_ROOT && yarn test:smoke:ci"

[tasks."test:suite"]
description = "runs test suite specified by SUITE variable"
depends = [ "prep" ]
run = "cd $ICT_TEST_ROOT && yarn test:${SUITE}"

[tasks."test:check"]
description = "check playwright results"
run = [
    "chmod +x $ICT_TEST_ROOT/check.sh",
    "$ICT_TEST_ROOT/check.sh $ICT_TEST_ROOT/results/junit/ict-test-results.xml"
]
