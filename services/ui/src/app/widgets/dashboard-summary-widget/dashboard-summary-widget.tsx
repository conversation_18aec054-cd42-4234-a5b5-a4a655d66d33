import { Heading } from "@carbon/react";
import { useQuery } from "@tanstack/react-query";
import { dashboardSummaryResolver } from "../../api/resolver/dashboard-resolver/dashboard-resolver";
import type { DashboardSummaryResponse } from "../../api/resolver/dashboard-resolver/dashboard-resolver-types";
import type { DashboardSummaryType } from "../../api/resolver/dashboard-resolver/dashboard-resolver-types";
import { WidgetContainer } from "../../components/widget-container/widget-container";
import type { BaseWidgetProps, WidgetFilters } from "../widget.types";
import { useTranslation } from "react-i18next";
import { useDates } from "../../hooks/use-dates";
import { useWidgetAutoRefresh } from "../../hooks/use-widget-auto-refresh";
import { useTheme } from "@carbon/react";
import { ThemeMode } from "../../layout/theme";
import { getDashboardColorById } from "../../components/echarts/utils/carbon-colors";
import styles from "./dashboard-summary-widget.module.css";
import { useFilterStore } from "../../stores/filter-store";

export interface DashboardSummaryWidgetOptions {
  type: DashboardSummaryType;
  title: string;
  precision: number;
  unit: boolean;
  filters?: WidgetFilters;
  dms_id: string;
  chartColor?: string;
  [key: string]: unknown;
}

interface DashboardSummaryWidgetProps
  extends BaseWidgetProps<DashboardSummaryWidgetOptions> {
  options: DashboardSummaryWidgetOptions;
}

export const DashboardSummaryWidget = ({
  options,
  filters,
}: DashboardSummaryWidgetProps) => {
  const mergedFilters = {
    dms_id: options.dms_id,
    ...options?.filters,
    ...filters,
  };
  const { startDate, endDate } = useDates(mergedFilters.datePeriodRange);
  const { t } = useTranslation();
  const { theme } = useTheme();
  const isDark = theme === ThemeMode.DARK;
  const widgetTitle = options.title || "Dashboard Summary";
  const filtersVersion = useFilterStore((s) => s.filtersVersion);
  const selectedFilterValues = useFilterStore((s) => s.selectedFilterValues);
  const inputValues = useFilterStore((s) => s.inputValues);

  const {
    data: summary,
    isLoading,
    error,
    refetch,
  } = useQuery<DashboardSummaryResponse>({
    queryKey: [
      "dashboard-summary",
      options.type,
      mergedFilters,
      selectedFilterValues,
      filtersVersion,
      inputValues,
    ],
    queryFn: () =>
      dashboardSummaryResolver.getSummary(
        options.type,
        { ...mergedFilters, ...selectedFilterValues, ...inputValues },
        startDate,
        endDate,
      ),
    enabled: !!options.type && !!mergedFilters.dms_id,
    retry: 0,
  });
  useWidgetAutoRefresh({ filters, refetch });

  // Initial state handling
  if (!options.type) {
    return <WidgetContainer title={options.title} initializing />;
  }
  if (error) {
    return (
      <WidgetContainer
        title={options.title}
        error={error}
        errorMessage={t("dashboardSummary.errorLoadingDashboardSummary")}
      />
    );
  }
  if (isLoading || !summary) {
    return <WidgetContainer title={options.title} loading />;
  }
  if (summary.result === "204" || !summary.data?.length) {
    return <WidgetContainer title={options.title} noData />;
  }

  // Rendered widget
  return (
    <WidgetContainer title={widgetTitle}>
      <div className={styles.container}>
        {summary.data.map((item) => {
          const keyName = item.name ? item.name.toLowerCase() : "";
          const backgroundColor =
            options.chartColor || getDashboardColorById(keyName, isDark);
          const { totalMovements, percentage, time, ...extraFields } =
            item.value || {};

          return (
            <div
              key={keyName}
              className={styles.movementBox}
              style={{ backgroundColor }}
              data-testid={`dashboard-summary-${keyName}`}
            >
              <div className={styles.movementType}>
                <Heading className={styles.smallHeading}>{item.name}</Heading>
              </div>
              <div className={styles.movementValues}>
                {/* Percentage (optional) */}
                {percentage !== undefined && percentage !== null && (
                  <>
                    {typeof percentage === "number"
                      ? `${percentage.toFixed(2)}%`
                      : percentage}
                  </>
                )}

                {/* Separator if both exist */}
                {percentage !== undefined &&
                  percentage !== null &&
                  totalMovements !== undefined &&
                  totalMovements !== null &&
                  " | "}

                {/* Total movements (optional) */}
                {totalMovements !== undefined && totalMovements !== null && (
                  <>
                    {typeof totalMovements === "number"
                      ? totalMovements.toFixed(options.precision)
                      : totalMovements}{" "}
                    {options.unit ? t(summary.unit || "") : ""}
                  </>
                )}

                {/* Optional time */}
                {time && <> | {time}</>}

                {/* Render additional fields dynamically */}
                {Object.entries(extraFields).map(([key, value]) => (
                  <span key={key}>
                    {" "}
                    | {t(`dashboardSummary.${key}`, key)}: {String(value)}
                  </span>
                ))}
              </div>
            </div>
          );
        })}
      </div>
    </WidgetContainer>
  );
};

export default DashboardSummaryWidget;
