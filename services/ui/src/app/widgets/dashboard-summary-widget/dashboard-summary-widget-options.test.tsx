import { vi } from "vitest";
import { render, screen } from "../../../test-utils";
import { DashboardSummaryWidgetOptionsForm } from "./dashboard-summary-widget-options";
import type { DashboardSummaryWidgetOptions } from "./types";
import { DatePeriod } from "../../types/date-types";
const defaultFilters = { datePeriodRange: DatePeriod.today };

// --- Mock Carbon React partially ---
vi.mock("@carbon/react", async (importOriginal) => {
  const actual = (await importOriginal()) as any;
  return {
    ...actual,
    useTheme: vi.fn().mockReturnValue({ theme: "white" }),
  };
});

// --- Mock color utilities ---
vi.mock(
  "../../components/echarts/utils/carbon-colors",
  async (importOriginal) => {
    const actual = (await importOriginal()) as any;
    return {
      ...actual,
      getColorById: vi.fn((id) => `#color-${id}`),
      getColorOptions: vi.fn(() => [
        { id: "color-1", label: "Color 1" },
        { id: "color-2", label: "Color 2" },
        { id: "color-3", label: "Color 3" },
      ]),
    };
  },
);

describe("DashboardSummaryWidgetOptionsForm", () => {
  const mockOnChange = vi.fn();
  const defaultOptions: DashboardSummaryWidgetOptions = {
    colorId: "color-1",
    chartColor: "#color-1",
    stringOption: "mock",
    numberOption: 0,
    booleanOption: false,
    filters: defaultFilters,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("renders the accordion and color dropdown", () => {
    render(
      <DashboardSummaryWidgetOptionsForm
        options={defaultOptions}
        onChange={mockOnChange}
      />,
    );

    expect(screen.getByText("Styling")).toBeInTheDocument();
    expect(screen.getByText("Color")).toBeInTheDocument();
  });

  it("passes the selectedColorId correctly", () => {
    render(
      <DashboardSummaryWidgetOptionsForm
        options={defaultOptions}
        onChange={mockOnChange}
      />,
    );

    const dropdown = screen.getByText("Color");
    expect(dropdown).toBeInTheDocument();
  });

  it("renders the ColorPalette icon", () => {
    render(
      <DashboardSummaryWidgetOptionsForm
        options={defaultOptions}
        onChange={mockOnChange}
      />,
    );
    // Query by title, label, or alt if your icon provides one; fallback: query svg by data-testid or role
    expect(
      screen.getByRole("img", { hidden: true }) ||
        screen.getByTestId("color-palette-icon"),
    ).toBeDefined();
  });

  it("uses the current colorId from options as selectedColorId", () => {
    render(
      <DashboardSummaryWidgetOptionsForm
        options={{ ...defaultOptions, colorId: "color-3" }}
        onChange={mockOnChange}
      />,
    );

    // Verify that Color 3 is rendered as selected
    expect(screen.getByText("Color")).toBeInTheDocument();
    // optional: if dropdown shows swatch/selected text
    expect(screen.getByText("Color 3")).toBeInTheDocument();
  });

  it("renders with no colorId set (undefined)", () => {
    render(
      <DashboardSummaryWidgetOptionsForm
        options={{
          ...defaultOptions,
          colorId: undefined,
          chartColor: undefined,
        }}
        onChange={mockOnChange}
      />,
    );

    expect(screen.getByText("Color")).toBeInTheDocument();
  });

  it("does not crash if onChange is not provided", () => {
    // @ts-expect-error intentionally omit onChange
    render(<DashboardSummaryWidgetOptionsForm options={defaultOptions} />);
    expect(screen.getByText("Styling")).toBeInTheDocument();
  });

  it("renders with showSelectedSwatch enabled", () => {
    render(
      <DashboardSummaryWidgetOptionsForm
        options={defaultOptions}
        onChange={mockOnChange}
      />,
    );

    // ColorDropdown with showSelectedSwatch={true} should render a swatch or indicator
    expect(screen.getByText("Color")).toBeInTheDocument();
  });

  it("renders inside an OptionsAccordion", () => {
    render(
      <DashboardSummaryWidgetOptionsForm
        options={defaultOptions}
        onChange={mockOnChange}
      />,
    );

    // Verify the accordion group exists
    expect(screen.getByText("Styling")).toBeInTheDocument();
  });
});
