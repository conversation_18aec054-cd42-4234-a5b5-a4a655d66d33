import { useTheme } from "@carbon/react";
import { useState, useMemo } from "react";
import { useQuery } from "@tanstack/react-query";
import { categoryChartResolver } from "../../api/resolver/category-chart-resolver/category-chart-resolver";
import { ComboChartComponent } from "../../components/combo-chart/combo-chart-component";
import { getColorById } from "../../components/echarts/utils/carbon-colors";
import { WidgetContainer } from "../../components/widget-container/widget-container";
import { useConfigSetting } from "../../config/hooks/use-config";
import { ThemeMode } from "../../layout/theme";
import type { BaseWidgetProps } from "../widget.types";
import styles from "./combo-chart-ext-widget.module.css";
import type { ComboChartExtWidgetOptions } from "./types";
import { useDateRangeFormatter, useDates } from "../../hooks/use-dates";
import { useWidgetAutoRefresh } from "../../hooks/use-widget-auto-refresh";
import { CategoryChartType } from "src/app/api/resolver/category-chart-resolver/category-chart-types";
import {
  CategoryChartData,
  CategoryChartSeriesData,
  CategoryChartSeriesDataPoints,
} from "src/app/api/resolver/category-chart-resolver/category-chart-resolver-types";
import { useFilterStore } from "../../stores/filter-store";
import { SortAscending, SortDescending, CaretDown } from "@carbon/icons-react";
import { UnitUtil, type UnitType } from "../../utils/unit-util";

interface ComboChartWidgetProps
  extends BaseWidgetProps<ComboChartExtWidgetOptions> {
  options: ComboChartExtWidgetOptions;
}

export const ComboChartExtWidget = ({
  options,
  filters,
}: ComboChartWidgetProps) => {
  const mergedFilters = { ...options.filters, ...filters };
  const { setting: timezoneConfig } = useConfigSetting("site-time-zone");
  const { startDate, endDate } = useDates(mergedFilters.datePeriodRange);
  const defaultDateFormat = useDateRangeFormatter(filters.datePeriodRange);
  const dateFormat = options.dateFormat ?? defaultDateFormat;
  const { theme } = useTheme();
  const isDark = theme === ThemeMode.DARK;
  const [currentSortBy, setCurrentSortBy] = useState(options.sortBy || "none");
  const [currentSortByName, setCurrentSortByName] = useState(
    options.sortByName || "none",
  );
  const [isDropdownOpen, setDropdownOpen] = useState(false);
  const filtersVersion = useFilterStore((s) => s.filtersVersion);
  const selectedFilterValues = useFilterStore((s) => s.selectedFilterValues);
  const inputValues = useFilterStore((s) => s.inputValues);
  const valueAxis = options.chartStyle === "stacked-row";

  type SortOrder = "none" | "ascending" | "descending";
  const dashboardColors = [
    "storage",
    "shuffle",
    "retrieval",
    "bypass",
    "iat",
    "positioning",
    "all",
  ];

  const {
    data: response,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: [
      "chart",
      options.type,
      options.filters,
      filters.datePeriodRange,
      selectedFilterValues,
      filtersVersion,
      inputValues,
    ],
    queryFn: () =>
      categoryChartResolver.getCategoryChart(
        options.type as CategoryChartType,
        { ...mergedFilters, ...selectedFilterValues, ...inputValues },
        startDate,
        endDate,
      ),
    enabled: !!options.type,
    retry: 0,
  });

  const handleRefresh = () => {
    refetch();
  };

  useWidgetAutoRefresh({
    filters,
    refetch: handleRefresh,
  });

  if (!options.type) {
    return (
      <WidgetContainer title={options.title || "Combo Chart"} initializing />
    );
  }

  if (error) {
    return (
      <WidgetContainer title={options.title || "Combo Chart"} error={error} />
    );
  }

  if (isLoading || !response) {
    return <WidgetContainer title={options.title || "Combo Chart"} loading />;
  }

  const chartTitle = options.title || "Combo Chart";

  if (
    !response?.success ||
    !response.data ||
    !("series" in response.data) ||
    !response.data.series ||
    response.data.series.length === 0 ||
    response.data.series.some(
      (series: CategoryChartSeriesData) =>
        !series.data ||
        series.data.length === 0 ||
        series.data.some((point: CategoryChartSeriesDataPoints) =>
          isNaN(point.value),
        ),
    )
  ) {
    return <WidgetContainer title={chartTitle} noData />;
  }

  const toggleDropdown = () => {
    setDropdownOpen(!isDropdownOpen);
  };

  function getNextSortOrder(current: SortOrder, valueAxis: boolean): SortOrder {
    if (!valueAxis) {
      // Normal order: none → ascending → descending
      if (current === "none") return "ascending";
      if (current === "ascending") return "descending";
      return "none";
    } else {
      // Flipped order for stacked-row: none → descending → ascending
      if (current === "none") return "descending";
      if (current === "descending") return "ascending";
      return "none";
    }
  }

  const handleSortClick = (type: "value" | "name") => {
    if (type === "value") {
      setCurrentSortBy(getNextSortOrder(currentSortBy, valueAxis));
      setCurrentSortByName("none");
    } else {
      setCurrentSortByName(getNextSortOrder(currentSortByName, valueAxis));
      setCurrentSortBy("none");
    }
  };

  const chartData = response.data as CategoryChartData;
  const chartColor = options.colorId
    ? getColorById(options.colorId, isDark)
    : dashboardColors;

  function detectLabelType(
    values: (string | number | null | undefined)[],
  ): "number" | "date" | "string" | "mixed" {
    let hasNumber = false;
    let hasString = false;

    for (const v of values) {
      if (v == null) continue;
      const str = String(v);

      if (!isNaN(Number(str))) {
        hasNumber = true;
      } else if (!isNaN(new Date(str).getTime())) {
        return "date";
      } else {
        hasString = true;
      }
    }

    if (hasNumber && hasString) return "mixed";
    if (hasNumber) return "number";
    if (hasString) return "string";
    return "string";
  }

  const sortedChartData = useMemo(() => {
    const clonedData = {
      ...chartData,
      series: chartData.series.map((series) => ({
        ...series,
        data: [...series.data],
      })),
    };

    if (currentSortBy === "none" && currentSortByName === "none") {
      return clonedData;
    }

    let allUnits = Array.from(
      new Set(
        clonedData.series.flatMap((series) =>
          series.data.map((d) => d?.unit ?? ""),
        ),
      ),
    );

    const unitType = detectLabelType(allUnits);
    const valueType = detectLabelType(
      clonedData.series.flatMap((s) => s.data.map((d) => d?.value)),
    );

    if (currentSortBy !== "none") {
      clonedData.series = clonedData.series.map((series) => {
        const unitMap = new Map(series.data.map((d) => [d.unit, d.value]));
        return {
          ...series,
          data: allUnits.map((unit) => ({
            unit,
            value: unitMap.get(unit) ?? 0,
          })),
        };
      });

      const barTotals = allUnits.map((unit) => {
        const total = clonedData.series.reduce(
          (sum, series) =>
            sum + (series.data.find((d) => d.unit === unit)?.value || 0),
          0,
        );
        return { unit, total };
      });

      barTotals.sort((a, b) => {
        if (valueType === "date") {
          const dateA = new Date(a.total).getTime() || 0;
          const dateB = new Date(b.total).getTime() || 0;
          return currentSortBy === "ascending" ? dateA - dateB : dateB - dateA;
        }
        if (valueType === "number") {
          return currentSortBy === "ascending"
            ? a.total - b.total
            : b.total - a.total;
        }
        return currentSortBy === "ascending"
          ? String(a.total).localeCompare(String(b.total))
          : String(b.total).localeCompare(String(a.total));
      });

      allUnits = barTotals.map(({ unit }) => unit);

      clonedData.series = clonedData.series.map((series) => ({
        ...series,
        data: allUnits.map(
          (unit) =>
            series.data.find((d) => d.unit === unit) ?? { unit, value: 0 },
        ),
      }));
    }

    if (currentSortByName !== "none") {
      allUnits.sort((valA, valB) => {
        if (
          unitType === "number" ||
          (/^\d+$/.test(valA) && /^\d+$/.test(valB))
        ) {
          return currentSortByName === "ascending"
            ? Number(valA) - Number(valB)
            : Number(valB) - Number(valA);
        }

        if (unitType === "date") {
          const dateA = new Date(valA).getTime() || 0;
          const dateB = new Date(valB).getTime() || 0;
          return currentSortByName === "ascending"
            ? dateA - dateB
            : dateB - dateA;
        }

        const isNumA = /^\d+$/.test(valA);
        const isNumB = /^\d+$/.test(valB);
        if (isNumA && !isNumB) {
          return currentSortByName === "ascending" ? -1 : 1;
        }
        if (!isNumA && isNumB) {
          return currentSortByName === "ascending" ? 1 : -1;
        }

        return currentSortByName === "ascending"
          ? String(valA).localeCompare(String(valB))
          : String(valB).localeCompare(String(valA));
      });

      clonedData.series = clonedData.series.map((series) => {
        const unitMap = new Map(series.data.map((d) => [d.unit, d.value]));
        return {
          ...series,
          data: allUnits.map((unit) => ({
            unit,
            value: unitMap.get(unit) ?? 0,
          })),
        };
      });
    }

    return clonedData;
  }, [chartData, currentSortBy, currentSortByName]);

  const rawUnit = chartData.series[0]?.unit;

  let yAxisTitle: string | undefined;
  let xAxisTitle: string | undefined;

  if (Array.isArray(rawUnit)) {
    yAxisTitle = UnitUtil.getUnitLabel(rawUnit[0] as UnitType);
    xAxisTitle = UnitUtil.getUnitLabel(rawUnit[1] as UnitType);
  } else if (typeof rawUnit === "string") {
    yAxisTitle = UnitUtil.getUnitLabel(rawUnit as UnitType);
    xAxisTitle = "X-Axis";
  }

  return (
    <WidgetContainer title={chartTitle}>
      <div
        className={styles.container}
        onMouseDown={(e) => e.stopPropagation()}
        onTouchStart={(e) => e.stopPropagation()}
      >
        <div className={styles.chartWrapper}>
          {options.enableSorting && (
            <div className={styles.sortWrapper}>
              <span className={styles.rotatedSortIcon}>
                {currentSortBy === "ascending" && (
                  <SortAscending
                    onClick={() =>
                      handleSortClick(valueAxis ? "name" : "value")
                    }
                  />
                )}
                {currentSortBy === "descending" && (
                  <SortDescending
                    onClick={() =>
                      handleSortClick(valueAxis ? "name" : "value")
                    }
                  />
                )}
                {currentSortBy === "none" && (
                  <SortAscending
                    onClick={() =>
                      handleSortClick(valueAxis ? "name" : "value")
                    }
                  />
                )}
              </span>

              <span className={styles.caretIcon}>
                <CaretDown onClick={() => toggleDropdown()} />
              </span>

              {isDropdownOpen && (
                <div className={styles.dropdownMenu}>
                  {[yAxisTitle, xAxisTitle, "None"].map((title, idx) => {
                    let sortLabel = "";
                    let isSelected = false;

                    // figure out which option this idx refers to
                    const isValueOption = valueAxis ? idx === 1 : idx === 0;
                    const isNameOption = valueAxis ? idx === 0 : idx === 1;

                    const getLabel = (order: SortOrder) => {
                      if (!valueAxis)
                        return order === "ascending" ? "Asc" : "Desc";
                      // Flip labels for stacked-row
                      return order === "ascending" ? "Desc" : "Asc";
                    };

                    if (isValueOption) {
                      if (currentSortBy !== "none") {
                        sortLabel = getLabel(currentSortBy);
                      }
                      isSelected = currentSortBy !== "none";
                    } else if (isNameOption) {
                      if (currentSortByName !== "none") {
                        sortLabel = getLabel(currentSortByName);
                      }
                      isSelected = currentSortByName !== "none";
                    }

                    return (
                      <div
                        key={idx}
                        className={styles.dropdownItem}
                        onClick={() => {
                          if (isValueOption) {
                            setCurrentSortBy(
                              getNextSortOrder(currentSortBy, valueAxis),
                            );
                            setCurrentSortByName("none");
                          } else if (isNameOption) {
                            setCurrentSortByName(
                              getNextSortOrder(currentSortByName, valueAxis),
                            );
                            setCurrentSortBy("none");
                          } else {
                            setCurrentSortBy("none");
                            setCurrentSortByName("none");
                          }
                          setDropdownOpen(false);
                        }}
                      >
                        {isSelected && (
                          <span className={styles.tickMark}>✔</span>
                        )}
                        <span className={styles.dropdownLabel}>
                          {title} {sortLabel && `(${sortLabel})`}
                        </span>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          )}

          <ComboChartComponent
            chartData={sortedChartData}
            chartStyle={options.chartStyle}
            color={chartColor}
            showAverageLine={options.showAverageLine}
            showTargetLine={options.showTargetLine}
            showLegend={options.showLegend}
            targetValue={options.targetValue}
            timezone={timezoneConfig?.value as string}
            dateFormat={dateFormat}
          />

          {options.enableSorting && (
            <div
              className={styles.bottomSortIcon}
              onClick={() => handleSortClick(valueAxis ? "value" : "name")}
            >
              {currentSortByName === "ascending" && <SortAscending />}
              {currentSortByName === "descending" && <SortDescending />}
              {currentSortByName === "none" && <SortAscending />}
            </div>
          )}
        </div>
      </div>
    </WidgetContainer>
  );
};

export default ComboChartExtWidget;
