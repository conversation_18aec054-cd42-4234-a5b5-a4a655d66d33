import {
  Analytics,
  ColorPalette,
  DataBase,
  SortDescending,
} from "@carbon/icons-react";
import {
  ComboBox,
  NumberInput,
  TextInput,
  Toggle,
  useTheme,
} from "@carbon/react";
import { useQuery } from "@tanstack/react-query";
import { categoryChartResolver } from "../../api/resolver/category-chart-resolver/category-chart-resolver";
import { ColorDropdown } from "../../components/color-dropdown";
import { DateFormatDropdown } from "../../components/date-format-dropdown/date-format-dropdown";
import { getColorById } from "../../components/echarts/utils/carbon-colors";
import { OptionsAccordionGroup } from "../../components/options/options-accordion-group/options-accordion-group";
import { OptionsAccordion } from "../../components/options/options-accordion/options-accordion";
import { ThemeMode } from "../../layout/theme";
import type { BaseWidgetOptionsProps } from "../widget.types";
import type { ComboChartExtWidgetOptions } from "./types";
import { useTranslation } from "react-i18next";
import { useEffect, useState } from "react";
import { CategoryChartType } from "src/app/api/resolver/category-chart-resolver/category-chart-types";
import { ChartInfo } from "src/app/api/resolver/category-chart-resolver/category-chart-resolver-types";

function formatLabel(value: string): string {
  return value
    .split("_")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
}

export const ComboChartExtWidgetOptionsForm = ({
  options,
  onChange,
}: BaseWidgetOptionsProps) => {
  // Type assertion to ensure options has the correct shape
  const chartOptions = options as ComboChartExtWidgetOptions;
  const { theme } = useTheme();
  const isDark = theme === ThemeMode.DARK;
  const { t } = useTranslation();

  const [selectedChart, setSelectedChart] = useState<ChartInfo | null>(null);

  const { data: categoryChartInfo } = useQuery({
    queryKey: ["category-chart-info"],
    queryFn: () => categoryChartResolver.getCategoryChartInfo(),
  });

  useEffect(() => {
    if (categoryChartInfo) {
      const selectedMetric = categoryChartInfo?.find(
        (info) => info.id === chartOptions.type,
      );
      setSelectedChart(selectedMetric || null);
    }
  }, [categoryChartInfo, chartOptions.type]);

  const handleChartTypeChange = (selectedId: string | null) => {
    if (!selectedId) {
      return;
    }

    const newChartType = selectedId as CategoryChartType;
    const selected = categoryChartInfo?.find((info) => info.id === selectedId);
    if (selected) {
      onChange({
        ...chartOptions,
        type: newChartType,
        groupBy: "",
        dmsId: "",
      });
    }
  };

  const handleGroupByChange = (selectedGroupBy: string | null) => {
    if (!selectedGroupBy) {
      return;
    }

    onChange({
      ...options,
      filters: {
        // datePeriodRange: options.filters?.datePeriodRange || DatePeriod.today,
        // ...options.filters,
        ...(options?.filters || {}),
        groupBy: selectedGroupBy,
        // dmsId: options.filters?.dmsId
      },
    });
  };

  const handleDMSChange = (selectedDMS: string | null) => {
    if (!selectedDMS) {
      return;
    }

    onChange({
      ...options,
      filters: {
        // datePeriodRange: options.filters?.datePeriodRange || DatePeriod.today,
        // ...options.filters,
        // groupBy: options.filters?.groupBy,
        ...(options?.filters || {}),
        dmsId: selectedDMS,
      },
    });
  };

  const groupByOptions = selectedChart?.groupBy || [];
  const dmsOptions = selectedChart?.dmsId || [];

  return (
    <OptionsAccordion>
      <OptionsAccordionGroup
        id="display"
        title={t("chartWidgetOptions.displayTitle", "Display")}
        icon={<Analytics size="24" />}
      >
        <TextInput
          labelText={t("chartWidgetOptions.chartTitle", "Chart Title")}
          id="chart-title"
          value={chartOptions.title || ""}
          onChange={(e) => onChange({ ...chartOptions, title: e.target.value })}
        />
      </OptionsAccordionGroup>

      <OptionsAccordionGroup
        id="data"
        title={t("chartWidgetOptions.dataTitle", "Data")}
        icon={<DataBase size="24" />}
      >
        <ComboBox
          titleText={t("chartWidgetOptions.chartType", "Chart Type")}
          id="chart-type"
          items={
            categoryChartInfo
              ? [...categoryChartInfo]
                  .sort((a, b) => a.title.localeCompare(b.title))
                  .map((info) => ({
                    id: info.id,
                    text: info.title, // Assuming info.title is translated or key
                  }))
              : []
          }
          selectedItem={
            chartOptions.type
              ? {
                  id: chartOptions.type,
                  text:
                    categoryChartInfo?.find(
                      (info) => info.id === chartOptions.type,
                    )?.title || "",
                }
              : null
          }
          onChange={({ selectedItem }) => {
            if (selectedItem) {
              handleChartTypeChange(selectedItem.id);
            }
          }}
          itemToString={(item) => (item ? item.text : "")}
          allowCustomValue
        />

        {chartOptions.type && dmsOptions.length > 0 && (
          <ComboBox
            titleText={t("chartWidgetOptions.groupBy", "Type")}
            id="dms-type"
            items={dmsOptions.map((option) => ({
              id: option,
              // Assuming the transformed text here doesn't need translation itself
              text: formatLabel(option),
            }))}
            selectedItem={
              chartOptions?.filters?.dmsId
                ? {
                    id: chartOptions.filters.dmsId,
                    text: formatLabel(chartOptions.filters.dmsId),
                  }
                : null
            }
            onChange={({ selectedItem }) => {
              if (selectedItem) {
                handleDMSChange(selectedItem.id);
              }
            }}
            itemToString={(item) => (item ? item.text : "")}
          />
        )}

        {chartOptions.type && groupByOptions.length > 0 && (
          <ComboBox
            titleText={t("chartWidgetOptions.groupBy", "Group By")}
            id="group-by"
            items={groupByOptions.map((option) => ({
              id: option,
              // Assuming the transformed text here doesn't need translation itself
              text: formatLabel(option),
            }))}
            selectedItem={
              chartOptions?.filters?.groupBy
                ? {
                    id: chartOptions.filters.groupBy,
                    text: formatLabel(chartOptions.filters.groupBy),
                  }
                : null
            }
            onChange={({ selectedItem }) => {
              if (selectedItem) {
                handleGroupByChange(selectedItem.id);
              }
            }}
            itemToString={(item) => (item ? item.text : "")}
          />
        )}

        <ComboBox
          titleText={t("chartWidgetOptions.chartStyle", "Chart Style")}
          id="chart-style"
          items={[
            { id: "line", text: t("chartWidgetOptions.styleLine", "Line") },
            { id: "area", text: t("chartWidgetOptions.styleArea", "Area") },
            {
              id: "column",
              text: t("chartWidgetOptions.styleColumn", "Column"),
            },
            {
              id: "stacked-area",
              text: t("chartWidgetOptions.styleStackedArea", "Stacked Area"),
            },
            {
              id: "stacked-column",
              text: t(
                "chartWidgetOptions.styleStackedColumn",
                "Stacked Column",
              ),
            },
            {
              id: "stacked-row",
              text: t("chartWidgetOptions.styleStackedRow", "Stacked Row"),
            },
          ]}
          selectedItem={
            chartOptions.chartStyle
              ? {
                  id: chartOptions.chartStyle,
                  text:
                    chartOptions.chartStyle.charAt(0).toUpperCase() +
                    chartOptions.chartStyle.slice(1).replace("-", " "),
                }
              : { id: "line", text: t("chartWidgetOptions.styleLine", "Line") }
          }
          onChange={({ selectedItem }) => {
            if (selectedItem) {
              onChange({
                ...chartOptions,
                chartStyle: selectedItem.id,
              });
            }
          }}
          itemToString={(item) => (item ? item.text : "")}
        />
        <Toggle
          labelText={t(
            "chartWidgetOptions.showAverageLine",
            "Show Average Line",
          )}
          id="show-average-line"
          toggled={chartOptions.showAverageLine || false}
          onToggle={(toggled) =>
            onChange({
              ...chartOptions,
              showAverageLine: toggled,
            })
          }
        />
        <Toggle
          labelText={t("chartWidgetOptions.showTargetLine", "Show Target Line")}
          id="show-target-line"
          toggled={chartOptions.showTargetLine || false}
          onToggle={(toggled) =>
            onChange({
              ...chartOptions,
              showTargetLine: toggled,
            })
          }
        />
        <Toggle
          labelText="Show Legend"
          id="show-legend"
          toggled={chartOptions.showLegend !== false}
          onToggle={(toggled) =>
            onChange({
              ...chartOptions,
              showLegend: toggled,
            })
          }
        />
        {chartOptions.showTargetLine && (
          <NumberInput
            label={t("chartWidgetOptions.targetValue", "Target Value")}
            id="target-value"
            value={chartOptions.targetValue || 0}
            onChange={(_e, { value }) =>
              onChange({ ...chartOptions, targetValue: Number(value) })
            }
            min={0}
          />
        )}
      </OptionsAccordionGroup>
      <OptionsAccordionGroup
        id="sorting"
        title={t("chartWidgetOptions.sortingTitle", "Sorting")}
        icon={<SortDescending size="24" />}
      >
        <Toggle
          labelText={t("chartWidgetOptions.enableSorting", "Enable Sorting")}
          id="enable-sorting"
          toggled={chartOptions.enableSorting}
          onToggle={(toggled) =>
            onChange({
              ...chartOptions,
              enableSorting: toggled,
              sortBy: "none",
              sortByName: "none",
            })
          }
        />
      </OptionsAccordionGroup>
      <OptionsAccordionGroup
        id="styling"
        title="Styling"
        icon={<ColorPalette size="24" />}
      >
        <ColorDropdown
          id="chart-color"
          titleText="Color"
          selectedColorId={chartOptions.colorId}
          onChange={(colorId) => {
            onChange({
              ...chartOptions,
              colorId: colorId,
              chartColor: colorId ? getColorById(colorId, isDark) : undefined,
            });
          }}
          showSelectedSwatch={true}
          allowDeselect={true}
        />
        <DateFormatDropdown
          id="date-format"
          titleText="Date Format"
          selectedDateFormat={chartOptions.dateFormat}
          onChange={(dateFormat) => {
            onChange({ ...chartOptions, dateFormat });
          }}
        />
      </OptionsAccordionGroup>
    </OptionsAccordion>
  );
};

export default ComboChartExtWidgetOptionsForm;
