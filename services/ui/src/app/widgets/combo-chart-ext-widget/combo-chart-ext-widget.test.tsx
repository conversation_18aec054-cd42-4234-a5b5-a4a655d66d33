import { useQuery, UseQueryResult } from "@tanstack/react-query";
import { vi } from "vitest";
import { render, screen } from "../../../test-utils";
import { categoryChartResolver } from "../../api/resolver/category-chart-resolver/category-chart-resolver";
import { CategoryChartType } from "../../api/resolver/category-chart-resolver/category-chart-types";
import type { CategoryChartData } from "../../api/resolver/category-chart-resolver/category-chart-resolver-types";
import { DatePeriod } from "../../types/date-types";
import { ComboChartExtWidget } from "./combo-chart-ext-widget";
import type { ComboChartExtWidgetOptions } from "./types";

const startDate = new Date("2023-01-01");
const endDate = new Date("2023-01-31");

vi.mock("../../hooks/use-dates", () => ({
  useDates: () => ({
    startDate,
    endDate,
  }),
  useDateRangeFormatter: () => ({ month: "2-digit", day: "2-digit" }),
}));

// Mock the chartResolver
vi.mock(
  import("../../api/resolver/category-chart-resolver/category-chart-resolver"),
  async (importOriginal) => {
    const actual = await importOriginal();
    return {
      ...actual,
      getCategoryChart: vi.fn(),
      getCategoryChartInfo: vi.fn(),
    };
  },
);

// Mock the useQuery hook
vi.mock("@tanstack/react-query", async () => {
  const actual = await vi.importActual("@tanstack/react-query");
  return {
    ...actual,
    useQuery: vi.fn().mockImplementation(({ queryFn, enabled }) => {
      if (!enabled) {
        return { isLoading: false, error: null, data: null };
      }
      try {
        const data = queryFn();
        return { isLoading: false, error: null, data };
      } catch (error) {
        return { isLoading: false, error, data: null };
      }
    }),
  };
});

// Mock Carbon components
vi.mock("@carbon/react", async () => {
  const actual = await vi.importActual("@carbon/react");
  return {
    ...actual,
    useTheme: () => ({ theme: "white" }),
    GlobalTheme: ({ children }: { children: React.ReactNode }) => children,
  };
});

// Mock color utility function
vi.mock("../../components/echarts/utils/carbon-colors", () => ({
  getColorById: vi.fn().mockReturnValue("#mock-color-id"),
}));

// Mock the ComboChartComponent
vi.mock("../../components/combo-chart/combo-chart-component", () => ({
  ComboChartComponent: ({
    chartData,
    chartStyle,
    color,
    showAverageLine,
    showTargetLine,
    targetValue,
    timezone,
    dateFormat,
  }: any) => (
    <div data-testid="combo-chart-component">
      <div data-testid="chart-data">{JSON.stringify(chartData)}</div>
      <div data-testid="chart-style">{chartStyle || "default-style"}</div>
      <div data-testid="color">{color || "auto"}</div>
      <div data-testid="show-average-line">
        {showAverageLine ? "Show Average Line" : "Hide Average Line"}
      </div>
      <div data-testid="show-target-line">
        {showTargetLine ? "Show Target Line" : "Hide Target Line"}
      </div>
      <div data-testid="target-value">{targetValue}</div>
      <div data-testid="timezone">{timezone}</div>
      <div data-testid="date-format">{JSON.stringify(dateFormat)}</div>
    </div>
  ),
}));

// Mock the WidgetContainer component
vi.mock("../../components/widget-container/widget-container", () => ({
  WidgetContainer: ({
    children,
    title,
    loading,
    error,
    noData,
    initializing,
  }: any) => (
    <div data-testid="widget-container">
      <h3>{title}</h3>
      {initializing && <div data-testid="initializing">Initializing</div>}
      {loading && <div data-testid="loading">Loading</div>}
      {error && <div data-testid="error">Error</div>}
      {noData && <div data-testid="no-data">No Data</div>}
      {!initializing && !loading && !error && !noData && children}
    </div>
  ),
}));

// Mock the useConfigSetting hook
vi.mock("../../config/hooks/use-config", () => ({
  useConfigSetting: () => ({
    setting: { value: "UTC" },
  }),
}));

describe("ComboChartWidget", () => {
  const mockTimeChartData: CategoryChartData = {
    unit: "%",
    id: "test_chart",
    series: [
      {
        name: "Test Series",
        data: [
          { time: "2023-01-01", value: 10 },
          { time: "2023-01-02", value: 20 },
          { time: "2023-01-03", value: 30 },
        ],
        unit: "percent",
      },
    ],
    xAxis: {
      type: "time",
      data: ["2023-01-01", "2023-01-02", "2023-01-03"],
    },
  } as any;

  const mockSuccessResponse = {
    success: true,
    data: mockTimeChartData,
  };

  const mockEmptyResponse = {
    success: true,
    data: null,
  };

  const defaultOptions: ComboChartExtWidgetOptions = {
    title: "Test Combo Chart",
    type: "test_chart" as CategoryChartType,
    chartStyle: "line",
    showAverageLine: false,
    showTargetLine: false,
    filters: {
      datePeriodRange: DatePeriod.today,
    },
  };

  const defaultFilters = {
    datePeriodRange: DatePeriod.today,
  };

  beforeEach(() => {
    vi.clearAllMocks();
    // Default mock implementation for successful data fetch
    categoryChartResolver.getCategoryChart = vi
      .fn()
      .mockReturnValue(mockSuccessResponse as any);
    // vi.mocked(categoryChartResolver.getCategoryChart).mockReturnValue(
    //   mockSuccessResponse as any,
    // );
  });

  it("renders the widget with chart data", () => {
    render(
      <ComboChartExtWidget
        id="test_chart"
        type="test_chart"
        options={defaultOptions}
        filters={defaultFilters}
      />,
    );

    // Check if widget container is rendered with correct title
    expect(screen.getByTestId("widget-container")).toBeInTheDocument();
    expect(screen.getByText("Test Combo Chart")).toBeInTheDocument();

    // Check if combo chart component is rendered
    expect(screen.getByTestId("combo-chart-component")).toBeInTheDocument();

    // Check if chart data is passed correctly
    expect(screen.getByTestId("chart-data")).toHaveTextContent(
      JSON.stringify(mockTimeChartData),
    );

    // Check if chart style is passed correctly
    expect(screen.getByTestId("chart-style")).toHaveTextContent("line");
  });

  it("shows initializing state when no chart type is provided", () => {
    const optionsWithoutType = { ...defaultOptions, type: undefined };
    render(
      <ComboChartExtWidget
        id="test_chart"
        type="test_chart"
        options={optionsWithoutType}
        filters={defaultFilters}
      />,
    );

    // Check if initializing state is displayed
    expect(screen.getByTestId("initializing")).toBeInTheDocument();
  });

  it("shows loading state while fetching data", () => {
    // Mock the useQuery hook to return loading state
    vi.mocked(useQuery).mockReturnValueOnce({
      data: null,
      isLoading: true,
      error: null,
    } as unknown as UseQueryResult<unknown, unknown>);

    render(
      <ComboChartExtWidget
        id="test_chart"
        type="test_chart"
        options={defaultOptions}
        filters={defaultFilters}
      />,
    );

    // Check if loading state is displayed
    expect(screen.getByTestId("loading")).toBeInTheDocument();
  });

  it("shows error state when API request fails", () => {
    // Mock the useQuery hook to return error state
    vi.mocked(useQuery).mockReturnValueOnce({
      data: { foo: "bar" },
      isLoading: false,
      error: new Error("Failed to fetch data"),
    } as unknown as UseQueryResult<unknown, unknown>);

    render(
      <ComboChartExtWidget
        id="test_chart"
        type="test_chart"
        options={defaultOptions}
        filters={defaultFilters}
      />,
    );

    // Check if error state is displayed
    expect(screen.getByTestId("error")).toBeInTheDocument();
  });

  it("shows no data state when API returns empty data", () => {
    // Mock the chartResolver to return empty data
    vi.mocked(categoryChartResolver.getCategoryChart).mockReturnValue(
      mockEmptyResponse as any,
    );

    render(
      <ComboChartExtWidget
        id="test_chart"
        type="test_chart"
        options={defaultOptions}
        filters={defaultFilters}
      />,
    );

    // Check if no data state is displayed
    expect(screen.getByTestId("no-data")).toBeInTheDocument();
  });

  it("makes API request with correct parameters", () => {
    render(
      <ComboChartExtWidget
        id="test_chart"
        type="test_chart"
        options={defaultOptions}
        filters={defaultFilters}
      />,
    );

    // Check if the API was called with the correct parameters
    expect(categoryChartResolver.getCategoryChart).toHaveBeenCalledWith(
      "test_chart",
      {
        ...defaultOptions.filters,
        ...defaultFilters,
      },
      startDate,
      endDate,
    );
  });

  it("merges filters from options and props", () => {
    const customFilters = {
      datePeriodRange: DatePeriod.yesterday,
      customFilter: "custom_value",
    };

    render(
      <ComboChartExtWidget
        id="test_chart"
        type="test_chart"
        options={defaultOptions}
        filters={customFilters}
      />,
    );

    // Check if the API was called with merged filters
    expect(categoryChartResolver.getCategoryChart).toHaveBeenCalledWith(
      "test_chart",
      {
        ...defaultOptions.filters,
        ...customFilters,
      },
      startDate,
      endDate,
    );
  });

  it("uses color from colorId when provided", () => {
    const optionsWithColorId = {
      ...defaultOptions,
      colorId: "purple",
    };

    render(
      <ComboChartExtWidget
        id="test_chart"
        type="test_chart"
        options={optionsWithColorId}
        filters={defaultFilters}
      />,
    );

    // Check that the color from colorId is being used (via the mock)
    expect(screen.getByTestId("color")).toHaveTextContent("#mock-color-id");
  });

  it("uses default title when none is provided", () => {
    const optionsWithoutTitle = {
      ...defaultOptions,
      title: undefined,
    };

    render(
      <ComboChartExtWidget
        id="test_chart"
        type="test_chart"
        options={optionsWithoutTitle}
        filters={defaultFilters}
      />,
    );

    // Check that the default title is used
    expect(screen.getByText("Combo Chart")).toBeInTheDocument();
  });

  it("shows average line when enabled", () => {
    const optionsWithAverageLine = {
      ...defaultOptions,
      showAverageLine: true,
    };

    render(
      <ComboChartExtWidget
        id="test_chart"
        type="test_chart"
        options={optionsWithAverageLine}
        filters={defaultFilters}
      />,
    );

    expect(screen.getByTestId("show-average-line")).toHaveTextContent(
      "Show Average Line",
    );
  });

  it("shows target line with target value when enabled", () => {
    const optionsWithTargetLine = {
      ...defaultOptions,
      showTargetLine: true,
      targetValue: 50,
    };

    render(
      <ComboChartExtWidget
        id="test_chart"
        type="test_chart"
        options={optionsWithTargetLine}
        filters={defaultFilters}
      />,
    );

    expect(screen.getByTestId("show-target-line")).toHaveTextContent(
      "Show Target Line",
    );
    expect(screen.getByTestId("target-value")).toHaveTextContent("50");
  });

  it("passes timezone and date format to chart component", () => {
    render(
      <ComboChartExtWidget
        id="test_chart"
        type="test_chart"
        options={defaultOptions}
        filters={defaultFilters}
      />,
    );

    expect(screen.getByTestId("timezone")).toHaveTextContent("UTC");
    expect(screen.getByTestId("date-format")).toHaveTextContent(
      JSON.stringify({ month: "2-digit", day: "2-digit" }),
    );
  });
});
