import { useQuery, useQueries } from "@tanstack/react-query";
import { useFilterStore } from "../../stores/filter-store";
import { WidgetContainer } from "../../components/widget-container/widget-container";
import { Button, ComboBox, TextInput } from "@carbon/react";
import { Search } from "@carbon/icons-react";
import styles from "./search-filters-widget.module.css";
import type { BaseWidgetProps } from "../widget.types";
import type { SearchFiltersWidgetOptions } from "./types";
import { searchFilterResolver } from "../../api/resolver/search-filter-resolver/search-filter-resolver";
import { SearchFilterTypes } from "../../api/resolver/search-filter-resolver/search-filter-types";
import { useState } from "react";
import { useTranslation } from "react-i18next";

type SearchState = {
  mainInput: string;
  additionalInputs: { [key: string]: string };
};

interface SearchFiltersWidgetProps
  extends BaseWidgetProps<SearchFiltersWidgetOptions> {
  options: SearchFiltersWidgetOptions & {
    selectedFilters?: {
      id: string;
      label: string;
      apiKey: string;
      apiSearchKey: string;
    }[];
  };
}

export const SearchFiltersWidget = ({ options }: SearchFiltersWidgetProps) => {
  const { t } = useTranslation();
  const selectedFilterValues = useFilterStore((s) => s.selectedFilterValues);
  const setSelectedFilterValue = useFilterStore(
    (s) => s.setSelectedFilterValue,
  );
  const setInputValue = useFilterStore((s) => s.setInputValue);
  const bumpFiltersVersion = useFilterStore((s) => s.bumpFiltersVersion);

  // Combined state for main and additional search inputs
  const [searchState, setSearchState] = useState<SearchState>({
    mainInput: "",
    additionalInputs: {},
  });

  // Main filter fetch
  const {
    data: filter,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["filter", options.dataTypeId],
    queryFn: () =>
      searchFilterResolver.getFilter(options.dataTypeId as SearchFilterTypes),
    enabled: !!options.dataTypeId,
    retry: 0,
  });

  const { data: searchFilterInfo } = useQuery({
    queryKey: ["searchFilterInfo"],
    queryFn: () => searchFilterResolver.getSearchFilterInfo(),
  });

  // Additional filter fetches
  const additionalFilterQueries = (options.selectedFilters || [])
    .filter((f) => f.id)
    .map((f) => ({
      queryKey: ["filter", f.id],
      queryFn: () => searchFilterResolver.getFilter(f.id as SearchFilterTypes),
      enabled: !!f.id,
    }));
  const additionalFilterResults = useQueries({
    queries: additionalFilterQueries,
  });

  if (!options.dataTypeId) return <WidgetContainer title={""} initializing />;
  if (error)
    return (
      <WidgetContainer
        title={""}
        error={error}
        errorMessage={t("filterWidget.errorLoadingFilters")}
      />
    );
  if (isLoading || !filter) return <WidgetContainer title={""} loading />;

  const filterItems = filter.value.map((filterId: string) => ({
    id: filterId,
    text: filterId
      .split("_")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" "),
  }));

  const toSnakeCase = (str: string) => str.replace(/-/g, "_");
  const mainDropdownKey = toSnakeCase(options.dataTypeId);

  const mainSearchEnabled =
    Array.isArray(searchFilterInfo) &&
    !!searchFilterInfo.find(
      (info) => info.id === options.dataTypeId && info.enableSearch,
    );

  return (
    <WidgetContainer title={options.title || ""}>
      <div className={styles.widgetRow}>
        {/* Main filter */}
        <div className={styles.dropdown}>
          <ComboBox
            titleText={options.dataTypeLabel || "Filter Type"}
            id="filter-type"
            items={filterItems}
            selectedItem={
              filterItems.find(
                (item) => item.id === selectedFilterValues[mainDropdownKey],
              ) || null
            }
            onChange={({ selectedItem }) => {
              if (selectedItem && options.apiKey) {
                setSelectedFilterValue(options.apiKey, selectedItem.id);
                bumpFiltersVersion();
              }
            }}
            itemToString={(item) => (item ? item.text : "")}
          />
          {mainSearchEnabled && (
            <div className={styles.textbox}>
              <TextInput
                labelText={options.dataTypeLabel || "Search"}
                id={`search-input-${options.dataTypeId}`}
                placeholder=""
                value={searchState.mainInput}
                onChange={(e) =>
                  setSearchState((prev) => ({
                    ...prev,
                    mainInput: e.target.value,
                  }))
                }
              />
              <Button
                className={styles.searchBtn}
                kind="primary"
                hasIconOnly
                renderIcon={Search}
                iconDescription="Search"
                onClick={() => {
                  if (options.apiSearchKey) {
                    setInputValue(options.apiSearchKey, searchState.mainInput);
                  }
                  bumpFiltersVersion();
                }}
                tooltipPosition="top"
              />
            </div>
          )}
        </div>
        {/* Additional filters */}
        {(options.selectedFilters || []).map((filterObj, idx) => {
          if (!filterObj.id || !filterObj.label) return null;
          const filterResult = additionalFilterResults[idx];
          const items =
            filterResult && filterResult.data
              ? (filterResult.data.value || []).map((filterId: string) => ({
                  id: filterId,
                  text: filterId
                    .split("_")
                    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
                    .join(" "),
                }))
              : [];
          const ddKey = toSnakeCase(filterObj.id);
          const extraSearchEnabled =
            Array.isArray(searchFilterInfo) &&
            !!searchFilterInfo.find(
              (info) => info.id === filterObj.id && info.enableSearch,
            );
          return (
            <div className={styles.dropdown} key={filterObj.id + idx}>
              <ComboBox
                titleText={filterObj.label}
                id={`additional-filter-${filterObj.id}-${idx}`}
                items={items}
                selectedItem={
                  items.find(
                    (item) => item.id === selectedFilterValues[ddKey],
                  ) || null
                }
                onChange={({ selectedItem }) => {
                  if (selectedItem) {
                    setSelectedFilterValue(filterObj.apiKey, selectedItem.id);
                    bumpFiltersVersion();
                  }
                }}
                itemToString={(item) => (item ? item.text : "")}
              />
              {extraSearchEnabled && (
                <div className={styles.textbox}>
                  <TextInput
                    labelText={filterObj.label}
                    id={`search-input-${filterObj.id}`}
                    placeholder=""
                    value={
                      searchState.additionalInputs[filterObj.apiSearchKey] || ""
                    }
                    onChange={(e) =>
                      setSearchState((prev) => ({
                        ...prev,
                        additionalInputs: {
                          ...prev.additionalInputs,
                          [filterObj.apiSearchKey]: e.target.value,
                        },
                      }))
                    }
                  />
                  <Button
                    className={styles.searchBtn}
                    kind="primary"
                    hasIconOnly
                    renderIcon={Search}
                    iconDescription="Search"
                    onClick={() => {
                      setInputValue(
                        filterObj.apiSearchKey,
                        searchState.additionalInputs[filterObj.apiSearchKey] ||
                          "",
                      );
                      bumpFiltersVersion();
                    }}
                    tooltipPosition="top"
                  />
                </div>
              )}
            </div>
          );
        })}
      </div>
    </WidgetContainer>
  );
};

export default SearchFiltersWidget;
