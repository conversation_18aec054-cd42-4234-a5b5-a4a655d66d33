// --- MOCK ResizeObserver globally for test environment ---
global.ResizeObserver =
  global.ResizeObserver ||
  class {
    observe() {}
    unobserve() {}
    disconnect() {}
  };

import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import "@testing-library/jest-dom";
import { vi, describe, beforeEach, it, expect } from "vitest";

// ---- Define shared mocks at module scope (above component import!) ----
const mockSetSelectedFilterValue = vi.fn();
const mockSetInputValue = vi.fn();
const mockBumpFiltersVersion = vi.fn();

vi.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string) =>
      key === "filterWidget.errorLoadingFilters"
        ? "Failed to load filter data. Please try again later."
        : key,
    i18n: { changeLanguage: () => new Promise(() => {}) },
  }),
}));

const fakeState = {
  selectedFilterValues: {},
  setSelectedFilterValue: mockSetSelectedFilterValue,
  setInputValue: mockSetInputValue,
  bumpFiltersVersion: mockBumpFiltersVersion,
};

vi.mock("@tanstack/react-query", () => ({
  useQuery: vi.fn(),
  useQueries: vi.fn(),
}));

// ---- CRITICAL: Mock store as a selector-style function ----
vi.mock("../../stores/filter-store", () => ({
  useFilterStore: (selector?: any) =>
    typeof selector === "function" ? selector(fakeState) : fakeState,
}));

vi.mock(
  "../../api/resolver/search-filter-resolver/search-filter-resolver",
  () => ({
    searchFilterResolver: {
      getFilter: vi.fn(),
      getSearchFilterInfo: vi.fn(),
    },
  }),
);

import { useQuery, useQueries } from "@tanstack/react-query";
import SearchFiltersWidget from "./search-filters-widget";
import { DatePeriod } from "../../types/date-types";

const baseProps = {
  id: "search-filter",
  type: "search-filter",
  filters: { datePeriodRange: DatePeriod.today },
  options: {
    dataTypeId: "category",
    dataTypeLabel: "Category",
    description: "Test Description",
    enableSearch: true,
    apiKey: "category_key",
    apiSearchKey: "category_search_key",
    selectedFilters: [
      {
        id: "country",
        label: "Country",
        apiKey: "country_key",
        apiSearchKey: "country_search_key",
      },
    ],
  },
};

describe("SearchFiltersWidget", () => {
  beforeEach(() => {
    vi.clearAllMocks();

    (useQuery as any).mockImplementation((params: any) => {
      if (params?.queryKey?.[0] === "filter") {
        return {
          isLoading: false,
          error: null,
          data: { value: ["option_1", "option_2"] },
        };
      }
      if (params?.queryKey?.[0] === "searchFilterInfo") {
        return {
          isLoading: false,
          error: null,
          data: [
            { id: "category", enableSearch: true },
            { id: "country", enableSearch: true },
          ],
        };
      }
      return { isLoading: false, error: null, data: undefined };
    });
    (useQueries as any).mockReturnValue([{ data: { value: ["country_1"] } }]);
  });

  it("renders without crashing", async () => {
    render(<SearchFiltersWidget {...baseProps} />);
    expect(
      await screen.findByRole("combobox", { name: /Category/i }),
    ).toBeInTheDocument();
    expect(
      await screen.findByRole("combobox", { name: /Country/i }),
    ).toBeInTheDocument();
  });

  it("renders error state on query error", () => {
    (useQuery as any).mockImplementationOnce(() => ({
      isLoading: false,
      error: new Error("API error"),
      data: null,
    }));
    const { container } = render(<SearchFiltersWidget {...baseProps} />);
    expect(container.textContent).toContain(
      "Failed to load filter data. Please try again later.",
    );
  });

  it("renders loading/empty state if data missing", () => {
    (useQuery as any).mockImplementationOnce(() => ({
      isLoading: true,
      error: null,
      data: null,
    }));
    render(<SearchFiltersWidget {...baseProps} />);
    expect(screen.getByText(/loading/i)).toBeInTheDocument();
  });

  it("calls setInputValue and bumpFiltersVersion on search button click", async () => {
    render(<SearchFiltersWidget {...baseProps} />);
    const input = document.getElementById(
      "search-input-category",
    ) as HTMLInputElement;
    expect(input).toBeTruthy();
    await userEvent.type(input, "test search");
    const searchBtns = await screen.findAllByRole("button", {
      name: /search/i,
    });
    await userEvent.click(searchBtns[0]); // Main filter's Search button
    expect(mockSetInputValue).toHaveBeenCalledWith(
      "category_search_key",
      "test search",
    );
    expect(mockBumpFiltersVersion).toHaveBeenCalled();
  });

  it("calls setSelectedFilterValue when selecting from dropdown", async () => {
    render(<SearchFiltersWidget {...baseProps} />);
    const combo = await screen.findByRole("combobox", { name: /category/i });
    await userEvent.click(combo);
    const opt = await screen.findByText(/Option 1/i);
    await userEvent.click(opt);
    expect(mockSetSelectedFilterValue).toHaveBeenCalled();
  });
});
