import { DataBase, Add } from "@carbon/icons-react";
import { <PERSON><PERSON><PERSON><PERSON>, Button } from "@carbon/react";
import { useQuery } from "@tanstack/react-query";
import { searchFilterResolver } from "../../api/resolver/search-filter-resolver/search-filter-resolver";
import { OptionsAccordionGroup } from "../../components/options/options-accordion-group/options-accordion-group";
import { OptionsAccordion } from "../../components/options/options-accordion/options-accordion";
import type { BaseWidgetOptionsProps } from "../widget.types";
import type { ComboBoxItem, SearchFiltersWidgetOptions } from "./types";

interface SearchFiltersWidgetOptionsProps
  extends BaseWidgetOptionsProps<SearchFiltersWidgetOptions> {
  options: SearchFiltersWidgetOptions & {
    selectedFilters?: {
      id: string;
      label: string;
      apiKey?: string;
      apiSearchKey?: string;
    }[];
  };
}

export const SearchFiltersWidgetOptionsForm = ({
  options,
  onChange,
}: SearchFiltersWidgetOptionsProps) => {
  const { data: searchFilterInfo } = useQuery({
    queryKey: ["searchFilterInfo"],
    queryFn: () => searchFilterResolver.getSearchFilterInfo(),
  });

  // Add a new empty filter row
  const handleAddFilter = () => {
    onChange({
      ...options,
      selectedFilters: [
        ...(options.selectedFilters || []),
        { id: "", label: "" },
      ],
    });
  };

  // Update a filter at specific index (now checks for duplicates including main filter)
  const handleChangeFilter = (idx: number, selectedItem: ComboBoxItem) => {
    const updatedFilters = options.selectedFilters
      ? [...options.selectedFilters]
      : [];
    // Prevent duplicate selection in other filters or the main Data Type
    if (
      options.dataTypeId === selectedItem.id ||
      updatedFilters.some((f, i) => f.id === selectedItem.id && i !== idx)
    ) {
      // Optionally, show a notification here
      return;
    }
    updatedFilters[idx] = {
      id: selectedItem.id,
      label: selectedItem.text,
      apiKey: selectedItem.apiKey,
      apiSearchKey: selectedItem.apiSearchKey,
    };
    onChange({ ...options, selectedFilters: updatedFilters });
  };

  // Remove a filter by index
  const handleDeleteFilter = (idx: number) => {
    const updatedFilters = [...(options.selectedFilters || [])];
    updatedFilters.splice(idx, 1);
    onChange({ ...options, selectedFilters: updatedFilters });
  };

  // Update main filter (Data Type) while checking for duplicates in additional filters
  const handleMainFilterChange = (
    selectedItem: ComboBoxItem | null | undefined,
  ) => {
    if (!selectedItem || !searchFilterInfo) return;
    // Prevent duplicate selection in additional filters
    if ((options.selectedFilters || []).some((f) => f.id === selectedItem.id)) {
      // Optionally, show a notification here
      return;
    }
    const matched = searchFilterInfo.find(
      (info) => info.id === selectedItem.id,
    );
    onChange({
      ...options,
      dataTypeLabel: selectedItem.text,
      dataTypeId: selectedItem.id,
      apiKey: matched?.apiKey ? matched?.apiKey : "",
      apiSearchKey: matched?.apiSearchKey ? matched?.apiSearchKey : "",
    });
  };

  // Get all selected IDs
  const allSelectedIds = [
    options.dataTypeId,
    ...(options.selectedFilters || []).map((f) => f.id),
  ].filter(Boolean);

  // Count how many unique filter types are left to add
  const availableFilterCount = searchFilterInfo
    ? searchFilterInfo.filter((info) => !allSelectedIds.includes(info.id))
        .length
    : 0;

  return (
    <OptionsAccordion>
      <OptionsAccordionGroup
        id="data"
        title="Data"
        icon={<DataBase size="24" />}
      >
        <ComboBox
          titleText="Data Type"
          id="data-type"
          items={
            searchFilterInfo
              ? [...searchFilterInfo]
                  .sort((a, b) => a.label.localeCompare(b.label))
                  // Exclude any filter already in additional filters
                  .filter(
                    (info) =>
                      !(options.selectedFilters || []).some(
                        (f) => f.id === info.id,
                      ),
                  )
                  .map((info) => ({
                    id: info.id,
                    text: info.label,
                  }))
              : []
          }
          selectedItem={
            options.dataTypeLabel && options.dataTypeId
              ? { text: options.dataTypeLabel, id: options.dataTypeId }
              : null
          }
          onChange={({ selectedItem }) => handleMainFilterChange(selectedItem)}
          itemToString={(item) => (item ? item.text : "")}
          allowCustomValue={false}
        />

        {/* Multiple Filter ComboBoxes */}
        {(options.selectedFilters || []).map((filter, idx) => {
          // Gather IDs of already-selected filters except for current index + main Data Type ID
          const otherSelectedIds = [
            ...(options.selectedFilters || [])
              .map((f, i) => (i !== idx ? f.id : null))
              .filter(Boolean),
            options.dataTypeId,
          ].filter(Boolean);

          return (
            <div
              key={idx}
              style={{ display: "flex", alignItems: "center", marginTop: 8 }}
            >
              <ComboBox
                titleText={idx === 0 ? "Additional Filter" : undefined}
                id={`data-type-filter-${idx}`}
                items={
                  searchFilterInfo
                    ? [...searchFilterInfo]
                        .sort((a, b) => a.label.localeCompare(b.label))
                        // Exclude already-selected filters (other + main filter)
                        .filter((info) => !otherSelectedIds.includes(info.id))
                        .map((info) => ({
                          id: info.id,
                          text: info.label,
                        }))
                    : []
                }
                selectedItem={
                  filter.id && filter.label
                    ? { id: filter.id, text: filter.label }
                    : null
                }
                onChange={({ selectedItem }) => {
                  if (selectedItem) {
                    handleChangeFilter(idx, selectedItem);
                  }
                }}
                itemToString={(item) => (item ? item.text : "")}
                allowCustomValue={false}
              />
              <Button
                kind="ghost"
                hasIconOnly
                size="sm"
                iconDescription="Remove filter"
                style={{ marginLeft: 8 }}
                onClick={() => handleDeleteFilter(idx)}
              >
                ×
              </Button>
            </div>
          );
        })}
        <Button
          kind="ghost"
          hasIconOnly={false}
          renderIcon={Add}
          size="sm"
          iconDescription="Add filter"
          onClick={handleAddFilter}
          style={{ marginTop: 8 }}
          disabled={availableFilterCount === 0}
        >
          Add Filter
        </Button>
      </OptionsAccordionGroup>
    </OptionsAccordion>
  );
};

export default SearchFiltersWidgetOptionsForm;
