import { renderHook, waitFor } from "@testing-library/react";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { AppConfigSettingSource, type AppConfigSetting } from "@ict/sdk/types";
import { ictApi } from "../../api/ict-api";
import { useSourceManagement } from "./use-source-management";

// Mock the ictApi
vi.mock("../../api/ict-api", () => ({
  ictApi: {
    client: {
      queryOptions: vi.fn(),
    },
    queryClient: {
      fetchQuery: vi.fn(),
    },
  },
}));

// Mock the use-config imports
vi.mock("./use-config", () => ({
  updateConfigSetting: vi.fn(),
  deleteConfigSetting: vi.fn(),
}));

describe("useSourceManagement", () => {
  const mockSettings: AppConfigSetting[] = [
    {
      id: "1",
      name: "test-setting",
      value: "facility-value",
      dataType: "string" as const,
      source: AppConfigSettingSource.facility,
      group: "test",
      description: "Test setting",
    },
    {
      id: "2",
      name: "test-setting",
      value: "tenant-value",
      dataType: "string" as const,
      source: AppConfigSettingSource.tenant,
      group: "test",
      description: "Test setting",
    },
  ];

  beforeEach(() => {
    vi.resetAllMocks();
  });

  it("should initialize with default state", () => {
    ictApi.queryClient.fetchQuery = vi.fn().mockResolvedValue([]);

    const { result } = renderHook(() => useSourceManagement(""));

    expect(result.current.allSources).toEqual([]);
    expect(result.current.activeSetting).toBeNull();
    expect(result.current.activeSource).toBeNull();
    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toBeNull();
    expect(result.current.hasUnsavedChanges).toBe(false);
  });

  it("should load all sources and determine active setting", async () => {
    ictApi.client.queryOptions = vi.fn().mockReturnValue({
      queryKey: ["get", "/config/settings"],
    });
    ictApi.queryClient.fetchQuery = vi.fn().mockResolvedValue(mockSettings);

    const { result } = renderHook(() => useSourceManagement("test-setting"));

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.allSources).toHaveLength(3);
    expect(result.current.activeSetting).toEqual(mockSettings[0]); // facility has higher priority
    expect(result.current.activeSource).toBe("facility");
  });

  it("should handle 404 error gracefully", async () => {
    const error404 = new Error("Not found");
    (error404 as any).status = 404;

    ictApi.client.queryOptions = vi.fn().mockReturnValue({
      queryKey: ["get", "/config/settings"],
    });
    ictApi.queryClient.fetchQuery = vi.fn().mockRejectedValue(error404);

    const { result } = renderHook(() => useSourceManagement("test-setting"));

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.allSources).toHaveLength(3);
    expect(result.current.allSources.every((s) => !s.exists)).toBe(true);
    expect(result.current.activeSetting).toBeNull();
    expect(result.current.error).toBeNull();
  });

  it("should switch to existing source", async () => {
    ictApi.client.queryOptions = vi.fn().mockReturnValue({
      queryKey: ["get", "/config/settings"],
    });
    ictApi.queryClient.fetchQuery = vi.fn().mockResolvedValue(mockSettings);

    const { result } = renderHook(() => useSourceManagement("test-setting"));

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    await waitFor(() => {
      // Switch to tenant source
      result.current.switchToSource("tenant");
      expect(result.current.activeSource).toBe("tenant");
    });

    expect(result.current.activeSetting).toEqual(mockSettings[1]);
  });
});
