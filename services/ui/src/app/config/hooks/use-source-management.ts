import type { AppConfigSetting } from "@ict/sdk/types";
import { useEffect, useState } from "react";
import { ictApi } from "../../api/ict-api";
import {
  deleteConfigSetting,
  updateConfigSetting,
  type UpdateSettingSource,
} from "./use-config";
import { components } from "../../../../../../libs/shared/ict-sdk/generated/openapi-react-query";

export type SourceLevel = components["schemas"]["AppConfigSettingSource"];

export interface SourceLevelInfo {
  level: SourceLevel;
  exists: boolean;
  setting?: AppConfigSetting;
  isActive: boolean;
}

export interface SourceManagementState {
  allSources: SourceLevelInfo[];
  activeSetting: AppConfigSetting | null;
  activeSource: SourceLevel | null;
  isLoading: boolean;
  error: string | null;
  hasUnsavedChanges: boolean;
}

/**
 * Hook to manage configuration source levels and hierarchy
 */
export const useSourceManagement = (
  settingName: string,
  onShowToast?: (message: string, title?: string) => void,
  onNotFound?: () => void,
) => {
  const [state, setState] = useState<SourceManagementState>({
    allSources: [],
    activeSetting: null,
    activeSource: null,
    isLoading: false,
    error: null,
    hasUnsavedChanges: false,
  });

  // Load all source levels for the setting
  const loadAllSources = async () => {
    if (!settingName) return;

    setState((prev) => ({ ...prev, isLoading: true, error: null }));

    try {
      const response = await ictApi.queryClient.fetchQuery(
        ictApi.client.queryOptions("get", "/config/settings", {
          params: {
            query: {
              settingName: settingName,
              allStoredValues: true,
            },
          },
        }),
      );

      const settings = (response as AppConfigSetting[]) || [];

      // Create source level info for all possible levels
      const sourceHierarchy: SourceLevel[] = ["facility", "tenant", "default"];
      const allSources: SourceLevelInfo[] = sourceHierarchy.map((level) => {
        const setting = settings.find((s) => s.source === level);
        return {
          level,
          exists: !!setting,
          setting,
          isActive: false, // Will be determined below
        };
      });

      // Determine active source (highest priority that exists)
      const activeSetting = allSources.find((s) => s.exists)?.setting || null;
      const activeSource = (activeSetting?.source as SourceLevel) || null;

      // Mark the active source
      allSources.forEach((source) => {
        source.isActive = source.level === activeSource;
      });

      setState((prev) => ({
        ...prev,
        allSources,
        activeSetting,
        activeSource,
        isLoading: false,
      }));
    } catch (err) {
      // If we get a 404, it means no settings exist for this setting name
      // This is a valid state (empty sources) rather than an error
      if (
        err &&
        typeof err === "object" &&
        "status" in err &&
        err.status === 404
      ) {
        // If onNotFound callback is provided, call it to handle navigation
        if (onNotFound) {
          onNotFound();
          return;
        }

        // Create empty source level info for all possible levels
        const sourceHierarchy: SourceLevel[] = [
          "facility",
          "tenant",
          "default",
        ];
        const allSources: SourceLevelInfo[] = sourceHierarchy.map((level) => ({
          level,
          exists: false,
          setting: undefined,
          isActive: false,
        }));

        setState((prev) => ({
          ...prev,
          allSources,
          activeSetting: null,
          activeSource: null,
          isLoading: false,
          error: null, // Clear error for 404 case
        }));
      } else {
        setState((prev) => ({
          ...prev,
          error: err instanceof Error ? err.message : "Failed to load settings",
          isLoading: false,
        }));
      }
    }
  };

  // Switch to a different source level
  const switchToSource = (targetSource: SourceLevel) => {
    const targetSourceInfo = state.allSources.find(
      (s) => s.level === targetSource,
    );

    if (!targetSourceInfo) return;

    if (targetSourceInfo.exists) {
      // Source exists, just switch to it
      setState((prev) => ({
        ...prev,
        activeSetting: targetSourceInfo.setting!,
        activeSource: targetSource,
        allSources: prev.allSources.map((s) => ({
          ...s,
          isActive: s.level === targetSource,
        })),
      }));
    } else {
      // Source doesn't exist, copy from next lowest level
      const sourceHierarchy: SourceLevel[] = ["facility", "tenant", "default"];
      const targetIndex = sourceHierarchy.indexOf(targetSource);

      let sourceSettingToCopy: AppConfigSetting | null = null;
      let fromSource: SourceLevel | null = null;

      // Find the next lowest level that exists
      for (let i = targetIndex + 1; i < sourceHierarchy.length; i++) {
        const lowerLevel = sourceHierarchy[i];
        const lowerLevelInfo = state.allSources.find(
          (s) => s.level === lowerLevel,
        );
        if (lowerLevelInfo?.exists) {
          sourceSettingToCopy = lowerLevelInfo.setting!;
          fromSource = lowerLevel;
          break;
        }
      }

      if (sourceSettingToCopy) {
        // Create a copy with the new source level
        const { id: _id, ...settingWithoutId } = sourceSettingToCopy;
        const copiedSetting = {
          ...settingWithoutId,
          source: targetSource as UpdateSettingSource,
          id: undefined, // Will be generated when saved
        } as unknown as AppConfigSetting;

        setState((prev) => ({
          ...prev,
          activeSetting: copiedSetting,
          activeSource: targetSource,
          hasUnsavedChanges: true,
          allSources: prev.allSources.map((s) => ({
            ...s,
            isActive: s.level === targetSource,
            // Mark the target as existing since we're creating it
            exists: s.level === targetSource ? true : s.exists,
            setting: s.level === targetSource ? copiedSetting : s.setting,
          })),
        }));

        // Show toast notification
        if (onShowToast && fromSource) {
          const sourceLabels = {
            default: "Default",
            tenant: "Tenant",
            facility: "Facility",
            user: "User",
          };
          onShowToast(
            `Configuration copied from ${sourceLabels[fromSource]} to ${sourceLabels[targetSource]}. Save to persist this level.`,
            "Configuration Copied",
          );
        }
      }
    }
  };

  // Save the current setting
  const saveSetting = async (setting: AppConfigSetting) => {
    try {
      await updateConfigSetting(setting, setting.source as UpdateSettingSource);

      setState((prev) => ({
        ...prev,
        hasUnsavedChanges: false,
      }));

      // Reload all sources to get the latest state
      await loadAllSources();
    } catch (err) {
      setState((prev) => ({
        ...prev,
        error: err instanceof Error ? err.message : "Failed to save setting",
      }));
    }
  };

  // Update the local setting (for draft changes)
  const updateLocalSetting = (updatedSetting: AppConfigSetting) => {
    setState((prev) => ({
      ...prev,
      activeSetting: updatedSetting,
      hasUnsavedChanges: true,
    }));
  };

  // Delete a source level
  const deleteSourceLevel = async (
    sourceLevel: SourceLevel,
    setting: AppConfigSetting,
  ) => {
    try {
      await deleteConfigSetting({ ...setting, source: sourceLevel });

      // Reload all sources to get the latest state
      await loadAllSources();
    } catch (err) {
      setState((prev) => ({
        ...prev,
        error:
          err instanceof Error ? err.message : "Failed to delete source level",
      }));
      throw err;
    }
  };

  // Load sources when setting name changes
  useEffect(() => {
    if (settingName) {
      loadAllSources();
    }
  }, [settingName]);

  return {
    ...state,
    switchToSource,
    saveSetting,
    updateLocalSetting,
    deleteSourceLevel,
    refresh: loadAllSources,
  };
};
