import { DateTime } from "luxon";
import * as i18n from "i18next";
import { DatePeriod } from "../types/date-types";
import { DateRange } from "../types/date-types";

/**
 * Utility functions for date operations using Luxon
 */

/**
 * Converts a Date or DateTime object to a DateTime with the specified timezone
 * @param date The date to convert
 * @param timezone The timezone to set (e.g., 'America/New_York')
 * @returns Luxon DateTime object in the specified timezone
 */
export function toSiteDate(
  date: Date | DateTime | string,
  timezone: string,
): DateTime {
  // Parse date strings directly in the site timezone to avoid browser UTC
  // conversions that would shift dates in some timezones.
  if (typeof date === "string") {
    if (/^\d+$/.test(date)) {
      return DateTime.invalid("Invalid Date");
    }
    let dt = DateTime.fromISO(date, { zone: timezone });
    if (!dt.isValid) {
      // Try common BigQuery string format: "2025-05-05 04:00:00 UTC"
      dt = DateTime.fromFormat(date, "yyyy-MM-dd HH:mm:ss 'UTC'", {
        zone: "utc",
      }).setZone(timezone);
    }
    if (!dt.isValid) {
      // Try generic SQL timestamp without timezone
      dt = DateTime.fromSQL(date, { zone: timezone });
    }
    return dt;
  }

  return date instanceof DateTime
    ? date.setZone(timezone)
    : DateTime.fromJSDate(date).setZone(timezone);
}

/**
 * Options for formatting time values
 */
interface FormatTimeOptions {
  /** Format style: 'compact' (e.g., "2hr5m") or 'verbose' (e.g., "2 hrs 5 mins") */
  style?: "compact" | "verbose";
  /** Precision: 'integer' or 'decimal' for sub-second precision */
  precision?: "integer" | "decimal";
  /** Maximum number of time units to display (2, 3, or 4) */
  maxUnits?: 2 | 3 | 4;
  /** Whether to include days in the output */
  includeDays?: boolean;
  /** Only show seconds if total duration is less than this threshold in minutes */
  secondsThreshold?: number;
}

/**
 * Unified function to format time values into human-readable strings
 * @param value The time value to format
 * @param inputUnit The unit of the input value
 * @param options Formatting options
 * @returns Formatted string based on the specified options
 */
export function formatTime(
  value: number,
  inputUnit: "milliseconds" | "seconds" | "minutes" | "hours",
  options: FormatTimeOptions = {},
): string {
  const t = i18n.t;
  const {
    style = "verbose",
    precision = "integer",
    maxUnits = 4,
    includeDays = false,
    secondsThreshold = 10,
  } = options;

  if (Number.isNaN(value) || value < 0) {
    const defaultUnit =
      inputUnit === "milliseconds" ? "s" : inputUnit.charAt(0);
    return style === "compact"
      ? `0${defaultUnit}`
      : t("formatTime.nan", "0 {{unit}}", { value: 0, unit: inputUnit });
  }

  // Convert to seconds for consistent handling
  let totalSecondsFloat: number;
  switch (inputUnit) {
    case "milliseconds":
      totalSecondsFloat = value / 1000;
      break;
    case "minutes":
      totalSecondsFloat = value * 60;
      break;
    case "hours":
      totalSecondsFloat = value * 3600;
      break;
    case "seconds":
    default:
      totalSecondsFloat = value;
      break;
  }

  const totalSeconds = Math.floor(totalSecondsFloat);

  // Handle sub-second durations for decimal precision
  if (precision === "decimal" && totalSeconds === 0 && value > 0) {
    const seconds = totalSecondsFloat.toFixed(1);
    return style === "compact"
      ? t("formatDuration.subSecond", "{{seconds}}s", { seconds })
      : t("formatTime.hasSeconds", "{{seconds}} secs", { seconds });
  }

  // For decimal precision with durations less than 60 seconds
  if (
    precision === "decimal" &&
    totalSeconds < 60 &&
    totalSecondsFloat % 1 !== 0
  ) {
    const seconds = totalSecondsFloat.toFixed(1);
    return style === "compact"
      ? t("formatDuration.subSecond", "{{seconds}}s", { seconds })
      : t("formatTime.hasSeconds", "{{seconds}} secs", { seconds });
  }

  // Calculate time units
  const days = includeDays ? Math.floor(totalSeconds / 86400) : 0;
  const hours = Math.floor(
    (totalSeconds % (includeDays ? 86400 : Infinity)) / 3600,
  );
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = totalSeconds % 60;

  const units: Array<{
    value: number;
    key: string;
    compact: string;
    verbose: string;
  }> = [];

  if (days > 0) {
    units.push({
      value: days,
      key: "days",
      compact: " days",
      verbose: days === 1 ? "day" : "days",
    });
  }

  if (hours > 0) {
    units.push({
      value: hours,
      key: "hours",
      compact: "hr",
      verbose: hours === 1 ? "hr" : "hrs",
    });
  }

  if (minutes > 0) {
    units.push({
      value: minutes,
      key: "minutes",
      compact: "m",
      verbose: minutes === 1 ? "min" : "mins",
    });
  }

  // Apply seconds threshold logic
  const totalMinutes = totalSecondsFloat / 60;
  const shouldShowSeconds =
    seconds > 0 &&
    (style === "compact" ||
      (style === "verbose" && totalMinutes < secondsThreshold));

  if (shouldShowSeconds) {
    units.push({
      value: seconds,
      key: "seconds",
      compact: "s",
      verbose: seconds === 1 ? "sec" : "secs",
    });
  }

  // Limit to maxUnits
  const displayUnits = units.slice(0, maxUnits);

  // Handle case where no units to display
  if (displayUnits.length === 0) {
    return style === "compact"
      ? "0s"
      : t("formatTime.hasSeconds", "{{seconds}} secs", { seconds: 0 });
  }

  // Format based on style
  if (style === "compact") {
    return displayUnits.map((unit) => `${unit.value}${unit.compact}`).join(" ");
  } else {
    // Verbose style with proper i18n keys
    if (displayUnits.length === 1) {
      const unit = displayUnits[0];
      if (unit.key === "days") {
        return t("formatDuration.daysOnly", "{{days}} days", {
          days: unit.value,
        });
      } else if (unit.key === "hours") {
        return t("formatTime.hasHoursNoMinutes", "{{hours}} hrs", {
          hours: unit.value,
        });
      } else if (unit.key === "minutes") {
        return t("formatTime.hasMinutesNoSeconds", "{{minutes}} mins", {
          minutes: unit.value,
        });
      } else {
        return t("formatTime.hasSeconds", "{{seconds}} secs", {
          seconds: unit.value,
        });
      }
    } else if (displayUnits.length === 2) {
      const [first, second] = displayUnits;
      if (first.key === "days" && second.key === "hours") {
        return t("formatDuration.daysHours", "{{days}} days {{hours}}hr", {
          days: first.value,
          hours: second.value,
        });
      } else if (first.key === "hours" && second.key === "minutes") {
        return t("formatTime.hasHours", "{{hours}} hrs {{minutes}} mins", {
          hours: first.value,
          minutes: second.value,
        });
      } else if (first.key === "minutes" && second.key === "seconds") {
        return t(
          "formatTime.hasMinutes",
          "{{minutes}} mins {{seconds}} seconds",
          { minutes: first.value, seconds: second.value },
        );
      }
    }

    // Fallback for other combinations
    return displayUnits
      .map((unit) => `${unit.value} ${unit.verbose}`)
      .join(" ");
  }
}

/**
 * Formats a time value into a human-readable string based on the unit
 * @param value The time value to format
 * @param unit The unit of the time value ('seconds', 'minutes', or 'hours')
 * @returns Formatted string (e.g., "45 mins", "2 hrs 5 mins", "1 min 30 secs")
 */
export function formatTimeValue(value: number, unit: string): string {
  return formatTime(value, unit as "seconds" | "minutes" | "hours", {
    style: "verbose",
    precision: "integer",
    maxUnits: 2,
    includeDays: false,
    secondsThreshold: 10,
  });
}

/**
 * Formats a number of minutes into a human-readable string
 * @param minutes The number of minutes to format
 * @returns Formatted string (e.g., "45 mins", "2 hrs 5 mins", "2 hrs")
 */
export function formatMinutes(minutes: number): string {
  return formatTime(minutes, "minutes", {
    style: "verbose",
    precision: "integer",
    maxUnits: 2,
    includeDays: false,
    secondsThreshold: 10,
  });
}

/**
 * Formats a duration in milliseconds into a human-readable string
 * @param milliseconds The duration in milliseconds to format
 * @returns Formatted string (e.g., "6.3s", "4m10s", "4hr3m", "3 days 2hr")
 */
export function formatDuration(milliseconds: number): string {
  return formatTime(milliseconds, "milliseconds", {
    style: "compact",
    precision: "decimal",
    maxUnits: 2,
    includeDays: true,
    secondsThreshold: Infinity, // Always show seconds in compact format
  });
}

/**
 * Formats an ISO string to a specified human readable date string.
 * @param isoString ISO 8601 datetime string
 * @param format format string or Intl.DateTimeFormatOptions
 * @param timezone timezone to use, defaults to browser timezone
 * @returns formatted date string
 */
export function formatISOTime(
  isoString: string,
  format: string | Intl.DateTimeFormatOptions,
  timezone = Intl.DateTimeFormat().resolvedOptions().timeZone,
): string {
  const siteDate = DateTime.fromISO(isoString).setZone(timezone);
  if (!siteDate.isValid) {
    return "Invalid Date";
  }
  if (typeof format === "string") {
    return siteDate.toFormat(format);
  }
  return siteDate.toLocaleString(format);
}

/**
 * Formats a date relative to now (e.g., "2 hours ago", "Just now")
 * @param date The date to format
 * @returns Relative time string following codebase patterns
 */
export function formatRelativeTime(date: Date): string {
  const diffMs = Date.now() - date.getTime();

  if (diffMs < 60000) return "Just now"; // < 1 minute

  const diffMinutes = Math.floor(diffMs / 60000);
  if (diffMinutes < 60)
    return `${diffMinutes} minute${diffMinutes !== 1 ? "s" : ""} ago`;

  const diffHours = Math.floor(diffMinutes / 60);
  if (diffHours < 24)
    return `${diffHours} hour${diffHours !== 1 ? "s" : ""} ago`;

  const diffDays = Math.floor(diffHours / 24);
  if (diffDays < 7) return `${diffDays} day${diffDays !== 1 ? "s" : ""} ago`;

  return date.toLocaleString();
}

/**
 * Formats the UTC timestamp to a human readable date and time format
 * This example is the preferred ISO standard.
 * example: 2024-06-05T09:00:00.000Z -> 2024-06-05 9:00:00 AM
 */
export function formatISOTimeToReadableDateTime(
  isoString: string,
  timezone?: string,
): string {
  return formatISOTime(isoString, "yyyy-LL-dd h:mm:ss a", timezone);
}

/**
 * Gets the start date for a specific date period
 * @param datePeriod The date period enum value
 * @param timezone The timezone to use for the date period
 * @returns Luxon DateTime object representing the start of the period
 * @throws Error if an invalid date period is provided
 */
export function getStartDateOfPeriod(
  datePeriod: DatePeriod,
  timezone: string,
): DateTime {
  const now = DateTime.now().setZone(timezone);

  switch (datePeriod) {
    case DatePeriod.today:
      return now.startOf("day");
    case DatePeriod.yesterday:
      return now.minus({ days: 1 }).startOf("day");
    case DatePeriod.thisWeek:
      return now.startOf("week");
    case DatePeriod.lastWeek:
      return now.minus({ weeks: 1 }).startOf("week");
    case DatePeriod.thisMonth:
      return now.startOf("month");
    case DatePeriod.lastMonth:
      return now.minus({ months: 1 }).startOf("month");
    case DatePeriod.thisYear:
      return now.startOf("year");
    case DatePeriod.last7days:
      return now.minus({ days: 7 }).startOf("day");
    case DatePeriod.last14days:
      return now.minus({ days: 14 }).startOf("day");
    case DatePeriod.last30days:
      return now.minus({ days: 30 }).startOf("day");
    case DatePeriod.last60days:
      return now.minus({ days: 60 }).startOf("day");
    default:
      throw new Error(`Invalid date period: ${datePeriod}`);
  }
}

/**
 * Gets the end date for a specific date period
 * @param datePeriod The date period enum value
 * @param timezone The timezone to use for the date period
 * @returns Luxon DateTime object representing the end of the period
 * @throws Error if an invalid date period is provided
 */
export function getEndDateOfPeriod(
  datePeriod: DatePeriod,
  timezone: string,
): DateTime {
  const now = DateTime.now().setZone(timezone);

  switch (datePeriod) {
    case DatePeriod.today:
      return now.endOf("day");
    case DatePeriod.yesterday:
      return now.minus({ days: 1 }).endOf("day");
    case DatePeriod.thisWeek:
      return now;
    case DatePeriod.lastWeek:
      return now.minus({ weeks: 1 }).endOf("week");
    case DatePeriod.thisMonth:
      return now;
    case DatePeriod.lastMonth:
      return now.minus({ months: 1 }).endOf("month");
    case DatePeriod.thisYear:
    case DatePeriod.last7days:
    case DatePeriod.last14days:
    case DatePeriod.last30days:
    case DatePeriod.last60days:
      return now;
    default:
      throw new Error(`Invalid date period: ${datePeriod}`);
  }
}

/**
 * Determines the appropriate date format based on the range between dates
 * @param dateRange The date range to analyze
 * @returns DateTimeFormatOptions appropriate for the range:
 *   - TIME_SIMPLE for ranges less than 2 days
 *   - DATE_SHORT for ranges less than 40 days
 *   - Month/day format for longer ranges
 */
export function dateRangeToDateFormat(
  dateRange: DateRange,
): Intl.DateTimeFormatOptions {
  // Calculate the difference in days
  const diffInDays = Math.floor(
    DateTime.fromJSDate(dateRange.endDate).diff(
      DateTime.fromJSDate(dateRange.startDate),
      "days",
    ).days,
  );

  // Return appropriate format based on the date range
  if (diffInDays < 2) {
    // For very short ranges (less than 2 days), use time format
    return DateTime.TIME_SIMPLE;
  }
  if (diffInDays < 40) {
    // For medium ranges (less than 40 days), use short date format
    return DateTime.DATE_SHORT;
  }

  // For longer ranges, use month and day format
  return { month: "short", day: "numeric" };
}
