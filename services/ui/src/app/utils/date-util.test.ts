import { DateTime } from "luxon";
import { beforeAll, beforeEach, describe, expect, it, vi } from "vitest";
import { DatePeriod } from "../types";
import {
  dateRangeToDateFormat,
  formatDuration,
  formatMinutes,
  formatRelativeTime,
  formatTimeValue,
  getEndDateOfPeriod,
  getStartDateOfPeriod,
  toSiteDate,
} from "./date-util";
import { initTestI18n } from "../../test-utils";

describe("date-util", () => {
  const timezone = "America/New_York";

  // Initialize i18n before all tests - this is only needed for non-React tests
  beforeAll(async () => {
    await initTestI18n();
  });

  // Mock the current date for consistent testing
  beforeEach(() => {
    // Mock DateTime.now() to return a fixed date (2023-05-15)
    vi.spyOn(DateTime, "now").mockImplementation(
      () =>
        DateTime.fromObject(
          {
            year: 2023,
            month: 5,
            day: 15,
          },
          { zone: timezone },
        ) as DateTime<true>,
    );
  });

  describe("getStartDateOfPeriod", () => {
    it("should return correct start date for today", () => {
      const result = getStartDateOfPeriod(DatePeriod.today, timezone);
      expect(result.toISODate()).toBe("2023-05-15");
      expect(result.hour).toBe(0);
      expect(result.minute).toBe(0);
    });

    it("should return correct start date for thisWeek", () => {
      const result = getStartDateOfPeriod(DatePeriod.thisWeek, timezone);
      expect(result.weekday).toBe(1); // Monday
    });

    it("should throw error for invalid period", () => {
      expect(() =>
        getStartDateOfPeriod("invalid" as DatePeriod, timezone),
      ).toThrow();
    });
  });

  describe("getEndDateOfPeriod", () => {
    it("should return correct end date for today", () => {
      const result = getEndDateOfPeriod(DatePeriod.today, timezone);
      expect(result.toISODate()).toBe("2023-05-15");
      expect(result.hour).toBe(23);
      expect(result.minute).toBe(59);
    });

    it("should return current time for last7days", () => {
      const result = getEndDateOfPeriod(DatePeriod.last7days, timezone);
      expect(result.toISODate()).toBe("2023-05-15");
    });
  });

  describe("toSiteDate", () => {
    it("should convert JS Date to DateTime with timezone", () => {
      const date = new Date(Date.UTC(2023, 4, 15, 12, 0, 0)); // Using noon UTC to avoid date boundary issues
      const result = toSiteDate(date, "America/New_York");

      expect(result.zoneName).toBe("America/New_York");
      expect(result.isValid).toBe(true);
    });

    it("should set timezone for DateTime object", () => {
      const dateTime = DateTime.fromObject({ year: 2023, month: 5, day: 15 });
      const result = toSiteDate(dateTime, "Europe/London");

      expect(result.zoneName).toBe("Europe/London");
    });
  });

  describe("formatMinutes", () => {
    it("should format minutes less than 60", () => {
      expect(formatMinutes(45)).toBe("45 mins");
    });

    it("should format hours and minutes", () => {
      expect(formatMinutes(125)).toBe("2 hrs 5 mins");
    });

    it("should format whole hours without minutes", () => {
      expect(formatMinutes(120)).toBe("2 hrs");
    });

    it("should handle invalid inputs", () => {
      expect(formatMinutes(Number.NaN)).toBe("0 minutes");
      expect(formatMinutes(-10)).toBe("0 minutes");
    });
  });

  describe("dateRangeToDateFormat", () => {
    it("should return time format for short ranges", () => {
      const today = new Date();
      const tomorrow = new Date(today);
      tomorrow.setDate(today.getDate() + 1);

      const result = dateRangeToDateFormat({
        startDate: today,
        endDate: tomorrow,
      });

      expect(result).toEqual(DateTime.TIME_SIMPLE);
    });

    it("should return date format for medium ranges", () => {
      const start = new Date(2023, 4, 1);
      const end = new Date(2023, 4, 20);

      const result = dateRangeToDateFormat({
        startDate: start,
        endDate: end,
      });

      expect(result).toEqual(DateTime.DATE_SHORT);
    });

    it("should return month/day format for long ranges", () => {
      const start = new Date(2023, 1, 1);
      const end = new Date(2023, 4, 15);

      const result = dateRangeToDateFormat({
        startDate: start,
        endDate: end,
      });

      expect(result).toEqual({ month: "short", day: "numeric" });
    });
  });

  describe("formatTimeValue", () => {
    it("should format seconds correctly", () => {
      expect(formatTimeValue(30, "seconds")).toBe("30 secs");
      expect(formatTimeValue(90, "seconds")).toBe("1 mins 30 seconds");
      expect(formatTimeValue(3600, "seconds")).toBe("1 hrs");
      expect(formatTimeValue(3661, "seconds")).toBe("1 hrs 1 mins");
      expect(formatTimeValue(7325, "seconds")).toBe("2 hrs 2 mins");
    });

    it("should format minutes correctly", () => {
      expect(formatTimeValue(5, "minutes")).toBe("5 mins");
      expect(formatTimeValue(60, "minutes")).toBe("1 hrs");
      expect(formatTimeValue(65, "minutes")).toBe("1 hrs 5 mins");
      expect(formatTimeValue(125, "minutes")).toBe("2 hrs 5 mins");
    });

    it("should format hours correctly", () => {
      expect(formatTimeValue(1, "hours")).toBe("1 hrs");
      expect(formatTimeValue(2.5, "hours")).toBe("2 hrs 30 mins");
      expect(formatTimeValue(4.75, "hours")).toBe("4 hrs 45 mins");
    });

    it("should handle edge cases", () => {
      expect(formatTimeValue(0, "seconds")).toBe("0 secs");
      expect(formatTimeValue(Number.NaN, "minutes")).toBe("0 minutes");
      expect(formatTimeValue(-10, "hours")).toBe("0 hours");
    });
  });

  describe("formatDuration", () => {
    it("should format sub-second durations with decimals", () => {
      expect(formatDuration(300)).toBe("0.3s");
      expect(formatDuration(650)).toBe("0.7s");
      expect(formatDuration(6300)).toBe("6.3s");
    });

    it("should format seconds only", () => {
      expect(formatDuration(4000)).toBe("4s");
      expect(formatDuration(30000)).toBe("30s");
      expect(formatDuration(59000)).toBe("59s");
    });

    it("should format minutes and seconds", () => {
      expect(formatDuration(60000)).toBe("1m");
      expect(formatDuration(70000)).toBe("1m 10s");
      expect(formatDuration(250000)).toBe("4m 10s");
      expect(formatDuration(3540000)).toBe("59m");
    });

    it("should format hours and minutes", () => {
      expect(formatDuration(3600000)).toBe("1hr");
      expect(formatDuration(3780000)).toBe("1hr 3m");
      expect(formatDuration(14460000)).toBe("4hr 1m");
      expect(formatDuration(86340000)).toBe("23hr 59m");
    });

    it("should format days and hours", () => {
      expect(formatDuration(86400000)).toBe("1 days");
      expect(formatDuration(90000000)).toBe("1 days 1hr");
      expect(formatDuration(273600000)).toBe("3 days 4hr");
      expect(formatDuration(259200000)).toBe("3 days");
    });

    it("should handle edge cases", () => {
      expect(formatDuration(0)).toBe("0s");
      expect(formatDuration(Number.NaN)).toBe("0s");
      expect(formatDuration(-1000)).toBe("0s");
    });

    it("should handle exact millisecond boundaries", () => {
      expect(formatDuration(999)).toBe("1.0s");
      expect(formatDuration(1000)).toBe("1s");
      expect(formatDuration(59999)).toBe("60.0s");
    });
  });
});
