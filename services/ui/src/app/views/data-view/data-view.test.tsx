import { vi } from "vitest";
import { render, screen } from "../../../test-utils";
import { DataView } from "./data-view";

// Mock React Router
vi.mock("react-router", () => ({
  Routes: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="routes">{children}</div>
  ),
  Route: ({ element }: { element: React.ReactNode }) => (
    <div data-testid="route">{element}</div>
  ),
}));

// Mock view components
vi.mock("./components/data-view-table/data-view-table", () => ({
  DataViewTable: () => <div data-testid="data-view-table">DataViewTable</div>,
}));

vi.mock("./components/data-point-view/data-point-view", () => ({
  DataPointView: () => <div data-testid="data-point-view">DataPointView</div>,
}));

vi.mock("./components/category-series-view/category-series-view", () => ({
  CategorySeriesView: () => (
    <div data-testid="category-series-view">CategorySeriesView</div>
  ),
}));

vi.mock("./components/time-series-view/time-series-view", () => ({
  TimeSeriesView: () => (
    <div data-testid="time-series-view">TimeSeriesView</div>
  ),
}));

vi.mock("./components/tabular-view/tabular-view", () => ({
  TabularView: () => <div data-testid="tabular-view">TabularView</div>,
}));

vi.mock("./components/filter-view/filter-view", () => ({
  FilterView: () => <div data-testid="filter-view">FilterView</div>,
}));

describe("DataView", () => {
  const mockProps = {
    // BaseViewProps - empty object since the component doesn't use props
  };

  it("renders without crashing", () => {
    render(<DataView {...(mockProps as any)} />);

    expect(screen.getByTestId("routes")).toBeInTheDocument();
  });

  it("renders all route components", () => {
    render(<DataView {...(mockProps as any)} />);

    // Check that all view components are rendered (since our mock Route renders all elements)
    expect(screen.getByTestId("data-view-table")).toBeInTheDocument();
    expect(screen.getByTestId("data-point-view")).toBeInTheDocument();
    expect(screen.getByTestId("category-series-view")).toBeInTheDocument();
    expect(screen.getByTestId("time-series-view")).toBeInTheDocument();
    expect(screen.getByTestId("tabular-view")).toBeInTheDocument();
    expect(screen.getByTestId("filter-view")).toBeInTheDocument();
  });
});
