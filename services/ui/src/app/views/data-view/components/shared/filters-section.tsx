import { Button, FilterableMultiSelect } from "@carbon/react";
import { <PERSON><PERSON><PERSON>, TrashCan } from "@carbon/react/icons";
import { OptionsAccordionGroup } from "../../../../components/options/options-accordion-group/options-accordion-group";
import { BaseDataValue } from "./base-options";
import styles from "./filters-section.module.scss";

interface FiltersSectionProps {
  draftValue: BaseDataValue;
  updateDraftValue: (updates: Partial<BaseDataValue>) => void;
  availableFilterItems: Array<{ id: string; text: string }>;
}

export const FiltersSection = ({
  draftValue,
  updateDraftValue,
  availableFilterItems,
}: FiltersSectionProps) => {
  const handleFiltersChange = ({
    selectedItems,
  }: {
    selectedItems: Array<{ id: string; text: string }>;
  }) => {
    const filterNames = selectedItems.map((item) => item.id);
    updateDraftValue({ filters: filterNames });
  };

  const handleRemoveFilter = (filterName: string) => {
    const updatedFilters = (draftValue.filters || []).filter(
      (name) => name !== filterName,
    );
    updateDraftValue({ filters: updatedFilters });
  };

  return (
    <OptionsAccordionGroup
      id="filters"
      title="Filters"
      defaultOpen={false}
      icon={<Settings size={20} />}
    >
      <FilterableMultiSelect
        id="filter-selector"
        titleText="Filters"
        placeholder="Select filters..."
        items={availableFilterItems}
        selectedItems={availableFilterItems.filter((item) =>
          (draftValue.filters || []).includes(item.id),
        )}
        onChange={handleFiltersChange}
        itemToString={(item) =>
          item ? `@${item.text.replace(/-/g, "_")}` : ""
        }
        helperText="Select filter settings to inject into your query. Dashes in filter names become underscores (e.g., my-filter becomes @my_filter)"
        selectionFeedback="top-after-reopen"
      />

      {(draftValue.filters || []).length > 0 && (
        <div className={styles.selectedFiltersContainer}>
          <h4 className={styles.selectedFiltersTitle}>Selected Filters</h4>
          {(draftValue.filters || []).map((filterName) => (
            <div key={filterName} className={styles.filterItem}>
              <code>@{filterName.replace(/-/g, "_")}</code>
              <Button
                kind="danger--ghost"
                size="sm"
                hasIconOnly
                renderIcon={TrashCan}
                iconDescription="Remove filter"
                onClick={() => handleRemoveFilter(filterName)}
              />
            </div>
          ))}
        </div>
      )}
    </OptionsAccordionGroup>
  );
};
