import { Button, FilterableMultiSelect } from "@carbon/react";
import { <PERSON><PERSON><PERSON>, TrashCan } from "@carbon/react/icons";
import { OptionsAccordionGroup } from "../../../../components/options/options-accordion-group/options-accordion-group";
import { BaseDataValue } from "./base-options";
import styles from "./config-parameters-section.module.scss";

interface ConfigParametersSectionProps {
  draftValue: BaseDataValue;
  updateDraftValue: (updates: Partial<BaseDataValue>) => void;
  availableConfigItems: Array<{ id: string; text: string }>;
}

export const ConfigParametersSection = ({
  draftValue,
  updateDraftValue,
  availableConfigItems,
}: ConfigParametersSectionProps) => {
  const handleConfigChange = ({
    selectedItems,
  }: {
    selectedItems: Array<{ id: string; text: string }>;
  }) => {
    const configNames = selectedItems.map((item) => item.id);
    updateDraftValue({ config: configNames });
  };

  const handleRemoveConfigParam = (configName: string) => {
    const updatedConfig = (draftValue.config || []).filter(
      (name) => name !== configName,
    );
    updateDraftValue({ config: updatedConfig });
  };

  return (
    <OptionsAccordionGroup
      id="config-parameters"
      title="Config Parameters"
      defaultOpen={false}
      icon={<Settings size={20} />}
    >
      <FilterableMultiSelect
        id="config-param-selector"
        titleText="Config Parameters"
        placeholder="Select config settings..."
        items={availableConfigItems}
        selectedItems={availableConfigItems.filter((item) =>
          (draftValue.config || []).includes(item.id),
        )}
        onChange={handleConfigChange}
        itemToString={(item) =>
          item ? `@${item.text.replace(/-/g, "_")}` : ""
        }
        helperText="Select config settings to inject into your query. Dashes in config names become underscores (e.g., my-config becomes @my_config)"
        selectionFeedback="top-after-reopen"
      />

      {(draftValue.config || []).length > 0 && (
        <div className={styles.selectedConfigContainer}>
          <h4 className={styles.selectedConfigTitle}>
            Selected Config Parameters
          </h4>
          {(draftValue.config || []).map((configName) => (
            <div key={configName} className={styles.configItem}>
              <code>@{configName.replace(/-/g, "_")}</code>
              <Button
                kind="danger--ghost"
                size="sm"
                hasIconOnly
                renderIcon={TrashCan}
                iconDescription="Remove config parameter"
                onClick={() => handleRemoveConfigParam(configName)}
              />
            </div>
          ))}
        </div>
      )}
    </OptionsAccordionGroup>
  );
};
