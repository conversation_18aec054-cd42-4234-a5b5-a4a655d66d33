import { vi } from "vitest";
import { render, screen, fireEvent } from "../../../../../test-utils";
import { BaseOptions } from "./base-options";
import type { BaseOptionsConfig, BaseDataValue } from "./base-options";
import type { AppConfigSetting } from "@ict/sdk/types";

// Mock Carbon React components
vi.mock("@carbon/react", async () => {
  const actual = await vi.importActual("@carbon/react");
  return {
    ...actual,
    TextInput: ({ labelText, value, onChange, helperText }: any) => (
      <div data-testid="text-input">
        <label data-testid="label">{labelText}</label>
        <input data-testid="input" value={value} onChange={onChange} />
        <div data-testid="helper-text">{helperText}</div>
      </div>
    ),
    TextArea: ({ labelText, value, onChange }: any) => (
      <div data-testid="text-area">
        <label data-testid="textarea-label">{labelText}</label>
        <textarea data-testid="textarea" value={value} onChange={onChange} />
      </div>
    ),
    Toggle: ({ labelText, toggled, onToggle }: any) => (
      <div data-testid="toggle">
        <label data-testid="toggle-label">{labelText}</label>
        <input
          type="checkbox"
          data-testid="toggle-input"
          checked={toggled}
          onChange={(e) => onToggle(e.target.checked)}
        />
      </div>
    ),
    Select: ({ labelText, value, onChange, children }: any) => (
      <div data-testid="select">
        <label data-testid="select-label">{labelText}</label>
        <select data-testid="select-input" value={value} onChange={onChange}>
          {children}
        </select>
      </div>
    ),
    SelectItem: ({ value, text }: any) => <option value={value}>{text}</option>,
    Button: ({ onClick, children, kind }: any) => (
      <button
        data-testid={kind === "danger--ghost" ? "delete-button" : "button"}
        onClick={onClick}
      >
        {children}
      </button>
    ),
    CopyButton: ({ onClick }: any) => (
      <button data-testid="copy-button" onClick={onClick}>
        Copy
      </button>
    ),
    Checkbox: ({ labelText, checked, onChange }: any) => (
      <div data-testid="checkbox">
        <input
          type="checkbox"
          data-testid="checkbox-input"
          checked={checked}
          onChange={onChange}
        />
        <label data-testid="checkbox-label">{labelText}</label>
      </div>
    ),
    GlobalTheme: ({ children }: { children: React.ReactNode }) => children,
  };
});

// Mock components
vi.mock(
  "../../../../components/options/options-container/options-container",
  () => ({
    OptionsContainer: ({ children, onSave, onClose }: any) => (
      <div data-testid="options-container">
        <button data-testid="save-button" onClick={onSave}>
          Apply
        </button>
        <button data-testid="close-button" onClick={onClose}>
          Close
        </button>
        {children}
      </div>
    ),
  }),
);

vi.mock(
  "../../../../components/options/options-accordion/options-accordion",
  () => ({
    OptionsAccordion: ({ children }: any) => (
      <div data-testid="options-accordion">{children}</div>
    ),
  }),
);

vi.mock(
  "../../../../components/options/options-accordion-group/options-accordion-group",
  () => ({
    OptionsAccordionGroup: ({ children, title, id }: any) => (
      <div data-testid={`accordion-group-${id}`}>
        <div data-testid="accordion-title">{title}</div>
        {children}
      </div>
    ),
  }),
);

describe("BaseOptions", () => {
  const mockOnClose = vi.fn();
  const mockOnApply = vi.fn();

  const mockSetting: AppConfigSetting = {
    name: "test-setting",
    value: {
      id: "test",
      label: "Test Label",
      description: "Test Description",
      type: "singleValue",
      isDraft: false,
      metadata: { unit: "count", category: "test" },
      dataSources: [{ id: "ds1", type: "edp-bigquery", table: "test_table" }],
      filters: ["filter1"],
      parameters: { required: ["start_date", "end_date"] },
      queryProperties: [],
    } as unknown as BaseDataValue,
    dataType: "json",
    id: "test-setting",
  };

  const defaultConfig: BaseOptionsConfig = {
    showConfigParameters: true,
    showQueryProperties: true,
    showFilters: true,
    showUnit: true,
    showCategoryFilter: false,
    multipleDataSources: false,
  };

  const baseProps = {
    setting: mockSetting,
    onClose: mockOnClose,
    onApply: mockOnApply,
    config: defaultConfig,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("renders without crashing", () => {
    render(<BaseOptions {...baseProps} />);

    expect(screen.getByTestId("options-container")).toBeInTheDocument();
    expect(screen.getByTestId("options-accordion")).toBeInTheDocument();
  });

  it("displays basic information section", () => {
    render(<BaseOptions {...baseProps} />);

    expect(screen.getByTestId("accordion-group-basic")).toBeInTheDocument();

    const inputs = screen.getAllByTestId("input");
    expect(inputs[0]).toHaveValue("test");
    expect(inputs[1]).toHaveValue("Test Label");

    expect(screen.getByTestId("textarea")).toHaveValue("Test Description");
  });

  it("displays metadata section with unit field", () => {
    render(<BaseOptions {...baseProps} />);

    expect(screen.getByTestId("accordion-group-metadata")).toBeInTheDocument();

    const unitInput = screen
      .getAllByTestId("input")
      .find((input) => (input as HTMLInputElement).value === "count");
    expect(unitInput).toBeInTheDocument();
  });

  it("displays data source section", () => {
    render(<BaseOptions {...baseProps} />);

    expect(
      screen.getByTestId("accordion-group-data-sources"),
    ).toBeInTheDocument();
    expect(screen.getByText("Add Data Source")).toBeInTheDocument();
  });

  it("displays query parameters section", () => {
    render(<BaseOptions {...baseProps} />);

    expect(
      screen.getByTestId("accordion-group-parameters"),
    ).toBeInTheDocument();
    expect(
      screen.getByText(
        "Select which parameters are required for this data query.",
      ),
    ).toBeInTheDocument();
  });

  it("updates draft value when input changes", () => {
    render(<BaseOptions {...baseProps} />);

    const inputs = screen.getAllByTestId("input");
    const idInput = inputs[0];

    fireEvent.change(idInput, { target: { value: "updated-id" } });

    // Check that the value is updated in the DOM
    expect(idInput).toHaveValue("updated-id");
  });

  it("calls onApply when save button clicked", () => {
    render(<BaseOptions {...baseProps} />);

    const saveButton = screen.getByTestId("save-button");
    fireEvent.click(saveButton);

    expect(mockOnApply).toHaveBeenCalledWith(
      expect.objectContaining({
        name: expect.any(String),
        value: expect.any(Object),
      }),
    );
  });

  it("calls onClose when close button clicked", () => {
    render(<BaseOptions {...baseProps} />);

    const closeButton = screen.getByTestId("close-button");
    fireEvent.click(closeButton);

    expect(mockOnClose).toHaveBeenCalled();
  });

  it("adds data source when add button clicked", () => {
    render(<BaseOptions {...baseProps} />);

    const addButton = screen.getByText("Add Data Source");
    fireEvent.click(addButton);

    // Should now have inputs for the new data source
    expect(screen.getAllByTestId("text-input").length).toBeGreaterThan(3);
  });

  it("handles multiple data sources when config enabled", () => {
    const multipleDataSourcesConfig = {
      ...baseProps,
      config: { ...defaultConfig, multipleDataSources: true },
    };

    render(<BaseOptions {...multipleDataSourcesConfig} />);

    expect(screen.getByText("Data Sources")).toBeInTheDocument();
    expect(screen.getByText("Data Source 1")).toBeInTheDocument();
  });

  it("shows category filter toggle when enabled in config", () => {
    const categoryFilterConfig = {
      ...baseProps,
      config: { ...defaultConfig, showCategoryFilter: true },
    };

    render(<BaseOptions {...categoryFilterConfig} />);

    expect(screen.getByText("Category Filter")).toBeInTheDocument();
  });

  it("renders custom sections when provided", () => {
    const renderCustomSections = () => (
      <div data-testid="custom-section">Custom Section</div>
    );

    render(
      <BaseOptions
        {...baseProps}
        renderCustomSections={renderCustomSections}
      />,
    );

    expect(screen.getByTestId("custom-section")).toBeInTheDocument();
  });

  it("handles empty setting value", () => {
    const emptySettingProps = {
      ...baseProps,
      setting: { name: "empty", value: null },
    };

    render(<BaseOptions {...(emptySettingProps as any)} />);

    expect(screen.getByTestId("options-container")).toBeInTheDocument();

    const inputs = screen.getAllByTestId("input");
    expect(inputs[0]).toHaveValue("");
  });
});
