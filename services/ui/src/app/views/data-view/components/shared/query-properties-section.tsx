/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  Button,
  CopyButton,
  Select,
  SelectItem,
  TextInput,
} from "@carbon/react";
import { Add, Parameter, TrashCan } from "@carbon/react/icons";
import { DateTimeGranularityValues } from "@ict/sdk/types";
import { OptionsAccordionGroup } from "../../../../components/options/options-accordion-group/options-accordion-group";
import { components } from "../../../../../../../../libs/shared/ict-sdk/generated/openapi-react-query";
import { BaseDataValue } from "./base-options";
import styles from "./query-properties-section.module.scss";

export type DataQueryProperty = components["schemas"]["DataQueryProperty"];

interface QueryPropertiesSectionProps {
  draftValue: BaseDataValue;
  updateDraftValue: (updates: Partial<BaseDataValue>) => void;
  defaultLimitValue?: number;
  validatePropertyId?: (id: string) => boolean;
}

export const QueryPropertiesSection = ({
  draftValue,
  updateDraftValue,
  defaultLimitValue = 100,
  validatePropertyId = (id: string) => /^[a-zA-Z_][a-zA-Z0-9_]*$/.test(id),
}: QueryPropertiesSectionProps) => {
  const draftQueryProperties = (draftValue.queryProperties ||
    []) as DataQueryProperty[];

  const handleAddQueryProperty = () => {
    const newProperty: DataQueryProperty = {
      id: "",
      type: "time_bucket",
      defaultValue: "DAY",
    };
    updateDraftValue({
      queryProperties: [...draftQueryProperties, newProperty],
    });
  };

  const handleQueryPropertyChange = (
    index: number,
    field: keyof DataQueryProperty,
    value: any,
  ) => {
    const updatedProperties = [...draftQueryProperties];
    if (updatedProperties[index]) {
      if (field === "id" && !validatePropertyId(value) && value !== "") {
        // Don't update if invalid (but allow empty for editing)
        return;
      }

      if (field === "type") {
        // Reset defaultValue when type changes
        let defaultValue: any;
        const additionalProps: any = {};
        switch (value) {
          case "time_bucket":
            defaultValue = "DAY";
            break;
          case "sort":
            defaultValue = "ASC";
            break;
          case "limit":
            defaultValue = defaultLimitValue;
            break;
          case "group_by":
            defaultValue = "";
            additionalProps.groupableFields = [];
            break;
          default:
            defaultValue = "";
        }
        updatedProperties[index] = {
          ...updatedProperties[index],
          type: value,
          defaultValue,
          ...additionalProps,
        };
      } else {
        updatedProperties[index] = {
          ...updatedProperties[index],
          [field]: value,
        };
      }
      updateDraftValue({ queryProperties: updatedProperties });
    }
  };

  const handleRemoveQueryProperty = (index: number) => {
    const updatedProperties = draftQueryProperties.filter(
      (_, i) => i !== index,
    );
    updateDraftValue({ queryProperties: updatedProperties });
  };

  return (
    <OptionsAccordionGroup
      id="query-properties"
      title="Query Properties"
      defaultOpen={false}
      icon={<Parameter size={20} />}
    >
      {draftQueryProperties.length > 0 ? (
        draftQueryProperties.map((property, index) => (
          <div key={index} className={styles.propertyContainer}>
            <div className={styles.propertyHeader}>
              <h4 className={styles.propertyTitle}>
                Query Property {index + 1}
              </h4>
              <Button
                kind="danger--ghost"
                size="sm"
                hasIconOnly
                renderIcon={TrashCan}
                iconDescription="Delete query property"
                onClick={() => handleRemoveQueryProperty(index)}
              />
            </div>

            <div className={styles.inputWithCopyButton}>
              <TextInput
                id={`query-property-id-${index}`}
                labelText="Property ID"
                value={property.id || ""}
                onChange={(e) =>
                  handleQueryPropertyChange(index, "id", e.target.value)
                }
                helperText="Unique identifier (letters, numbers, underscores only)"
                className={styles.propertyField}
                invalid={
                  property.id.length > 0 && !validatePropertyId(property.id)
                }
                invalidText="ID can only contain letters, numbers, and underscores"
              />
              <CopyButton
                autoAlign
                onClick={() => {
                  navigator.clipboard.writeText(`{{${property.id || ""}}}`);
                }}
                className={styles.copyButton}
              />
            </div>

            <Select
              id={`query-property-type-${index}`}
              labelText="Property Type"
              value={property.type || "time_bucket"}
              onChange={(e) =>
                handleQueryPropertyChange(index, "type", e.target.value)
              }
              className={styles.propertyField}
            >
              <SelectItem value="time_bucket" text="Time Bucket" />
              <SelectItem value="sort" text="Sort Order" />
              <SelectItem value="limit" text="Limit" />
              <SelectItem value="group_by" text="Group By" />
            </Select>

            {/* Dynamic default value input based on type */}
            {property.type === "time_bucket" && (
              <Select
                id={`query-property-default-${index}`}
                labelText="Default Time Granularity"
                value={property.defaultValue || "DAY"}
                onChange={(e) =>
                  handleQueryPropertyChange(
                    index,
                    "defaultValue",
                    e.target.value,
                  )
                }
              >
                {DateTimeGranularityValues.map((value) => (
                  <SelectItem key={value} value={value} text={value} />
                ))}
              </Select>
            )}

            {property.type === "sort" && (
              <Select
                id={`query-property-default-${index}`}
                labelText="Default Sort Order"
                value={property.defaultValue || "ASC"}
                onChange={(e) =>
                  handleQueryPropertyChange(
                    index,
                    "defaultValue",
                    e.target.value,
                  )
                }
              >
                <SelectItem value="ASC" text="Ascending" />
                <SelectItem value="DESC" text="Descending" />
              </Select>
            )}

            {property.type === "limit" && (
              <TextInput
                id={`query-property-default-${index}`}
                labelText="Default Limit"
                type="number"
                value={
                  property.defaultValue?.toString() ||
                  defaultLimitValue.toString()
                }
                onChange={(e) =>
                  handleQueryPropertyChange(
                    index,
                    "defaultValue",
                    parseInt(e.target.value) || defaultLimitValue,
                  )
                }
                helperText="Default number limit"
              />
            )}

            {property.type === "group_by" && (
              <>
                <div className={styles.groupableFieldsContainer}>
                  <h5 className={styles.groupableFieldsTitle}>
                    Groupable Fields
                  </h5>
                  <div className={styles.addFieldButton}>
                    <Button
                      kind="secondary"
                      size="sm"
                      renderIcon={Add}
                      onClick={() => {
                        const updatedProperties = [...draftQueryProperties];
                        const currentProperty = updatedProperties[index];
                        if (
                          currentProperty &&
                          currentProperty.type === "group_by"
                        ) {
                          const currentFields =
                            (currentProperty as any).groupableFields || [];
                          updatedProperties[index] = {
                            ...currentProperty,
                            groupableFields: [...currentFields, ""],
                          };
                          updateDraftValue({
                            queryProperties: updatedProperties,
                          });
                        }
                      }}
                    >
                      Add Field
                    </Button>
                  </div>

                  {((property as any).groupableFields || []).map(
                    (field: string, fieldIndex: number) => (
                      <div key={fieldIndex} className={styles.fieldRow}>
                        <TextInput
                          id={`groupable-field-${index}-${fieldIndex}`}
                          labelText=""
                          value={field}
                          onChange={(e) => {
                            const updatedProperties = [...draftQueryProperties];
                            const currentProperty = updatedProperties[index];
                            if (
                              currentProperty &&
                              currentProperty.type === "group_by"
                            ) {
                              const currentFields = [
                                ...((currentProperty as any).groupableFields ||
                                  []),
                              ];
                              currentFields[fieldIndex] = e.target.value;
                              updatedProperties[index] = {
                                ...currentProperty,
                                groupableFields: currentFields,
                              };
                              updateDraftValue({
                                queryProperties: updatedProperties,
                              });
                            }
                          }}
                          placeholder="Field name"
                          className={styles.fieldInput}
                        />
                        <Button
                          kind="danger--ghost"
                          size="sm"
                          hasIconOnly
                          renderIcon={TrashCan}
                          iconDescription="Remove field"
                          onClick={() => {
                            const updatedProperties = [...draftQueryProperties];
                            const currentProperty = updatedProperties[index];
                            if (
                              currentProperty &&
                              currentProperty.type === "group_by"
                            ) {
                              const currentFields = [
                                ...((currentProperty as any).groupableFields ||
                                  []),
                              ];
                              currentFields.splice(fieldIndex, 1);
                              updatedProperties[index] = {
                                ...currentProperty,
                                groupableFields: currentFields,
                                defaultValue:
                                  currentFields.length > 0
                                    ? currentFields.includes(
                                        property.defaultValue as string,
                                      )
                                      ? property.defaultValue
                                      : currentFields[0]
                                    : "",
                              };
                              updateDraftValue({
                                queryProperties: updatedProperties,
                              });
                            }
                          }}
                        />
                      </div>
                    ),
                  )}
                </div>

                <Select
                  id={`query-property-default-${index}`}
                  labelText="Default Group By Field"
                  value={property.defaultValue || ""}
                  onChange={(e) =>
                    handleQueryPropertyChange(
                      index,
                      "defaultValue",
                      e.target.value,
                    )
                  }
                  helperText="Select the default field to group by"
                  disabled={!((property as any).groupableFields?.length > 0)}
                >
                  <SelectItem value="" text="Select a field..." />
                  {((property as any).groupableFields || []).map(
                    (field: string, fieldIndex: number) =>
                      field && (
                        <SelectItem
                          key={fieldIndex}
                          value={field}
                          text={field}
                        />
                      ),
                  )}
                </Select>
              </>
            )}
          </div>
        ))
      ) : (
        <p className={styles.emptyStateText}>
          No query properties configured. Click &quot;Add Query Property&quot;
          to get started.
        </p>
      )}
      <div className={styles.addButton}>
        <Button
          kind="secondary"
          size="sm"
          renderIcon={Add}
          onClick={handleAddQueryProperty}
        >
          Add Query Property
        </Button>
      </div>
    </OptionsAccordionGroup>
  );
};
