import {
  Button,
  Checkbox,
  Select,
  SelectItem,
  TextArea,
  TextInput,
  Toggle,
  CopyButton,
} from "@carbon/react";
import { Add, Code, DataBase, Settings, TrashCan } from "@carbon/react/icons";
import type { AppConfigSetting } from "@ict/sdk/types";
import { ReactNode, useState } from "react";
import { OptionsAccordionGroup } from "../../../../components/options/options-accordion-group/options-accordion-group";
import { OptionsAccordion } from "../../../../components/options/options-accordion/options-accordion";
import { OptionsContainer } from "../../../../components/options/options-container/options-container";
import { components } from "../../../../../../../../libs/shared/ict-sdk/generated/openapi-react-query";
import styles from "./base-options.module.scss";

export type BaseDataValue = components["schemas"]["DataQuery"];

export interface BaseOptionsConfig {
  // Control which sections to show
  showConfigParameters?: boolean;
  showQueryProperties?: boolean;
  showFilters?: boolean;
  showUnit?: boolean;
  showCategoryFilter?: boolean;

  // Control data sources behavior
  multipleDataSources?: boolean;

  // Default values
  defaultLimitValue?: number;
  defaultRequiredParams?: string[];

  // Custom validation
  validatePropertyId?: (id: string) => boolean;
}

export interface BaseOptionsProps {
  setting: AppConfigSetting;
  onClose: () => void;
  onApply: (updatedSetting: AppConfigSetting) => void;
  config: BaseOptionsConfig;

  // Render props for custom sections
  renderCustomSections?: (props: {
    draftValue: BaseDataValue;
    updateDraftValue: (updates: Partial<BaseDataValue>) => void;
  }) => ReactNode;
}

export const BaseOptions = ({
  setting,
  onClose,
  onApply,
  config,
  renderCustomSections,
}: BaseOptionsProps) => {
  const dataValue = (setting.value as BaseDataValue) || {};

  // Local state for draft changes
  const [draftValue, setDraftValue] = useState<BaseDataValue>({
    ...dataValue,
    id: dataValue.id || "",
    label: dataValue.label || "",
    description: dataValue.description || "",
    isDraft: dataValue.isDraft ?? false,
    metadata: {
      ...dataValue.metadata,
      unit: dataValue.metadata?.unit || "",
      category: dataValue.metadata?.category || "",
      isCategoryFilter: dataValue.metadata?.isCategoryFilter ?? false,
    },
    dataSources: dataValue.dataSources || [],
    config: dataValue.config || [],
    filters: dataValue.filters || [],
    parameters: {
      ...dataValue.parameters,
      required: dataValue.parameters?.required ||
        config.defaultRequiredParams || ["start_date", "end_date"],
    },
    queryProperties: dataValue.queryProperties || [],
  });

  const updateDraftValue = (updates: Partial<BaseDataValue>) => {
    setDraftValue((prev) => ({ ...prev, ...updates }));
  };

  const handleApply = () => {
    const updatedSetting: AppConfigSetting = {
      ...setting,
      value: draftValue,
      name: draftValue.id,
    };
    onApply(updatedSetting);
  };

  // Data source handlers
  const handleDataSourceChange = (
    index: number,
    field: string,
    value: string,
  ) => {
    const updatedDataSources = [...(draftValue.dataSources || [])];
    if (updatedDataSources[index]) {
      updatedDataSources[index] = {
        ...updatedDataSources[index],
        [field]: value,
      };
      updateDraftValue({ dataSources: updatedDataSources });
    }
  };

  const handleAddDataSource = () => {
    const newDataSource = {
      id: "",
      type: "edp-bigquery",
      table: "",
    };
    updateDraftValue({
      dataSources: [...(draftValue.dataSources || []), newDataSource],
    });
  };

  const handleDeleteDataSource = (index: number) => {
    const updatedDataSources = (draftValue.dataSources || []).filter(
      (_, i) => i !== index,
    );
    updateDraftValue({ dataSources: updatedDataSources });
  };

  // Parameter handlers
  const handleRequiredParamChange = (
    paramName: string,
    isRequired: boolean,
  ) => {
    const currentRequired = draftValue.parameters?.required || [];
    let updatedRequired: string[];

    if (isRequired) {
      if (!currentRequired.includes(paramName)) {
        updatedRequired = [...currentRequired, paramName];
      } else {
        updatedRequired = currentRequired;
      }
    } else {
      updatedRequired = currentRequired.filter((name) => name !== paramName);
    }

    updateDraftValue({
      parameters: {
        ...draftValue.parameters,
        required: updatedRequired,
      },
    });
  };

  // Get all available parameters
  const allAvailableParams = [
    "start_date",
    "end_date",
    ...(draftValue.filters || []).map((filter) => filter.replace(/-/g, "_")),
    ...(draftValue.queryProperties || [])
      .map((property) => property.id)
      .filter((id) => id && id.length > 0),
  ];

  return (
    <OptionsContainer
      onClose={onClose}
      onSave={handleApply}
      saveButtonText="Apply"
      saveButtonDisabled={false}
    >
      <OptionsAccordion>
        {/* Basic Information */}
        <OptionsAccordionGroup
          id="basic"
          title="Basic Information"
          defaultOpen={false}
          icon={<Settings size={20} />}
        >
          <TextInput
            id="data-id"
            labelText="ID"
            value={draftValue.id || ""}
            onChange={(e) => updateDraftValue({ id: e.target.value })}
            helperText="Unique identifier"
          />
          <TextInput
            id="data-label"
            labelText="Label"
            value={draftValue.label || ""}
            onChange={(e) => updateDraftValue({ label: e.target.value })}
            helperText="Display name"
          />
          <TextArea
            id="data-description"
            labelText="Description"
            value={draftValue.description || ""}
            onChange={(e) => updateDraftValue({ description: e.target.value })}
            helperText="Detailed description"
            rows={3}
          />
          <Toggle
            labelText="Draft"
            id="is-draft"
            toggled={draftValue.isDraft ?? false}
            onToggle={(toggled) => updateDraftValue({ isDraft: toggled })}
          />
        </OptionsAccordionGroup>

        {/* Metadata */}
        <OptionsAccordionGroup
          id="metadata"
          title="Metadata"
          defaultOpen={false}
          icon={<Code size={20} />}
        >
          {config.showUnit && (
            <TextInput
              id="metadata-unit"
              labelText="Unit"
              value={draftValue.metadata?.unit || ""}
              onChange={(e) =>
                updateDraftValue({
                  metadata: { ...draftValue.metadata, unit: e.target.value },
                })
              }
              helperText="Unit of measurement (e.g., 'count', 'percentage', 'seconds')"
            />
          )}
          <TextInput
            id="metadata-category"
            labelText="Category"
            value={draftValue.metadata?.category || ""}
            onChange={(e) =>
              updateDraftValue({
                metadata: { ...draftValue.metadata, category: e.target.value },
              })
            }
            helperText="Category or domain this belongs to"
          />
          {config.showCategoryFilter && (
            <>
              <Toggle
                labelText="Category Filter"
                id="is-category-filter"
                toggled={draftValue.metadata?.isCategoryFilter ?? false}
                onToggle={(toggled) =>
                  updateDraftValue({
                    metadata: {
                      ...draftValue.metadata,
                      isCategoryFilter: toggled,
                    },
                  })
                }
              />
              <p className={styles.categoryFilterDescription}>
                Category filters control which data series are visible in
                dashboard widgets, rather than filtering the underlying query
                data
              </p>
            </>
          )}
        </OptionsAccordionGroup>

        {/* Data Sources */}
        <OptionsAccordionGroup
          id="data-sources"
          title={config.multipleDataSources ? "Data Sources" : "Data Source"}
          defaultOpen={false}
          icon={<DataBase size={20} />}
        >
          {config.multipleDataSources ? (
            <>
              {(draftValue.dataSources || []).length > 0 ? (
                (draftValue.dataSources || []).map((dataSource, index) => (
                  <div key={index} className={styles.dataSourceContainer}>
                    <div className={styles.dataSourceHeader}>
                      <h4 className={styles.dataSourceTitle}>
                        Data Source {index + 1}
                      </h4>
                      <Button
                        kind="danger--ghost"
                        size="sm"
                        hasIconOnly
                        renderIcon={TrashCan}
                        iconDescription="Delete data source"
                        onClick={() => handleDeleteDataSource(index)}
                      />
                    </div>
                    <div className={styles.inputWithCopyButton}>
                      <TextInput
                        id={`data-source-id-${index}`}
                        labelText="Data Source ID"
                        value={dataSource.id || ""}
                        onChange={(e) =>
                          handleDataSourceChange(index, "id", e.target.value)
                        }
                        helperText="Unique identifier for this data source"
                        className={styles.dataSourceField}
                      />
                      <CopyButton
                        autoAlign
                        onClick={() =>
                          navigator.clipboard.writeText(
                            `{{${dataSource.id || ""}}}`,
                          )
                        }
                        className={styles.copyButton}
                      />
                    </div>
                    <Select
                      id={`data-source-type-${index}`}
                      labelText="Type"
                      value={dataSource.type || "edp-bigquery"}
                      onChange={(e) =>
                        handleDataSourceChange(index, "type", e.target.value)
                      }
                      className={styles.dataSourceField}
                    >
                      <SelectItem value="edp-bigquery" text="EDP BigQuery" />
                    </Select>
                    <TextInput
                      id={`data-source-table-${index}`}
                      labelText="Table/Resource"
                      value={dataSource.table || ""}
                      onChange={(e) =>
                        handleDataSourceChange(index, "table", e.target.value)
                      }
                      helperText="Table name or resource identifier"
                    />
                  </div>
                ))
              ) : (
                <p className={styles.emptyStateText}>
                  No data sources configured. Click &quot;Add Data Source&quot;
                  to get started.
                </p>
              )}
            </>
          ) : (
            <div className={styles.singleDataSourceContainer}>
              <div className={styles.inputWithCopyButton}>
                <TextInput
                  id="data-source-id"
                  labelText="Data Source ID"
                  value={draftValue.dataSources?.[0]?.id || ""}
                  onChange={(e) =>
                    handleDataSourceChange(0, "id", e.target.value)
                  }
                  helperText="Unique identifier for this data source"
                  className={styles.dataSourceField}
                />
                <CopyButton
                  autoAlign
                  onClick={() =>
                    navigator.clipboard.writeText(
                      `{{${draftValue.dataSources?.[0]?.id || ""}}}`,
                    )
                  }
                  className={styles.copyButton}
                />
              </div>

              <Select
                id="data-source-type"
                labelText="Type"
                value={draftValue.dataSources?.[0]?.type || "edp-bigquery"}
                onChange={(e) =>
                  handleDataSourceChange(0, "type", e.target.value)
                }
                className={styles.dataSourceField}
              >
                <SelectItem value="edp-bigquery" text="EDP BigQuery" />
              </Select>

              <TextInput
                id="data-source-table"
                labelText="Table/Resource"
                value={draftValue.dataSources?.[0]?.table || ""}
                onChange={(e) =>
                  handleDataSourceChange(0, "table", e.target.value)
                }
                helperText="Table name or resource identifier"
              />
            </div>
          )}
          <div className={styles.addButton}>
            <Button
              kind="secondary"
              size="sm"
              renderIcon={Add}
              onClick={handleAddDataSource}
            >
              Add Data Source
            </Button>
          </div>
        </OptionsAccordionGroup>

        {/* Custom sections rendered here */}
        {renderCustomSections?.({ draftValue, updateDraftValue })}

        {/* Query Parameters */}
        <OptionsAccordionGroup
          id="parameters"
          title="Query Parameters"
          defaultOpen={false}
          icon={<Code size={20} />}
        >
          <div className={styles.parametersContainer}>
            <p className={styles.parameterDescription}>
              Select which parameters are required for this data query.
            </p>

            {allAvailableParams.map((param) => (
              <div key={param} className={styles.parameterItem}>
                <code className={styles.parameterCode}>@{param}</code>
                <div className={styles.parameterCheckbox}>
                  <Checkbox
                    id={`param-required-${param}`}
                    labelText="Required"
                    checked={(draftValue.parameters?.required || []).includes(
                      param,
                    )}
                    onChange={(event) =>
                      handleRequiredParamChange(param, event.target.checked)
                    }
                  />
                </div>
              </div>
            ))}

            {allAvailableParams.length === 0 && (
              <p className={styles.emptyStateText}>No parameters available.</p>
            )}
          </div>
        </OptionsAccordionGroup>
      </OptionsAccordion>
    </OptionsContainer>
  );
};

export default BaseOptions;
