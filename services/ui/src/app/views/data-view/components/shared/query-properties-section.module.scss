.addButton {
  margin-bottom: 1rem;
}

.propertyContainer {
  margin-bottom: 1rem;
  padding: 1rem;
  border: 1px solid var(--cds-border-subtle);
  border-radius: 4px;
  position: relative;
}

.propertyHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.propertyTitle {
  margin: 0;
}

.propertyField {
  margin-bottom: 1rem;
}

.groupableFieldsContainer {
  margin-bottom: 1rem;
}

.groupableFieldsTitle {
  margin-bottom: 0.5rem;
}

.addFieldButton {
  margin-bottom: 0.5rem;
}

.fieldRow {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.fieldInput {
  flex-grow: 1;
}

.emptyStateText {
  color: var(--cds-text-secondary);
  font-style: italic;
  text-align: center;
  padding: 2rem;
}

.inputWithCopyButton {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.copyButton {
  margin-bottom: 1rem;
  background-color: transparent;
}
