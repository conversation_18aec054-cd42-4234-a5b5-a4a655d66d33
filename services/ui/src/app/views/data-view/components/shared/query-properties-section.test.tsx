import { vi } from "vitest";
import { render, screen, fireEvent } from "../../../../../test-utils";
import { QueryPropertiesSection } from "./query-properties-section";
import type { BaseDataValue } from "./base-options";

// Mock Carbon React components
vi.mock("@carbon/react", async () => {
  const actual = await vi.importActual("@carbon/react");
  return {
    ...actual,
    Button: ({ onClick, renderIcon: Icon, children }: any) => (
      <button data-testid="button" onClick={onClick}>
        {Icon && <Icon data-testid="icon" />}
        {children}
      </button>
    ),
    TextInput: ({ labelText, value, onChange, invalid, helperText }: any) => (
      <div data-testid="text-input">
        <label data-testid="label">{labelText}</label>
        <input
          data-testid="input"
          value={value}
          onChange={onChange}
          data-invalid={invalid}
        />
        <div data-testid="helper-text">{helperText}</div>
      </div>
    ),
    Select: ({ labelText, value, onChange, children }: any) => (
      <div data-testid="select">
        <label data-testid="select-label">{labelText}</label>
        <select data-testid="select-input" value={value} onChange={onChange}>
          {children}
        </select>
      </div>
    ),
    SelectItem: ({ value, text }: any) => <option value={value}>{text}</option>,
    CopyButton: ({ onClick }: any) => (
      <button data-testid="copy-button" onClick={onClick}>
        Copy
      </button>
    ),
    GlobalTheme: ({ children }: { children: React.ReactNode }) => children,
  };
});

// Mock OptionsAccordionGroup
vi.mock(
  "../../../../components/options/options-accordion-group/options-accordion-group",
  () => ({
    OptionsAccordionGroup: ({ children, title }: any) => (
      <div data-testid="accordion-group">
        <div data-testid="accordion-title">{title}</div>
        {children}
      </div>
    ),
  }),
);

describe("QueryPropertiesSection", () => {
  const mockUpdateDraftValue = vi.fn();

  const baseDraftValue: BaseDataValue = {
    id: "test",
    label: "Test",
    description: "Test description",
    type: "singleValue",
    isDraft: false,
    metadata: { category: "" },
    dataSources: [],
    filters: [],
    parameters: { required: [] },
    config: [],
    queryProperties: [],
    query: "",
  };

  const baseProps = {
    draftValue: baseDraftValue,
    updateDraftValue: mockUpdateDraftValue,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("renders without crashing", () => {
    render(<QueryPropertiesSection {...baseProps} />);

    expect(screen.getByTestId("accordion-group")).toBeInTheDocument();
    expect(screen.getByTestId("accordion-title")).toHaveTextContent(
      "Query Properties",
    );
  });

  it("shows empty state when no properties", () => {
    render(<QueryPropertiesSection {...baseProps} />);

    expect(
      screen.getByText(/No query properties configured/),
    ).toBeInTheDocument();
  });

  it("shows add query property button", () => {
    render(<QueryPropertiesSection {...baseProps} />);

    expect(screen.getByText("Add Query Property")).toBeInTheDocument();
  });

  it("adds new query property when add button clicked", () => {
    render(<QueryPropertiesSection {...baseProps} />);

    const addButton = screen.getByText("Add Query Property");
    fireEvent.click(addButton);

    expect(mockUpdateDraftValue).toHaveBeenCalledWith({
      queryProperties: [
        {
          id: "",
          type: "time_bucket",
          defaultValue: "DAY",
        },
      ],
    });
  });

  it("renders existing query properties", () => {
    const propsWithProperties = {
      ...baseProps,
      draftValue: {
        ...baseDraftValue,
        queryProperties: [
          { id: "time_granularity", type: "time_bucket", defaultValue: "DAY" },
        ],
      },
    };

    render(<QueryPropertiesSection {...(propsWithProperties as any)} />);

    expect(screen.getByText("Query Property 1")).toBeInTheDocument();
    expect(screen.getByTestId("text-input")).toBeInTheDocument();
    expect(screen.getAllByTestId("select")).toHaveLength(2); // Type select + default value select
  });

  it("updates property ID when text input changes", () => {
    const propsWithProperties = {
      ...baseProps,
      draftValue: {
        ...baseDraftValue,
        queryProperties: [{ id: "", type: "time_bucket", defaultValue: "DAY" }],
      },
    };

    render(<QueryPropertiesSection {...(propsWithProperties as any)} />);

    const input = screen.getByTestId("input");
    fireEvent.change(input, { target: { value: "test_id" } });

    expect(mockUpdateDraftValue).toHaveBeenCalledWith({
      queryProperties: [
        { id: "test_id", type: "time_bucket", defaultValue: "DAY" },
      ],
    });
  });

  it("updates property type and resets default value", () => {
    const propsWithProperties = {
      ...baseProps,
      draftValue: {
        ...baseDraftValue,
        queryProperties: [
          { id: "test", type: "time_bucket", defaultValue: "DAY" },
        ],
      },
    };

    render(<QueryPropertiesSection {...(propsWithProperties as any)} />);

    // Get the first select (property type select)
    const selects = screen.getAllByTestId("select-input");
    const typeSelect = selects[0];
    fireEvent.change(typeSelect, { target: { value: "sort" } });

    expect(mockUpdateDraftValue).toHaveBeenCalledWith({
      queryProperties: [{ id: "test", type: "sort", defaultValue: "ASC" }],
    });
  });

  it("removes query property when delete button clicked", () => {
    const propsWithProperties = {
      ...baseProps,
      draftValue: {
        ...baseDraftValue,
        queryProperties: [
          { id: "test1", type: "time_bucket", defaultValue: "DAY" },
          { id: "test2", type: "sort", defaultValue: "ASC" },
        ],
      },
    };

    render(<QueryPropertiesSection {...(propsWithProperties as any)} />);

    const deleteButtons = screen.getAllByTestId("button");
    // First button should be delete (trash icon)
    const deleteButton = deleteButtons.find((button) =>
      button.querySelector('[data-testid="icon"]'),
    );

    if (deleteButton) {
      fireEvent.click(deleteButton);

      expect(mockUpdateDraftValue).toHaveBeenCalledWith({
        queryProperties: [{ id: "test2", type: "sort", defaultValue: "ASC" }],
      });
    }
  });

  it("validates property ID format", () => {
    const propsWithInvalidProperty = {
      ...baseProps,
      draftValue: {
        ...baseDraftValue,
        queryProperties: [
          { id: "invalid-id!", type: "time_bucket", defaultValue: "DAY" },
        ],
      },
    };

    render(<QueryPropertiesSection {...(propsWithInvalidProperty as any)} />);

    const input = screen.getByTestId("input");
    expect(input).toHaveAttribute("data-invalid", "true");
  });

  it("handles default limit value", () => {
    const propsWithCustomLimit = {
      ...baseProps,
      defaultLimitValue: 200,
    };

    render(<QueryPropertiesSection {...propsWithCustomLimit} />);

    const addButton = screen.getByText("Add Query Property");
    fireEvent.click(addButton);

    // Verify the first call adds a property with default limit
    expect(mockUpdateDraftValue).toHaveBeenCalledWith({
      queryProperties: [{ id: "", type: "time_bucket", defaultValue: "DAY" }],
    });
  });
});
