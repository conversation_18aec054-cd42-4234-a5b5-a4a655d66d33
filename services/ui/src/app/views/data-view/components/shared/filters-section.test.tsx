import { vi } from "vitest";
import { render, screen, fireEvent } from "../../../../../test-utils";
import { FiltersSection } from "./filters-section";
import type { BaseDataValue } from "./base-options";

// Mock Carbon React components
vi.mock("@carbon/react", async () => {
  const actual = await vi.importActual("@carbon/react");
  return {
    ...actual,
    FilterableMultiSelect: ({
      selectedItems,
      onChange,
      items,
      titleText,
    }: any) => (
      <div data-testid="filterable-multi-select">
        <div data-testid="title">{titleText}</div>
        <div data-testid="selected-count">{selectedItems?.length || 0}</div>
        <button
          data-testid="select-item"
          onClick={() => onChange({ selectedItems: [items[0]] })}
        >
          Select First Item
        </button>
      </div>
    ),
    Button: ({ onClick, iconDescription }: any) => (
      <button data-testid="remove-button" onClick={onClick}>
        {iconDescription}
      </button>
    ),
    GlobalTheme: ({ children }: { children: React.ReactNode }) => children,
  };
});

// Mock OptionsAccordionGroup
vi.mock(
  "../../../../components/options/options-accordion-group/options-accordion-group",
  () => ({
    OptionsAccordionGroup: ({ children, title }: any) => (
      <div data-testid="accordion-group">
        <div data-testid="accordion-title">{title}</div>
        {children}
      </div>
    ),
  }),
);

describe("FiltersSection", () => {
  const mockUpdateDraftValue = vi.fn();

  const baseDraftValue: BaseDataValue = {
    id: "test",
    label: "Test",
    description: "Test description",
    type: "singleValue",
    isDraft: false,
    metadata: { category: "" },
    dataSources: [],
    filters: [],
    parameters: { required: [] },
    config: [],
    queryProperties: [],
    query: "",
  };

  const availableFilterItems = [
    { id: "filter1", text: "Filter One" },
    { id: "filter2", text: "Filter Two" },
  ];

  const baseProps = {
    draftValue: baseDraftValue,
    updateDraftValue: mockUpdateDraftValue,
    availableFilterItems,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("renders without crashing", () => {
    render(<FiltersSection {...baseProps} />);

    expect(screen.getByTestId("accordion-group")).toBeInTheDocument();
    expect(screen.getByTestId("accordion-title")).toHaveTextContent("Filters");
  });

  it("displays filterable multi select", () => {
    render(<FiltersSection {...baseProps} />);

    expect(screen.getByTestId("filterable-multi-select")).toBeInTheDocument();
    expect(screen.getByTestId("title")).toHaveTextContent("Filters");
  });

  it("shows selected filters count", () => {
    const propsWithFilters = {
      ...baseProps,
      draftValue: { ...baseDraftValue, filters: ["filter1"] },
    };

    render(<FiltersSection {...propsWithFilters} />);

    expect(screen.getByTestId("selected-count")).toHaveTextContent("1");
  });

  it("calls updateDraftValue when filter selection changes", () => {
    render(<FiltersSection {...baseProps} />);

    fireEvent.click(screen.getByTestId("select-item"));

    expect(mockUpdateDraftValue).toHaveBeenCalledWith({ filters: ["filter1"] });
  });

  it("displays selected filters", () => {
    const propsWithFilters = {
      ...baseProps,
      draftValue: { ...baseDraftValue, filters: ["filter1", "filter2"] },
    };

    render(<FiltersSection {...propsWithFilters} />);

    expect(screen.getByText("Selected Filters")).toBeInTheDocument();
    expect(screen.getByText("@filter1")).toBeInTheDocument();
    expect(screen.getByText("@filter2")).toBeInTheDocument();
  });

  it("removes filter when remove button clicked", () => {
    const propsWithFilters = {
      ...baseProps,
      draftValue: { ...baseDraftValue, filters: ["filter1", "filter2"] },
    };

    render(<FiltersSection {...propsWithFilters} />);

    const removeButtons = screen.getAllByTestId("remove-button");
    fireEvent.click(removeButtons[0]);

    expect(mockUpdateDraftValue).toHaveBeenCalledWith({ filters: ["filter2"] });
  });

  it("handles empty filters array", () => {
    render(<FiltersSection {...baseProps} />);

    expect(screen.queryByText("Selected Filters")).not.toBeInTheDocument();
    expect(screen.getByTestId("selected-count")).toHaveTextContent("0");
  });
});
