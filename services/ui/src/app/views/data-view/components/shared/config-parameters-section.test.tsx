import { vi } from "vitest";
import { render, screen, fireEvent } from "../../../../../test-utils";
import { ConfigParametersSection } from "./config-parameters-section";
import type { BaseDataValue } from "./base-options";

// Mock Carbon React components
vi.mock("@carbon/react", async () => {
  const actual = await vi.importActual("@carbon/react");
  return {
    ...actual,
    FilterableMultiSelect: ({
      selectedItems,
      onChange,
      items,
      titleText,
    }: any) => (
      <div data-testid="filterable-multi-select">
        <div data-testid="title">{titleText}</div>
        <div data-testid="selected-count">{selectedItems?.length || 0}</div>
        <button
          data-testid="select-item"
          onClick={() => onChange({ selectedItems: [items[0]] })}
        >
          Select First Item
        </button>
      </div>
    ),
    Button: ({ onClick, iconDescription }: any) => (
      <button data-testid="remove-button" onClick={onClick}>
        {iconDescription}
      </button>
    ),
    GlobalTheme: ({ children }: { children: React.ReactNode }) => children,
  };
});

// Mock OptionsAccordionGroup
vi.mock(
  "../../../../components/options/options-accordion-group/options-accordion-group",
  () => ({
    OptionsAccordionGroup: ({ children, title }: any) => (
      <div data-testid="accordion-group">
        <div data-testid="accordion-title">{title}</div>
        {children}
      </div>
    ),
  }),
);

describe("ConfigParametersSection", () => {
  const mockUpdateDraftValue = vi.fn();

  const baseDraftValue: BaseDataValue = {
    id: "test",
    label: "Test",
    description: "Test description",
    type: "singleValue",
    isDraft: false,
    metadata: { category: "" },
    dataSources: [],
    filters: [],
    parameters: { required: [] },
    config: [],
    queryProperties: [],
    query: "",
  };

  const availableConfigItems = [
    { id: "config1", text: "Config One" },
    { id: "config2", text: "Config Two" },
  ];

  const baseProps = {
    draftValue: baseDraftValue,
    updateDraftValue: mockUpdateDraftValue,
    availableConfigItems,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("renders without crashing", () => {
    render(<ConfigParametersSection {...baseProps} />);

    expect(screen.getByTestId("accordion-group")).toBeInTheDocument();
    expect(screen.getByTestId("accordion-title")).toHaveTextContent(
      "Config Parameters",
    );
  });

  it("displays filterable multi select", () => {
    render(<ConfigParametersSection {...baseProps} />);

    expect(screen.getByTestId("filterable-multi-select")).toBeInTheDocument();
    expect(screen.getByTestId("title")).toHaveTextContent("Config Parameters");
  });

  it("shows selected config count", () => {
    const propsWithConfig = {
      ...baseProps,
      draftValue: { ...baseDraftValue, config: ["config1"] },
    };

    render(<ConfigParametersSection {...propsWithConfig} />);

    expect(screen.getByTestId("selected-count")).toHaveTextContent("1");
  });

  it("calls updateDraftValue when config selection changes", () => {
    render(<ConfigParametersSection {...baseProps} />);

    fireEvent.click(screen.getByTestId("select-item"));

    expect(mockUpdateDraftValue).toHaveBeenCalledWith({ config: ["config1"] });
  });

  it("displays selected config parameters", () => {
    const propsWithConfig = {
      ...baseProps,
      draftValue: { ...baseDraftValue, config: ["config1", "config2"] },
    };

    render(<ConfigParametersSection {...propsWithConfig} />);

    expect(screen.getByText("Selected Config Parameters")).toBeInTheDocument();
    expect(screen.getByText("@config1")).toBeInTheDocument();
    expect(screen.getByText("@config2")).toBeInTheDocument();
  });

  it("removes config parameter when remove button clicked", () => {
    const propsWithConfig = {
      ...baseProps,
      draftValue: { ...baseDraftValue, config: ["config1", "config2"] },
    };

    render(<ConfigParametersSection {...propsWithConfig} />);

    const removeButtons = screen.getAllByTestId("remove-button");
    fireEvent.click(removeButtons[0]);

    expect(mockUpdateDraftValue).toHaveBeenCalledWith({ config: ["config2"] });
  });

  it("handles empty config array", () => {
    render(<ConfigParametersSection {...baseProps} />);

    expect(
      screen.queryByText("Selected Config Parameters"),
    ).not.toBeInTheDocument();
    expect(screen.getByTestId("selected-count")).toHaveTextContent("0");
  });
});
