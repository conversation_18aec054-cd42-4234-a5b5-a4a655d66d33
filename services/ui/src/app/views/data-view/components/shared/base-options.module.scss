.dataSourceContainer {
  margin-bottom: 1rem;
  padding: 1rem;
  border: 1px solid var(--cds-border-subtle);
  border-radius: 4px;
  position: relative;
}

.dataSourceHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.dataSourceTitle {
  margin: 0;
}

.dataSourceField {
  margin-bottom: 1rem;
}

.addButton {
  margin-bottom: 1rem;
}

.emptyStateText {
  color: var(--cds-text-secondary);
  font-style: italic;
  text-align: center;
  padding: 2rem;
}

.singleDataSourceContainer {
  padding: 1rem;
  border: 1px solid var(--cds-border-subtle);
  border-radius: 4px;
}

.parametersContainer {
  margin-bottom: 1rem;
}

.parameterDescription {
  color: var(--cds-text-secondary);
  margin-bottom: 1rem;
}

.parameterItem {
  margin-bottom: 0.5rem;
  padding: 0.5rem;
  background-color: var(--cds-layer-01);
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.parameterCode {
  min-width: 120px;
  flex-shrink: 0;
}

.parameterCheckbox {
  margin-left: auto;
}

.categoryFilterDescription {
  color: var(--cds-text-secondary);
  font-size: 12px;
  margin-top: 0.25rem;
  margin-bottom: 1rem;
  line-height: 1.4;
}

.inputWithCopyButton {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.copyButton {
  margin-bottom: 1rem;
  background-color: transparent;
}
