import { vi } from "vitest";
import { render, screen } from "../../../../../../test-utils";
import { TabularRenderer } from "./tabular-renderer";
import type { DataQueryResult } from "../../../types/data-query-types";

// Mock Datagrid component
vi.mock("../../../../../components/datagrid", () => ({
  Datagrid: ({ columns, data, mode }: any) => (
    <div data-testid="datagrid">
      <div data-testid="datagrid-mode">{mode}</div>
      <div data-testid="datagrid-columns">
        {JSON.stringify(columns?.length)}
      </div>
      <div data-testid="datagrid-data">{JSON.stringify(data?.length)}</div>
    </div>
  ),
}));

// Mock @tanstack/react-table
vi.mock("@tanstack/react-table", () => ({
  createColumnHelper: () => ({
    accessor: (key: string, config: any) => ({
      accessorKey: key,
      header: config.header,
      cell: config.cell,
    }),
  }),
}));

describe("TabularRenderer", () => {
  const mockQueryResult: DataQueryResult = {
    rows: [
      { id: 1, name: "John", age: 30 },
      { id: 2, name: "Jane", age: 25 },
    ],
    fields: [
      { name: "id", label: "ID", type: "number" },
      { name: "name", label: "Name", type: "string" },
      { name: "age", label: "Age", type: "number" },
    ],
    validation: { isValid: true },
    type: "tabular",
    metadata: {},
    queryRequest: "",
  };

  it("renders datagrid with data", () => {
    render(<TabularRenderer queryResult={mockQueryResult} />);

    expect(screen.getByTestId("datagrid")).toBeInTheDocument();
    expect(screen.getByTestId("datagrid-mode")).toHaveTextContent("client");
    expect(screen.getByTestId("datagrid-columns")).toHaveTextContent("3");
    expect(screen.getByTestId("datagrid-data")).toHaveTextContent("2");
  });

  it("shows empty message when no data available", () => {
    const emptyQueryResult: DataQueryResult = {
      rows: [],
      fields: [
        { name: "id", label: "ID", type: "number" },
        { name: "name", label: "Name", type: "string" },
      ],
      validation: { isValid: true },
      type: "tabular",
      metadata: {},
      queryRequest: "",
    };

    render(<TabularRenderer queryResult={emptyQueryResult} />);

    expect(screen.getByText("No tabular data to display.")).toBeInTheDocument();
    expect(screen.queryByTestId("datagrid")).not.toBeInTheDocument();
  });

  it("shows empty message when rows is null", () => {
    const nullRowsQueryResult: DataQueryResult = {
      rows: null as any,
      fields: [{ name: "id", label: "ID", type: "number" }],
      validation: { isValid: true },
      type: "tabular",
      metadata: {},
      queryRequest: "",
    };

    render(<TabularRenderer queryResult={nullRowsQueryResult} />);

    expect(screen.getByText("No tabular data to display.")).toBeInTheDocument();
    expect(screen.queryByTestId("datagrid")).not.toBeInTheDocument();
  });
});
