import React from "react";
import { DataQueryResult } from "../../../types/data-query-types";
import classes from "./renderer.module.css";

interface SingleValueRendererProps {
  queryResult: DataQueryResult;
}

export const SingleValueRenderer: React.FC<SingleValueRendererProps> = ({
  queryResult,
}) => {
  const { rows, fields } = queryResult;

  // Get the first value from the first row
  const firstRow = rows?.[0];
  const firstField = fields?.[0];
  const value = firstRow && firstField ? firstRow[firstField.name] : undefined;

  return (
    <div className={classes.rendererContainer}>
      {/* Single Value Display */}
      <div className={classes.valueContainer}>
        <div className={classes.valueDisplay}>
          <span className={classes.valueText}>
            {value !== undefined ? String(value) : "No value"}
          </span>
          {firstField?.unit && (
            <span className={classes.valueUnit}>{firstField.unit}</span>
          )}
        </div>
        {firstField?.label && (
          <div className={classes.valueLabel}>{firstField.label}</div>
        )}
      </div>
    </div>
  );
};
