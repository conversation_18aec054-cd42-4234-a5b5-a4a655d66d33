.rendererContainer {
  height: 100%;
  overflow: hidden;
}

.validationStatus {
  padding: 0.5rem 1rem;
  border-radius: 4px;
  background-color: var(--cds-layer-01);
  border: 1px solid var(--cds-border-subtle-01);
}

.validIcon {
  color: var(--cds-support-success);
}

.errorIcon {
  color: var(--cds-support-error);
}

.validText {
  color: var(--cds-support-success);
  font-weight: 500;
}

.errorText {
  color: var(--cds-support-error);
  font-weight: 500;
}

.validationReason {
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: var(--cds-text-secondary);
  font-style: italic;
}

.valueContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 150px;
  padding: 2rem;
  background-color: var(--cds-layer-01);
  border-radius: 8px;
}

.valueDisplay {
  display: flex;
  align-items: baseline;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.valueText {
  font-size: 2.5rem;
  font-weight: 600;
  color: var(--cds-text-primary);
}

.valueUnit {
  font-size: 1.2rem;
  color: var(--cds-text-secondary);
  font-weight: 400;
}

.valueLabel {
  font-size: 1rem;
  color: var(--cds-text-secondary);
  text-align: center;
}

.emptyMessage {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--cds-text-secondary);
  font-style: italic;
}

.errorMessage {
  padding: 1rem;
  background-color: var(--cds-support-error-inverse);
  border: 1px solid var(--cds-support-error);
  border-radius: 4px;
  color: var(--cds-text-error);
}

.loadingMessage {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--cds-text-secondary);
}

.chartWrapper {
  position: relative;
  width: 100%;
  height: 100%;
}

.floatingContentSwitcher {
  position: absolute;
  top: 1rem;
  right: 1rem;
  z-index: 10;
  background-color: var(--cds-layer-accent-01);
  border-radius: 8px;
  box-shadow: var(--cds-shadow);
  padding: 0.25rem;
}
