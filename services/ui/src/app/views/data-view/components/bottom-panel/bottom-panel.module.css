.bottomPanel {
  position: relative;
  background: var(--cds-layer-01);
  border-top: 1px solid var(--cds-border-subtle);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.animateHeightTransition {
  transition: height 0.3s ease;
}

.resizeHandle {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  cursor: row-resize;
  z-index: 10;
  background: transparent;
}

.resizeHandle:hover {
  background: var(--cds-border-interactive);
}

.content {
  height: 100%;
  flex: 1;
  overflow: hidden;
  padding: 0;
}

.tabHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-right: 1rem;
}

.executeButton {
  flex-shrink: 0;
}

.tabContent {
  height: calc(100% - 48px); /* Account for tab header height */
  overflow: auto;
  /* padding: 1rem; */
}

.metadataContent {
  height: 100%;
  overflow: auto;
}

.valueDisplay {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
}

.valueText {
  font-size: 3rem;
  font-weight: 600;
  color: var(--cds-text-primary);
  text-align: center;
}

.errorMessage {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--cds-text-error);
  text-align: center;
}

.loadingMessage {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--cds-text-secondary);
  text-align: center;
}

.emptyMessage {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--cds-text-secondary);
  text-align: center;
  font-style: italic;
}

:global(.cds--tab-content) {
  height: 100% !important;
}

/* Ensure tab panel content takes full height */
:global([id*="tabpanel"]) {
  height: 100% !important;
}
