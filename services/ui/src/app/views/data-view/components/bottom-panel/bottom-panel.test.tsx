import { vi } from "vitest";
import { render, screen, fireEvent } from "../../../../../test-utils";
import { BottomPanel } from "./bottom-panel";
import type { DataQuery, DataQueryResult } from "../../types/data-query-types";

// Mock dependencies
vi.mock("../../../../config/hooks/use-config", () => ({
  useConfigSetting: vi.fn(() => ({
    setting: { value: "local" },
  })),
}));

vi.mock("../../../../hooks/use-dates", () => ({
  useDates: vi.fn(() => ({
    startDate: new Date("2024-01-01T00:00:00.000Z"),
    endDate: new Date("2024-01-01T23:59:59.999Z"),
  })),
}));

vi.mock("../../../../components/filter-combo-box", () => ({
  FilterComboBox: ({ titleText }: any) => (
    <div data-testid="filter-combo-box">{titleText}</div>
  ),
}));

vi.mock("../../../../components/data-query-property-input", () => ({
  DataQueryPropertyInput: ({ property }: any) => (
    <div data-testid="data-query-property-input">{property.id}</div>
  ),
}));

vi.mock("../../../../components/datagrid", () => ({
  Datagrid: ({ data, columns }: any) => {
    // Distinguish between response data and metadata based on data structure
    const isMetadata = data?.[0]?.name && data?.[0]?.value;
    const testId = isMetadata ? "metadata-datagrid" : "response-datagrid";

    return (
      <div data-testid={testId}>
        <div data-testid="datagrid-rows">{data?.length || 0}</div>
        <div data-testid="datagrid-columns">{columns?.length || 0}</div>
      </div>
    );
  },
}));

vi.mock("./renderers", () => ({
  SingleValueRenderer: ({ queryResult }: any) => (
    <div data-testid="single-value-renderer">{queryResult.type}</div>
  ),
  TabularRenderer: ({ queryResult }: any) => (
    <div data-testid="tabular-renderer">{queryResult.type}</div>
  ),
  CategorySeriesRenderer: ({ queryResult }: any) => (
    <div data-testid="category-series-renderer">{queryResult.type}</div>
  ),
  TimeSeriesRenderer: ({ queryResult }: any) => (
    <div data-testid="time-series-renderer">{queryResult.type}</div>
  ),
}));

// Mock Carbon React components
vi.mock("@carbon/react", async () => {
  const actual = await vi.importActual("@carbon/react");
  return {
    ...actual,
    GlobalTheme: ({ children }: { children: React.ReactNode }) => children,
  };
});

describe("BottomPanel", () => {
  const mockOnExecute = vi.fn();
  const mockOnRequestChange = vi.fn();
  const mockOnCopyQuery = vi.fn();

  const baseProps = {
    isVisible: true,
    onExecute: mockOnExecute,
    onRequestChange: mockOnRequestChange,
    onCopyQuery: mockOnCopyQuery,
    isExecuteDisabled: false,
    isExecuting: false,
    isCopyingQuery: false,
  };

  const mockDataQuery: DataQuery = {
    id: "test-query",
    label: "Test Query",
    description: "Test description",
    type: "singleValue",
    isDraft: false,
    metadata: { category: "test" },
    dataSources: [],
    filters: [],
    parameters: { required: [] },
    query: "SELECT * FROM test",
  };

  const mockQueryResult: DataQueryResult = {
    rows: [{ id: 1, name: "John" }],
    fields: [
      { name: "id", label: "ID", type: "number" },
      { name: "name", label: "Name", type: "string" },
    ],
    validation: { isValid: true },
    type: "singleValue",
    metadata: { jobId: "test-job-123" },
    queryRequest: "SELECT * FROM test",
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("visibility", () => {
    it("renders when visible", () => {
      render(<BottomPanel {...baseProps} />);

      expect(screen.getByText("Request")).toBeInTheDocument();
      expect(screen.getByText("Response")).toBeInTheDocument();
    });

    it("does not render when not visible", () => {
      render(<BottomPanel {...baseProps} isVisible={false} />);

      expect(screen.queryByText("Request")).not.toBeInTheDocument();
    });
  });

  describe("tabs", () => {
    it("renders all tabs", () => {
      render(<BottomPanel {...baseProps} />);

      expect(screen.getByText("Request")).toBeInTheDocument();
      expect(screen.getByText("Response")).toBeInTheDocument();
      expect(screen.getByText("Result View")).toBeInTheDocument();
      expect(screen.getByText("Metadata")).toBeInTheDocument();
    });

    it("starts with Request tab selected", () => {
      render(<BottomPanel {...baseProps} />);

      // Check for date range selector which is in the Request tab
      expect(screen.getByText("Date Range")).toBeInTheDocument();
    });

    it("switches tabs when clicked", () => {
      render(<BottomPanel {...baseProps} queryResult={mockQueryResult} />);

      // Click Response tab
      fireEvent.click(screen.getByText("Response"));

      // Should show validation status in Response tab
      expect(screen.getByText("Valid Result")).toBeInTheDocument();
    });
  });

  describe("execute functionality", () => {
    it("shows execute button", () => {
      render(<BottomPanel {...baseProps} />);

      expect(screen.getByText("Execute")).toBeInTheDocument();
    });

    it("calls onExecute when execute button clicked", () => {
      render(<BottomPanel {...baseProps} />);

      fireEvent.click(screen.getByText("Execute"));

      expect(mockOnExecute).toHaveBeenCalledTimes(1);
    });

    it("shows executing state", () => {
      render(<BottomPanel {...baseProps} isExecuting={true} />);

      expect(screen.getByText("Executing...")).toBeInTheDocument();
    });

    it("disables execute button when disabled", () => {
      render(<BottomPanel {...baseProps} isExecuteDisabled={true} />);

      const executeButton = screen.getByText("Execute");
      expect(executeButton).toBeDisabled();
    });
  });

  describe("copy query functionality", () => {
    it("shows copy query button", () => {
      render(<BottomPanel {...baseProps} />);

      expect(screen.getByText("Copy Query")).toBeInTheDocument();
    });

    it("calls onCopyQuery when copy button clicked", () => {
      render(<BottomPanel {...baseProps} />);

      fireEvent.click(screen.getByText("Copy Query"));

      expect(mockOnCopyQuery).toHaveBeenCalledTimes(1);
    });

    it("shows copying state", () => {
      render(<BottomPanel {...baseProps} isCopyingQuery={true} />);

      expect(screen.getByText("Copying...")).toBeInTheDocument();
    });
  });

  describe("response tab", () => {
    it("shows loading state", () => {
      render(<BottomPanel {...baseProps} isLoading={true} />);

      fireEvent.click(screen.getByText("Response"));
      // Use getAllByText since the same message appears in multiple tabs
      const loadingMessages = screen.getAllByText("Executing query...");
      expect(loadingMessages.length).toBeGreaterThan(0);
    });

    it("shows error state", () => {
      render(<BottomPanel {...baseProps} error="Test error" />);

      fireEvent.click(screen.getByText("Response"));
      // Use getAllByText since the same message appears in multiple tabs
      const errorMessages = screen.getAllByText(
        /Error executing query: Test error/,
      );
      expect(errorMessages.length).toBeGreaterThan(0);
    });

    it("shows query result data", () => {
      render(<BottomPanel {...baseProps} queryResult={mockQueryResult} />);

      fireEvent.click(screen.getByText("Response"));
      expect(screen.getByText("Valid Result")).toBeInTheDocument();
      expect(screen.getByTestId("response-datagrid")).toBeInTheDocument();
    });

    it("shows empty message when no data", () => {
      render(<BottomPanel {...baseProps} />);

      fireEvent.click(screen.getByText("Response"));
      expect(
        screen.getByText("No data to display. Execute a query to see results."),
      ).toBeInTheDocument();
    });
  });

  describe("result view tab", () => {
    it("renders correct renderer based on query result type", () => {
      const singleValueResult = {
        ...mockQueryResult,
        type: "singleValue" as const,
      };
      render(<BottomPanel {...baseProps} queryResult={singleValueResult} />);

      fireEvent.click(screen.getByText("Result View"));
      expect(screen.getByTestId("single-value-renderer")).toBeInTheDocument();
    });

    it("renders tabular renderer", () => {
      const tabularResult = { ...mockQueryResult, type: "tabular" as const };
      render(<BottomPanel {...baseProps} queryResult={tabularResult} />);

      fireEvent.click(screen.getByText("Result View"));
      expect(screen.getByTestId("tabular-renderer")).toBeInTheDocument();
    });

    it("renders category series renderer", () => {
      const categoryResult = {
        ...mockQueryResult,
        type: "categorySeries" as const,
      };
      render(<BottomPanel {...baseProps} queryResult={categoryResult} />);

      fireEvent.click(screen.getByText("Result View"));
      expect(
        screen.getByTestId("category-series-renderer"),
      ).toBeInTheDocument();
    });

    it("renders time series renderer", () => {
      const timeSeriesResult = {
        ...mockQueryResult,
        type: "timeSeries" as const,
      };
      render(<BottomPanel {...baseProps} queryResult={timeSeriesResult} />);

      fireEvent.click(screen.getByText("Result View"));
      expect(screen.getByTestId("time-series-renderer")).toBeInTheDocument();
    });
  });

  describe("metadata tab", () => {
    it("shows metadata when available", () => {
      render(<BottomPanel {...baseProps} queryResult={mockQueryResult} />);

      fireEvent.click(screen.getByText("Metadata"));
      expect(screen.getByTestId("metadata-datagrid")).toBeInTheDocument();
    });

    it("shows empty message when no metadata", () => {
      render(<BottomPanel {...baseProps} />);

      fireEvent.click(screen.getByText("Metadata"));
      expect(
        screen.getByText(
          "No metadata to display. Execute a query to see metadata.",
        ),
      ).toBeInTheDocument();
    });
  });

  describe("filters and properties", () => {
    it("renders filter combo boxes", () => {
      const queryWithFilters = {
        ...mockDataQuery,
        filters: ["facility-filter"],
      };
      render(<BottomPanel {...baseProps} dataQuery={queryWithFilters} />);

      expect(screen.getByTestId("filter-combo-box")).toBeInTheDocument();
    });

    it("renders property inputs", () => {
      const queryWithProperties = {
        ...mockDataQuery,
        queryProperties: [
          { id: "limit", type: "limit" as const, defaultValue: 100 },
        ],
      };
      render(<BottomPanel {...baseProps} dataQuery={queryWithProperties} />);

      expect(
        screen.getByTestId("data-query-property-input"),
      ).toBeInTheDocument();
    });
  });

  describe("resize functionality", () => {
    it("renders resize handle", () => {
      render(<BottomPanel {...baseProps} />);

      expect(screen.getByTestId("resize-handle")).toBeInTheDocument();
    });

    it("starts resize on mouse down", () => {
      render(<BottomPanel {...baseProps} />);

      const resizeHandle = screen.getByTestId("resize-handle");
      fireEvent.mouseDown(resizeHandle);

      // This is difficult to test without more complex mocking of getBoundingClientRect
      // but we can at least verify the resize handle exists
      expect(resizeHandle).toBeInTheDocument();
    });
  });
});
