import { render, screen } from "../../../../../../test-utils";
import { SingleValueRenderer } from "./single-value-renderer";
import type { DataQueryResult } from "../../../types/data-query-types";

describe("SingleValueRenderer", () => {
  it("renders single value with label and unit", () => {
    const mockQueryResult: DataQueryResult = {
      rows: [{ temperature: 25.5 }],
      fields: [
        {
          name: "temperature",
          label: "Temperature",
          type: "number",
          unit: "°C",
        },
      ],
      validation: { isValid: true },
      type: "singleValue",
      metadata: {},
      queryRequest: "",
    };

    render(<SingleValueRenderer queryResult={mockQueryResult} />);

    expect(screen.getByText("25.5")).toBeInTheDocument();
    expect(screen.getByText("°C")).toBeInTheDocument();
    expect(screen.getByText("Temperature")).toBeInTheDocument();
  });

  it("renders single value without unit", () => {
    const mockQueryResult: DataQueryResult = {
      rows: [{ count: 42 }],
      fields: [
        {
          name: "count",
          label: "Total Count",
          type: "number",
        },
      ],
      validation: { isValid: true },
      type: "singleValue",
      metadata: {},
      queryRequest: "",
    };

    render(<SingleValueRenderer queryResult={mockQueryResult} />);

    expect(screen.getByText("42")).toBeInTheDocument();
    expect(screen.getByText("Total Count")).toBeInTheDocument();
    expect(screen.queryByText("°C")).not.toBeInTheDocument();
  });

  it("renders 'No value' when no data available", () => {
    const mockQueryResult: DataQueryResult = {
      rows: [],
      fields: [],
      validation: { isValid: true },
      type: "singleValue",
      metadata: {},
      queryRequest: "",
    };

    render(<SingleValueRenderer queryResult={mockQueryResult} />);

    expect(screen.getByText("No value")).toBeInTheDocument();
  });

  it("renders 'No value' when value is undefined", () => {
    const mockQueryResult: DataQueryResult = {
      rows: [{ temperature: undefined }],
      fields: [
        {
          name: "temperature",
          label: "Temperature",
          type: "number",
        },
      ],
      validation: { isValid: true },
      type: "singleValue",
      metadata: {},
      queryRequest: "",
    };

    render(<SingleValueRenderer queryResult={mockQueryResult} />);

    expect(screen.getByText("No value")).toBeInTheDocument();
  });
});
