import React, { useState } from "react";
import { ContentSwitcher, IconSwitch } from "@carbon/react";
import { ChartColumn, ChartStacked } from "@carbon/react/icons";
import { DataQueryResult } from "../../../types/data-query-types";
import { CategoryChartComponent } from "../../../../../components/category-chart/category-chart-component";
import { ChartStyle } from "../../../../../components/combo-chart/types";
import classes from "./renderer.module.css";
import { useMeasure } from "@uidotdev/usehooks";

interface CategorySeriesRendererProps {
  queryResult: DataQueryResult;
}

const chartStyleOptions = [
  { id: "column", text: "Grouped Bars", icon: ChartColumn },
  { id: "stacked-column", text: "Stacked Bars", icon: ChartStacked },
];

export const CategorySeriesRenderer: React.FC<CategorySeriesRendererProps> = ({
  queryResult,
}) => {
  const { validation, rows, fields } = queryResult;
  const [ref, { height }] = useMeasure();
  const [selectedChartStyle, setSelectedChartStyle] =
    useState<ChartStyle>("stacked-column");
  const [selectedIndex, setSelectedIndex] = useState(1); // Default to stacked
  const orientation = "horizontal"; // Fixed to horizontal for now

  // Get all numeric fields (excluding the first field which is the category)
  const valueFields = fields.slice(1).filter((field) => {
    // Check if field type indicates it's numeric
    return field.type === "number";
  });

  return (
    <div className={classes.rendererContainer} ref={ref}>
      {/* Category Series Chart */}
      {validation.isValid &&
      rows &&
      rows.length > 0 &&
      valueFields.length > 0 ? (
        <div className={classes.chartWrapper}>
          <CategoryChartComponent
            dataQueryResult={queryResult}
            height={height ? `${height}px` : "400px"}
            chartStyle={selectedChartStyle}
            orientation={orientation}
            showLegend={valueFields.length > 1}
          />
          <div className={classes.floatingContentSwitcher}>
            <div style={{ marginBottom: "8px" }}></div>
            <ContentSwitcher
              selectedIndex={selectedIndex}
              size="sm"
              onChange={({ index }) => {
                if (index !== undefined) {
                  setSelectedIndex(index);
                  setSelectedChartStyle(
                    chartStyleOptions[index].id as ChartStyle,
                  );
                }
              }}
            >
              {chartStyleOptions.map((option) => {
                const IconComponent = option.icon;
                return (
                  <IconSwitch
                    key={option.id}
                    name={option.id}
                    text={option.text}
                  >
                    <IconComponent />
                  </IconSwitch>
                );
              })}
            </ContentSwitcher>
          </div>
        </div>
      ) : (
        <div className={classes.emptyMessage}>
          <p>
            {!validation.isValid
              ? "Cannot display chart due to validation errors."
              : "No category series data to display."}
          </p>
        </div>
      )}
    </div>
  );
};
