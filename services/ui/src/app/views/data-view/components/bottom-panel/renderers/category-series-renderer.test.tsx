import { vi } from "vitest";
import { render, screen, fireEvent } from "../../../../../../test-utils";
import { CategorySeriesRenderer } from "./category-series-renderer";
import type { DataQueryResult } from "../../../types/data-query-types";

// Mock CategoryChartComponent
vi.mock(
  "../../../../../components/category-chart/category-chart-component",
  () => ({
    CategoryChartComponent: ({ dataQueryResult, height, chartStyle }: any) => (
      <div data-testid="category-chart">
        <div data-testid="chart-height">{height}</div>
        <div data-testid="chart-style">{chartStyle}</div>
        <div data-testid="chart-data">{JSON.stringify(dataQueryResult)}</div>
      </div>
    ),
  }),
);

// Mock useMeasure hook
vi.mock("@uidotdev/usehooks", () => ({
  useMeasure: () => [() => {}, { height: 400 }],
}));

// Mock Carbon React components
vi.mock("@carbon/react", async () => {
  const actual = await vi.importActual("@carbon/react");
  return {
    ...actual,
    ContentSwitcher: ({ children, onChange, selectedIndex }: any) => (
      <div data-testid="content-switcher">
        <button
          data-testid="chart-style-button"
          onClick={() => onChange({ index: selectedIndex === 0 ? 1 : 0 })}
        >
          Toggle Chart Style
        </button>
        {children}
      </div>
    ),
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    IconSwitch: ({ children, name, text }: any) => (
      <div data-testid={`icon-switch-${name}`}>{text}</div>
    ),
    GlobalTheme: ({ children }: { children: React.ReactNode }) => children,
  };
});

describe("CategorySeriesRenderer", () => {
  const mockQueryResult: DataQueryResult = {
    rows: [
      { category: "A", value1: 10, value2: 20 },
      { category: "B", value1: 15, value2: 25 },
    ],
    fields: [
      { name: "category", label: "Category", type: "string" },
      { name: "value1", label: "Value 1", type: "number" },
      { name: "value2", label: "Value 2", type: "number" },
    ],
    validation: { isValid: true },
    type: "categorySeries",
    metadata: {},
    queryRequest: "",
  };

  it("renders category chart when data is valid", () => {
    render(<CategorySeriesRenderer queryResult={mockQueryResult} />);

    expect(screen.getByTestId("category-chart")).toBeInTheDocument();
    expect(screen.getByTestId("chart-height")).toHaveTextContent("400px");
    expect(screen.getByTestId("chart-style")).toHaveTextContent(
      "stacked-column",
    );
  });

  it("shows chart style switcher", () => {
    render(<CategorySeriesRenderer queryResult={mockQueryResult} />);

    expect(screen.getByTestId("content-switcher")).toBeInTheDocument();
    expect(screen.getByTestId("icon-switch-column")).toBeInTheDocument();
    expect(
      screen.getByTestId("icon-switch-stacked-column"),
    ).toBeInTheDocument();
  });

  it("changes chart style when switcher is used", () => {
    render(<CategorySeriesRenderer queryResult={mockQueryResult} />);

    const button = screen.getByTestId("chart-style-button");
    fireEvent.click(button);

    expect(screen.getByTestId("chart-style")).toHaveTextContent("column");
  });

  it("shows empty message when validation is invalid", () => {
    const invalidQueryResult: DataQueryResult = {
      ...mockQueryResult,
      validation: { isValid: false },
    };

    render(<CategorySeriesRenderer queryResult={invalidQueryResult} />);

    expect(
      screen.getByText("Cannot display chart due to validation errors."),
    ).toBeInTheDocument();
    expect(screen.queryByTestId("category-chart")).not.toBeInTheDocument();
  });

  it("shows empty message when no data available", () => {
    const emptyQueryResult: DataQueryResult = {
      rows: [],
      fields: [],
      validation: { isValid: true },
      type: "categorySeries",
      metadata: {},
      queryRequest: "",
    };

    render(<CategorySeriesRenderer queryResult={emptyQueryResult} />);

    expect(
      screen.getByText("No category series data to display."),
    ).toBeInTheDocument();
    expect(screen.queryByTestId("category-chart")).not.toBeInTheDocument();
  });

  it("shows empty message when no numeric fields available", () => {
    const noNumericQueryResult: DataQueryResult = {
      rows: [{ category: "A", text: "some text" }],
      fields: [
        { name: "category", label: "Category", type: "string" },
        { name: "text", label: "Text", type: "string" },
      ],
      validation: { isValid: true },
      type: "categorySeries",
      metadata: {},
      queryRequest: "",
    };

    render(<CategorySeriesRenderer queryResult={noNumericQueryResult} />);

    expect(
      screen.getByText("No category series data to display."),
    ).toBeInTheDocument();
  });
});
