import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  TabPanel,
  TabPanels,
  <PERSON><PERSON>,
} from "@carbon/react";
import { CheckmarkFilled, <PERSON><PERSON>, ErrorFilled, Play } from "@carbon/react/icons";
import { createColumnHelper } from "@tanstack/react-table";
import { DateTime } from "luxon";
import {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from "react";
import { useTranslation } from "react-i18next";
import { Datagrid } from "../../../../components/datagrid";
import { DataQueryPropertyInput } from "../../../../components/data-query-property-input";
import { FilterComboBox } from "../../../../components/filter-combo-box";
import type { FilterSelection } from "../../../../components/filter-combo-box/filter-combo-box";
import { useConfigSetting } from "../../../../config/hooks/use-config";
import { useDates } from "../../../../hooks/use-dates";
import { allDateRangeOptions, DatePeriod } from "../../../../types/date-types";
import { toSiteDate } from "../../../../utils/date-util";
import { DataQuery, DataQueryResult } from "../../types/data-query-types";
import classes from "./bottom-panel.module.css";
import {
  CategorySeriesRenderer,
  SingleValueRenderer,
  TabularRenderer,
  TimeSeriesRenderer,
} from "./renderers";

export interface RequestFilters {
  startDate: string;
  endDate: string;
  [key: string]: string | string[] | number | null | undefined;
}

export interface BottomPanelRef {
  switchToResponseTab: () => void;
}

interface BottomPanelProps {
  isVisible: boolean;
  defaultHeight?: number;
  minHeight?: number;
  maxHeight?: number;
  queryResult?: DataQueryResult | null;
  isLoading?: boolean;
  error?: string;
  onExecute?: () => void;
  isExecuteDisabled?: boolean;
  isExecuting?: boolean;
  dataQuery?: DataQuery | null;
  onRequestChange?: (filters: RequestFilters) => void;
  onCopyQuery?: () => void;
  isCopyingQuery?: boolean;
}

export const BottomPanel = forwardRef<BottomPanelRef, BottomPanelProps>(
  (
    {
      isVisible,
      defaultHeight = 300,
      minHeight = 150,
      maxHeight = 600,
      queryResult,
      isLoading = false,
      error,
      onExecute,
      isExecuteDisabled = false,
      isExecuting = false,
      dataQuery,
      onRequestChange,
      onCopyQuery,
      isCopyingQuery = false,
    },
    ref,
  ) => {
    const { t } = useTranslation();
    const [height, setHeight] = useState(defaultHeight);
    const [isResizing, setIsResizing] = useState(false);
    const [selectedTabIndex, setSelectedTabIndex] = useState(0);
    const panelRef = useRef<HTMLDivElement>(null);

    // Get timezone configuration
    const { setting: timezoneConfig } = useConfigSetting("site-time-zone");
    const timezone = (timezoneConfig?.value as string) ?? "local";

    // Expose methods to parent component
    useImperativeHandle(ref, () => ({
      switchToResponseTab: () => setSelectedTabIndex(1),
    }));

    // State for request filters
    const [selectedDateRange, setSelectedDateRange] = useState<DatePeriod>(
      DatePeriod.today,
    );
    const [filterValues, setFilterValues] = useState<
      Record<string, string | string[]>
    >({});
    const [propertyValues, setPropertyValues] = useState<
      Record<string, string | number>
    >({});

    // Get start and end dates from selected date range
    const { startDate, endDate } = useDates(selectedDateRange);

    useEffect(() => {
      const initialPropertyValues = dataQuery?.queryProperties?.reduce(
        (acc, property) => {
          acc[property.id] = property.defaultValue;
          return acc;
        },
        {} as Record<string, string | number>,
      );
      setPropertyValues(initialPropertyValues || {});
    }, [dataQuery]);

    // Emit changes to parent when filters change
    useEffect(() => {
      if (onRequestChange) {
        const requestFilters: RequestFilters = {
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
          ...filterValues,
          ...propertyValues,
        };
        onRequestChange(requestFilters);
      }
    }, [startDate, endDate, filterValues, propertyValues, onRequestChange]);

    // Handler for date range changes
    const handleDateRangeChange = useCallback(
      (event: React.ChangeEvent<HTMLSelectElement>) => {
        const value = event.target.value as DatePeriod;
        setSelectedDateRange(value);
      },
      [],
    );

    // Handler for filter changes
    const handleFilterChange = useCallback(
      (filterId: string, selection: FilterSelection) => {
        setFilterValues((prev) => {
          const newFilterValues = { ...prev };
          if (selection.value === null) {
            delete newFilterValues[filterId];
          } else {
            newFilterValues[filterId] = selection.value;
          }
          return newFilterValues;
        });
      },
      [],
    );

    // Handler for property changes
    const handlePropertyChange = useCallback(
      (propertyId: string, value: string | number) => {
        setPropertyValues((prev) => ({
          ...prev,
          [propertyId]: value,
        }));
      },
      [],
    );

    // Handler for execute that switches to Response tab
    const handleExecute = useCallback(() => {
      if (onExecute) {
        onExecute();
        // Switch to Response tab (index 1)
        setSelectedTabIndex(1);
      }
    }, [onExecute]);

    // Handler for tab changes
    const handleTabChange = useCallback((event: { selectedIndex: number }) => {
      setSelectedTabIndex(event.selectedIndex);
    }, []);

    const startResize = useCallback((e: React.MouseEvent) => {
      e.preventDefault();
      setIsResizing(true);
    }, []);

    const handleMouseMove = useCallback(
      (e: MouseEvent) => {
        if (!isResizing || !panelRef.current) return;

        const panelRect = panelRef.current.getBoundingClientRect();
        const newHeight = panelRect.bottom - e.clientY;

        // Constrain height within min/max bounds
        const constrainedHeight = Math.max(
          minHeight,
          Math.min(maxHeight, newHeight),
        );
        setHeight(constrainedHeight);
      },
      [isResizing, minHeight, maxHeight],
    );

    const stopResize = useCallback(() => {
      setIsResizing(false);
    }, []);

    useEffect(() => {
      if (isResizing) {
        document.addEventListener("mousemove", handleMouseMove);
        document.addEventListener("mouseup", stopResize);
        document.body.style.cursor = "row-resize";
        document.body.style.userSelect = "none";
      } else {
        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("mouseup", stopResize);
        document.body.style.cursor = "";
        document.body.style.userSelect = "";
      }

      return () => {
        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("mouseup", stopResize);
        document.body.style.cursor = "";
        document.body.style.userSelect = "";
      };
    }, [isResizing, handleMouseMove, stopResize]);

    // Create columns for the response datagrid
    const columnHelper = createColumnHelper<Record<string, unknown>>();

    // Extract columns from fields if available, otherwise from the first data row
    const columns = queryResult?.fields?.length
      ? queryResult.fields
      : queryResult?.rows?.[0]
        ? Object.keys(queryResult.rows[0]).map((name) => ({
            name,
            type: "string" as const,
          }))
        : [];

    const responseColumns = columns.map((col) =>
      columnHelper.accessor(col.name, {
        header: "label" in col ? col.label || col.name : col.name,
        cell: (info) => {
          const value = info.getValue();

          if (value == null) {
            return "";
          }

          // Handle date strings (common patterns from SQL/BigQuery)
          if (typeof value === "string" && value.match(/^\d{4}-\d{2}-\d{2}/)) {
            try {
              const siteDateObj = toSiteDate(value, timezone);
              if (siteDateObj.isValid) {
                return siteDateObj.toLocaleString(DateTime.DATETIME_SHORT);
              }
            } catch {
              // If date parsing fails, fall through to string conversion
            }
          }

          return String(value);
        },
      }),
    );

    // Create columns for metadata datagrid
    const metadataColumnHelper = createColumnHelper<{
      name: string;
      value: string;
    }>();
    const metadataColumns = [
      metadataColumnHelper.accessor("name", {
        header: t("dataView.bottomPanel.metadata.property", "Property"),
      }),
      metadataColumnHelper.accessor("value", {
        header: t("dataView.bottomPanel.metadata.value", "Value"),
        cell: (info) => {
          const name = info.row.original.name;
          const value = info.getValue();
          if (name === "jobId" && typeof value === "string" && value.trim()) {
            const url = `https://console.cloud.google.com/bigquery?project=edp-d-us-east2-etl&inv=1&invt=Ab5wqg&ws=!1m5!1m4!1m3!1sedp-d-us-east2-etl!2s${encodeURIComponent(
              value,
            )}!3sUS`;
            return (
              <a href={url} target="_blank" rel="noreferrer noopener">
                {value}
              </a>
            );
          }
          return String(value ?? "");
        },
      }),
    ];

    if (!isVisible) {
      return null;
    }

    return (
      <div
        ref={panelRef}
        className={`${classes.bottomPanel} ${!isResizing ? classes.animateHeightTransition : ""}`}
        style={{ height: isVisible ? height : 0 }}
      >
        <div
          className={classes.resizeHandle}
          onMouseDown={startResize}
          data-testid="resize-handle"
        />
        <div className={classes.content}>
          <Tabs selectedIndex={selectedTabIndex} onChange={handleTabChange}>
            <div className={classes.tabHeader}>
              <TabList
                aria-label={t(
                  "dataView.bottomPanel.tabs.ariaLabel",
                  "Query result tabs",
                )}
              >
                <Tab>{t("dataView.bottomPanel.tabs.request", "Request")}</Tab>
                <Tab>{t("dataView.bottomPanel.tabs.response", "Response")}</Tab>
                <Tab>
                  {t("dataView.bottomPanel.tabs.resultView", "Result View")}
                </Tab>
                <Tab>{t("dataView.bottomPanel.tabs.metadata", "Metadata")}</Tab>
              </TabList>
              {onExecute && (
                <Button
                  kind="primary"
                  size="sm"
                  renderIcon={Play}
                  onClick={handleExecute}
                  disabled={isExecuteDisabled}
                  className={classes.executeButton}
                >
                  {isExecuting
                    ? t(
                        "dataView.bottomPanel.buttons.executing",
                        "Executing...",
                      )
                    : t("dataView.bottomPanel.buttons.execute", "Execute")}
                </Button>
              )}
            </div>
            <TabPanels>
              <TabPanel>
                <div className={classes.tabContent}>
                  <div
                    style={{
                      padding: "1rem",
                      display: "flex",
                      flexDirection: "column",
                      gap: "1rem",
                    }}
                  >
                    {/* Date Range Selector */}
                    <Select
                      id="date-range-selector"
                      labelText={t(
                        "dataView.bottomPanel.request.dateRangeLabel",
                        "Date Range",
                      )}
                      value={selectedDateRange}
                      onChange={handleDateRangeChange}
                    >
                      {allDateRangeOptions.map((option) => (
                        <SelectItem
                          key={option.value}
                          value={option.value}
                          text={option.label}
                        />
                      ))}
                    </Select>

                    {/* Filter Combo Boxes */}
                    {dataQuery?.filters?.map((filterId) => (
                      <FilterComboBox
                        key={filterId}
                        startDate={startDate.toISOString()}
                        endDate={endDate.toISOString()}
                        allowMultiSelect={true}
                        dataQueryId={filterId}
                        onSelectionChange={(selection) =>
                          handleFilterChange(filterId, selection)
                        }
                        titleText={t(
                          "dataView.bottomPanel.request.filterTitle",
                          "Filter: {{filterId}}",
                          { filterId },
                        )}
                        placeholder={t(
                          "dataView.bottomPanel.request.filterPlaceholder",
                          "Select {{filterId}}...",
                          { filterId },
                        )}
                      />
                    ))}

                    {/* Query Property Inputs */}
                    {dataQuery?.queryProperties?.map((property) => (
                      <DataQueryPropertyInput
                        key={property.id}
                        property={property}
                        value={propertyValues[property.id]}
                        onChange={(value) =>
                          handlePropertyChange(property.id, value)
                        }
                      />
                    ))}

                    {/* Copy Query Button */}
                    <Stack orientation="horizontal" gap={6}>
                      <div /> {/* Spacer */}
                      {onCopyQuery && (
                        <Button
                          kind="secondary"
                          size="sm"
                          renderIcon={Copy}
                          onClick={onCopyQuery}
                          disabled={isCopyingQuery}
                        >
                          {isCopyingQuery
                            ? t(
                                "dataView.bottomPanel.buttons.copying",
                                "Copying...",
                              )
                            : t(
                                "dataView.bottomPanel.buttons.copyQuery",
                                "Copy Query",
                              )}
                        </Button>
                      )}
                    </Stack>
                  </div>
                </div>
              </TabPanel>
              <TabPanel>
                <div className={classes.tabContent}>
                  {error ? (
                    <div className={classes.errorMessage}>
                      <p>
                        {t(
                          "dataView.bottomPanel.response.errorExecuting",
                          "Error executing query: {{error}}",
                          { error },
                        )}
                      </p>
                    </div>
                  ) : isLoading ? (
                    <div className={classes.loadingMessage}>
                      <p>
                        {t(
                          "dataView.bottomPanel.response.executing",
                          "Executing query...",
                        )}
                      </p>
                    </div>
                  ) : queryResult ? (
                    <Stack orientation="vertical" gap={4}>
                      {/* Validation Status */}
                      <div style={{ padding: "0 1rem" }}>
                        <Stack orientation="horizontal" gap={3}>
                          {queryResult.validation.isValid ? (
                            <CheckmarkFilled
                              size={16}
                              style={{ color: "var(--cds-support-success)" }}
                            />
                          ) : (
                            <ErrorFilled
                              size={16}
                              style={{ color: "var(--cds-support-error)" }}
                            />
                          )}
                          <span
                            style={{
                              color: queryResult.validation.isValid
                                ? "var(--cds-support-success)"
                                : "var(--cds-support-error)",
                              fontWeight: "500",
                            }}
                          >
                            {queryResult.validation.isValid
                              ? t(
                                  "dataView.bottomPanel.response.validResult",
                                  "Valid Result",
                                )
                              : t(
                                  "dataView.bottomPanel.response.invalidResult",
                                  "Invalid Result",
                                )}
                          </span>
                        </Stack>
                        {!queryResult.validation.isValid &&
                          queryResult.validation.reason && (
                            <div
                              style={{
                                marginTop: "0.5rem",
                                padding: "0.5rem",
                                backgroundColor:
                                  "var(--cds-notification-background-error)",
                                borderRadius: "4px",
                                color: "var(--cds-text-primary)",
                                fontSize: "0.875rem",
                              }}
                            >
                              {queryResult.validation.reason}
                            </div>
                          )}
                      </div>

                      {/* Data Display */}
                      {queryResult.rows ? (
                        <Datagrid
                          columns={responseColumns}
                          data={queryResult.rows}
                          mode="client"
                          enableSelection={false}
                          showPagination={false}
                          showHeader={false}
                          initialDensity="compact"
                        />
                      ) : (
                        <div style={{ padding: "0 1rem" }}>
                          <p>
                            {t(
                              "dataView.bottomPanel.response.noDataRows",
                              "No data rows to display.",
                            )}
                          </p>
                        </div>
                      )}
                    </Stack>
                  ) : (
                    <div className={classes.emptyMessage}>
                      <p>
                        {t(
                          "dataView.bottomPanel.response.noDataToDisplay",
                          "No data to display. Execute a query to see results.",
                        )}
                      </p>
                    </div>
                  )}
                </div>
              </TabPanel>
              <TabPanel>
                <div className={classes.tabContent}>
                  {error ? (
                    <div className={classes.errorMessage}>
                      <p>
                        {t(
                          "dataView.bottomPanel.resultView.errorExecuting",
                          "Error executing query: {{error}}",
                          { error },
                        )}
                      </p>
                    </div>
                  ) : isLoading ? (
                    <div className={classes.loadingMessage}>
                      <p>
                        {t(
                          "dataView.bottomPanel.resultView.executing",
                          "Executing query...",
                        )}
                      </p>
                    </div>
                  ) : queryResult ? (
                    <>
                      {queryResult.type === "singleValue" && (
                        <SingleValueRenderer queryResult={queryResult} />
                      )}
                      {queryResult.type === "tabular" && (
                        <TabularRenderer queryResult={queryResult} />
                      )}
                      {queryResult.type === "categorySeries" && (
                        <CategorySeriesRenderer queryResult={queryResult} />
                      )}
                      {queryResult.type === "timeSeries" && (
                        <TimeSeriesRenderer queryResult={queryResult} />
                      )}
                    </>
                  ) : (
                    <div className={classes.emptyMessage}>
                      <p>
                        {t(
                          "dataView.bottomPanel.resultView.noResultToDisplay",
                          "No result to display. Execute a query to see the result.",
                        )}
                      </p>
                    </div>
                  )}
                </div>
              </TabPanel>
              <TabPanel>
                <div className={classes.tabContent}>
                  {error ? (
                    <div className={classes.errorMessage}>
                      <p>
                        {t(
                          "dataView.bottomPanel.metadata.errorExecuting",
                          "Error executing query: {{error}}",
                          { error },
                        )}
                      </p>
                    </div>
                  ) : isLoading ? (
                    <div className={classes.loadingMessage}>
                      <p>
                        {t(
                          "dataView.bottomPanel.metadata.executing",
                          "Executing query...",
                        )}
                      </p>
                    </div>
                  ) : queryResult?.metadata ? (
                    <div className={classes.metadataContent}>
                      <Datagrid
                        columns={metadataColumns}
                        data={Object.entries(queryResult.metadata).map(
                          ([name, value]) => ({
                            name,
                            value: String(value),
                          }),
                        )}
                        mode="client"
                        enableSelection={false}
                        showPagination={false}
                        showHeader={false}
                        initialDensity="compact"
                      />
                    </div>
                  ) : (
                    <div className={classes.emptyMessage}>
                      <p>
                        {t(
                          "dataView.bottomPanel.metadata.noMetadataToDisplay",
                          "No metadata to display. Execute a query to see metadata.",
                        )}
                      </p>
                    </div>
                  )}
                </div>
              </TabPanel>
            </TabPanels>
          </Tabs>
        </div>
      </div>
    );
  },
);

BottomPanel.displayName = "BottomPanel";
