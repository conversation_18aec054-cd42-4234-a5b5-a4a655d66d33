import React from "react";
import { createColumnHelper } from "@tanstack/react-table";
import { Datagrid } from "../../../../../components/datagrid";
import { DataQueryResult } from "../../../types/data-query-types";
import classes from "./renderer.module.css";

interface TabularRendererProps {
  queryResult: DataQueryResult;
}

export const TabularRenderer: React.FC<TabularRendererProps> = ({
  queryResult,
}) => {
  const { rows, fields } = queryResult;

  // Create columns from fields
  const columnHelper = createColumnHelper<Record<string, unknown>>();

  const columns = fields.map((field) =>
    columnHelper.accessor(field.name, {
      header: field.label || field.name,
      cell: (info) => {
        const value = info.getValue();
        return value !== null && value !== undefined ? String(value) : "";
      },
    }),
  );

  return (
    <div className={classes.rendererContainer}>
      {/* Table Data */}
      {rows && rows.length > 0 ? (
        <Datagrid
          columns={columns}
          data={rows}
          mode="client"
          enableSelection={false}
          showPagination={true}
          showHeader={true}
          initialDensity="compact"
        />
      ) : (
        <div className={classes.emptyMessage}>
          <p>No tabular data to display.</p>
        </div>
      )}
    </div>
  );
};
