import React, { useState } from "react";
import { ContentSwitcher, IconSwitch } from "@carbon/react";
import {
  ChartLine,
  ChartArea,
  ChartColumn,
  ChartStacked,
} from "@carbon/react/icons";
import { DataQueryResult } from "../../../types/data-query-types";
import { ComboChartComponent } from "../../../../../components/combo-chart/combo-chart-component";
import {
  TimeChartData,
  TimeChartSeriesData,
} from "../../../../../api/resolver/time-chart-resolver/time-chart-resolver-types";
import { TimeChartType } from "../../../../../api/resolver/time-chart-resolver/time-chart-types";
import { ChartStyle } from "../../../../../components/combo-chart/types";
import classes from "./renderer.module.css";
import { DateTime } from "luxon";
import { useMeasure } from "@uidotdev/usehooks";

interface TimeSeriesRendererProps {
  queryResult: DataQueryResult;
}

const chartStyleOptions = [
  { id: "line", text: "Line Chart", icon: ChartLine },
  { id: "area", text: "Area Chart", icon: ChartArea },
  { id: "column", text: "Column Chart", icon: ChartColumn },
  { id: "stacked-column", text: "Stacked Column Chart", icon: ChartStacked },
  { id: "stacked-area", text: "Stacked Area Chart", icon: ChartStacked },
];

export const TimeSeriesRenderer: React.FC<TimeSeriesRendererProps> = ({
  queryResult,
}) => {
  const { validation, rows, fields } = queryResult;
  const [ref, { height }] = useMeasure();
  const [selectedChartStyle, setSelectedChartStyle] =
    useState<ChartStyle>("line");
  const [selectedIndex, setSelectedIndex] = useState(0);

  // Find timestamp field
  const timestampField = fields.find((field) => field.type === "timestamp");
  const valueFields = fields.filter((field) => field.type !== "timestamp");

  // Transform data for ComboChartComponent
  const transformToChartData = (): TimeChartData | null => {
    if (
      !rows ||
      rows.length === 0 ||
      !timestampField ||
      valueFields.length === 0
    ) {
      return null;
    }

    const series: TimeChartSeriesData[] = valueFields.map((field) => ({
      id: field.name,
      unit: field.unit || "",
      data: rows.map((row) => ({
        unit: String(row[timestampField.name] ?? ""),
        value: Number(row[field.name] ?? 0),
      })),
    }));

    return {
      id: "faults" as TimeChartType, // Using a generic chart type for time series display
      series,
    };
  };

  const chartData = transformToChartData();

  return (
    <div className={classes.rendererContainer} ref={ref}>
      {/* Time Series Chart */}
      {chartData && validation.isValid ? (
        <div className={classes.chartWrapper}>
          <ComboChartComponent
            chartData={chartData}
            height={height ? `${height}px` : "200px"}
            chartStyle={selectedChartStyle}
            showLegend={valueFields.length > 1}
            dateFormat={DateTime.DATETIME_SHORT}
          />
          <div className={classes.floatingContentSwitcher}>
            <ContentSwitcher
              selectedIndex={selectedIndex}
              size="sm"
              onChange={({ index }) => {
                if (index !== undefined) {
                  setSelectedIndex(index);
                  setSelectedChartStyle(
                    chartStyleOptions[index].id as ChartStyle,
                  );
                }
              }}
            >
              {chartStyleOptions.map((option) => {
                const IconComponent = option.icon;
                return (
                  <IconSwitch
                    key={option.id}
                    name={option.id}
                    text={option.text}
                  >
                    <IconComponent />
                  </IconSwitch>
                );
              })}
            </ContentSwitcher>
          </div>
        </div>
      ) : (
        <div className={classes.emptyMessage}>
          <p>
            {!validation.isValid
              ? "Cannot display chart due to validation errors."
              : "No time series data to display."}
          </p>
        </div>
      )}
    </div>
  );
};
