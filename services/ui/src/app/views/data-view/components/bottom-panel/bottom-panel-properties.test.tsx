import "@testing-library/jest-dom";
import { screen, fireEvent } from "@testing-library/react";
import { render } from "../../../../../test-utils/render";
import { BottomPanel } from "./bottom-panel";
import type { DataQuery } from "../../types/data-query-types";

// Mock dependencies
vi.mock("../../../../config/hooks/use-config", () => ({
  useConfigSetting: vi.fn(() => ({
    setting: { value: "local" },
  })),
}));

vi.mock("../../../../hooks/use-dates", () => ({
  useDates: vi.fn(() => ({
    startDate: new Date("2024-01-01T00:00:00.000Z"),
    endDate: new Date("2024-01-01T23:59:59.999Z"),
  })),
}));

vi.mock("../../../../components/filter-combo-box", () => ({
  FilterComboBox: ({ titleText, onSelectionChange }: any) => (
    <div data-testid="filter-combo-box">
      <label>{titleText}</label>
      <button onClick={() => onSelectionChange(["test-filter-value"])}>
        Select Filter
      </button>
    </div>
  ),
}));

describe("BottomPanel - Query Properties Integration", () => {
  const mockOnRequestChange = vi.fn();
  const mockOnExecute = vi.fn();
  const mockOnCopyQuery = vi.fn();

  const baseProps = {
    isVisible: true,
    onRequestChange: mockOnRequestChange,
    onExecute: mockOnExecute,
    onCopyQuery: mockOnCopyQuery,
    isExecuteDisabled: false,
    isExecuting: false,
    isCopyingQuery: false,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("query properties rendering", () => {
    const dataQueryWithProperties: DataQuery = {
      id: "test-query",
      label: "Test Query",
      description: "Test description",
      type: "singleValue",
      isDraft: false,
      metadata: { category: "test" },
      dataSources: [],
      filters: [],
      parameters: { required: [] },
      query: "SELECT * FROM test",
      queryProperties: [
        {
          id: "time_granularity",
          type: "time_bucket",
          defaultValue: "DAY",
        },
        {
          id: "sort_order",
          type: "sort",
          defaultValue: "ASC",
        },
        {
          id: "row_limit",
          type: "limit",
          defaultValue: 100,
        },
      ],
    };

    it("renders property inputs for each query property", () => {
      render(
        <BottomPanel {...baseProps} dataQuery={dataQueryWithProperties} />,
      );

      // Check that property inputs are rendered using the correct selectors
      expect(
        screen.getByText("time_granularity (Time Granularity)"),
      ).toBeInTheDocument();
      expect(screen.getByText("sort_order (Sort Order)")).toBeInTheDocument();
      expect(screen.getByText("row_limit (Limit)")).toBeInTheDocument();
    });

    it("does not render property inputs when no queryProperties", () => {
      const dataQueryWithoutProperties: DataQuery = {
        ...dataQueryWithProperties,
        queryProperties: undefined,
      };

      render(
        <BottomPanel {...baseProps} dataQuery={dataQueryWithoutProperties} />,
      );

      // Should not find property-specific elements
      expect(
        screen.queryByText("time_granularity (Time Granularity)"),
      ).not.toBeInTheDocument();
      expect(
        screen.queryByText("sort_order (Sort Order)"),
      ).not.toBeInTheDocument();
    });

    it("does not render property inputs when queryProperties array is empty", () => {
      const dataQueryWithEmptyProperties: DataQuery = {
        ...dataQueryWithProperties,
        queryProperties: [],
      };

      render(
        <BottomPanel {...baseProps} dataQuery={dataQueryWithEmptyProperties} />,
      );

      // Should not find property-specific elements
      expect(
        screen.queryByText("time_granularity (Time Granularity)"),
      ).not.toBeInTheDocument();
    });
  });

  describe("property value handling", () => {
    const dataQueryWithProperties: DataQuery = {
      id: "test-query",
      label: "Test Query",
      description: "Test description",
      type: "singleValue",
      isDraft: false,
      metadata: { category: "test" },
      dataSources: [],
      filters: [],
      parameters: { required: [] },
      query: "SELECT * FROM test",
      queryProperties: [
        {
          id: "time_granularity",
          type: "time_bucket",
          defaultValue: "DAY",
        },
        {
          id: "row_limit",
          type: "limit",
          defaultValue: 100,
        },
      ],
    };

    it("includes property values in request filters when changed", async () => {
      render(
        <BottomPanel {...baseProps} dataQuery={dataQueryWithProperties} />,
      );

      // Change time granularity
      const timeGranularitySelect = screen.getByLabelText(
        "time_granularity (Time Granularity)",
      );
      fireEvent.change(timeGranularitySelect, { target: { value: "MONTH" } });

      // Change limit
      const limitInput = screen.getByLabelText("row_limit (Limit)");
      fireEvent.change(limitInput, { target: { value: "250" } });

      // Verify that onRequestChange was called with property values
      expect(mockOnRequestChange).toHaveBeenCalledWith(
        expect.objectContaining({
          startDate: expect.any(String),
          endDate: expect.any(String),
          time_granularity: "MONTH",
          row_limit: 250,
        }),
      );
    });

    it("sends initial request with default property values", () => {
      render(
        <BottomPanel {...baseProps} dataQuery={dataQueryWithProperties} />,
      );

      // Component calls onRequestChange twice - once with just dates, then with property values
      expect(mockOnRequestChange).toHaveBeenCalledTimes(2);

      // Check the second call includes the property values
      const secondCall = mockOnRequestChange.mock.calls[1][0];
      expect(secondCall).toMatchObject({
        startDate: expect.any(String),
        endDate: expect.any(String),
        time_granularity: "DAY",
        row_limit: 100,
      });
    });

    it("updates request filters when property values change", () => {
      render(
        <BottomPanel {...baseProps} dataQuery={dataQueryWithProperties} />,
      );

      const initialCallCount = mockOnRequestChange.mock.calls.length;

      // Change a property value
      const timeGranularitySelect = screen.getByLabelText(
        "time_granularity (Time Granularity)",
      );
      fireEvent.change(timeGranularitySelect, { target: { value: "WEEK" } });

      // Should have been called again with the new property value
      expect(mockOnRequestChange).toHaveBeenCalledTimes(initialCallCount + 1);

      const lastCall =
        mockOnRequestChange.mock.calls[
          mockOnRequestChange.mock.calls.length - 1
        ][0];
      expect(lastCall).toMatchObject({
        time_granularity: "WEEK",
      });
    });

    it("handles multiple property changes correctly", () => {
      render(
        <BottomPanel {...baseProps} dataQuery={dataQueryWithProperties} />,
      );

      // Change multiple properties
      const timeGranularitySelect = screen.getByLabelText(
        "time_granularity (Time Granularity)",
      );
      fireEvent.change(timeGranularitySelect, { target: { value: "HOUR" } });

      const limitInput = screen.getByLabelText("row_limit (Limit)");
      fireEvent.change(limitInput, { target: { value: "500" } });

      // Should include both property values
      const lastCall =
        mockOnRequestChange.mock.calls[
          mockOnRequestChange.mock.calls.length - 1
        ][0];
      expect(lastCall).toMatchObject({
        time_granularity: "HOUR",
        row_limit: 500,
      });
    });
  });

  describe("integration with existing filters", () => {
    const dataQueryWithFiltersAndProperties: DataQuery = {
      id: "test-query",
      label: "Test Query",
      description: "Test description",
      type: "singleValue",
      isDraft: false,
      metadata: { category: "test" },
      dataSources: [],
      filters: ["facility-filter"],
      parameters: { required: [] },
      query: "SELECT * FROM test",
      queryProperties: [
        {
          id: "sort_direction",
          type: "sort",
          defaultValue: "ASC",
        },
      ],
    };

    it("includes both filter values and property values in request", () => {
      render(
        <BottomPanel
          {...baseProps}
          dataQuery={dataQueryWithFiltersAndProperties}
        />,
      );

      // Simulate filter selection
      const filterButton = screen.getByText("Select Filter");
      fireEvent.click(filterButton);

      // Change property value
      const sortSelect = screen.getByLabelText("sort_direction (Sort Order)");
      fireEvent.change(sortSelect, { target: { value: "DESC" } });

      // Should include both filter and property values
      const lastCall =
        mockOnRequestChange.mock.calls[
          mockOnRequestChange.mock.calls.length - 1
        ][0];
      expect(lastCall).toMatchObject({
        startDate: expect.any(String),
        endDate: expect.any(String),
        // Note: the actual filter key would depend on the FilterComboBox implementation
        sort_direction: "DESC",
      });
    });
  });

  describe("error handling", () => {
    it("handles property with undefined id gracefully", () => {
      const dataQueryWithBadProperty: DataQuery = {
        id: "test-query",
        label: "Test Query",
        description: "Test description",
        type: "singleValue",
        isDraft: false,
        metadata: { category: "test" },
        dataSources: [],
        filters: [],
        parameters: { required: [] },
        query: "SELECT * FROM test",
        queryProperties: [
          {
            id: "",
            type: "sort",
            defaultValue: "ASC",
          } as any,
        ],
      };

      // Should not crash when rendering
      expect(() => {
        render(
          <BottomPanel {...baseProps} dataQuery={dataQueryWithBadProperty} />,
        );
      }).not.toThrow();
    });
  });
});
