import { vi } from "vitest";
import { render, screen, fireEvent } from "../../../../../../test-utils";
import { TimeSeriesRenderer } from "./time-series-renderer";
import type { DataQueryResult } from "../../../types/data-query-types";

// Mock ComboChartComponent
vi.mock("../../../../../components/combo-chart/combo-chart-component", () => ({
  ComboChartComponent: ({ chartData, height, chartStyle }: any) => (
    <div data-testid="combo-chart">
      <div data-testid="chart-height">{height}</div>
      <div data-testid="chart-style">{chartStyle}</div>
      <div data-testid="chart-data">{JSON.stringify(chartData)}</div>
    </div>
  ),
}));

// Mock useMeasure hook
vi.mock("@uidotdev/usehooks", () => ({
  useMeasure: () => [() => {}, { height: 300 }],
}));

// Mock luxon DateTime
vi.mock("luxon", () => ({
  DateTime: {
    DATETIME_SHORT: "short-format",
  },
}));

// Mock Carbon React components
vi.mock("@carbon/react", async () => {
  const actual = await vi.importActual("@carbon/react");
  return {
    ...actual,
    ContentSwitcher: ({ children, onChange, selectedIndex }: any) => (
      <div data-testid="content-switcher">
        <button
          data-testid="chart-style-button"
          onClick={() => onChange({ index: selectedIndex === 0 ? 1 : 0 })}
        >
          Toggle Chart Style
        </button>
        {children}
      </div>
    ),
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    IconSwitch: ({ children, name, text }: any) => (
      <div data-testid={`icon-switch-${name}`}>{text}</div>
    ),
    GlobalTheme: ({ children }: { children: React.ReactNode }) => children,
  };
});

describe("TimeSeriesRenderer", () => {
  const mockQueryResult: DataQueryResult = {
    rows: [
      { timestamp: "2024-01-01T12:00:00Z", value1: 10, value2: 20 },
      { timestamp: "2024-01-01T13:00:00Z", value1: 15, value2: 25 },
    ],
    fields: [
      { name: "timestamp", label: "Timestamp", type: "timestamp" },
      { name: "value1", label: "Value 1", type: "number", unit: "units" },
      { name: "value2", label: "Value 2", type: "number", unit: "count" },
    ],
    type: "timeSeries",
    metadata: {},
    queryRequest: "",
    validation: { isValid: true },
  };

  it("renders time series chart when data is valid", () => {
    render(<TimeSeriesRenderer queryResult={mockQueryResult} />);

    expect(screen.getByTestId("combo-chart")).toBeInTheDocument();
    expect(screen.getByTestId("chart-height")).toHaveTextContent("300px");
    expect(screen.getByTestId("chart-style")).toHaveTextContent("line");
  });

  it("shows chart style switcher with multiple options", () => {
    render(<TimeSeriesRenderer queryResult={mockQueryResult} />);

    expect(screen.getByTestId("content-switcher")).toBeInTheDocument();
    expect(screen.getByTestId("icon-switch-line")).toBeInTheDocument();
    expect(screen.getByTestId("icon-switch-area")).toBeInTheDocument();
    expect(screen.getByTestId("icon-switch-column")).toBeInTheDocument();
    expect(
      screen.getByTestId("icon-switch-stacked-column"),
    ).toBeInTheDocument();
    expect(screen.getByTestId("icon-switch-stacked-area")).toBeInTheDocument();
  });

  it("changes chart style when switcher is used", () => {
    render(<TimeSeriesRenderer queryResult={mockQueryResult} />);

    const button = screen.getByTestId("chart-style-button");
    fireEvent.click(button);

    expect(screen.getByTestId("chart-style")).toHaveTextContent("area");
  });

  it("shows empty message when validation is invalid", () => {
    const invalidQueryResult: DataQueryResult = {
      ...mockQueryResult,
      validation: { isValid: false },
    };

    render(<TimeSeriesRenderer queryResult={invalidQueryResult} />);

    expect(
      screen.getByText("Cannot display chart due to validation errors."),
    ).toBeInTheDocument();
    expect(screen.queryByTestId("combo-chart")).not.toBeInTheDocument();
  });

  it("shows empty message when no timestamp field available", () => {
    const noTimestampQueryResult: DataQueryResult = {
      rows: [{ value1: 10, value2: 20 }],
      fields: [
        { name: "value1", label: "Value 1", type: "number" },
        { name: "value2", label: "Value 2", type: "number" },
      ],
      type: "timeSeries",
      metadata: {},
      queryRequest: "",
      validation: { isValid: true },
    };

    render(<TimeSeriesRenderer queryResult={noTimestampQueryResult} />);

    expect(
      screen.getByText("No time series data to display."),
    ).toBeInTheDocument();
    expect(screen.queryByTestId("combo-chart")).not.toBeInTheDocument();
  });

  it("shows empty message when no data available", () => {
    const emptyQueryResult: DataQueryResult = {
      rows: [],
      fields: [],
      type: "timeSeries",
      metadata: {},
      queryRequest: "",
      validation: { isValid: true },
    };

    render(<TimeSeriesRenderer queryResult={emptyQueryResult} />);

    expect(
      screen.getByText("No time series data to display."),
    ).toBeInTheDocument();
    expect(screen.queryByTestId("combo-chart")).not.toBeInTheDocument();
  });

  it("transforms data correctly for chart component", () => {
    render(<TimeSeriesRenderer queryResult={mockQueryResult} />);

    const chartDataElement = screen.getByTestId("chart-data");
    const chartData = JSON.parse(chartDataElement.textContent || "{}");

    expect(chartData).toHaveProperty("id");
    expect(chartData).toHaveProperty("series");
    expect(chartData.series).toHaveLength(2);
    expect(chartData.series[0]).toHaveProperty("id", "value1");
    expect(chartData.series[0]).toHaveProperty("unit", "units");
    expect(chartData.series[1]).toHaveProperty("id", "value2");
    expect(chartData.series[1]).toHaveProperty("unit", "count");
  });
});
