import { vi } from "vitest";
import { render, screen } from "../../../../../test-utils";
import { FilterView } from "./filter-view";

// Mock BaseSqlEditorView
vi.mock("../base-sql-editor-view", () => ({
  BaseSqlEditorView: ({ config, testId, OptionsComponent }: any) => (
    <div data-testid="base-sql-editor-view">
      <div data-testid="config">{JSON.stringify(config)}</div>
      <div data-testid="test-id">{testId}</div>
      <div data-testid="options-component">
        {OptionsComponent ? <OptionsComponent /> : "No Options"}
      </div>
    </div>
  ),
}));

// Mock FilterOptions
vi.mock("./filter-options", () => ({
  FilterOptions: () => <div data-testid="filter-options">FilterOptions</div>,
}));

describe("FilterView", () => {
  it("renders without crashing", () => {
    render(<FilterView />);

    expect(screen.getByTestId("base-sql-editor-view")).toBeInTheDocument();
  });

  it("passes correct config to BaseSqlEditorView", () => {
    render(<FilterView />);

    const configElement = screen.getByTestId("config");
    const config = JSON.parse(configElement.textContent || "{}");

    expect(config).toEqual({
      settingGroup: "data-filter",
      entityName: "Filter",
      supportsCopyQuery: false,
    });
  });

  it("passes correct testId to BaseSqlEditorView", () => {
    render(<FilterView />);

    expect(screen.getByTestId("test-id")).toHaveTextContent("ict-filter-view");
  });

  it("passes FilterOptions as OptionsComponent", () => {
    render(<FilterView />);

    expect(screen.getByTestId("filter-options")).toBeInTheDocument();
  });
});
