import { vi } from "vitest";
import { render, screen } from "../../../../../test-utils";
import { FilterOptions } from "./filter-options";
import type { AppConfigSetting } from "@ict/sdk/types";

// Simple mocks
vi.mock("../shared/base-options", () => ({
  BaseOptions: () => <div data-testid="base-options">BaseOptions</div>,
}));

describe("FilterOptions", () => {
  const mockSetting: AppConfigSetting = {
    id: "test-filter",
    name: "Test Filter",
    group: "data-filter",
    value: {
      dataSources: [
        {
          id: "test-source",
          type: "edp-bigquery",
          table: "test_table",
        },
      ],
    },
    dataType: "json",
  };

  const mockProps = {
    setting: mockSetting,
    onClose: vi.fn(),
    onApply: vi.fn(),
  };

  it("renders without crashing", () => {
    render(<FilterOptions {...mockProps} />);

    expect(screen.getByTestId("base-options")).toBeInTheDocument();
  });
});
