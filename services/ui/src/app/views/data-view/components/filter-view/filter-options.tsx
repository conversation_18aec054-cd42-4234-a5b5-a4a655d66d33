import type { AppConfigSetting } from "@ict/sdk/types";
import { BaseOptions, type BaseOptionsConfig } from "../shared/base-options";
import { components } from "@ict/sdk/openapi-react-query";

interface FilterOptionsProps {
  setting: AppConfigSetting;
  onClose: () => void;
  onApply: (updatedSetting: AppConfigSetting) => void;
}

export type FilterValue = components["schemas"]["DataQuery"];

export const FilterOptions = ({
  setting,
  onClose,
  onApply,
}: FilterOptionsProps) => {
  const config: BaseOptionsConfig = {
    showConfigParameters: false,
    showQueryProperties: false,
    showFilters: false,
    showUnit: false,
    showCategoryFilter: true,
    multipleDataSources: false,
    defaultRequiredParams: ["start_date", "end_date"],
  };

  // Ensure single data source structure for filters
  const filterValue = setting.value as FilterValue;
  const normalizedSetting = {
    ...setting,
    value: {
      ...filterValue,
      dataSources:
        filterValue.dataSources?.length > 0
          ? [filterValue.dataSources[0]]
          : [
              {
                id: "edp-bigquery",
                type: "edp-bigquery",
                table: "",
              },
            ],
    },
  };

  return (
    <BaseOptions
      setting={normalizedSetting}
      onClose={onClose}
      onApply={onApply}
      config={config}
    />
  );
};
