import { render, screen, userEvent } from "../../../../../test-utils";
import { vi } from "vitest";
import { NewDataQueryModal } from "./new-data-query-modal";

// Mock Carbon components
vi.mock("@carbon/react", () => ({
  GlobalTheme: ({ children }: { children: React.ReactNode }) => children,
  Modal: ({
    open,
    onRequestClose,
    modalHeading,
    primaryButtonText,
    secondaryButtonText,
    onRequestSubmit,
    primaryButtonDisabled,
    children,
  }: {
    open: boolean;
    onRequestClose: () => void;
    modalHeading: string;
    primaryButtonText: string;
    secondaryButtonText: string;
    onRequestSubmit: () => void;
    primaryButtonDisabled?: boolean;
    children: React.ReactNode;
  }) =>
    open ? (
      <div data-testid="mock-modal" aria-label={modalHeading}>
        <h2>{modalHeading}</h2>
        <div>{children}</div>
        <button
          data-testid="primary-button"
          type="button"
          onClick={onRequestSubmit}
          disabled={primaryButtonDisabled}
        >
          {primaryButtonText}
        </button>
        <button
          data-testid="secondary-button"
          type="button"
          onClick={onRequestClose}
        >
          {secondaryButtonText}
        </button>
      </div>
    ) : null,
  ModalBody: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="modal-body">{children}</div>
  ),
  TextInput: ({
    id,
    labelText,
    placeholder,
    value,
    onChange,
    invalid,
  }: {
    id: string;
    labelText: string;
    placeholder: string;
    value: string;
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    invalid?: boolean;
  }) => (
    <div>
      <label htmlFor={id}>{labelText}</label>
      <input
        id={id}
        data-testid={id}
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        aria-invalid={invalid}
      />
    </div>
  ),
  Select: ({
    id,
    labelText,
    value,
    onChange,
    children,
  }: {
    id: string;
    labelText: string;
    value: string;
    onChange: (e: React.ChangeEvent<HTMLSelectElement>) => void;
    children: React.ReactNode;
  }) => (
    <div>
      <label htmlFor={id}>{labelText}</label>
      <select id={id} data-testid={id} value={value} onChange={onChange}>
        {children}
      </select>
    </div>
  ),
  SelectItem: ({ value, text }: { value: string; text: string }) => (
    <option value={value}>{text}</option>
  ),
}));

describe("NewDataQueryModal", () => {
  const onCloseMock = vi.fn();
  const onCreateMock = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("should render when open is true", () => {
    render(
      <NewDataQueryModal
        open={true}
        onClose={onCloseMock}
        onCreate={onCreateMock}
      />,
    );

    expect(screen.getByTestId("mock-modal")).toBeInTheDocument();
    expect(screen.getByText("New Data Query")).toBeInTheDocument();
  });

  it("should not render when open is false", () => {
    render(
      <NewDataQueryModal
        open={false}
        onClose={onCloseMock}
        onCreate={onCreateMock}
      />,
    );

    expect(screen.queryByTestId("mock-modal")).not.toBeInTheDocument();
  });

  it("should render form fields with correct labels", () => {
    render(
      <NewDataQueryModal
        open={true}
        onClose={onCloseMock}
        onCreate={onCreateMock}
      />,
    );

    expect(screen.getByLabelText("Name")).toBeInTheDocument();
    expect(screen.getByLabelText("Type")).toBeInTheDocument();
  });

  it("should allow user to input name", async () => {
    render(
      <NewDataQueryModal
        open={true}
        onClose={onCloseMock}
        onCreate={onCreateMock}
      />,
    );

    const nameInput = screen.getByTestId("data-query-name");
    await userEvent.type(nameInput, "Test Query");

    expect(nameInput).toHaveValue("Test Query");
  });

  it("should allow user to select type", async () => {
    render(
      <NewDataQueryModal
        open={true}
        onClose={onCloseMock}
        onCreate={onCreateMock}
      />,
    );

    const typeSelect = screen.getByTestId("data-query-type");
    await userEvent.selectOptions(typeSelect, "filter");

    expect(typeSelect).toHaveValue("filter");
  });

  it("should call onCreate with correct parameters when Create button is clicked", async () => {
    render(
      <NewDataQueryModal
        open={true}
        onClose={onCloseMock}
        onCreate={onCreateMock}
      />,
    );

    const nameInput = screen.getByTestId("data-query-name");
    const createButton = screen.getByTestId("primary-button");

    await userEvent.type(nameInput, "Test Query");
    await userEvent.click(createButton);

    expect(onCreateMock).toHaveBeenCalledWith("Test Query", "singleValue");
  });

  it("should call onClose when Cancel button is clicked", async () => {
    render(
      <NewDataQueryModal
        open={true}
        onClose={onCloseMock}
        onCreate={onCreateMock}
      />,
    );

    const cancelButton = screen.getByTestId("secondary-button");
    await userEvent.click(cancelButton);

    expect(onCloseMock).toHaveBeenCalledTimes(1);
  });

  it("should disable Create button when name is empty", () => {
    render(
      <NewDataQueryModal
        open={true}
        onClose={onCloseMock}
        onCreate={onCreateMock}
      />,
    );

    const createButton = screen.getByTestId("primary-button");
    expect(createButton).toBeDisabled();
  });

  it("should enable Create button when name is provided", async () => {
    render(
      <NewDataQueryModal
        open={true}
        onClose={onCloseMock}
        onCreate={onCreateMock}
      />,
    );

    const nameInput = screen.getByTestId("data-query-name");
    const createButton = screen.getByTestId("primary-button");

    await userEvent.type(nameInput, "Test Query");

    expect(createButton).not.toBeDisabled();
  });

  it("should show 'Creating...' text when isLoading is true", () => {
    render(
      <NewDataQueryModal
        open={true}
        onClose={onCloseMock}
        onCreate={onCreateMock}
        isLoading={true}
      />,
    );

    const createButton = screen.getByTestId("primary-button");
    expect(createButton).toHaveTextContent("Creating...");
    expect(createButton).toBeDisabled();
  });

  it("should reset form when modal is closed", async () => {
    render(
      <NewDataQueryModal
        open={true}
        onClose={onCloseMock}
        onCreate={onCreateMock}
      />,
    );

    const nameInput = screen.getByTestId("data-query-name");
    const typeSelect = screen.getByTestId("data-query-type");
    const cancelButton = screen.getByTestId("secondary-button");

    // Fill form
    await userEvent.type(nameInput, "Test Query");
    await userEvent.selectOptions(typeSelect, "filter");

    // Close modal
    await userEvent.click(cancelButton);

    // Verify onClose was called
    expect(onCloseMock).toHaveBeenCalledTimes(1);
  });
});
