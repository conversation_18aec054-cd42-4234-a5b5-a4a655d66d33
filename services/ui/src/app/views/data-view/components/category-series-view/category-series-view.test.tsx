import { vi } from "vitest";
import { render, screen } from "../../../../../test-utils";
import { CategorySeriesView } from "./category-series-view";

// Mock BaseSqlEditorView
vi.mock("../base-sql-editor-view", () => ({
  BaseSqlEditorView: ({ config, testId, OptionsComponent }: any) => (
    <div data-testid="base-sql-editor-view">
      <div data-testid="config">{JSON.stringify(config)}</div>
      <div data-testid="test-id">{testId}</div>
      <div data-testid="options-component">
        {OptionsComponent ? <OptionsComponent /> : "No Options"}
      </div>
    </div>
  ),
}));

// Mock CategorySeriesOptions
vi.mock("./category-series-options", () => ({
  CategorySeriesOptions: () => (
    <div data-testid="category-series-options">CategorySeriesOptions</div>
  ),
}));

describe("CategorySeriesView", () => {
  it("renders without crashing", () => {
    render(<CategorySeriesView />);

    expect(screen.getByTestId("base-sql-editor-view")).toBeInTheDocument();
  });

  it("passes correct config to BaseSqlEditorView", () => {
    render(<CategorySeriesView />);

    const configElement = screen.getByTestId("config");
    const config = JSON.parse(configElement.textContent || "{}");

    expect(config).toEqual({
      settingGroup: "data-category-series",
      entityName: "Category Series",
      supportsCopyQuery: true,
    });
  });

  it("passes correct testId to BaseSqlEditorView", () => {
    render(<CategorySeriesView />);

    expect(screen.getByTestId("test-id")).toHaveTextContent(
      "ict-category-series-view",
    );
  });

  it("passes CategorySeriesOptions as OptionsComponent", () => {
    render(<CategorySeriesView />);

    expect(screen.getByTestId("category-series-options")).toBeInTheDocument();
  });
});
