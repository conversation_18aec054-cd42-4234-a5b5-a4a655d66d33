import { BaseSqlEditorView } from "../base-sql-editor-view";
import { CategorySeriesOptions } from "./category-series-options";

export const CategorySeriesView = () => {
  return (
    <BaseSqlEditorView
      config={{
        settingGroup: "data-category-series",
        entityName: "Category Series",
        supportsCopyQuery: true,
      }}
      testId="ict-category-series-view"
      OptionsComponent={CategorySeriesOptions}
    />
  );
};
