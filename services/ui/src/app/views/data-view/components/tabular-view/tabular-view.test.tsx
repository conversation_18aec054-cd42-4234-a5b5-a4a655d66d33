import { vi } from "vitest";
import { render, screen } from "../../../../../test-utils";
import { TabularView } from "./tabular-view";

// Mock BaseSqlEditorView
vi.mock("../base-sql-editor-view", () => ({
  BaseSqlEditorView: ({ config, testId, OptionsComponent }: any) => (
    <div data-testid="base-sql-editor-view">
      <div data-testid="config">{JSON.stringify(config)}</div>
      <div data-testid="test-id">{testId}</div>
      <div data-testid="options-component">
        {OptionsComponent ? <OptionsComponent /> : "No Options"}
      </div>
    </div>
  ),
}));

// Mock TabularOptions
vi.mock("./tabular-options", () => ({
  TabularOptions: () => <div data-testid="tabular-options">TabularOptions</div>,
}));

describe("TabularView", () => {
  it("renders without crashing", () => {
    render(<TabularView />);

    expect(screen.getByTestId("base-sql-editor-view")).toBeInTheDocument();
  });

  it("passes correct config to BaseSqlEditorView", () => {
    render(<TabularView />);

    const configElement = screen.getByTestId("config");
    const config = JSON.parse(configElement.textContent || "{}");

    expect(config).toEqual({
      settingGroup: "data-tabular",
      entityName: "Tabular",
      supportsCopyQuery: true,
    });
  });

  it("passes correct testId to BaseSqlEditorView", () => {
    render(<TabularView />);

    expect(screen.getByTestId("test-id")).toHaveTextContent("ict-tabular-view");
  });

  it("passes TabularOptions as OptionsComponent", () => {
    render(<TabularView />);

    expect(screen.getByTestId("tabular-options")).toBeInTheDocument();
  });
});
