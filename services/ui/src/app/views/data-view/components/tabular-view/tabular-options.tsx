import type { AppConfigSetting } from "@ict/sdk/types";
import { useConfig, useConfigGroup } from "../../../../config/hooks/use-config";
import { BaseOptions, type BaseOptionsConfig } from "../shared/base-options";
import { ConfigParametersSection } from "../shared/config-parameters-section";
import { FiltersSection } from "../shared/filters-section";
import { QueryPropertiesSection } from "../shared/query-properties-section";

interface TabularOptionsProps {
  setting: AppConfigSetting;
  onClose: () => void;
  onApply: (updatedSetting: AppConfigSetting) => void;
}

export const TabularOptions = ({
  setting,
  onClose,
  onApply,
}: TabularOptionsProps) => {
  const { data: configSettings } = useConfig();
  const { settings: dataFilterSettings } = useConfigGroup("data-filter");

  const config: BaseOptionsConfig = {
    showConfigParameters: true,
    showQueryProperties: true,
    showFilters: true,
    showUnit: true,
    showCategoryFilter: false,
    multipleDataSources: true,
    defaultLimitValue: 100,
    defaultRequiredParams: ["start_date", "end_date"],
  };

  // Get available items for dropdowns
  const availableConfigItems = (configSettings || [])
    .map((config) => ({
      id: config.name,
      text: config.name,
    }))
    .filter((item) => item.id)
    .sort((a, b) => a.text.localeCompare(b.text));

  const availableFilterItems = (dataFilterSettings || [])
    .map((filter) => ({
      id: filter.name,
      text: filter.name,
    }))
    .filter((item) => item.id)
    .sort((a, b) => a.text.localeCompare(b.text));

  return (
    <BaseOptions
      setting={setting}
      onClose={onClose}
      onApply={onApply}
      config={config}
      renderCustomSections={({ draftValue, updateDraftValue }) => (
        <>
          <ConfigParametersSection
            draftValue={draftValue}
            updateDraftValue={updateDraftValue}
            availableConfigItems={availableConfigItems}
          />
          <FiltersSection
            draftValue={draftValue}
            updateDraftValue={updateDraftValue}
            availableFilterItems={availableFilterItems}
          />
          <QueryPropertiesSection
            draftValue={draftValue}
            updateDraftValue={updateDraftValue}
            defaultLimitValue={100}
          />
        </>
      )}
    />
  );
};
