import { Button, InlineNotification, Link } from "@carbon/react";
import { Add } from "@carbon/react/icons";
import type { AppConfigSetting } from "@ict/sdk/types";
import { AppConfigSettingSource } from "@ict/sdk/types";
import { createColumnHelper } from "@tanstack/react-table";
import { useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router";
import { useRoles } from "../../../../auth/hooks/use-roles";
import { Datagrid } from "../../../../components/datagrid";
import { FullPageContainer } from "../../../../components/full-page-container/full-page-container";
import { useNotification } from "../../../../components/toast/use-notification";
import { ViewBar } from "../../../../components/view-bar/view-bar";
import {
  updateConfigSetting,
  useConfig,
} from "../../../../config/hooks/use-config";
import { useLogger } from "../../../../utils/logger";
import { DataQuery } from "../../types/data-query-types";
import {
  createNewDataSetting,
  getNavigationPath,
  isDataQueryTypeSupported,
  type DataQueryType,
} from "../../util/data-view-setting.util";
import { NewDataQueryModal } from "../new-data-query-modal/new-data-query-modal";

const DATA_GROUPS = [
  "data-single-value",
  "data-category-series",
  "data-time-series",
  "data-tabular",
  "data-filter",
];

export const DataViewTable = () => {
  const { t } = useTranslation();
  const logger = useLogger("DataViewTable");
  const { data: appConfigSettings, isLoading, error } = useConfig();
  const navigate = useNavigate();
  const { hasConfiguratorAccess } = useRoles();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const { error: showErrorToast } = useNotification();

  const handleCreateDataQuery = async (name: string, type: string) => {
    // Validate data query type
    if (!isDataQueryTypeSupported(type)) {
      logger.warn(`${type} queries are not yet implemented`);
      return;
    }

    setIsCreating(true);

    try {
      // Create new setting using utility function
      const newSetting = createNewDataSetting(name, type as DataQueryType);

      // Save the new setting
      const response = await updateConfigSetting(
        newSetting as AppConfigSetting,
        AppConfigSettingSource.default,
      );

      // Navigate to the appropriate view with the returned ID
      const createdSettingId = response?.id || newSetting.id || name;
      const navigationPath = getNavigationPath(
        type as DataQueryType,
        createdSettingId,
      );
      navigate(navigationPath);

      setIsModalOpen(false);
    } catch (err) {
      showErrorToast(
        t(
          "dataView.table.errors.failedToCreateDataQuery",
          "Failed to create data query",
        ),
        {
          subtitle: (err as Error)?.message,
        },
      );
      logger.error("Failed to create data query:", err);
    } finally {
      setIsCreating(false);
    }
  };

  // Filter config settings to only show data-related groups
  const dataConfigSettings: AppConfigSetting[] = useMemo(() => {
    return ((appConfigSettings as AppConfigSetting[]) || []).filter(
      (setting) => {
        return DATA_GROUPS.includes(setting.group || "");
      },
    );
  }, [appConfigSettings]);

  // Define column helper for type safety
  const columnHelper = createColumnHelper<AppConfigSetting>();

  // Define columns for the table
  const columns = useMemo(
    () => [
      columnHelper.accessor("name", {
        header: t("dataView.table.columns.name", "Name"),
        minSize: 200,
        cell: ({ row, getValue }) => {
          const setting = row.original;
          const dataQuery = setting.value as DataQuery;
          const queryType = dataQuery?.type;

          // Check if this is a supported data query type
          if (queryType && isDataQueryTypeSupported(queryType)) {
            const navigationPath = getNavigationPath(
              queryType as DataQueryType,
              setting.id,
            );
            return (
              <Link
                href="#"
                onClick={(e) => {
                  e.preventDefault();
                  navigate(navigationPath);
                }}
              >
                {dataQuery?.id}
              </Link>
            );
          }

          return getValue();
        },
      }),
      columnHelper.accessor("description", {
        header: t("dataView.table.columns.description", "Description"),
        minSize: 250,
      }),
      columnHelper.accessor((row) => (row.value as DataQuery)?.isDraft, {
        header: t("dataView.table.columns.draft", "Draft"),
        cell: ({ row }) => {
          const setting = row.original;
          return (setting.value as DataQuery)?.isDraft
            ? t("dataView.table.values.yes", "Yes")
            : t("dataView.table.values.no", "No");
        },
      }),
      columnHelper.accessor("source", {
        header: t("dataView.table.columns.source", "Source"),
        maxSize: 120,
        cell: (value) => value.getValue(),
      }),
      columnHelper.accessor("group", {
        header: t("dataView.table.columns.type", "Type"),
        maxSize: 120,
        cell: (value) => value.getValue(),
      }),
    ],
    [columnHelper, navigate, t],
  );

  // Handle error state
  if (error) {
    return (
      <InlineNotification
        kind="error"
        title={t("dataView.table.errors.error", "Error")}
        subtitle={t(
          "dataView.table.errors.errorLoadingDataConfigSettings",
          "Error loading data config settings: {{error}}",
          { error: (error as Error)?.message || String(error) },
        )}
      />
    );
  }

  return (
    <>
      <ViewBar title={t("dataView.table.title", "Data Configuration")}>
        {hasConfiguratorAccess && (
          <Button
            kind="primary"
            size="sm"
            renderIcon={Add}
            onClick={() => setIsModalOpen(true)}
          >
            {t("dataView.table.actions.newDataQuery", "New Data Query")}
          </Button>
        )}
      </ViewBar>
      <FullPageContainer>
        <Datagrid
          columns={columns}
          data={dataConfigSettings}
          mode="client"
          enableSelection={false}
          showExportButton={false}
          initialPagination={{ pageIndex: 0, pageSize: 100 }}
          initialDensity="compact"
          initialSorting={[{ id: "name", desc: false }]}
          isLoading={isLoading}
        />
      </FullPageContainer>

      <NewDataQueryModal
        open={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onCreate={handleCreateDataQuery}
        isLoading={isCreating}
      />
    </>
  );
};
