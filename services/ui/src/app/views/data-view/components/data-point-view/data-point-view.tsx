import { BaseSqlEditorView } from "../base-sql-editor-view";
import { DataPointOptions } from "./data-point-options";

export const DataPointView = () => {
  return (
    <BaseSqlEditorView
      config={{
        settingGroup: "data-single-value",
        entityName: "Data Point",
        supportsCopyQuery: true,
      }}
      testId="ict-data-point-view"
      OptionsComponent={DataPointOptions}
    />
  );
};
