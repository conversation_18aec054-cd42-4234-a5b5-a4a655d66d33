import { vi } from "vitest";
import { render, screen } from "../../../../../test-utils";
import { DataPointView } from "./data-point-view";

// Mock BaseSqlEditorView
vi.mock("../base-sql-editor-view", () => ({
  BaseSqlEditorView: ({ config, testId, OptionsComponent }: any) => (
    <div data-testid="base-sql-editor-view">
      <div data-testid="config">{JSON.stringify(config)}</div>
      <div data-testid="test-id">{testId}</div>
      <div data-testid="options-component">
        {OptionsComponent ? <OptionsComponent /> : "No Options"}
      </div>
    </div>
  ),
}));

// Mock DataPointOptions
vi.mock("./data-point-options", () => ({
  DataPointOptions: () => (
    <div data-testid="data-point-options">DataPointOptions</div>
  ),
}));

describe("DataPointView", () => {
  it("renders without crashing", () => {
    render(<DataPointView />);

    expect(screen.getByTestId("base-sql-editor-view")).toBeInTheDocument();
  });

  it("passes correct config to BaseSqlEditorView", () => {
    render(<DataPointView />);

    const configElement = screen.getByTestId("config");
    const config = JSON.parse(configElement.textContent || "{}");

    expect(config).toEqual({
      settingGroup: "data-single-value",
      entityName: "Data Point",
      supportsCopyQuery: true,
    });
  });

  it("passes correct testId to BaseSqlEditorView", () => {
    render(<DataPointView />);

    expect(screen.getByTestId("test-id")).toHaveTextContent(
      "ict-data-point-view",
    );
  });

  it("passes DataPointOptions as OptionsComponent", () => {
    render(<DataPointView />);

    expect(screen.getByTestId("data-point-options")).toBeInTheDocument();
  });
});
