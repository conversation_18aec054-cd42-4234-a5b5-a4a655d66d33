import { BaseSqlEditorView } from "../base-sql-editor-view";
import { TimeSeriesOptions } from "./time-series-options";

// Example of how easy it is to create a new SQL editor view
export const TimeSeriesView = () => {
  return (
    <BaseSqlEditorView
      config={{
        settingGroup: "data-time-series",
        entityName: "Time Series",
        sqlPlaceholder: "Enter your SQL query to generate time series data...",
        supportsCopyQuery: true,
      }}
      testId="ict-time-series-view"
      OptionsComponent={TimeSeriesOptions}
    />
  );
};
