import { vi } from "vitest";
import { render, screen } from "../../../../../test-utils";
import { TimeSeriesOptions } from "./time-series-options";
import type { AppConfigSetting } from "@ict/sdk/types";

// Simple mocks
vi.mock("../../../../config/hooks/use-config", () => ({
  useConfig: () => ({ data: [] }),
  useConfigGroup: () => ({ settings: [] }),
}));

vi.mock("../shared/base-options", () => ({
  BaseOptions: () => <div data-testid="base-options">BaseOptions</div>,
}));

vi.mock("../shared/config-parameters-section", () => ({
  ConfigParametersSection: () => <div>ConfigParametersSection</div>,
}));

vi.mock("../shared/filters-section", () => ({
  FiltersSection: () => <div>FiltersSection</div>,
}));

vi.mock("../shared/query-properties-section", () => ({
  QueryPropertiesSection: () => <div>QueryPropertiesSection</div>,
}));

describe("TimeSeriesOptions", () => {
  const mockSetting: AppConfigSetting = {
    id: "test-time-series",
    name: "Test Time Series",
    group: "time-series",
    value: {},
    dataType: "json",
  };

  const mockProps = {
    setting: mockSetting,
    onClose: vi.fn(),
    onApply: vi.fn(),
  };

  it("renders without crashing", () => {
    render(<TimeSeriesOptions {...mockProps} />);

    expect(screen.getByTestId("base-options")).toBeInTheDocument();
  });
});
