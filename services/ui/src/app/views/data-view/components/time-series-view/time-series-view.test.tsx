import { vi } from "vitest";
import { render, screen } from "../../../../../test-utils";
import { TimeSeriesView } from "./time-series-view";

// Mock BaseSqlEditorView
vi.mock("../base-sql-editor-view", () => ({
  BaseSqlEditorView: ({ config, testId, OptionsComponent }: any) => (
    <div data-testid="base-sql-editor-view">
      <div data-testid="config">{JSON.stringify(config)}</div>
      <div data-testid="test-id">{testId}</div>
      <div data-testid="options-component">
        {OptionsComponent ? <OptionsComponent /> : "No Options"}
      </div>
    </div>
  ),
}));

// Mock TimeSeriesOptions
vi.mock("./time-series-options", () => ({
  TimeSeriesOptions: () => (
    <div data-testid="time-series-options">TimeSeriesOptions</div>
  ),
}));

describe("TimeSeriesView", () => {
  it("renders without crashing", () => {
    render(<TimeSeriesView />);

    expect(screen.getByTestId("base-sql-editor-view")).toBeInTheDocument();
  });

  it("passes correct config to BaseSqlEditorView", () => {
    render(<TimeSeriesView />);

    const configElement = screen.getByTestId("config");
    const config = JSON.parse(configElement.textContent || "{}");

    expect(config).toEqual({
      settingGroup: "data-time-series",
      entityName: "Time Series",
      sqlPlaceholder: "Enter your SQL query to generate time series data...",
      supportsCopyQuery: true,
    });
  });

  it("passes correct testId to BaseSqlEditorView", () => {
    render(<TimeSeriesView />);

    expect(screen.getByTestId("test-id")).toHaveTextContent(
      "ict-time-series-view",
    );
  });

  it("passes TimeSeriesOptions as OptionsComponent", () => {
    render(<TimeSeriesView />);

    expect(screen.getByTestId("time-series-options")).toBeInTheDocument();
  });
});
