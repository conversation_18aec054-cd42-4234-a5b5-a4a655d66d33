import { vi } from "vitest";
import { render, screen } from "../../../../../test-utils";
import { BaseSqlEditorView } from "./base-sql-editor-view";

// Mock hooks
const mockUseRoles = vi.hoisted(() =>
  vi.fn(() => ({ hasConfiguratorAccess: true })),
);
const mockUseSqlEditorView = vi.hoisted(() => vi.fn());

vi.mock("../../../../auth/hooks/use-roles", () => ({
  useRoles: mockUseRoles,
}));

vi.mock("../../hooks/use-sql-editor-view", () => ({
  useSqlEditorView: mockUseSqlEditorView,
}));

// Mock Carbon components
vi.mock("@carbon/react", async () => {
  const actual = await vi.importActual("@carbon/react");
  return {
    ...actual,
    Button: ({ children, onClick }: any) => (
      <button data-testid="button" onClick={onClick}>
        {children}
      </button>
    ),
    InlineNotification: ({ title, subtitle }: any) => (
      <div data-testid="inline-notification">
        <div>{title}</div>
        <div>{subtitle}</div>
      </div>
    ),
    GlobalTheme: ({ children }: { children: React.ReactNode }) => children,
  };
});

// Mock components
vi.mock("../../../../components/sql-editor", () => ({
  SqlEditor: () => <div data-testid="sql-editor">SQL Editor</div>,
}));

vi.mock("../../../../components/view-aside/view-aside", () => ({
  ViewAside: ({ children }: any) => (
    <div data-testid="view-aside">{children}</div>
  ),
}));

vi.mock("../../../../components/view-bar/view-bar", () => ({
  ViewBar: ({ title, children }: any) => (
    <div data-testid="view-bar">
      <div data-testid="view-bar-title">{title}</div>
      {children}
    </div>
  ),
}));

vi.mock("../bottom-panel/bottom-panel", () => ({
  BottomPanel: () => <div data-testid="bottom-panel">Bottom Panel</div>,
}));

vi.mock("../delete-source-modal/delete-source-modal", () => ({
  DeleteSourceModal: () => (
    <div data-testid="delete-source-modal">Delete Modal</div>
  ),
}));

vi.mock("../source-selector/source-selector", () => ({
  SourceSelector: () => (
    <div data-testid="source-selector">Source Selector</div>
  ),
}));

describe("BaseSqlEditorView", () => {
  const mockConfig = {
    entityName: "Test Entity",
    sqlPlaceholder: "Enter SQL...",
    supportsCopyQuery: false,
  } as any;

  const MockOptionsComponent = () => <div data-testid="options">Options</div>;

  const defaultMockHook = {
    setting: { value: { id: "test-query" } },
    isSettingsOpen: false,
    setIsSettingsOpen: vi.fn(),
    queryResult: null,
    isExecuting: false,
    queryError: null,
    draftSqlQuery: "SELECT * FROM test",
    setDraftSqlQuery: vi.fn(),
    isCopyingQuery: false,
    deleteModalOpen: false,
    sourceToDelete: null,
    bottomPanelRef: { current: null },
    hasUnsavedChanges: false,
    error: null,
    isLoading: false,
    sourceManagement: {
      allSources: [],
      activeSource: null,
      switchToSource: vi.fn(),
      updateLocalSetting: vi.fn(),
    },
    handleExecuteQuery: vi.fn(),
    handleCopyQuery: vi.fn(),
    handleSave: vi.fn(),
    handleDeleteSource: vi.fn(),
    handleConfirmDelete: vi.fn(),
    handleCancelDelete: vi.fn(),
    handleRequestChange: vi.fn(),
    getNextActiveSource: vi.fn(),
    navigate: vi.fn(),
  };

  beforeEach(() => {
    mockUseSqlEditorView.mockReturnValue(defaultMockHook);
  });

  it("renders with default state", () => {
    render(
      <BaseSqlEditorView
        config={mockConfig}
        testId="test-view"
        OptionsComponent={MockOptionsComponent}
      />,
    );

    expect(screen.getByTestId("test-view")).toBeInTheDocument();
    expect(screen.getByTestId("view-bar")).toBeInTheDocument();
    expect(screen.getByTestId("view-bar-title")).toHaveTextContent(
      "test-query",
    );
    expect(screen.getByTestId("sql-editor")).toBeInTheDocument();
    expect(screen.getByTestId("bottom-panel")).toBeInTheDocument();
  });

  it("renders error state", () => {
    mockUseSqlEditorView.mockReturnValue({
      ...defaultMockHook,
      error: new Error("Test error"),
    });

    render(
      <BaseSqlEditorView
        config={mockConfig}
        testId="test-view"
        OptionsComponent={MockOptionsComponent}
      />,
    );

    expect(screen.getByTestId("inline-notification")).toBeInTheDocument();
    expect(screen.getByText("Error")).toBeInTheDocument();
    expect(
      screen.getByText("Error loading test entity: Test error"),
    ).toBeInTheDocument();
  });

  it("renders not found state", () => {
    mockUseSqlEditorView.mockReturnValue({
      ...defaultMockHook,
      setting: null,
      isLoading: false,
    });

    render(
      <BaseSqlEditorView
        config={mockConfig}
        testId="test-view"
        OptionsComponent={MockOptionsComponent}
      />,
    );

    expect(screen.getByTestId("inline-notification")).toBeInTheDocument();
    expect(screen.getByText("Not Found")).toBeInTheDocument();
    expect(screen.getByText("Test Entity not found")).toBeInTheDocument();
  });

  it("renders options panel when settings are open", () => {
    mockUseSqlEditorView.mockReturnValue({
      ...defaultMockHook,
      isSettingsOpen: true,
    });

    render(
      <BaseSqlEditorView
        config={mockConfig}
        testId="test-view"
        OptionsComponent={MockOptionsComponent}
      />,
    );

    expect(screen.getByTestId("view-aside")).toBeInTheDocument();
    expect(screen.getByTestId("options")).toBeInTheDocument();
  });
});
