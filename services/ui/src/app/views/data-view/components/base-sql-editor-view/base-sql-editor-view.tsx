import { Button, InlineNotification } from "@carbon/react";
import { ArrowLeft } from "@carbon/react/icons";
import type { ReactNode } from "react";

import { useRoles } from "../../../../auth/hooks/use-roles";
import { SqlEditor } from "../../../../components/sql-editor";
import { ViewAside } from "../../../../components/view-aside/view-aside";
import { ViewBar } from "../../../../components/view-bar/view-bar";
import {
  useSqlEditorView,
  type SqlEditorViewConfig,
  type SqlQuery,
} from "../../hooks/use-sql-editor-view";
import { BottomPanel } from "../bottom-panel/bottom-panel";
import { DeleteSourceModal } from "../delete-source-modal/delete-source-modal";
import { SourceSelector } from "../source-selector/source-selector";
import styles from "./base-sql-editor-view.module.css";

export interface BaseSqlEditorViewProps {
  config: SqlEditorViewConfig;
  testId: string;
  OptionsComponent: React.ComponentType<{
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    setting: any;
    onClose: () => void;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    onApply: (updatedSetting: any) => void;
  }>;
}

export const BaseSqlEditorView = ({
  config,
  testId,
  OptionsComponent,
}: BaseSqlEditorViewProps) => {
  const { hasConfiguratorAccess } = useRoles();

  const {
    // State
    setting,
    isSettingsOpen,
    setIsSettingsOpen,
    queryResult,
    isExecuting,
    queryError,
    draftSqlQuery,
    setDraftSqlQuery,
    isCopyingQuery,
    deleteModalOpen,
    sourceToDelete,
    bottomPanelRef,
    hasUnsavedChanges,
    error,
    isLoading,

    // Source management
    sourceManagement,

    // Event handlers
    handleExecuteQuery,
    handleCopyQuery,
    handleSave,
    handleDeleteSource,
    handleConfirmDelete,
    handleCancelDelete,
    handleRequestChange,
    getNextActiveSource,

    // Navigation
    navigate,
  } = useSqlEditorView(config);

  // Handle error state
  if (error) {
    const errorMessage =
      typeof error === "object" && error !== null && "message" in error
        ? (error as Error).message
        : String(error);
    return (
      <InlineNotification
        kind="error"
        title="Error"
        subtitle={`Error loading ${config.entityName.toLowerCase()}: ${errorMessage}`}
      />
    );
  }

  // Handle not found
  if (!isLoading && !setting) {
    return (
      <InlineNotification
        kind="warning"
        title="Not Found"
        subtitle={`${config.entityName} not found`}
      />
    );
  }

  const settingValue = setting?.value as SqlQuery;
  const title = settingValue?.id || config.entityName;

  // Additional actions for ViewBar (can be overridden by specific views)
  const additionalActions: ReactNode[] = [];

  return (
    <div data-testid={testId} className={styles.container}>
      <ViewBar
        title={title}
        showSettings={true}
        onSettingsClick={() => setIsSettingsOpen(true)}
        showSave={true}
        hasConfiguratorAccess={hasConfiguratorAccess}
        saveEnabled={hasConfiguratorAccess && hasUnsavedChanges}
        onSaveClick={handleSave}
      >
        <Button
          kind="ghost"
          size="sm"
          renderIcon={ArrowLeft}
          onClick={() => navigate("..")}
        >
          Back to Data Configuration
        </Button>
        <SourceSelector
          sources={sourceManagement.allSources}
          activeSource={sourceManagement.activeSource}
          onSourceChange={sourceManagement.switchToSource}
          onDelete={handleDeleteSource}
          disabled={!hasConfiguratorAccess}
        />
        {additionalActions}
      </ViewBar>

      <div className={styles.contentContainer}>
        <div className={styles.innerContainer}>
          {setting && (
            <SqlEditor
              value={draftSqlQuery}
              onChange={setDraftSqlQuery}
              onExecute={handleExecuteQuery}
              height="100%"
              placeholder={
                config.sqlPlaceholder || "Enter your SQL query here..."
              }
            />
          )}
        </div>
        <BottomPanel
          ref={bottomPanelRef}
          isVisible={true}
          queryResult={queryResult}
          isLoading={isExecuting}
          error={queryError}
          onExecute={handleExecuteQuery}
          isExecuteDisabled={isExecuting || !draftSqlQuery.trim()}
          isExecuting={isExecuting}
          dataQuery={settingValue}
          onRequestChange={handleRequestChange}
          {...(config.supportsCopyQuery && {
            onCopyQuery: handleCopyQuery,
            isCopyingQuery: isCopyingQuery,
          })}
        />
      </div>

      <ViewAside isVisible={isSettingsOpen} className={styles.viewAside}>
        {setting && (
          <OptionsComponent
            setting={setting}
            onClose={() => setIsSettingsOpen(false)}
            onApply={(updatedSetting) => {
              // Preserve the current draft SQL query when applying settings changes
              const settingWithDraftSql = {
                ...updatedSetting,
                value: {
                  ...(updatedSetting.value as Record<string, unknown>),
                  query: draftSqlQuery,
                },
              };
              sourceManagement.updateLocalSetting(settingWithDraftSql);
              setIsSettingsOpen(false);
            }}
          />
        )}
      </ViewAside>

      <DeleteSourceModal
        isOpen={deleteModalOpen}
        sourceToDelete={sourceToDelete}
        nextActiveSource={
          sourceToDelete ? getNextActiveSource(sourceToDelete) : null
        }
        onConfirm={handleConfirmDelete}
        onCancel={handleCancelDelete}
      />
    </div>
  );
};
