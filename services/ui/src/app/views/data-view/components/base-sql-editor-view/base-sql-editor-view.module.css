.container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 48px);
  overflow: hidden;
}

.contentContainer {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
  position: relative;
}

.innerContainer {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.viewAside {
  width: 35vw;
  position: fixed;
  top: var(--header-height, 48px);
  right: 0;
}
