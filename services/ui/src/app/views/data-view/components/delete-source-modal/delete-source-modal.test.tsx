import { vi } from "vitest";
import { render, screen, fireEvent } from "../../../../../test-utils";
import { DeleteSourceModal } from "./delete-source-modal";

// Mock Carbon React Modal
vi.mock("@carbon/react", async () => {
  const actual = await vi.importActual("@carbon/react");
  return {
    ...actual,
    Modal: ({ children, open, modalHeading, onRequestSubmit, onSecondarySubmit }: any) => 
      open ? (
        <div data-testid="modal">
          <h2>{modalHeading}</h2>
          {children}
          <button onClick={onRequestSubmit} data-testid="confirm-button">Delete</button>
          <button onClick={onSecondarySubmit} data-testid="cancel-button">Cancel</button>
        </div>
      ) : null,
  };
});

describe("DeleteSourceModal", () => {
  const defaultProps = {
    isOpen: true,
    sourceToDelete: "facility" as const,
    nextActiveSource: "tenant" as const,
    onConfirm: vi.fn(),
    onCancel: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("renders when open with sourceToDelete", () => {
    render(<DeleteSourceModal {...defaultProps} />);
    
    expect(screen.getByTestId("modal")).toBeInTheDocument();
    expect(screen.getByText("Delete Facility Configuration")).toBeInTheDocument();
    expect(screen.getByText(/Are you sure you want to delete the/)).toBeInTheDocument();
    expect(screen.getByText(/This action cannot be undone/)).toBeInTheDocument();
  });

  it("returns null when sourceToDelete is null", () => {
    render(<DeleteSourceModal {...defaultProps} sourceToDelete={null} />);
    
    expect(screen.queryByTestId("modal")).not.toBeInTheDocument();
  });

  it("does not render when modal is closed", () => {
    render(<DeleteSourceModal {...defaultProps} isOpen={false} />);
    
    expect(screen.queryByTestId("modal")).not.toBeInTheDocument();
  });

  it("shows default configuration warning for default source", () => {
    render(<DeleteSourceModal {...defaultProps} sourceToDelete="default" />);
    
    expect(screen.getByText("Delete Default Configuration")).toBeInTheDocument();
    expect(screen.getByText(/This will delete all configurations/)).toBeInTheDocument();
  });

  it("shows next active source for non-default sources", () => {
    render(<DeleteSourceModal {...defaultProps} />);
    
    expect(screen.getByText(/level configuration will be used instead/)).toBeInTheDocument();
  });

  it("calls onConfirm when delete button is clicked", () => {
    render(<DeleteSourceModal {...defaultProps} />);
    
    fireEvent.click(screen.getByTestId("confirm-button"));
    
    expect(defaultProps.onConfirm).toHaveBeenCalledTimes(1);
  });

  it("calls onCancel when cancel button is clicked", () => {
    render(<DeleteSourceModal {...defaultProps} />);
    
    fireEvent.click(screen.getByTestId("cancel-button"));
    
    expect(defaultProps.onCancel).toHaveBeenCalledTimes(1);
  });

  it("displays correct labels for different source types", () => {
    const { rerender } = render(<DeleteSourceModal {...defaultProps} sourceToDelete="user" />);
    expect(screen.getByText("Delete User Configuration")).toBeInTheDocument();

    rerender(<DeleteSourceModal {...defaultProps} sourceToDelete="tenant" />);
    expect(screen.getByText("Delete Tenant Configuration")).toBeInTheDocument();
  });
});
