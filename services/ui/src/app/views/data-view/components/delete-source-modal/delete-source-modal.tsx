import { Modal } from "@carbon/react";
import type { SourceLevel } from "../../../../config/hooks/use-source-management";

interface DeleteSourceModalProps {
  isOpen: boolean;
  sourceToDelete: SourceLevel | null;
  nextActiveSource: SourceLevel | null;
  onConfirm: () => void;
  onCancel: () => void;
}

const SOURCE_LABELS = {
  default: "Default",
  tenant: "Tenant",
  facility: "Facility",
  user: "User",
};

export const DeleteSourceModal = ({
  isOpen,
  sourceToDelete,
  nextActiveSource,
  onConfirm,
  onCancel,
}: DeleteSourceModalProps) => {
  if (!sourceToDelete) return null;

  const sourceLabel = SOURCE_LABELS[sourceToDelete];
  const nextSourceLabel = nextActiveSource
    ? SOURCE_LABELS[nextActiveSource]
    : "Default";

  return (
    <Modal
      open={isOpen}
      onRequestClose={onCancel}
      modalHeading={`Delete ${sourceLabel} Configuration`}
      primaryButtonText="Delete"
      secondaryButtonText="Cancel"
      danger
      onRequestSubmit={onConfirm}
      onSecondarySubmit={onCancel}
    >
      <p>
        Are you sure you want to delete the <strong>{sourceLabel}</strong> level
        configuration?
      </p>
      {sourceToDelete === "default" ? (
        <p>
          This will delete all configurations for this setting, including the
          setting itself.
        </p>
      ) : (
        <p>
          After deletion, the <strong>{nextSourceLabel}</strong> level
          configuration will be used instead.
        </p>
      )}
      <p>
        <strong>This action cannot be undone.</strong>
      </p>
    </Modal>
  );
};
