import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "@carbon/react";
import { TrashCan } from "@carbon/react/icons";
import type {
  SourceLevel,
  SourceLevelInfo,
} from "../../../../config/hooks/use-source-management";
import styles from "./source-selector.module.css";

interface SourceSelectorProps {
  sources: SourceLevelInfo[];
  activeSource: SourceLevel | null;
  onSourceChange: (source: SourceLevel) => void;
  onDelete?: (source: SourceLevel) => void;
  disabled?: boolean;
}

const SOURCE_LABELS: Record<SourceLevel, string> = {
  default: "Default",
  tenant: "Tenant",
  facility: "Facility",
  user: "User",
};

export const SourceSelector = ({
  sources,
  activeSource,
  onSourceChange,
  onDelete,
  disabled = false,
}: SourceSelectorProps) => {
  const comboBoxItems = sources.map((source) => {
    return {
      id: source.level,
      text: SOURCE_LABELS[source.level],
      exists: source.exists,
    };
  });

  const selectedItem = comboBoxItems.find((item) => item.id === activeSource);

  // Single delete button that operates on the currently selected source level
  const selectedSource = sources.find((s) => s.level === activeSource);
  const canDeleteSelected =
    onDelete &&
    activeSource &&
    selectedSource?.exists &&
    (activeSource === "facility" ||
      activeSource === "tenant" ||
      activeSource === "default") &&
    !disabled;

  return (
    <div className={styles.container}>
      <ComboBox
        id="source-selector"
        placeholder="Select source level"
        items={comboBoxItems}
        selectedItem={selectedItem}
        itemToString={(item) => item?.text || ""}
        disabled={disabled}
        size="sm"
        onChange={({ selectedItem }) => {
          // Only change if a valid item is selected, preventing clearing
          if (selectedItem) {
            onSourceChange(selectedItem.id as SourceLevel);
          }
        }}
        itemToElement={(item) => {
          if (!item) return null;

          return (
            <div className={styles.sourceItem}>
              <span
                className={`${styles.sourceLabel} ${!item.exists ? styles.nonExistent : ""}`}
              >
                {item.text}
                {!item.exists && " (will be created)"}
              </span>
            </div>
          );
        }}
        className={styles.comboBox}
        allowCustomValue
      />

      {onDelete && (
        <Button
          kind="ghost"
          size="sm"
          hasIconOnly
          renderIcon={TrashCan}
          iconDescription={`Delete ${activeSource ? SOURCE_LABELS[activeSource] : ""} level`}
          onClick={() => activeSource && onDelete(activeSource)}
          disabled={!canDeleteSelected}
          className={styles.deleteButton}
          tooltipPosition="bottom"
        ></Button>
      )}
    </div>
  );
};
