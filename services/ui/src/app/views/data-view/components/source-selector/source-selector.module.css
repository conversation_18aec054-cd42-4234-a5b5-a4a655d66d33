.container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.comboBox {
  min-width: 200px;
  flex: 1;
}

.sourceItem {
  display: flex;
  align-items: center;
  padding: 0.25rem 0;
}

.deleteButton {
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.deleteButton:hover:not(:disabled) {
  opacity: 1;
}

.deleteButton:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.sourceLabel {
  font-weight: 500;
  color: var(--cds-text-primary);
}

.sourceLabel.nonExistent {
  color: var(--cds-text-secondary);
  font-style: italic;
}

.sourceDescription {
  font-size: 0.75rem;
  color: var(--cds-text-secondary);
  line-height: 1.2;
}
