import { vi } from "vitest";
import { render, screen, fireEvent } from "../../../../../test-utils";
import { SourceSelector } from "./source-selector";
import type {
  SourceLevelInfo,
  SourceLevel,
} from "../../../../config/hooks/use-source-management";

// Mock Carbon React components
vi.mock("@carbon/react", async () => {
  const actual = await vi.importActual("@carbon/react");
  return {
    ...actual,
    ComboBox: ({
      items,
      selectedItem,
      onChange,
      itemToElement,
      disabled,
      placeholder,
    }: any) => (
      <div data-testid="combo-box">
        <div data-testid="placeholder">{placeholder}</div>
        <div data-testid="disabled">{disabled ? "true" : "false"}</div>
        <select
          data-testid="select"
          value={selectedItem?.id || ""}
          disabled={disabled}
          onChange={(e) => {
            const item = items.find((i: any) => i.id === e.target.value);
            onChange({ selectedItem: item });
          }}
        >
          <option value="">Select...</option>
          {items.map((item: any) => (
            <option key={item.id} value={item.id}>
              {item.text}
            </option>
          ))}
        </select>
        {items.map((item: any) => (
          <div
            key={`element-${item.id}`}
            data-testid={`item-element-${item.id}`}
          >
            {itemToElement?.(item)}
          </div>
        ))}
      </div>
    ),
    Button: ({
      onClick,
      disabled,
      iconDescription,
      hasIconOnly,
      renderIcon: Icon,
    }: any) => (
      <button
        data-testid="delete-button"
        onClick={onClick}
        disabled={disabled}
        title={iconDescription}
        data-icon-only={hasIconOnly}
      >
        {Icon && <Icon data-testid="trash-icon" />}
        {iconDescription}
      </button>
    ),
    GlobalTheme: ({ children }: { children: React.ReactNode }) => children,
  };
});

describe("SourceSelector", () => {
  const mockOnSourceChange = vi.fn();
  const mockOnDelete = vi.fn();

  const mockSources: SourceLevelInfo[] = [
    { level: "default" as SourceLevel, exists: true, isActive: true },
    { level: "tenant" as SourceLevel, exists: true, isActive: true },
    { level: "facility" as SourceLevel, exists: false, isActive: true },
    { level: "user" as SourceLevel, exists: true, isActive: true },
  ];

  const baseProps = {
    sources: mockSources,
    activeSource: "tenant" as SourceLevel,
    onSourceChange: mockOnSourceChange,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("renders without crashing", () => {
    render(<SourceSelector {...baseProps} />);

    expect(screen.getByTestId("combo-box")).toBeInTheDocument();
  });

  it("displays correct placeholder", () => {
    render(<SourceSelector {...baseProps} />);

    expect(screen.getByTestId("placeholder")).toHaveTextContent(
      "Select source level",
    );
  });

  it("renders all source options", () => {
    render(<SourceSelector {...baseProps} />);

    expect(screen.getByRole("option", { name: "Default" })).toBeInTheDocument();
    expect(screen.getByRole("option", { name: "Tenant" })).toBeInTheDocument();
    expect(
      screen.getByRole("option", { name: "Facility" }),
    ).toBeInTheDocument();
    expect(screen.getByRole("option", { name: "User" })).toBeInTheDocument();
  });

  it("shows active source as selected", () => {
    render(<SourceSelector {...baseProps} />);

    const select = screen.getByTestId("select");
    expect(select).toHaveValue("tenant");
  });

  it("calls onSourceChange when selection changes", () => {
    render(<SourceSelector {...baseProps} />);

    const select = screen.getByTestId("select");
    fireEvent.change(select, { target: { value: "facility" } });

    expect(mockOnSourceChange).toHaveBeenCalledWith("facility");
  });

  it("shows non-existent sources with 'will be created' label", () => {
    render(<SourceSelector {...baseProps} />);

    const facilityElement = screen.getByTestId("item-element-facility");
    expect(facilityElement).toHaveTextContent("Facility (will be created)");
  });

  it("shows existing sources without extra label", () => {
    render(<SourceSelector {...baseProps} />);

    const tenantElement = screen.getByTestId("item-element-tenant");
    expect(tenantElement).toHaveTextContent("Tenant");
    expect(tenantElement).not.toHaveTextContent("(will be created)");
  });

  it("disables combo box when disabled prop is true", () => {
    render(<SourceSelector {...baseProps} disabled={true} />);

    expect(screen.getByTestId("disabled")).toHaveTextContent("true");
    expect(screen.getByTestId("select")).toBeDisabled();
  });

  describe("delete functionality", () => {
    it("renders delete button when onDelete is provided", () => {
      render(<SourceSelector {...baseProps} onDelete={mockOnDelete} />);

      expect(screen.getByTestId("delete-button")).toBeInTheDocument();
    });

    it("does not render delete button when onDelete is not provided", () => {
      render(<SourceSelector {...baseProps} />);

      expect(screen.queryByTestId("delete-button")).not.toBeInTheDocument();
    });

    it("calls onDelete when delete button clicked", () => {
      render(<SourceSelector {...baseProps} onDelete={mockOnDelete} />);

      const deleteButton = screen.getByTestId("delete-button");
      fireEvent.click(deleteButton);

      expect(mockOnDelete).toHaveBeenCalledWith("tenant");
    });

    it("disables delete button when selected source cannot be deleted", () => {
      render(
        <SourceSelector
          {...baseProps}
          activeSource="user"
          onDelete={mockOnDelete}
        />,
      );

      const deleteButton = screen.getByTestId("delete-button");
      expect(deleteButton).toBeDisabled();
    });

    it("disables delete button when component is disabled", () => {
      render(
        <SourceSelector
          {...baseProps}
          onDelete={mockOnDelete}
          disabled={true}
        />,
      );

      const deleteButton = screen.getByTestId("delete-button");
      expect(deleteButton).toBeDisabled();
    });

    it("shows correct delete button description", () => {
      render(<SourceSelector {...baseProps} onDelete={mockOnDelete} />);

      const deleteButton = screen.getByTestId("delete-button");
      expect(deleteButton).toHaveAttribute("title", "Delete Tenant level");
    });
  });

  it("handles null activeSource gracefully", () => {
    render(
      <SourceSelector
        {...baseProps}
        activeSource={null}
        onDelete={mockOnDelete}
      />,
    );

    const select = screen.getByTestId("select");
    expect(select).toHaveValue("");

    const deleteButton = screen.getByTestId("delete-button");
    expect(deleteButton).toBeDisabled();
  });
});
