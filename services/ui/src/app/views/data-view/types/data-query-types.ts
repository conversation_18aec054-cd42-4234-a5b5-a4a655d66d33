import { components } from "../../../../../../../libs/shared/ict-sdk/generated/openapi-react-query";

export type DataQuery = components["schemas"]["DataQuery"];
export type DataQueryResult = components["schemas"]["DataQueryResult"];
export type DataQueryType = components["schemas"]["DataQueryType"];

export const DATA_QUERY_TYPES: Array<{ value: DataQueryType; label: string }> =
  [
    { value: "singleValue", label: "Single Value" },
    { value: "categorySeries", label: "Category Series" },
    { value: "timeSeries", label: "Time Series" },
    { value: "tabular", label: "Tabular" },
    { value: "filter", label: "Filter" },
  ];
