import { AppConfigSettingSource } from "@ict/sdk/types";
import {
  createNewDataSetting,
  getNavigationPath,
  isDataQueryTypeSupported,
} from "./data-view-setting.util";
import { AppConfigSetting } from "../../../config/menu/types";

describe("data-view-setting.util", () => {
  describe("createNewDataSetting", () => {
    it("should create a singleValue setting", () => {
      const result = createNewDataSetting("Test Query", "singleValue") as any;

      expect(result.name).toBe("Test Query");
      expect(result.description).toBe("Test Query single value query");
      expect(result.group).toBe("data-single-value");
      expect(result.dataType).toBe("json");
      expect(result.source).toBe(AppConfigSettingSource.default);
      expect(result.value).toBeDefined();
      expect(result.value?.type).toBe("singleValue");
      expect(result.value?.isDraft).toBe(true);
      expect(result.value?.config).toEqual([]);
      expect(result.value?.metadata?.unit).toBe("");
    });

    it("should create a categorySeries setting", () => {
      const result = createNewDataSetting(
        "Test Category",
        "categorySeries",
      ) as any;

      expect(result.name).toBe("Test Category");
      expect(result.description).toBe("Test Category category series query");
      expect(result.group).toBe("data-category-series");
      expect(result.value?.type).toBe("categorySeries");
      expect(result.value?.config).toEqual([]);
      expect(result.value?.metadata?.unit).toBe("");
    });

    it("should create a timeSeries setting", () => {
      const result = createNewDataSetting("Test Time", "timeSeries") as any;

      expect(result.name).toBe("Test Time");
      expect(result.description).toBe("Test Time time series query");
      expect(result.group).toBe("data-time-series");
      expect(result.value?.type).toBe("timeSeries");
      expect(result.value?.config).toEqual([]);
      expect(result.value?.metadata?.unit).toBe("");
    });

    it("should create a tabular setting", () => {
      const result = createNewDataSetting("Test Table", "tabular") as any;

      expect(result.name).toBe("Test Table");
      expect(result.description).toBe("Test Table tabular query");
      expect(result.group).toBe("data-tabular");
      expect(result.value?.type).toBe("tabular");
      expect(result.value?.config).toEqual([]);
      expect(result.value?.metadata?.unit).toBe("");
    });

    it("should create a filter setting", () => {
      const result = createNewDataSetting("Test Filter", "filter") as any;

      expect(result.name).toBe("Test Filter");
      expect(result.description).toBe("Test Filter filter query");
      expect(result.group).toBe("data-filter");
      expect(result.value).toBeDefined();
      expect(result.value?.type).toBe("filter");
      expect(result.value?.isDraft).toBe(true);
      expect(result.value?.config).toBeUndefined();
      expect(result.value?.metadata?.unit).toBeUndefined();
    });

    it("should include correct query template for singleValue", () => {
      const result = createNewDataSetting(
        "Test",
        "singleValue",
      ) as AppConfigSetting;

      expect(result.value).toBeDefined();
    });

    it("should include correct query template for filter", () => {
      const result = createNewDataSetting("Test", "filter");

      expect(result.value).toBeDefined();
    });
  });

  describe("getNavigationPath", () => {
    it("should return correct path for singleValue", () => {
      const result = getNavigationPath("singleValue", "test-id");

      expect(result).toBe("data-point/test-id");
    });

    it("should return correct path for categorySeries", () => {
      const result = getNavigationPath("categorySeries", "test-id");

      expect(result).toBe("category-series/test-id");
    });

    it("should return correct path for timeSeries", () => {
      const result = getNavigationPath("timeSeries", "test-id");

      expect(result).toBe("time-series/test-id");
    });

    it("should return correct path for tabular", () => {
      const result = getNavigationPath("tabular", "test-id");

      expect(result).toBe("tabular/test-id");
    });

    it("should return correct path for filter", () => {
      const result = getNavigationPath("filter", "test-id");

      expect(result).toBe("filter/test-id");
    });
  });

  describe("isDataQueryTypeSupported", () => {
    it("should return true for singleValue", () => {
      expect(isDataQueryTypeSupported("singleValue")).toBe(true);
    });

    it("should return true for categorySeries", () => {
      expect(isDataQueryTypeSupported("categorySeries")).toBe(true);
    });

    it("should return true for timeSeries", () => {
      expect(isDataQueryTypeSupported("timeSeries")).toBe(true);
    });

    it("should return true for tabular", () => {
      expect(isDataQueryTypeSupported("tabular")).toBe(true);
    });

    it("should return true for filter", () => {
      expect(isDataQueryTypeSupported("filter")).toBe(true);
    });

    it("should return false for unsupported types", () => {
      expect(isDataQueryTypeSupported("data-point")).toBe(false);
      expect(isDataQueryTypeSupported("trend")).toBe(false);
      expect(isDataQueryTypeSupported("grid")).toBe(false);
      expect(isDataQueryTypeSupported("invalid")).toBe(false);
      expect(isDataQueryTypeSupported("")).toBe(false);
    });
  });
});
