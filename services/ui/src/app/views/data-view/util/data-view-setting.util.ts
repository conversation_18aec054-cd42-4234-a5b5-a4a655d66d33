import type { AppConfigSetting } from "@ict/sdk/types";
import { AppConfigSettingSource } from "@ict/sdk/types";

export type DataQueryType =
  | "singleValue"
  | "categorySeries"
  | "timeSeries"
  | "tabular"
  | "filter";

/**
 * Creates a new data setting configuration for different data query types
 */
export const createNewDataSetting = (
  name: string,
  type: DataQueryType,
): Partial<AppConfigSetting> => {
  const isFilter = type === "filter";
  const getTypeLabel = (queryType: DataQueryType): string => {
    switch (queryType) {
      case "singleValue":
        return "single value";
      case "categorySeries":
        return "category series";
      case "timeSeries":
        return "time series";
      case "tabular":
        return "tabular";
      case "filter":
        return "filter";
      default:
        return "data";
    }
  };

  const getGroup = (queryType: DataQueryType): string => {
    switch (queryType) {
      case "singleValue":
        return "data-single-value";
      case "categorySeries":
        return "data-category-series";
      case "timeSeries":
        return "data-time-series";
      case "tabular":
        return "data-tabular";
      case "filter":
        return "data-filter";
      default:
        return "data-point";
    }
  };

  const getQueryTemplate = (): string => {
    return "-- Enter your SQL query here\n";
  };

  return {
    name: name,
    description: `${name} ${getTypeLabel(type)} query`,
    group: getGroup(type),
    dataType: "json",
    source: AppConfigSettingSource.default,
    value: {
      id: name,
      label: name,
      type: type,
      description: `${name} ${getTypeLabel(type)} query`,
      filters: [],
      isDraft: true,
      metadata: {
        ...(isFilter ? {} : { unit: "" }), // No unit for filters
        category: "",
      },
      dataSources: [
        {
          id: "edp-bigquery",
          type: "edp-bigquery",
          table: "",
        },
      ],
      // No config for filters
      ...(isFilter ? {} : { config: [] }),
      parameters: {
        required: ["start_date", "end_date"],
      },
      query: getQueryTemplate(),
    },
  };
};

/**
 * Gets the navigation path for a data query type
 */
export const getNavigationPath = (type: DataQueryType, id: string): string => {
  switch (type) {
    case "singleValue":
      return `data-point/${id}`;
    case "categorySeries":
      return `category-series/${id}`;
    case "timeSeries":
      return `time-series/${id}`;
    case "tabular":
      return `tabular/${id}`;
    case "filter":
      return `filter/${id}`;
    default:
      return `data-point/${id}`;
  }
};

/**
 * Validates if a data query type is supported
 */
export const isDataQueryTypeSupported = (
  type: string,
): type is DataQueryType => {
  return (
    type === "singleValue" ||
    type === "categorySeries" ||
    type === "timeSeries" ||
    type === "tabular" ||
    type === "filter"
  );
};
