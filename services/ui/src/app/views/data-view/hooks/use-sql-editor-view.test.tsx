import { vi } from "vitest";
import { renderHook, act } from "../../../../test-utils";
import { useSqlEditorView } from "./use-sql-editor-view";
import type { SqlEditorViewConfig } from "./use-sql-editor-view";

// Mock data
const mockAppConfigSettings = [
  {
    id: "test-setting-id",
    group: "test-group",
    name: "Test Setting",
    value: { query: "SELECT * FROM test_table" },
  },
];

// Mock functions - hoisted
const mockNavigate = vi.hoisted(() => vi.fn());
const mockFetchQuery = vi.hoisted(() => vi.fn());
const mockQueryOptions = vi.hoisted(() => vi.fn());
const mockShowInfoToast = vi.hoisted(() => vi.fn());
const mockShowSuccessToast = vi.hoisted(() => vi.fn());
const mockShowErrorToast = vi.hoisted(() => vi.fn());
const mockSaveSetting = vi.hoisted(() => vi.fn());
const mockDeleteSourceLevel = vi.hoisted(() => vi.fn());

// Mock implementations
vi.mock("react-router", () => ({
  useParams: () => ({ settingId: "test-setting-id" }),
  useNavigate: () => mockNavigate(),
}));

vi.mock("../../../api/ict-api", () => ({
  ictApi: {
    queryClient: {
      fetchQuery: mockFetchQuery(),
    },
    client: {
      queryOptions: mockQueryOptions(),
    },
  },
}));

vi.mock("../../../components/toast/use-notification", () => ({
  useNotification: () => ({
    info: mockShowInfoToast(),
    success: mockShowSuccessToast(),
    error: mockShowErrorToast(),
  }),
}));

vi.mock("../../../config/hooks/use-config", () => ({
  useConfig: () => ({
    data: mockAppConfigSettings,
    isLoading: false,
    error: null,
  }),
}));

vi.mock("../../../config/hooks/use-source-management", () => ({
  useSourceManagement: () => ({
    activeSetting: mockAppConfigSettings[0],
    allSources: [
      { level: "facility", exists: true },
      { level: "tenant", exists: true },
      { level: "default", exists: true },
    ],
    hasUnsavedChanges: false,
    isLoading: false,
    error: null,
    saveSetting: mockSaveSetting(),
    deleteSourceLevel: mockDeleteSourceLevel(),
  }),
}));

describe("useSqlEditorView", () => {
  const defaultConfig: SqlEditorViewConfig = {
    settingGroup: "test-group",
    entityName: "Data Point",
    sqlPlaceholder: "SELECT * FROM table",
    supportsCopyQuery: true,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("initializes with correct initial state", () => {
    const { result } = renderHook(() => useSqlEditorView(defaultConfig));

    expect(result.current.setting).toEqual(mockAppConfigSettings[0]);
    expect(result.current.isSettingsOpen).toBe(false);
    expect(result.current.queryResult).toBeNull();
    expect(result.current.isExecuting).toBe(false);
    expect(result.current.queryError).toBe("");
    expect(result.current.draftSqlQuery).toBe("SELECT * FROM test_table");
    expect(result.current.hasUnsavedChanges).toBe(false);
    expect(result.current.isLoading).toBe(false);
  });

  it("updates draft SQL query", () => {
    const { result } = renderHook(() => useSqlEditorView(defaultConfig));

    act(() => {
      result.current.setDraftSqlQuery("SELECT * FROM new_table");
    });

    expect(result.current.draftSqlQuery).toBe("SELECT * FROM new_table");
    expect(result.current.hasUnsavedChanges).toBe(true);
  });

  it("returns next active source correctly", () => {
    const { result } = renderHook(() => useSqlEditorView(defaultConfig));

    const nextSource = result.current.getNextActiveSource("facility");
    expect(nextSource).toBe("tenant");
  });

  it("handles settings modal state", () => {
    const { result } = renderHook(() => useSqlEditorView(defaultConfig));

    act(() => {
      result.current.setIsSettingsOpen(true);
    });

    expect(result.current.isSettingsOpen).toBe(true);
  });

  it("sets up delete modal correctly", () => {
    const { result } = renderHook(() => useSqlEditorView(defaultConfig));

    act(() => {
      result.current.handleDeleteSource("facility");
    });

    expect(result.current.deleteModalOpen).toBe(true);
    expect(result.current.sourceToDelete).toBe("facility");
  });

  it("cancels delete modal", () => {
    const { result } = renderHook(() => useSqlEditorView(defaultConfig));

    act(() => {
      result.current.handleDeleteSource("facility");
      result.current.handleCancelDelete();
    });

    expect(result.current.deleteModalOpen).toBe(false);
    expect(result.current.sourceToDelete).toBeNull();
  });
});
