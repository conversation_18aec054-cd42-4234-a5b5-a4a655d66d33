import type { components } from "@ict/sdk/openapi-react-query";
import type { AppConfigSetting } from "@ict/sdk/types";
import { useCallback, useEffect, useRef, useState } from "react";
import { useNavigate, useParams } from "react-router";

import { ictApi } from "../../../api/ict-api";
import { useNotification } from "../../../components/toast/use-notification";
import { useConfig } from "../../../config/hooks/use-config";
import {
  useSourceManagement,
  type SourceLevel,
} from "../../../config/hooks/use-source-management";
import type {
  BottomPanelRef,
  RequestFilters,
} from "../components/bottom-panel/bottom-panel";
import { Logger } from "../../../utils";

export type SqlQuery = components["schemas"]["DataQuery"];
export type SqlQueryResult = components["schemas"]["DataQueryResult"];

export interface SqlEditorViewConfig {
  settingGroup: string;
  entityName: string; // "Data Point", "Filter", etc.
  sqlPlaceholder?: string;
  supportsCopyQuery?: boolean;
}

export const useSqlEditorView = (config: SqlEditorViewConfig) => {
  const { settingId } = useParams<{ settingId: string }>();
  const {
    data: appConfigSettings,
    isLoading: configLoading,
    error: configError,
  } = useConfig();
  const navigate = useNavigate();

  // State management
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const [queryResult, setQueryResult] = useState<SqlQueryResult | null>(null);
  const [isExecuting, setIsExecuting] = useState(false);
  const [queryError, setQueryError] = useState<string>("");
  const [draftSqlQuery, setDraftSqlQuery] = useState<string>("");
  const [isCopyingQuery, setIsCopyingQuery] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [sourceToDelete, setSourceToDelete] = useState<SourceLevel | null>(
    null,
  );
  const [requestFilters, setRequestFilters] = useState<RequestFilters | null>(
    null,
  );
  const bottomPanelRef = useRef<BottomPanelRef>(null);

  const {
    info: showInfoToast,
    success: showSuccessToast,
    error: showErrorToast,
  } = useNotification();

  const logger = new Logger("UseSqlEditorView");

  // Find the specific setting
  const fallbackSetting = (
    (appConfigSettings as AppConfigSetting[]) || []
  ).find((s) => s.id === settingId && s.group === config.settingGroup);

  // Extract setting name for source management
  const settingName = fallbackSetting?.name || settingId || "";

  // Use source management hook
  const sourceManagement = useSourceManagement(
    settingName,
    (message, title) => showInfoToast(message, { title }),
    () => {
      logger.info("No configuration found, redirecting to data configuration");
      navigate("..");
    },
  );

  // Use the active setting from source management or fallback
  const setting = sourceManagement.activeSetting || fallbackSetting;

  // Extract the SQL query from the setting
  const settingValue = setting?.value as Record<string, unknown>;
  const sqlQuery = (settingValue?.query as string) || "";

  // Initialize local state when setting changes
  useEffect(() => {
    if (setting) {
      setDraftSqlQuery(sqlQuery);
    }
  }, [setting, sqlQuery]);

  // Handle request filter changes
  const handleRequestChange = useCallback((filters: RequestFilters) => {
    setRequestFilters(filters);
  }, []);

  // Common query execution logic
  const executeQuery = async (isDryRun = false) => {
    if (!draftSqlQuery.trim()) {
      setQueryError("No query to execute");
      return null;
    }

    // Use request filters if available, otherwise fall back to today
    let queryStartDate: string;
    let queryEndDate: string;
    const queryParams: Record<string, string> = {};

    if (requestFilters) {
      queryStartDate = requestFilters.startDate;
      queryEndDate = requestFilters.endDate;

      // Add filter and property query parameters, using pipe delimiter for arrays
      for (const [key, value] of Object.entries(requestFilters)) {
        if (
          key !== "startDate" &&
          key !== "endDate" &&
          value !== null &&
          value !== undefined
        ) {
          // Replace hyphens with underscores for query param names
          const paramKey = key.replace(/-/g, "_");
          if (Array.isArray(value)) {
            queryParams[paramKey] = value.join("|");
          } else {
            queryParams[paramKey] = String(value);
          }
        }
      }
    } else {
      // Fallback to today's date range
      const today = new Date();
      const startOfDay = new Date(
        today.getFullYear(),
        today.getMonth(),
        today.getDate(),
      );
      const endOfDay = new Date(
        today.getFullYear(),
        today.getMonth(),
        today.getDate() + 1,
      );
      queryStartDate = startOfDay.toISOString();
      queryEndDate = endOfDay.toISOString();
    }

    // Build the query request
    const settingValue = setting?.value as Record<string, unknown>;
    const sqlQueryObj: SqlQuery = {
      ...(settingValue as SqlQuery),
      query: draftSqlQuery,
    };

    // Execute the query
    const response = await ictApi.queryClient.fetchQuery(
      ictApi.client.queryOptions("post", "/data/query", {
        params: {
          query: {
            start_date: queryStartDate,
            end_date: queryEndDate,
            ...(isDryRun && { dryRun: "true" }),
            ...queryParams,
          },
        },
        body: sqlQueryObj,
      }),
    );

    return response as SqlQueryResult;
  };

  const handleExecuteQuery = async () => {
    setIsExecuting(true);
    setQueryError("");

    // Switch to Response tab when starting execution
    bottomPanelRef.current?.switchToResponseTab();

    try {
      const queryResult = await executeQuery();
      if (queryResult) {
        setQueryResult(queryResult);
      }
    } catch (err) {
      const errorDetail = (err as { detail: string }).detail;
      setQueryError(errorDetail || "Failed to execute query");
    } finally {
      setIsExecuting(false);
    }
  };

  const handleCopyQuery = async () => {
    if (!config.supportsCopyQuery) {
      showErrorToast("Copy query not supported for this view");
      return;
    }

    if (!draftSqlQuery.trim()) {
      showErrorToast("No query to copy");
      return;
    }

    setIsCopyingQuery(true);

    try {
      const queryResult = await executeQuery(true); // dry run

      // Copy the queryRequest to clipboard
      if (queryResult?.queryRequest) {
        await navigator.clipboard.writeText(queryResult.queryRequest);
        showSuccessToast("Query copied to clipboard!");
      } else {
        showErrorToast("No query request found in response");
      }
    } catch (err) {
      const errorDetail = (err as { detail: string }).detail;
      showErrorToast(errorDetail || "Failed to copy query");
    } finally {
      setIsCopyingQuery(false);
    }
  };

  const handleSave = async () => {
    if (!setting) return;

    // Create updated setting with SQL changes
    const updatedSetting = {
      ...setting,
      value: {
        ...(setting.value as Record<string, unknown>),
        query: draftSqlQuery,
      },
    };

    logger.info(
      `Saving ${config.entityName.toLowerCase()} setting:`,
      updatedSetting,
    );
    try {
      await sourceManagement.saveSetting(updatedSetting);
    } catch (err) {
      logger.error(
        `Failed to save ${config.entityName.toLowerCase()} setting:`,
        err,
      );
    }
  };

  // Handle delete source request
  const handleDeleteSource = (sourceLevel: SourceLevel) => {
    setSourceToDelete(sourceLevel);
    setDeleteModalOpen(true);
  };

  // Handle delete confirmation
  const handleConfirmDelete = async () => {
    if (!sourceToDelete || !setting?.id) return;

    try {
      await sourceManagement.deleteSourceLevel(sourceToDelete, setting);
      showSuccessToast(
        `${sourceToDelete} configuration level deleted successfully`,
      );
      setDeleteModalOpen(false);
      setSourceToDelete(null);
    } catch (err) {
      showErrorToast(
        err instanceof Error
          ? err.message
          : "Failed to delete configuration level",
      );
    }
  };

  // Handle delete cancel
  const handleCancelDelete = () => {
    setDeleteModalOpen(false);
    setSourceToDelete(null);
  };

  // Determine next active source after deletion
  const getNextActiveSource = (
    sourceToDelete: SourceLevel,
  ): SourceLevel | null => {
    const sourceHierarchy: SourceLevel[] = ["facility", "tenant", "default"];
    const deleteIndex = sourceHierarchy.indexOf(sourceToDelete);

    // Find the next lower level that exists
    for (let i = deleteIndex + 1; i < sourceHierarchy.length; i++) {
      const lowerLevel = sourceHierarchy[i];
      const lowerLevelExists = sourceManagement.allSources.find(
        (s) => s.level === lowerLevel && s.exists,
      );
      if (lowerLevelExists) {
        return lowerLevel;
      }
    }
    return "default"; // Fallback to default
  };

  // Check if there are unsaved changes
  const hasUnsavedChanges =
    draftSqlQuery !== sqlQuery || sourceManagement.hasUnsavedChanges;

  // Handle error state
  const error = configError || sourceManagement.error;
  const isLoading = configLoading || sourceManagement.isLoading;

  return {
    // State
    setting,
    isSettingsOpen,
    setIsSettingsOpen,
    queryResult,
    isExecuting,
    queryError,
    draftSqlQuery,
    setDraftSqlQuery,
    isCopyingQuery,
    deleteModalOpen,
    sourceToDelete,
    bottomPanelRef,
    hasUnsavedChanges,
    error,
    isLoading,

    // Source management
    sourceManagement,

    // Event handlers
    handleExecuteQuery,
    handleCopyQuery,
    handleSave,
    handleDeleteSource,
    handleConfirmDelete,
    handleCancelDelete,
    handleRequestChange,
    getNextActiveSource,

    // Navigation
    navigate,
  };
};
