import { Route, Routes } from "react-router";
import type { BaseViewProps } from "../view-registry.types";
import { DataViewTable } from "./components/data-view-table/data-view-table";
import { DataPointView } from "./components/data-point-view/data-point-view";
import { CategorySeriesView } from "./components/category-series-view/category-series-view";
import { TimeSeriesView } from "./components/time-series-view/time-series-view";
import { TabularView } from "./components/tabular-view/tabular-view";
import { FilterView } from "./components/filter-view/filter-view";

export function DataView(_props: BaseViewProps) {
  return (
    <Routes>
      <Route index element={<DataViewTable />} />
      <Route path="data-point/:settingId" element={<DataPointView />} />
      <Route
        path="category-series/:settingId"
        element={<CategorySeriesView />}
      />
      <Route path="time-series/:settingId" element={<TimeSeriesView />} />
      <Route path="tabular/:settingId" element={<TabularView />} />
      <Route path="filter/:settingId" element={<FilterView />} />
    </Routes>
  );
}

export default DataView;
