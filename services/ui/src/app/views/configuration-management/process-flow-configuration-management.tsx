import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@carbon/react";
import { Datagrid } from "../../components/datagrid";
import { FullPageContainer } from "../../components/full-page-container/full-page-container";
import { mockFactData } from "../facility-process-flow/views/admin/configuration/mock-configuration-management-data";
import { createColumnHelper } from "@tanstack/react-table";
import { getFactDatagridColumns } from "../facility-process-flow/views/admin/configuration/column-definitions";
import {
  ConfigurationData,
  MetricConfigFacts,
  ConfigItemTypes,
  SelectedItem,
} from "../facility-process-flow/views/admin/configuration/types";
import { MetricConfigsList } from "../facility-process-flow/views/admin/configuration/metric-configs-list";
import { ViewBar } from "../../components/view-bar/view-bar";
import styles from "./process-flow-configuration-management.module.scss";
import { useTranslation } from "react-i18next";
import { Link, useSearchParams } from "react-router";
import { MetricConfigFactsList } from "../facility-process-flow/views/admin/configuration/metric-config-facts-list";

const getItemTypeFromIndex = (index: number): ConfigItemTypes | undefined => {
  const orderedConfigItemTypes: ConfigItemTypes[] = [
    ConfigItemTypes.Fact,
    ConfigItemTypes.Metric,
    ConfigItemTypes.Node,
  ];
  return orderedConfigItemTypes[index];
};

export function ConfigurationManagement() {
  const { t } = useTranslation();
  const [searchParams, setSearchParams] = useSearchParams();
  const [itemType, setItemType] = useState<ConfigItemTypes>(
    ConfigItemTypes.Fact,
  );
  const [selectedItem, setSelectedItem] = useState<SelectedItem | null>(null);

  // Get fact filter from URL params
  const factFilter = searchParams.get('fact_type');
  const [datagridData, setDatagridData] = useState<ConfigurationData[]>(
    mockFactData.data,
  );
  // TODO: Remove this any and work out the typescript errors.
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [columns, setColumns] = useState<any[]>([]); // ← new state for columns

  const handleDeleteFactButton = (factName: string): void => {
    if (!confirm(`Are you sure you want to delete ${factName}?`)) return;
    console.log("Delete fact config not implemented...");
    // TODO: Perform a soft-delete on metric config. Use the API to update the isDeleted column for metric config.
  };

  const handleViewFactButton = (factName: string): void => {
    console.log("View fact button clicked:", factName);
    // Switch to metrics tab and set URL param for filtering
    setItemType(ConfigItemTypes.Metric);
    setSearchParams({ fact_type: factName });
  };

  const handleClearFilter = (): void => {
    setSearchParams({});
  };



  useEffect(() => {
    // initialize columns for default tab: Facts
    const columnHelper = createColumnHelper<MetricConfigFacts>();
    const initialColumns = getFactDatagridColumns(
      columnHelper,
      handleDeleteFactButton,
      handleViewFactButton,
    );
    setColumns(initialColumns);
  }, []);

  const handleTabChange = (selectedTabIndex: number) => {
    const selectedItemType = getItemTypeFromIndex(selectedTabIndex);
    if (!selectedItemType) return;

    setItemType(selectedItemType);
    setSelectedItem(null); // Clear selection when tab changes

    if (selectedItemType === ConfigItemTypes.Fact) {
      const columnHelper = createColumnHelper<MetricConfigFacts>();
      const initialColumns = getFactDatagridColumns(
        columnHelper,
        handleDeleteFactButton,
        handleViewFactButton,
      );
      setDatagridData(mockFactData.data);
      setColumns(initialColumns);
    } else {
      setColumns([]);
      setDatagridData([]);
    }
  };

  const isError = false;
  const isLoading = false;

  /**
   * Renders the breadcrumb-style title with link back to the Process Flow graph
   */
  const renderTitleContent = () => {
    return (
      <div data-testid="process-flow-configuration-management">
        <Link to="/ict-facility-process-flow" className={styles.titleLink}>
          <span className={styles.titleText}>{"Facility Process Flow"}</span>
        </Link>
        <span className={styles.titleSeparator}>/</span>
        <span>
          {t("configurationManagement.title", "Configuration Management")}
          {factFilter && ` - Metrics for ${factFilter}`}
        </span>
      </div>
    );
  };

  const renderDetailView = () => {
    if (!selectedItem) return null;

    switch (selectedItem.type) {
      case ConfigItemTypes.Fact:
        return (
          <div>
            <h3>Fact Details for: {selectedItem.name}</h3>
            {/* Replace below with actual detail rendering */}
            <p>
              This is where detailed data for{" "}
              <strong>{selectedItem.name}</strong> will go.
            </p>
          </div>
        );
      case ConfigItemTypes.Node:
        return <div>Node Detail View - Not implemented.</div>;
      default:
        return null;
    }
  };

  // Get the tab index from the item type
  const getTabIndexFromItemType = (type: ConfigItemTypes): number => {
    const orderedConfigItemTypes: ConfigItemTypes[] = [
      ConfigItemTypes.Fact,
      ConfigItemTypes.Metric,
      ConfigItemTypes.Node,
    ];
    return orderedConfigItemTypes.indexOf(type);
  };

  return (
    <>
      <ViewBar
        title={renderTitleContent()}
        showDatePeriodRange={false}
        showSettings={false}
        showSave={false}
      />

      <FullPageContainer>
        {selectedItem ? (
          renderDetailView()
        ) : (
          <div className={styles.tabsContainer}>
            <Tabs
              onChange={({ selectedIndex }) => handleTabChange(selectedIndex)}
              selectedIndex={getTabIndexFromItemType(itemType)}
            >
              <TabList contained>
                <Tab id="fact">
                  {t("configurationManagement.tabs.facts", "Facts")}
                </Tab>
                <Tab id="metric">
                  {t("configurationManagement.tabs.metrics", "Metrics")}
                </Tab>
                <Tab id="node">
                  {t("configurationManagement.tabs.nodes", "Nodes")}
                </Tab>
              </TabList>

              <TabPanels>
                <TabPanel className={styles.tableContainer}>
                  <MetricConfigFactsList onViewFact={handleViewFactButton} />
                </TabPanel>
                <TabPanel className={styles.tableContainer}>
                  <MetricConfigsList
                    factTypeFilter={factFilter || undefined}
                    onClearFilter={handleClearFilter}
                  />
                </TabPanel>
                <TabPanel className={styles.tableContainer}>
                  <Datagrid
                    columns={columns}
                    data={datagridData ?? []}
                    mode="client"
                    enableSelection={false}
                    initialPagination={{ pageIndex: 0, pageSize: 100 }}
                    initialDensity="compact"
                    isLoading={isLoading}
                  />
                </TabPanel>
              </TabPanels>
            </Tabs>
            {isError && <div>Failed to load data</div>}
          </div>
        )}
      </FullPageContainer>
    </>
  );
}

export default ConfigurationManagement;
