.tabsContainer {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.tableContainer {
  height: calc(
    100vh - 200px
  ); // Full viewport minus ViewBar, FullPageContainer padding, and tab headers
  margin-top: 0; // No space between tabs and table
  overflow: visible; // Allow tooltips to extend beyond container and not be covered by the tabs
}

.titleLink {
  color: inherit;
  text-decoration: none;
  cursor: pointer;
}

.titleText {
  font-weight: bold;
}

.titleSeparator {
  margin: 0 8px;
}
