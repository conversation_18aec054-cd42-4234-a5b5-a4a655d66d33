.tabsContainer {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.tableContainer {
  display: flex;
  flex-direction: column;
  height: calc(
    100vh - 200px
  ); // Full viewport minus ViewBar, FullPageContainer padding, and tab headers
  margin-top: 0; // No space between tabs and table
  overflow: hidden; // Prevent content from spilling out
}

.titleLink {
  color: inherit;
  text-decoration: none;
  cursor: pointer;
}

.titleText {
  font-weight: bold;
}

.titleSeparator {
  margin: 0 8px;
}

.filterTag {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0;
  flex-shrink: 0; // Don't shrink when space is limited
}

.tableWrapper {
  flex: 1; // Take remaining space after filter tag
  overflow: hidden; // Prevent table from overflowing
  display: flex;
  flex-direction: column;
}
