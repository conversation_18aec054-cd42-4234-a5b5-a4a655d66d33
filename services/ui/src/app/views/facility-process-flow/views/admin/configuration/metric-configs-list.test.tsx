import { vi } from "vitest";
import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom/vitest";
import { MetricConfigsList } from "./metric-configs-list";
import { ictApi } from "../../../../../api/ict-api";
import { MetricConfigSummary } from "./types";

// Mock the ICT API
vi.mock("../../../../../api/ict-api", () => {
  return {
    ictApi: {
      client: {
        useQuery: vi.fn(),
      },
    },
  };
});

// Mock the MetricDetailModal component
vi.mock("./metric-detail-modal", () => ({
  MetricDetailModal: ({
    metricName,
    isModalOpen,
    onRequestClose,
  }: {
    metricName: string;
    isModalOpen: boolean;
    onRequestClose: () => void;
  }) =>
    isModalOpen ? (
      <div data-testid="metric-detail">
        <div data-testid="metric-name">{metricName}</div>
        <button data-testid="close-detail" onClick={onRequestClose}>
          Close
        </button>
      </div>
    ) : null,
}));

// Mock Carbon React components
vi.mock("@carbon/react", () => ({
  GlobalTheme: ({ children }: { children: React.ReactNode }) => children,
  Button: ({ children, onClick, kind, size, style, ...props }: any) => (
    <button
      onClick={onClick}
      data-testid="button"
      data-kind={kind}
      data-size={size}
      style={style}
      {...props}
    >
      {children}
    </button>
  ),
  IconButton: ({ children, onClick, label, kind, size, ...props }: any) => (
    <button
      onClick={onClick}
      data-testid="icon-button"
      data-label={label}
      data-kind={kind}
      data-size={size}
      aria-label={label}
      {...props}
    >
      {children}
    </button>
  ),
  Modal: ({ children, open, modalHeading, onRequestClose, ...props }: any) =>
    open ? (
      <div data-testid="modal" {...props}>
        <div data-testid="modal-header">{modalHeading}</div>
        <div data-testid="modal-body">{children}</div>
        <button data-testid="modal-close" onClick={onRequestClose}>
          Close
        </button>
      </div>
    ) : null,
  ComposedModal: ({ children, open, onClose, ...props }: any) =>
    open ? (
      <div data-testid="composed-modal" {...props}>
        {children}
        <button data-testid="composed-modal-close" onClick={onClose}>
          Close
        </button>
      </div>
    ) : null,
  ModalHeader: ({ title, closeModal, children, ...props }: any) => (
    <div data-testid="modal-header" {...props}>
      {title}
      {children}
      <button data-testid="modal-header-close" onClick={closeModal}>
        ×
      </button>
    </div>
  ),
  ModalBody: ({ children, ...props }: any) => (
    <div data-testid="modal-body" {...props}>
      {children}
    </div>
  ),
}));

// Mock Carbon icons
vi.mock("@carbon/icons-react", () => ({
  CheckmarkOutline: ({ color }: { color?: string }) => (
    <div data-testid="checkmark-outline" data-color={color}>
      ✓
    </div>
  ),
  CloseOutline: ({ color }: { color?: string }) => (
    <div data-testid="close-outline" data-color={color}>
      ✗
    </div>
  ),
  Edit: () => <div data-testid="edit-icon">Edit</div>,
  RowDelete: () => <div data-testid="delete-icon">Delete</div>,
}));

// Mock the Datagrid component
vi.mock("../../../../../components/datagrid", () => ({
  Datagrid: ({
    columns,
    data,
    isLoading,
    error,
    mode,
    enableSelection,
    initialDensity,
  }: any) => {
    if (isLoading) {
      return <div data-testid="datagrid-loading">Loading...</div>;
    }

    if (error) {
      return (
        <div data-testid="datagrid-error">
          {error.message || "Error occurred"}
        </div>
      );
    }

    return (
      <div
        data-testid="datagrid"
        data-mode={mode}
        data-enable-selection={enableSelection?.toString()}
        data-initial-density={initialDensity}
      >
        <table>
          <thead>
            <tr>
              {columns.map((col: any, index: number) => (
                <th key={index}>{col.header}</th>
              ))}
            </tr>
          </thead>
          <tbody>
            {data?.map((row: any, rowIndex: number) => (
              <tr key={rowIndex} data-testid={`datagrid-row-${rowIndex}`}>
                {columns.map((col: any, colIndex: number) => (
                  <td
                    key={colIndex}
                    data-testid={`cell-${rowIndex}-${colIndex}`}
                  >
                    {col.cell
                      ? col.cell({
                          getValue: () =>
                            col.accessorFn
                              ? col.accessorFn(row)
                              : row[col.accessorKey],
                        })
                      : row[col.accessorKey]}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  },
}));

// Simple test wrapper without complex providers
const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <div>{children}</div>
);

describe("MetricConfigsList", () => {
  const mockMetricConfigs: MetricConfigSummary[] = [
    {
      id: "1",
      metricName: "Test Metric 1",
      factType: "Test Fact",
      nodeName: "Test Node",
      views: ["facility", "receiving"],
      configType: "node",
      isCustom: false,
      enabled: true,
      facilityId: "test-facility-1",
    },
    {
      id: "2",
      metricName: "Test Metric 2",
      factType: "Custom Fact",
      nodeName: "Custom Node",
      views: ["multishuttle"],
      configType: "edge",
      isCustom: true,
      enabled: false,
      facilityId: "test-facility-2",
    },
  ];

  const mockUseQuery = vi.fn();

  beforeEach(() => {
    mockUseQuery.mockClear();
    (ictApi.client.useQuery as any) = mockUseQuery;

    // Mock console.log to avoid noise in tests
    vi.spyOn(console, "log").mockImplementation(() => {});
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  const renderComponent = () => {
    return render(<MetricConfigsList />, { wrapper: TestWrapper });
  };

  it("renders loading state when data is loading", () => {
    mockUseQuery.mockReturnValue({
      data: undefined,
      isLoading: true,
      error: null,
    });

    renderComponent();

    expect(screen.getByTestId("datagrid-loading")).toBeInTheDocument();
    expect(screen.getByText("Loading...")).toBeInTheDocument();
  });

  it("renders error state when there is an error", () => {
    const errorMessage = "Failed to load metric configs";
    mockUseQuery.mockReturnValue({
      data: undefined,
      isLoading: false,
      error: new Error(errorMessage),
    });

    renderComponent();

    expect(screen.getByTestId("datagrid-error")).toBeInTheDocument();
    expect(screen.getByText(errorMessage)).toBeInTheDocument();
  });

  it("renders datagrid with metric configs data", () => {
    mockUseQuery.mockReturnValue({
      data: mockMetricConfigs,
      isLoading: false,
      error: null,
    });

    renderComponent();

    expect(screen.getByTestId("datagrid")).toBeInTheDocument();

    // Check headers
    expect(screen.getByText("Enabled")).toBeInTheDocument();
    expect(screen.getByText("Metric Name")).toBeInTheDocument();
    expect(screen.getByText("Fact Name")).toBeInTheDocument();
    expect(screen.getByText("Node Name")).toBeInTheDocument();
    expect(screen.getByText("Views")).toBeInTheDocument();
    expect(screen.getByText("Metric Type")).toBeInTheDocument();
    expect(screen.getByText("Type")).toBeInTheDocument();

    // Check first row data
    expect(screen.getByText("Test Metric 1")).toBeInTheDocument();
    expect(screen.getByText("Test Fact")).toBeInTheDocument();
    expect(screen.getByText("Test Node")).toBeInTheDocument();
  });

  it("displays enabled status with correct icons", () => {
    mockUseQuery.mockReturnValue({
      data: mockMetricConfigs,
      isLoading: false,
      error: null,
    });

    renderComponent();

    // Check enabled icon (green checkmark)
    const enabledIcon = screen.getByTestId("checkmark-outline");
    expect(enabledIcon).toBeInTheDocument();
    expect(enabledIcon).toHaveAttribute("data-color", "green");

    // Check disabled icon (red close)
    const disabledIcon = screen.getByTestId("close-outline");
    expect(disabledIcon).toBeInTheDocument();
    expect(disabledIcon).toHaveAttribute("data-color", "red");
  });

  it("displays custom vs default type correctly", () => {
    mockUseQuery.mockReturnValue({
      data: mockMetricConfigs,
      isLoading: false,
      error: null,
    });

    renderComponent();

    expect(screen.getByText("Default")).toBeInTheDocument(); // First metric (isCustom: false)
    expect(screen.getByText("Custom")).toBeInTheDocument(); // Second metric (isCustom: true)
  });

  it("renders edit buttons for each metric", () => {
    mockUseQuery.mockReturnValue({
      data: mockMetricConfigs,
      isLoading: false,
      error: null,
    });

    renderComponent();

    const editButtons = screen.getAllByTestId("icon-button");
    expect(editButtons).toHaveLength(2); // One for each metric

    editButtons.forEach((button) => {
      expect(button).toHaveAttribute("data-label", "Edit");
      expect(button).toHaveAttribute("data-kind", "primary");
      expect(button).toHaveAttribute("data-size", "sm");
    });

    expect(screen.getAllByTestId("edit-icon")).toHaveLength(2);
  });

  it("renders delete buttons for each metric", () => {
    mockUseQuery.mockReturnValue({
      data: mockMetricConfigs,
      isLoading: false,
      error: null,
    });

    renderComponent();

    const deleteButtons = screen.getAllByTestId("button");
    expect(deleteButtons).toHaveLength(2); // One for each metric

    deleteButtons.forEach((button) => {
      expect(button).toHaveAttribute("data-kind", "danger--ghost");
      expect(button).toHaveAttribute("data-size", "sm");
    });

    expect(screen.getAllByTestId("delete-icon")).toHaveLength(2);
  });

  it("handles edit button click", () => {
    mockUseQuery.mockReturnValue({
      data: mockMetricConfigs,
      isLoading: false,
      error: null,
    });

    renderComponent();

    const editButtons = screen.getAllByTestId("icon-button");
    fireEvent.click(editButtons[0]);

    // Should navigate to MetricDetail view
    expect(screen.getByTestId("metric-detail")).toBeInTheDocument();
    expect(screen.getByTestId("metric-name")).toHaveTextContent(
      "Test Metric 1",
    );
  });

  it("handles delete button click", () => {
    const consoleSpy = vi.spyOn(console, "log");

    mockUseQuery.mockReturnValue({
      data: mockMetricConfigs,
      isLoading: false,
      error: null,
    });

    renderComponent();

    const deleteButtons = screen.getAllByTestId("button");
    fireEvent.click(deleteButtons[0]);

    // Should log the delete action (as it's not implemented)
    expect(consoleSpy).toHaveBeenCalledWith(
      "Delete metric config not implemented...",
      "1",
    );
  });

  it("navigates back from MetricDetail view", () => {
    mockUseQuery.mockReturnValue({
      data: mockMetricConfigs,
      isLoading: false,
      error: null,
    });

    renderComponent();

    // Click edit to open detail view
    const editButtons = screen.getAllByTestId("icon-button");
    fireEvent.click(editButtons[0]);

    expect(screen.getByTestId("metric-detail")).toBeInTheDocument();

    // Click close to go back to list
    fireEvent.click(screen.getByTestId("close-detail"));

    expect(screen.queryByTestId("metric-detail")).not.toBeInTheDocument();
    expect(screen.getByTestId("datagrid")).toBeInTheDocument();
  });

  it("handles empty data gracefully", () => {
    mockUseQuery.mockReturnValue({
      data: [],
      isLoading: false,
      error: null,
    });

    renderComponent();

    expect(screen.getByTestId("datagrid")).toBeInTheDocument();
    // Should render headers but no data rows
    expect(screen.getByText("Metric Name")).toBeInTheDocument();
    expect(screen.queryByText("Test Metric 1")).not.toBeInTheDocument();
  });

  it("calls API with correct parameters", () => {
    mockUseQuery.mockReturnValue({
      data: mockMetricConfigs,
      isLoading: false,
      error: null,
    });

    renderComponent();

    expect(mockUseQuery).toHaveBeenCalledWith(
      "get",
      "/config/process-flow/metric-configs",
      {},
      {
        staleTime: 1000 * 60 * 5, // 5 minutes
        retry: 1,
        retryDelay: expect.any(Function),
      },
    );
  });

  it("configures correct datagrid props", () => {
    mockUseQuery.mockReturnValue({
      data: mockMetricConfigs,
      isLoading: false,
      error: null,
    });

    renderComponent();

    const datagrid = screen.getByTestId("datagrid");
    expect(datagrid).toHaveAttribute("data-mode", "client");
    expect(datagrid).toHaveAttribute("data-enable-selection", "false");
    expect(datagrid).toHaveAttribute("data-initial-density", "compact");
  });

  it("handles missing item name in edit button gracefully", () => {
    const configWithoutName = {
      ...mockMetricConfigs[0],
      metricName: undefined as any,
    } as MetricConfigSummary;

    mockUseQuery.mockReturnValue({
      data: [configWithoutName],
      isLoading: false,
      error: null,
    });

    renderComponent();

    const datagrid = screen.getByTestId("datagrid");
    expect(datagrid).toBeInTheDocument();
    // Should render without errors even with missing metric name
  });

  it("handles missing item id in delete button gracefully", () => {
    const configWithoutId = {
      ...mockMetricConfigs[0],
      id: undefined as any,
    } as MetricConfigSummary;

    mockUseQuery.mockReturnValue({
      data: [configWithoutId],
      isLoading: false,
      error: null,
    });

    renderComponent();

    const datagrid = screen.getByTestId("datagrid");
    expect(datagrid).toBeInTheDocument();
    // Should render without errors even with missing id
  });
});
