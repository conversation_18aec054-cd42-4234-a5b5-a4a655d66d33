import { ictApi } from "../../../../../api/ict-api";
import { Datagrid } from "../../../../../components/datagrid";
import { createColumnHelper } from "@tanstack/react-table";
import { <PERSON><PERSON>, IconButton } from "@carbon/react";
import { MetricConfigSummary } from "./types";
import { useState } from "react";
import { MetricDetailModal } from "./metric-detail-modal";
import {
  CheckmarkOutline,
  CloseOutline,
  Edit,
  RowDelete,
} from "@carbon/icons-react";
import styles from "./metric-configs-list.module.scss";

interface MetricConfigsListProps {
  factTypeFilter?: string;
  onClearFilter?: () => void;
}

export function MetricConfigsList({ factTypeFilter, onClearFilter }: MetricConfigsListProps) {
  const [selectedMetricConfig, setSelectedMetricConfig] =
    useState<MetricConfigSummary | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  // Build query params
  const queryParams = factTypeFilter ? { fact_type: factTypeFilter } : {};

  console.log('MetricConfigsList - factTypeFilter:', factTypeFilter);
  console.log('MetricConfigsList - queryParams:', queryParams);

  const {
    data: metricConfigs,
    isLoading: isLoadingMetricConfigs,
    error: metricConfigsError,
  } = ictApi.client.useQuery(
    "get",
    "/config/process-flow/metric-configs",
    queryParams,
    {
      staleTime: 1000 * 60 * 5, // 5 minutes
      retry: 1,
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff
    },
  );

  console.log('MetricConfigsList - received data count:', metricConfigs?.length);
  console.log('MetricConfigsList - first few items:', metricConfigs?.slice(0, 3));

  // Check if any items actually match the filter
  const matchingItems = metricConfigs?.filter(item => item.factType === factTypeFilter);
  console.log('MetricConfigsList - items matching filter:', matchingItems?.length);
  console.log('MetricConfigsList - sample matching items:', matchingItems?.slice(0, 3));


  const handleDeleteMetricButton = (metricName: string): void => {
    console.log("Delete metric config not implemented...", metricName);
    // TODO: Perform a soft-delete on metric config. Use the API to update the isDeleted column for metric config.
  };
  const handleEditMetricButton = (metricName: string): void => {
    console.log("Edit metric config not implemented...", metricName);
    setSelectedMetricConfig(
      metricConfigs?.find((config) => config.metricName === metricName) ?? null,
    );
    setIsModalOpen(true);
  };

  const columnHelper = createColumnHelper<MetricConfigSummary>();
  const metricDatagridColumns = [
    columnHelper.accessor("enabled", {
      header: "Enabled",
      id: "enabled",
      size: 50,
      cell: ({ getValue }) => {
        const enabled = getValue();
        return enabled ? (
          <CheckmarkOutline color="green" />
        ) : (
          <CloseOutline color="red" />
        );
      },
    }),
    columnHelper.accessor("metricName", {
      header: "Metric Name",
      id: "metric_name",
      size: 200,
    }),
    columnHelper.accessor("factType", {
      header: "Fact Name",
      id: "fact_name",
      size: 125,
    }),
    columnHelper.accessor("nodeName", {
      header: "Node Name",
      id: "node_name",
      size: 125,
    }),
    columnHelper.accessor("views", {
      header: "Views",
      id: "views",
      size: 100,
    }),
    columnHelper.accessor("configType", {
      header: "Metric Type",
      id: "metric_type",
      size: 75,
    }),
    columnHelper.accessor("isCustom", {
      header: "Type",
      id: "isCustom",
      size: 50,
      cell: ({ getValue }) => {
        const isCustom = getValue();
        return isCustom ? "Custom" : "Default";
      },
    }),
    columnHelper.accessor("metricName", {
      header: "",
      id: "editButton",
      size: 10,
      cell: ({ getValue }) => {
        const itemName = getValue();
        if (!itemName) return <div>--</div>;

        return (
          <IconButton
            label="Edit"
            kind="primary"
            size="sm"
            onClick={() => handleEditMetricButton(itemName)}
          >
            <Edit />
          </IconButton>
        );
      },
    }),
    columnHelper.accessor("id", {
      header: "",
      id: "deleteButton",
      size: 10,
      cell: ({ getValue }) => {
        const itemId = getValue();
        if (!itemId) return <div>--</div>;
        return (
          <Button
            kind="danger--ghost"
            size="sm"
            className={styles.noPaddingButton}
            onClick={() => handleDeleteMetricButton(itemId)}
          >
            <RowDelete />
          </Button>
        );
      },
    }),
  ];

  const handleSubmit = () => {
    // TODO: send request to save changes, then close the modal
    // While saving, update the modal save button to show "Saving..."
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedMetricConfig(null);
  };

  return (
    <>
      {factTypeFilter && (
        <div style={{ marginBottom: '1rem', padding: '0.5rem', backgroundColor: '#f4f4f4', borderRadius: '4px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <span><strong>Filtered by fact type:</strong> {factTypeFilter}</span>
          {onClearFilter && (
            <Button kind="ghost" size="sm" onClick={onClearFilter}>
              Clear Filter
            </Button>
          )}
        </div>
      )}
      <Datagrid
        columns={metricDatagridColumns}
        data={metricConfigs}
        mode="client"
        initialPagination={{ pageIndex: 0, pageSize: 100 }}
        enableSelection={false}
        initialDensity="compact"
        isLoading={isLoadingMetricConfigs}
        error={metricConfigsError ?? undefined}
      />

      {/* Modal for editing metric configuration */}
      {selectedMetricConfig && (
        <>
          <MetricDetailModal
            metricName={selectedMetricConfig.metricName}
            isModalOpen={isModalOpen}
            onRequestSubmit={handleSubmit}
            onRequestClose={handleCloseModal}
          ></MetricDetailModal>
        </>
      )}
    </>
  );
}
