import { useState } from "react";
import { IoMdMenu } from "react-icons/io";
import { OverflowMenu, OverflowMenuItem } from "@carbon/react";
import { useNavigate } from "react-router";
import { useFeatureFlag } from "../../../../config/hooks/use-config";
import { ResetFlowChartForm } from "./reset-flow-chart-form";

export enum AdminModals {
  None = "none",
  ResetFlowChart = "reset-flow-chart",
}

interface CustomControlsProps {
  areaId: string;
  isInteractive: boolean;
  lastProcessedTime: string;
}

export const AdminControls = ({
  areaId,
  isInteractive,
  lastProcessedTime,
}: CustomControlsProps) => {
  const [activeModal, setActiveModal] = useState<AdminModals>(AdminModals.None);
  const navigate = useNavigate();
  const adminControlsFeatureFlag = useFeatureFlag(
    "ict-facility-process-flow-admin-controls",
  );
  const configManagementFeatureFlag = useFeatureFlag(
    "ict-facility-process-flow-config-management",
  );

  return (
    <>
      <div className="adminControlsContainer">
        {isInteractive && lastProcessedTime && (
          <div className="lastProcessedTime">
            <span title="Last Processed Time">
              Data updated: {lastProcessedTime}
            </span>
          </div>
        )}

        {adminControlsFeatureFlag.enabled && (
          <div className="adminMenu">
            <OverflowMenu
              renderIcon={IoMdMenu}
              iconDescription="Open context menu"
              flipped
            >
              {configManagementFeatureFlag.enabled && (
                <OverflowMenuItem
                  itemText="Configuration"
                  onClick={() => navigate("configuration")}
                />
              )}
              <OverflowMenuItem
                itemText="Reset..."
                onClick={() => setActiveModal(AdminModals.ResetFlowChart)}
              />
            </OverflowMenu>
          </div>
        )}

        {/* Modals */}
        <ResetFlowChartForm
          areaId={areaId}
          isModalOpen={activeModal === AdminModals.ResetFlowChart}
          setActiveModal={setActiveModal}
        />
      </div>
    </>
  );
};
