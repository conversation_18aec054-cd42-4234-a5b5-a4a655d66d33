/**
 * Silver Data Modal component styles
 * Following Carbon Design System patterns and existing codebase conventions
 */

.modalContent {
  height: 600px;
  width: 100%;
}

.freshnessIndicator {
  padding: 0.5rem 1rem;
  background-color: var(--cds-layer-01);
  border: 1px solid var(--cds-border-subtle-01);
  border-radius: 4px;
  margin-bottom: 1rem;
  font-size: 0.875rem;
  color: var(--cds-text-primary);
}

.freshnessText {
  font-weight: 500;
  color: var(--cds-text-primary);
}

.freshnessTimestamp {
  margin-left: 0.5rem;
  color: var(--cds-text-secondary);
  font-weight: 400;
}
