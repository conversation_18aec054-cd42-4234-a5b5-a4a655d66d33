/* Silver Data Modal Styles - Following curated data view and modal standards */

.modalContent {
  height: 600px;
  width: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background-color: var(--cds-layer);
  border-bottom: 1px solid var(--cds-border-subtle);
  position: sticky;
  top: 0;
  z-index: 2;
}

.headerInfo {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.tableTitle {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--cds-text-primary);
  margin: 0;
}

.tableSubtitle {
  font-size: 0.875rem;
  color: var(--cds-text-secondary);
  margin: 0;
}

.freshnessIndicator {
  padding: 0.75rem 1rem;
  border-radius: 4px;
  background-color: var(--cds-layer-01);
  border: 1px solid var(--cds-border-subtle-01);
  margin: 1rem;
  margin-bottom: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.freshnessIcon {
  color: var(--cds-support-success);
  flex-shrink: 0;
}

.freshnessText {
  color: var(--cds-text-primary);
  font-weight: 500;
}

.freshnessTime {
  color: var(--cds-text-secondary);
  font-size: 0.875rem;
}

.tableContainer {
  flex: 1;
  overflow: hidden;
  padding: 1rem;
  padding-top: 0;
  display: flex;
  flex-direction: column;
  background-color: var(--cds-layer-01);
}

.tableWrapper {
  flex: 1;
  overflow: hidden;
  background-color: var(--cds-layer);
  border: 1px solid var(--cds-border-subtle-01);
  border-radius: 4px;
}

.loadingContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--cds-text-secondary);
}

.errorContainer {
  padding: 1rem;
  background-color: var(--cds-support-error-inverse);
  border: 1px solid var(--cds-support-error);
  border-radius: 4px;
  color: var(--cds-text-error);
  margin: 1rem;
}

.emptyContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--cds-text-secondary);
  font-style: italic;
  flex-direction: column;
  gap: 0.5rem;
}

.emptyIcon {
  color: var(--cds-icon-secondary);
}

.recordCount {
  font-size: 0.75rem;
  color: var(--cds-text-helper);
  padding: 0.5rem 1rem;
  background-color: var(--cds-layer-accent-01);
  border-top: 1px solid var(--cds-border-subtle-01);
  text-align: center;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .modalContent {
    height: 500px;
  }
  
  .header {
    padding: 0.75rem;
  }
  
  .tableContainer {
    padding: 0.75rem;
    padding-top: 0;
  }
  
  .freshnessIndicator {
    margin: 0.75rem;
    margin-bottom: 0;
    padding: 0.5rem 0.75rem;
  }
}
