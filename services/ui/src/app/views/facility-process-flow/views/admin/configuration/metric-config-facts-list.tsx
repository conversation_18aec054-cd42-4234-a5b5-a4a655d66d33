import { Datagrid } from "../../../../../components/datagrid";
import { createColumnHelper } from "@tanstack/react-table";
import { But<PERSON> } from "@carbon/react";
import { CheckmarkOutline, CloseOutline, RowDelete } from "@carbon/icons-react";
import { ictApi } from "../../../../../api/ict-api";
import { useApiErrorState } from "../../../../../hooks/use-api-error-state";
import { MetricConfigFacts } from "./types";
import styles from "./configuration-management.module.scss";
import { useState } from "react";
import { SilverDataModal } from "./silver-data-modal";

interface MetricConfigFactsListProps {
  onViewFact?: (factType: string) => void;
  onViewData?: (factType: string) => void;
}

export function MetricConfigFactsList({
  onViewFact,
  onViewData,
}: MetricConfigFactsListProps) {
  const [isDataModalOpen, setIsDataModalOpen] = useState(false);
  const [selectedFactType, setSelectedFactType] = useState<string | null>(null);

  const {
    data: metricConfigFacts,
    isLoading: isLoadingFacts,
    error: factsError,
  } = ictApi.client.useQuery(
    "get",
    "/config/process-flow/metric-configs/facts",
    {},
    {
      staleTime: 1000 * 60 * 5, // 5 minutes
      retry: 1,
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff
    },
  );

  const isNoDataAvailable = useApiErrorState(factsError);

  const handleDeleteFactButton = (factType: string): void => {
    if (!confirm(`Are you sure you want to delete ${factType}?`)) return;
    console.log("Delete fact config not implemented...", factType);
    // TODO: Perform a soft-delete on metric config. Use the API to update the isDeleted column for metric config.
  };

  const handleViewFactButton = (factType: string): void => {
    if (onViewFact) {
      onViewFact(factType);
    } else {
      console.log("View fact config not implemented...", factType);
      // TODO: Navigate to fact detail view
    }
  };

  const handleViewDataButton = (factType: string): void => {
    if (onViewData) {
      onViewData(factType);
    } else {
      // Open modal to display silver table data
      setSelectedFactType(factType);
      setIsDataModalOpen(true);
    }
  };

  const handleCloseDataModal = (): void => {
    setIsDataModalOpen(false);
    setSelectedFactType(null);
  };

  const columnHelper = createColumnHelper<MetricConfigFacts>();
  const factDatagridColumns = [
    columnHelper.accessor("factType", {
      header: "Fact Name",
      id: "factType",
      size: 200,
    }),
    columnHelper.accessor("totalConfigs", {
      header: "Total Configs",
      id: "totalConfigs",
      size: 120,
    }),
    columnHelper.accessor("enabledConfigs", {
      header: "Enabled Configs",
      id: "enabledConfigs",
      size: 140,
    }),
    columnHelper.accessor("active", {
      header: "Active",
      id: "active",
      size: 100,
      cell: ({ getValue }) => {
        const active = getValue();
        return active ? (
          <CheckmarkOutline color="green" />
        ) : (
          <CloseOutline color="red" />
        );
      },
    }),
    columnHelper.display({
      header: "",
      id: "viewConfigButton",
      size: 35,
      cell: ({ row }) => {
        const factType = row.original.factType;
        if (!factType) return <div>--</div>;
        return (
          <Button
            className={styles.gridButton}
            kind="primary"
            size="sm"
            onClick={() => handleViewFactButton(factType)}
          >
            View Configs
          </Button>
        );
      },
    }),
    columnHelper.display({
      header: "",
      id: "viewDataButton",
      size: 35,
      cell: ({ row }) => {
        const factType = row.original.factType;
        if (!factType) return <div>--</div>;
        return (
          <Button
            className={styles.gridButton}
            kind="secondary"
            size="sm"
            onClick={() => handleViewDataButton(factType)}
          >
            View Data
          </Button>
        );
      },
    }),
    columnHelper.display({
      header: "",
      id: "deleteButton",
      size: 10,
      cell: ({ row }) => {
        const factType = row.original.factType;
        if (!factType) return <div>--</div>;
        return (
          <Button
            className={styles.gridButton}
            kind="danger--ghost"
            size="sm"
            onClick={() => handleDeleteFactButton(factType)}
          >
            <RowDelete />
          </Button>
        );
      },
    }),
  ];

  if (isNoDataAvailable) {
    return <div>No metric config facts data is currently available.</div>;
  }

  return (
    <>
      <Datagrid
        columns={factDatagridColumns}
        data={metricConfigFacts || []}
        mode="client"
        initialPagination={{ pageIndex: 0, pageSize: 15 }}
        enableSelection={false}
        initialDensity="compact"
        isLoading={isLoadingFacts}
        error={
          isNoDataAvailable
            ? undefined
            : factsError
              ? "Error fetching data"
              : undefined
        }
      />

      {/* Modal for viewing silver table data */}
      <SilverDataModal
        isOpen={isDataModalOpen}
        onClose={handleCloseDataModal}
        factType={selectedFactType}
      />
    </>
  );
}
