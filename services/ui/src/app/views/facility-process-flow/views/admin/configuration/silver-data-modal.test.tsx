import { vi } from "vitest";
import { render, screen } from "@testing-library/react";
import "@testing-library/jest-dom/vitest";
import { SilverDataModal } from "./silver-data-modal";

// Mock the ictApi
vi.mock("../../../../../api/ict-api", () => ({
  ictApi: {
    client: {
      useQuery: vi.fn(),
    },
  },
}));

// Mock the Datagrid component
vi.mock("../../../../../components/datagrid", () => ({
  Datagrid: ({ columns, data, isLoading, error }: any) => (
    <div data-testid="datagrid">
      {isLoading && <div>Loading...</div>}
      {error && <div>Error: {error}</div>}
      {data && <div>Data count: {data.length}</div>}
    </div>
  ),
}));

describe("SilverDataModal", () => {
  const defaultProps = {
    isOpen: true,
    onClose: vi.fn(),
    factType: "bin_utilization",
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("should render modal when open", () => {
    const mockUseQuery = vi.fn().mockReturnValue({
      data: [],
      error: null,
      isLoading: false,
      isFetching: false,
      refetch: vi.fn(),
    });

    vi.mocked(require("../../../../../api/ict-api").ictApi.client.useQuery).mockImplementation(mockUseQuery);

    render(<SilverDataModal {...defaultProps} />);

    expect(screen.getByText("Silver Table Data: silver_bin_utilization")).toBeInTheDocument();
    expect(screen.getByText("Recent 100 Records")).toBeInTheDocument();
  });

  it("should show data freshness indicator when data has ingestion time", () => {
    const mockData = [
      {
        columns: [
          { name: "__raw_message_ingestion_time", value: "2024-01-15T10:30:00Z", renderType: "string" },
          { name: "other_field", value: "test", renderType: "string" }
        ]
      }
    ];

    const mockUseQuery = vi.fn().mockReturnValue({
      data: mockData,
      error: null,
      isLoading: false,
      isFetching: false,
      refetch: vi.fn(),
    });

    vi.mocked(require("../../../../../api/ict-api").ictApi.client.useQuery).mockImplementation(mockUseQuery);

    render(<SilverDataModal {...defaultProps} />);

    expect(screen.getByText("Most recent data:")).toBeInTheDocument();
  });

  it("should not render modal when closed", () => {
    const mockUseQuery = vi.fn().mockReturnValue({
      data: [],
      error: null,
      isLoading: false,
      isFetching: false,
      refetch: vi.fn(),
    });

    vi.mocked(require("../../../../../api/ict-api").ictApi.client.useQuery).mockImplementation(mockUseQuery);

    render(<SilverDataModal {...defaultProps} isOpen={false} />);

    expect(screen.queryByText("Silver Table Data: silver_bin_utilization")).not.toBeInTheDocument();
  });

  it("should call API with correct table name", () => {
    const mockUseQuery = vi.fn().mockReturnValue({
      data: [],
      error: null,
      isLoading: false,
      isFetching: false,
      refetch: vi.fn(),
    });

    vi.mocked(require("../../../../../api/ict-api").ictApi.client.useQuery).mockImplementation(mockUseQuery);

    render(<SilverDataModal {...defaultProps} factType="connection_movement" />);

    expect(mockUseQuery).toHaveBeenCalledWith(
      "get",
      "/config/v2/curated-data",
      {
        params: {
          query: { table: "silver_connection_movement" },
        },
      },
      expect.objectContaining({
        enabled: true,
        staleTime: 120000, // 2 minutes
      })
    );
  });

  it("should not fetch data when modal is closed", () => {
    const mockUseQuery = vi.fn().mockReturnValue({
      data: [],
      error: null,
      isLoading: false,
      isFetching: false,
      refetch: vi.fn(),
    });

    vi.mocked(require("../../../../../api/ict-api").ictApi.client.useQuery).mockImplementation(mockUseQuery);

    render(<SilverDataModal {...defaultProps} isOpen={false} />);

    expect(mockUseQuery).toHaveBeenCalledWith(
      "get",
      "/config/v2/curated-data",
      {
        params: {
          query: { table: "silver_bin_utilization" },
        },
      },
      expect.objectContaining({
        enabled: false, // Should be false when modal is closed
      })
    );
  });
});
