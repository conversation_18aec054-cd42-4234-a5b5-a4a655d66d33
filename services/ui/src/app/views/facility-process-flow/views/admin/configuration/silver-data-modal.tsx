import { Modal } from "@carbon/react";
import { createColumnHelper } from "@tanstack/react-table";
import { useMemo } from "react";
import { ictApi } from "../../../../../api/ict-api";
import { Datagrid } from "../../../../../components/datagrid";
import { useTranslation } from "react-i18next";
import { useApiErrorState } from "../../../../../hooks/use-api-error-state";

interface SilverDataModalProps {
  isOpen: boolean;
  onClose: () => void;
  factType: string;
}

type CuratedDataColumn = {
  name: string;
  renderType: "string" | "number" | "boolean" | "null";
  value: unknown;
};

type CuratedDataRow = {
  columns: CuratedDataColumn[];
};

export function SilverDataModal({ isOpen, onClose, factType }: SilverDataModalProps) {
  const { t } = useTranslation();
  
  // Convert fact type to silver table name (e.g., "bin_utilization" -> "silver_bin_utilization")
  const silverTableName = `silver_${factType}`;
  
  const { data, error, isLoading, isFetching, refetch } =
    ictApi.client.useQuery(
      "get",
      "/config/v2/curated-data",
      {
        params: {
          query: { table: silverTableName },
        },
      },
      { 
        enabled: isOpen && !!factType, // Only fetch when modal is open and factType is provided
        staleTime: 1000 * 60 * 2, // 2 minutes cache
      },
    );

  const isNoDataAvailable = useApiErrorState(error);

  // Handle refresh click
  const handleRefresh = () => {
    refetch();
  };

  // Transform the data to flatten it for the table (same logic as CuratedDataTable)
  const tableData = useMemo(() => {
    if (!data) return [];

    return data.map((row: CuratedDataRow) => {
      const flatRow: Record<string, unknown> = {};

      // Parse the data JSON string if it exists
      if (row.columns) {
        for (const col of row.columns) {
          if (col.name === "data" && col.value) {
            try {
              const parsedData = JSON.parse(col.value as string)[0];

              for (const [key, value] of Object.entries(parsedData)) {
                flatRow[`data_${key}`] = value;
              }
            } catch (_e) {
              flatRow[col.name] = col.value;
            }
          } else {
            flatRow[col.name] = col.value;
          }
        }
      }

      return flatRow;
    });
  }, [data]);

  // Find the most recent ingestion time for the header indicator
  const mostRecentIngestionTime = useMemo(() => {
    if (!tableData.length) return null;

    let mostRecent: Date | null = null;

    for (const row of tableData) {
      const ingestionTime = row.__raw_message_ingestion_time || row.data___raw_message_ingestion_time;
      if (ingestionTime) {
        const date = new Date(ingestionTime as string);
        if (!isNaN(date.getTime()) && (!mostRecent || date > mostRecent)) {
          mostRecent = date;
        }
      }
    }

    return mostRecent;
  }, [tableData]);

  // Format the ingestion time for display
  const formatIngestionTime = (date: Date): string => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMinutes / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMinutes < 1) return "Just now";
    if (diffMinutes < 60) return `${diffMinutes} minute${diffMinutes !== 1 ? 's' : ''} ago`;
    if (diffHours < 24) return `${diffHours} hour${diffHours !== 1 ? 's' : ''} ago`;
    if (diffDays < 7) return `${diffDays} day${diffDays !== 1 ? 's' : ''} ago`;

    return date.toLocaleString();
  };

  // Define column helper for type safety
  const columnHelper = createColumnHelper<Record<string, unknown>>();

  // Generate columns dynamically based on the first row of data (same logic as CuratedDataTable)
  const columns = useMemo(() => {
    if (!tableData.length) return [];

    return Object.keys(tableData[0]).map((key) =>
      columnHelper.accessor(key, {
        header: key.startsWith("data_") ? key.replace("data_", "") : key,
        size: 200,
      }),
    );
  }, [tableData, columnHelper]);

  let errorMessage = undefined;
  if (!isNoDataAvailable && error) {
    errorMessage = t("silverDataModal.errorLoadingData", "Failed to load silver table data");
  }

  return (
    <Modal
      open={isOpen}
      onRequestClose={onClose}
      modalHeading={`Silver Table Data: ${silverTableName}`}
      modalLabel="Recent 100 Records"
      size="lg"
      hasScrollingContent
      primaryButtonText="Close"
      onRequestSubmit={onClose}
    >
      <div style={{ height: "600px", width: "100%" }}>
        {/* Data freshness indicator */}
        {mostRecentIngestionTime && (
          <div style={{
            padding: "8px 16px",
            backgroundColor: "#f4f4f4",
            borderRadius: "4px",
            marginBottom: "16px",
            fontSize: "14px",
            color: "#525252"
          }}>
            <strong>Most recent data:</strong> {formatIngestionTime(mostRecentIngestionTime)}
            <span style={{ marginLeft: "8px", color: "#6f6f6f" }}>
              ({mostRecentIngestionTime.toLocaleString()})
            </span>
          </div>
        )}

        <Datagrid
          columns={columns}
          error={errorMessage}
          isLoading={isLoading || isFetching}
          data={tableData}
          mode="client"
          enableSelection={false}
          initialPagination={{ pageIndex: 0, pageSize: 25 }}
          initialDensity="compact"
          showRefreshButton={true}
          onRefreshClick={handleRefresh}
          showExportButton={false}
        />
      </div>
    </Modal>
  );
}
