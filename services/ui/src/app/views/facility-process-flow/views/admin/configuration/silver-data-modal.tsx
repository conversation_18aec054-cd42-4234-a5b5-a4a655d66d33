import { Modal } from "@carbon/react";
import { useMemo } from "react";
import type { components } from "@ict/sdk/openapi-react-query";
import { ictApi } from "../../../../../api/ict-api";
import {
  formatRelativeTime,
  formatISOTime,
} from "../../../../../utils/date-util";
import { CuratedDataTable } from "../../../../curated-data/components/curated-data-table/curated-data-table";
import rendererStyles from "../../../../data-view/components/bottom-panel/renderers/renderer.module.css";
import styles from "./silver-data-modal.module.css";

type CuratedDataColumn = components["schemas"]["CuratedTableColumn"];

interface SilverDataModalProps {
  isOpen: boolean;
  onClose: () => void;
  factType: string | null;
}

type CuratedDataColumn = {
  name: string;
  renderType: "string" | "number" | "boolean" | "null";
  value: unknown;
};

export function SilverDataModal({
  isOpen,
  onClose,
  factType,
}: SilverDataModalProps) {
  // Don't render anything if factType is null
  if (!factType) {
    return null;
  }

  // Convert fact type to silver table name (e.g., "bin_utilization" -> "silver_bin_utilization")
  const silverTableName = `silver_${factType}`;

  // Only fetch data for the freshness indicator when modal is open
  const { data } = ictApi.client.useQuery(
    "get",
    "/config/v2/curated-data",
    {
      params: {
        query: { table: silverTableName },
      },
    },
    {
      enabled: isOpen && !!factType && factType.trim() !== "", // Only fetch when modal is open and factType is valid
      staleTime: 1000 * 60 * 2, // 2 minutes cache
    },
  );

  // Extract the most recent ingestion time from API data
  const mostRecentIngestionTime = useMemo(() => {
    if (!data?.length) return null;

    // Since API returns data sorted by __raw_message_ingestion_time DESC, first record is most recent
    const firstRow = data[0];
    if (firstRow.columns) {
      for (const col of firstRow.columns as CuratedDataColumn[]) {
        if (
          col.name === "__db_insert_timestamp" ||
          col.name === "__raw_message_ingestion_time"
        ) {
          const date = new Date(col.value as string);
          if (!isNaN(date.getTime())) {
            return date;
          }
        }
      }
    }

    return null;
  }, [data]);

  return (
    <Modal
      open={isOpen}
      onRequestClose={onClose}
      modalHeading={`Silver Table Data: ${silverTableName}`}
      modalLabel="Recent 100 Records"
      size="lg"
      hasScrollingContent
      passiveModal
    >
      <div className={styles.modalContent}>
        {/* Data freshness indicator - using existing renderer styles */}
        {mostRecentIngestionTime && (
          <div
            className={`${rendererStyles.validationStatus} ${styles.freshnessIndicator}`}
          >
            <strong>Most recent data:</strong>{" "}
            {formatRelativeTime(mostRecentIngestionTime)}{" "}
            <span className={rendererStyles.secondaryText}>
              (
              {formatISOTime(
                mostRecentIngestionTime.toISOString(),
                "M/d/yyyy h:mm a",
              )}
              )
            </span>
          </div>
        )}

        {/* Table container - using existing patterns */}
        <div className={styles.tableContainer}>
          <CuratedDataTable tableId={silverTableName} />
        </div>
      </div>
    </Modal>
  );
}
