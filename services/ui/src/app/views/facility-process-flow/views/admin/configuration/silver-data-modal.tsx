import { Modal } from "@carbon/react";
import { useMemo } from "react";
import { ictApi } from "../../../../../api/ict-api";
import { formatRelativeTime, formatISOTime } from "../../../../../utils/date-util";
import { CuratedDataTable } from "../../../../curated-data/components/curated-data-table/curated-data-table";

interface SilverDataModalProps {
  isOpen: boolean;
  onClose: () => void;
  factType: string;
}

type CuratedDataColumn = {
  name: string;
  renderType: "string" | "number" | "boolean" | "null";
  value: unknown;
};

export function SilverDataModal({ isOpen, onClose, factType }: SilverDataModalProps) {

  // Convert fact type to silver table name (e.g., "bin_utilization" -> "silver_bin_utilization")
  const silverTableName = `silver_${factType}`;

  // Only fetch data for the freshness indicator when modal is open
  const { data } = ictApi.client.useQuery(
    "get",
    "/config/v2/curated-data",
    {
      params: {
        query: { table: silverTableName },
      },
    },
    {
      enabled: isOpen && !!factType && factType.trim() !== "", // Only fetch when modal is open and factType is valid
      staleTime: 1000 * 60 * 2, // 2 minutes cache
    },
  );

  // Find the most recent ingestion time for the header indicator
  const mostRecentIngestionTime = useMemo(() => {
    if (!data?.length) return null;

    let mostRecent: Date | null = null;

    for (const row of data) {
      if (row.columns) {
        for (const col of row.columns as CuratedDataColumn[]) {
          if (col.name === "__db_insert_timestamp" || col.name === "__raw_message_ingestion_time") {
            const date = new Date(col.value as string);
            if (!isNaN(date.getTime()) && (!mostRecent || date > mostRecent)) {
              mostRecent = date;
            }
          }
        }
      }
    }

    return mostRecent;
  }, [data]);

  return (
    <Modal
      open={isOpen}
      onRequestClose={onClose}
      modalHeading={`Silver Table Data: ${silverTableName}`}
      modalLabel="Recent 100 Records"
      size="lg"
      hasScrollingContent
      primaryButtonText="Close"
      onRequestSubmit={onClose}
    >
      <div style={{ height: "600px", width: "100%" }}>
        {/* Data freshness indicator */}
        {mostRecentIngestionTime && (
          <div style={{
            padding: "8px 16px",
            backgroundColor: "#f4f4f4",
            borderRadius: "4px",
            marginBottom: "16px",
            fontSize: "14px",
            color: "#525252"
          }}>
            <strong>Most recent data:</strong> {formatRelativeTime(mostRecentIngestionTime)}
            <span style={{ marginLeft: "8px", color: "#6f6f6f" }}>
              ({formatISOTime(mostRecentIngestionTime.toISOString(), "M/d/yyyy h:mm a")})
            </span>
          </div>
        )}

        {/* Reuse existing CuratedDataTable component */}
        <CuratedDataTable tableId={silverTableName} />
      </div>
    </Modal>
  );
}
