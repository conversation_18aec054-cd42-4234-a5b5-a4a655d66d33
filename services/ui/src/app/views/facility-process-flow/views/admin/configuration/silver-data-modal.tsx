import { Modal } from "@carbon/react";
import { useMemo } from "react";
import { ictApi } from "../../../../../api/ict-api";
import { formatRelativeTime, formatISOTime } from "../../../../../utils/date-util";
import { CuratedDataTable } from "../../../../curated-data/components/curated-data-table/curated-data-table";
import rendererStyles from "../../../../data-view/components/bottom-panel/renderers/renderer.module.css";

interface SilverDataModalProps {
  isOpen: boolean;
  onClose: () => void;
  factType: string | null;
}

type CuratedDataColumn = {
  name: string;
  renderType: "string" | "number" | "boolean" | "null";
  value: unknown;
};

export function SilverDataModal({ isOpen, onClose, factType }: SilverDataModalProps) {
  // Don't render anything if factType is null
  if (!factType) {
    return null;
  }

  // Convert fact type to silver table name (e.g., "bin_utilization" -> "silver_bin_utilization")
  const silverTableName = `silver_${factType}`;

  // Only fetch data for the freshness indicator when modal is open
  const { data } = ictApi.client.useQuery(
    "get",
    "/config/v2/curated-data",
    {
      params: {
        query: { table: silverTableName },
      },
    },
    {
      enabled: isOpen && !!factType && factType.trim() !== "", // Only fetch when modal is open and factType is valid
      staleTime: 1000 * 60 * 2, // 2 minutes cache
    },
  );

  // Get the most recent ingestion time from the first record (API returns data sorted by ingestion time DESC)
  const mostRecentIngestionTime = useMemo(() => {
    if (!data?.length) return null;

    // Since API returns data sorted by __raw_message_ingestion_time DESC, first record is most recent
    const firstRow = data[0];
    if (firstRow.columns) {
      for (const col of firstRow.columns as CuratedDataColumn[]) {
        if (col.name === "__db_insert_timestamp" || col.name === "__raw_message_ingestion_time") {
          const date = new Date(col.value as string);
          if (!isNaN(date.getTime())) {
            return date;
          }
        }
      }
    }

    return null;
  }, [data]);

  return (
    <Modal
      open={isOpen}
      onRequestClose={onClose}
      modalHeading={`Silver Table Data: ${silverTableName}`}
      modalLabel="Recent 100 Records"
      size="lg"
      hasScrollingContent
      primaryButtonText="Close"
      onRequestSubmit={onClose}
    >
      <div style={{ height: "600px", width: "100%" }}>
        {/* Data freshness indicator - reusing existing validation status styling */}
        {mostRecentIngestionTime && (
          <div className={rendererStyles.validationStatus} style={{ marginBottom: "1rem" }}>
            <strong>Most recent data:</strong> {formatRelativeTime(mostRecentIngestionTime)}
            <span style={{ marginLeft: "0.5rem", color: "var(--cds-text-secondary)" }}>
              ({formatISOTime(mostRecentIngestionTime.toISOString(), "M/d/yyyy h:mm a")})
            </span>
          </div>
        )}

        {/* Reuse existing CuratedDataTable component */}
        <CuratedDataTable tableId={silverTableName} />
      </div>
    </Modal>
  );
}
