import { Dispatch, SetStateAction, useState } from "react";
import { Modal, ModalBody, RadioButton, RadioButtonGroup } from "@carbon/react";
import { ictApi } from "../../../../api/ict-api";
import { toastManager } from "../../../../components/toast/toast-container";
import { AdminModals } from "./admin-controls";

enum ResetChoices {
  None = "none",
  Graph = "graph_and_neo4j",
  GraphCache = "graph",
  Metrics = "metrics",
  All = "all",
}

interface ResetProcessFlowChartProps {
  areaId: string;
  isModalOpen: boolean;
  setActiveModal: Dispatch<SetStateAction<AdminModals>>;
}

export function ResetFlowChartForm(props: ResetProcessFlowChartProps) {
  const { areaId, isModalOpen, setActiveModal } = props;
  const [messageText, setMessageText] = useState("");
  const [resetChoice, setResetChoice] = useState<ResetChoices>(
    ResetChoices.None,
  );

  const handleResetChoiceChange = (selection: string | number | undefined) => {
    if (selection === ResetChoices.Graph) {
      setMessageText(
        "Clears the Neo4j graph database and all Redis keys associated with the graph for the tenant.",
      );
    } else if (selection === ResetChoices.GraphCache) {
      setMessageText(
        "Clears all Redis keys associated with the graph for the tenant, but does not delete the graph in Neo4j.",
      );
    } else if (selection === ResetChoices.Metrics) {
      setMessageText(
        "Clears all Redis keys associated with metrics for the tenant.",
      );
    } else if (selection === ResetChoices.All) {
      setMessageText(
        "Clears the Neo4j graph database and all Redis keys for the tenant.",
      );
    }

    setResetChoice(selection as ResetChoices);
  };

  // Create the callable POST Mutation.
  const { mutate: resetProcessFlowData, isPending } = ictApi.client.useMutation(
    "post",
    "/inventory/process-flow/clear-cache",
    {
      onSuccess: (_data) => {
        // Invalidate the queries to refresh the data
        ictApi.queryClient.invalidateQueries({
          queryKey: ictApi.client.queryOptions(
            "get",
            "/inventory/process-flow/areas",
          ).queryKey,
        });
        ictApi.queryClient.invalidateQueries({
          queryKey: ictApi.client.queryOptions(
            "get",
            "/inventory/process-flow/area/{areaId}",
            {
              params: {
                path: { areaId },
              },
            },
          ).queryKey,
        });
        setActiveModal(AdminModals.None);
        setResetChoice(ResetChoices.None);
        toastManager.addToast({
          id: "successful-graph-reset",
          title: "Reset successful",
          message: "The requested chart data has been reset.",
          type: "success",
          duration: 3000,
        });
      },
      onError: (err: Error) => {
        setMessageText(`Error: ${err}`);
      },
    },
  );

  const handleReset = () => {
    if (resetChoice === "none") {
      // we should never get here, but just in case
      alert("You must make a choice.");
      return false;
    }

    resetProcessFlowData({
      body: {
        scope: resetChoice,
      },
    });
  };

  return (
    <Modal
      open={isModalOpen}
      onRequestClose={() => setActiveModal(AdminModals.None)}
      onRequestSubmit={handleReset}
      primaryButtonText="Reset"
      primaryButtonDisabled={isPending || resetChoice === "none"}
      secondaryButtonText="Cancel"
    >
      <ModalBody>
        <div className="adminControlsModal">
          <h3>Reset Process Flow Chart</h3>
          <RadioButtonGroup
            name="reset-choice"
            valueSelected={resetChoice}
            onChange={handleResetChoiceChange}
          >
            <RadioButton
              value="graph_and_neo4j"
              id="graph_and_neo4j"
              labelText="Reset the graph"
            />
            <RadioButton
              value="graph"
              id="graph"
              labelText="Reset the graph cache"
            />
            <RadioButton
              value="metrics"
              id="metrics"
              labelText="Reset the metrics"
            />
            <RadioButton
              value="all"
              id="all"
              labelText="Reset the graph & metrics"
            />
          </RadioButtonGroup>
          <div className="adminControlsModalMessage">{messageText}</div>
        </div>
      </ModalBody>
    </Modal>
  );
}
