import { Route, Routes } from "react-router";
import type { BaseViewProps } from "../view-registry.types";
import { FacilityProcessFlow } from "./facility-process-flow";
import ConfigurationManagement from "../configuration-management/process-flow-configuration-management";

export function FacilityProcessFlowView(_props: BaseViewProps) {
  return (
    <Routes>
      <Route index element={<FacilityProcessFlow {..._props} />} />
      <Route path="configuration" element={<ConfigurationManagement />} />
    </Routes>
  );
}

export default FacilityProcessFlowView;
