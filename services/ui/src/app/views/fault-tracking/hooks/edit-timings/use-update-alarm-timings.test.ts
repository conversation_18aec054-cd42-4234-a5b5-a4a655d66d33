import { describe, it, expect, vi, beforeEach } from "vitest";
import { renderHook, act } from "@testing-library/react";
import { useUpdateAlarmTimings } from "./use-update-alarm-timings";
import type { EditTimingsForm } from "./use-edit-timings-form";
import type { FaultAlarm } from "../../types/types";

// Mock all dependencies
vi.mock("react-i18next", () => ({
  useTranslation: vi.fn(() => ({
    t: vi.fn((_key: string, defaultValue: string) => defaultValue),
  })),
}));

vi.mock("../../../../config/hooks/use-config", () => ({
  useConfigSetting: vi.fn(() => ({
    setting: { value: "America/New_York" },
  })),
}));

vi.mock("../../../../components/toast/use-notification", () => ({
  useNotification: vi.fn(() => ({
    success: vi.fn(),
    error: vi.fn(),
  })),
}));

vi.mock("../../../../utils/logger", () => ({
  Logger: vi.fn(() => ({
    info: vi.fn(),
    error: vi.fn(),
  })),
}));

vi.mock("../../utils/edit-timings-api-formatter", () => ({
  formatEditTimingsForAPI: vi.fn(() => ({
    id: "alarm-123",
    startDateTime: "2024-02-15T15:30:15.250Z",
    endDateTime: "2024-02-16T01:00:30.500Z",
    comments: "Updated comments",
    isIncluded: true,
  })),
}));

vi.mock("../../../../api/ict-api", () => ({
  ictApi: {
    fetchClient: {
      PUT: vi.fn(() =>
        Promise.resolve({ success: true, data: { id: "alarm-123" } }),
      ),
    },
  },
}));

describe("useUpdateAlarmTimings", () => {
  const mockFormData: EditTimingsForm = {
    startDate: new Date("2024-02-15"),
    endDate: new Date("2024-02-15"),
    startTime: "10:30",
    startPeriod: "AM" as const,
    endTime: "01:00",
    endPeriod: "PM" as const,
    startSeconds: "15:250",
    endSeconds: "30:500",
    comments: "Updated comments",
  };

  const mockSelectedAlarm: FaultAlarm = {
    id: "alarm-123",
    splitId: "SPLIT-001",
    faultId: "FAULT-001",
    title: "Test Alarm",
    description: "Test alarm description",
    tag: "TAG001",
    status: "KEPT",
    reason: "Test reason",
    comments: "Original comments",
    timing: {
      startTime: "2024-02-15T09:00:00.000Z",
      endTime: "2024-02-15T11:30:00.000Z",
      updatedStartTime: "",
      updatedEndTime: "",
      duration: "02:30:00",
    },
    location: {
      equipment: "Equipment 1",
      section: "Section 1",
      area: "Area A",
    },
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("hook initialization", () => {
    it("should initialize with correct default state", () => {
      const { result } = renderHook(() => useUpdateAlarmTimings());

      expect(result.current.isSubmitting).toBe(false);
      expect(typeof result.current.updateAlarmTimings).toBe("function");
    });

    it("should initialize with onSuccess callback", () => {
      const mockOnSuccess = vi.fn();
      const { result } = renderHook(() => useUpdateAlarmTimings(mockOnSuccess));

      expect(result.current.isSubmitting).toBe(false);
      expect(typeof result.current.updateAlarmTimings).toBe("function");
    });
  });

  describe("successful update", () => {
    it("should successfully update alarm timings", async () => {
      const { result } = renderHook(() => useUpdateAlarmTimings());

      let updateResult: boolean;
      await act(async () => {
        updateResult = await result.current.updateAlarmTimings(
          mockFormData,
          mockSelectedAlarm,
        );
      });

      expect(updateResult!).toBe(true);
      expect(result.current.isSubmitting).toBe(false);
    });

    it("should call onSuccess callback when provided", async () => {
      const mockOnSuccess = vi.fn();
      const { result } = renderHook(() => useUpdateAlarmTimings(mockOnSuccess));

      await act(async () => {
        await result.current.updateAlarmTimings(
          mockFormData,
          mockSelectedAlarm,
        );
      });

      expect(mockOnSuccess).toHaveBeenCalledTimes(1);
    });

    it("should not throw error when onSuccess is not provided", async () => {
      const { result } = renderHook(() => useUpdateAlarmTimings());

      await act(async () => {
        const updateResult = await result.current.updateAlarmTimings(
          mockFormData,
          mockSelectedAlarm,
        );
        expect(updateResult).toBe(true);
      });

      expect(result.current.isSubmitting).toBe(false);
    });
  });

  describe("error handling", () => {
    it("should handle API errors gracefully", async () => {
      // Mock API to reject
      const { ictApi } = await import("../../../../api/ict-api");
      vi.mocked(ictApi.fetchClient.PUT).mockRejectedValueOnce(
        new Error("API Error"),
      );

      const { result } = renderHook(() => useUpdateAlarmTimings());

      let updateResult: boolean;
      await act(async () => {
        updateResult = await result.current.updateAlarmTimings(
          mockFormData,
          mockSelectedAlarm,
        );
      });

      expect(updateResult!).toBe(false);
      expect(result.current.isSubmitting).toBe(false);
    });

    it("should not call onSuccess callback on error", async () => {
      const mockOnSuccess = vi.fn();

      // Mock API to reject
      const { ictApi } = await import("../../../../api/ict-api");
      vi.mocked(ictApi.fetchClient.PUT).mockRejectedValueOnce(
        new Error("API Error"),
      );

      const { result } = renderHook(() => useUpdateAlarmTimings(mockOnSuccess));

      await act(async () => {
        await result.current.updateAlarmTimings(
          mockFormData,
          mockSelectedAlarm,
        );
      });

      expect(mockOnSuccess).not.toHaveBeenCalled();
    });

    it("should show error notification when API call fails", async () => {
      // Mock API to reject
      const { ictApi } = await import("../../../../api/ict-api");
      vi.mocked(ictApi.fetchClient.PUT).mockRejectedValueOnce(
        new Error("API Error"),
      );

      const { useNotification } = await import(
        "../../../../components/toast/use-notification"
      );
      const mockShowError = vi.fn();
      vi.mocked(useNotification).mockReturnValueOnce({
        success: vi.fn(),
        error: mockShowError,
        info: vi.fn(),
        warning: vi.fn(),
        notify: vi.fn(),
        dismiss: vi.fn(),
      });

      const { result } = renderHook(() => useUpdateAlarmTimings());

      await act(async () => {
        await result.current.updateAlarmTimings(
          mockFormData,
          mockSelectedAlarm,
        );
      });

      expect(mockShowError).toHaveBeenCalledWith(
        "Failed to save alarm timing changes. Please try again.",
      );
    });

    it("should show success notification when API call succeeds", async () => {
      const { useNotification } = await import(
        "../../../../components/toast/use-notification"
      );
      const mockShowSuccess = vi.fn();
      vi.mocked(useNotification).mockReturnValueOnce({
        success: mockShowSuccess,
        error: vi.fn(),
        info: vi.fn(),
        warning: vi.fn(),
        notify: vi.fn(),
        dismiss: vi.fn(),
      });

      const { result } = renderHook(() => useUpdateAlarmTimings());

      await act(async () => {
        await result.current.updateAlarmTimings(
          mockFormData,
          mockSelectedAlarm,
        );
      });

      expect(mockShowSuccess).toHaveBeenCalledWith(
        "Edit alarm timings saved successfully",
      );
    });
  });

  describe("timezone configuration", () => {
    it("should use configured timezone from config setting", async () => {
      const { useConfigSetting } = await import(
        "../../../../config/hooks/use-config"
      );
      const { formatEditTimingsForAPI } = await import(
        "../../utils/edit-timings-api-formatter"
      );

      // Override the timezone for this test
      vi.mocked(useConfigSetting).mockReturnValueOnce({
        setting: {
          id: "facility-timezone",
          name: "facility-timezone",
          value: "Europe/London",
          dataType: "string",
        },
        isLoading: false,
        error: null,
      });

      const { result } = renderHook(() => useUpdateAlarmTimings());

      await act(async () => {
        await result.current.updateAlarmTimings(
          mockFormData,
          mockSelectedAlarm,
        );
      });

      expect(vi.mocked(formatEditTimingsForAPI)).toHaveBeenCalledWith(
        mockFormData,
        mockSelectedAlarm,
        "Europe/London",
      );
    });

    it("should default to America/New_York when config is null", async () => {
      const { useConfigSetting } = await import(
        "../../../../config/hooks/use-config"
      );
      const { formatEditTimingsForAPI } = await import(
        "../../utils/edit-timings-api-formatter"
      );

      // Override the config to return null
      vi.mocked(useConfigSetting).mockReturnValueOnce({
        setting: undefined,
        isLoading: false,
        error: null,
      });

      const { result } = renderHook(() => useUpdateAlarmTimings());

      await act(async () => {
        await result.current.updateAlarmTimings(
          mockFormData,
          mockSelectedAlarm,
        );
      });

      expect(vi.mocked(formatEditTimingsForAPI)).toHaveBeenCalledWith(
        mockFormData,
        mockSelectedAlarm,
        "America/New_York",
      );
    });
  });

  describe("API integration", () => {
    it("should call API with correct endpoint and payload", async () => {
      const { ictApi } = await import("../../../../api/ict-api");
      const { formatEditTimingsForAPI } = await import(
        "../../utils/edit-timings-api-formatter"
      );

      const expectedPayload = {
        id: "alarm-123",
        startDateLocal: "2024-02-15T15:30:15.250Z",
        endDateLocal: "2024-02-16T01:00:30.500Z",
        comments: "Updated comments",
        isIncluded: true,
      };

      vi.mocked(formatEditTimingsForAPI).mockReturnValueOnce(expectedPayload);

      const { result } = renderHook(() => useUpdateAlarmTimings());

      await act(async () => {
        await result.current.updateAlarmTimings(
          mockFormData,
          mockSelectedAlarm,
        );
      });

      expect(vi.mocked(ictApi.fetchClient.PUT)).toHaveBeenCalledWith(
        "/availability/alarms",
        { body: expectedPayload },
      );
    });

    it("should format data correctly for API", async () => {
      const { formatEditTimingsForAPI } = await import(
        "../../utils/edit-timings-api-formatter"
      );

      const { result } = renderHook(() => useUpdateAlarmTimings());

      await act(async () => {
        await result.current.updateAlarmTimings(
          mockFormData,
          mockSelectedAlarm,
        );
      });

      expect(vi.mocked(formatEditTimingsForAPI)).toHaveBeenCalledWith(
        mockFormData,
        mockSelectedAlarm,
        "America/New_York",
      );
    });
  });

  describe("multiple calls", () => {
    it("should handle multiple successful calls", async () => {
      const { ictApi } = await import("../../../../api/ict-api");

      const { result } = renderHook(() => useUpdateAlarmTimings());

      // First call
      let result1: boolean;
      await act(async () => {
        result1 = await result.current.updateAlarmTimings(
          mockFormData,
          mockSelectedAlarm,
        );
      });

      // Second call with different alarm
      let result2: boolean;
      await act(async () => {
        result2 = await result.current.updateAlarmTimings(mockFormData, {
          ...mockSelectedAlarm,
          id: "alarm-456",
        });
      });

      expect(result1!).toBe(true);
      expect(result2!).toBe(true);
      expect(vi.mocked(ictApi.fetchClient.PUT)).toHaveBeenCalledTimes(2);
      expect(result.current.isSubmitting).toBe(false);
    });

    it("should handle mixed success and error calls", async () => {
      const { ictApi } = await import("../../../../api/ict-api");

      const { result } = renderHook(() => useUpdateAlarmTimings());

      // First call - success
      let result1: boolean;
      await act(async () => {
        result1 = await result.current.updateAlarmTimings(
          mockFormData,
          mockSelectedAlarm,
        );
      });

      // Second call - error
      vi.mocked(ictApi.fetchClient.PUT).mockRejectedValueOnce(
        new Error("API Error"),
      );

      let result2: boolean;
      await act(async () => {
        result2 = await result.current.updateAlarmTimings(mockFormData, {
          ...mockSelectedAlarm,
          id: "alarm-456",
        });
      });

      expect(result1!).toBe(true);
      expect(result2!).toBe(false);
      expect(result.current.isSubmitting).toBe(false);
    });
  });
});
