import { describe, it, expect, vi, beforeEach } from "vitest";
import { renderHook } from "@testing-library/react";
import { useEditTimingsForm, getMinStartDate } from "./use-edit-timings-form";
import { useEditTimingsValidation } from "./use-edit-timings-validation";
import { useFaultTrackingForm } from "../shared/use-fault-tracking-form";
import {
  parseOriginalTimingData,
  parseAlarmToEditTimingsFormState,
} from "../../utils/alarm-data-parser";
import { hasFormDataChanged } from "../../utils/time-validation";

// Mock the form state hook
vi.mock("./use-edit-timings-form-state", () => ({
  useEditTimingsFormState: vi.fn(),
}));

// Mock the validation hook
vi.mock("./use-edit-timings-validation", () => ({
  useEditTimingsValidation: vi.fn(),
}));

// Mock the shared fault tracking form hook
vi.mock("../shared/use-fault-tracking-form", () => ({
  useFaultTrackingForm: vi.fn(),
  getMinStartDate: vi.fn().mockReturnValue(new Date("2024-01-15")),
}));

// Mock the alarm data parser
vi.mock("../../utils/alarm-data-parser", () => ({
  parseOriginalTimingData: vi.fn(),
  parseAlarmToEditTimingsFormState: vi.fn(),
}));

// Mock the time validation utilities
vi.mock("../../utils/time-validation", () => ({
  hasFormDataChanged: vi.fn(),
}));

describe("useEditTimingsForm", () => {
  const mockSelectedAlarm = {
    id: "test-alarm-123",
    splitId: "SPLIT-001",
    faultId: "FAULT-001",
    title: "Test Alarm",
    description: "Test alarm description",
    tag: "TAG001",
    status: "KEPT",
    reason: "Test reason",
    comments: "Test comment",
    location: {
      area: "Area A",
      section: "Section 1",
      equipment: "Equipment 1",
    },
    timing: {
      startTime: "2024-02-15T10:00:00.000Z",
      endTime: "2024-02-15T11:00:00.000Z",
      duration: "01:00:00",
      updatedStartTime: "2024-02-15T10:00:00.000Z",
      updatedEndTime: "2024-02-15T11:00:00.000Z",
      origStartTime: "2024-02-15T09:00:00.000Z",
      origEndTime: "2024-02-15T10:00:00.000Z",
      origDuration: "01:00:00",
    },
  };

  const mockOriginalData = {
    startDate: new Date("2024-02-15"),
    endDate: new Date("2024-02-15"),
    startTime: "9:00",
    startPeriod: "AM" as const,
    endTime: "10:00",
    endPeriod: "AM" as const,
    startSeconds: "",
    endSeconds: "",
  };

  beforeEach(() => {
    vi.clearAllMocks();

    // Setup default mock return value
    vi.mocked(useFaultTrackingForm).mockReturnValue({
      formData: {
        startDate: null,
        endDate: null,
        startTime: "",
        startPeriod: "AM",
        endTime: "",
        endPeriod: "AM",
        startSeconds: "",
        endSeconds: "",
        comments: "",
      },
      setField: vi.fn(),
      reset: vi.fn(),
      validate: vi.fn(),
      setErrors: vi.fn(),
      errors: {},
      touched: {},
      setFieldTouched: vi.fn(),
      isValid: true,
      calculateDuration: vi.fn().mockReturnValue("0h 0m"),
      resetTrigger: 0,
    });

    // Setup parseOriginalTimingData mock
    vi.mocked(parseOriginalTimingData).mockReturnValue(mockOriginalData);

    // Setup parseAlarmToEditTimingsFormState mock
    vi.mocked(parseAlarmToEditTimingsFormState).mockReturnValue({
      startDate: new Date("2024-02-15"),
      endDate: new Date("2024-02-15"),
      startTime: "10:00",
      startPeriod: "AM",
      endTime: "11:00",
      endPeriod: "AM",
      startSeconds: "",
      endSeconds: "",
      comments: "Test comment",
    });

    // Setup hasFormDataChanged mock
    vi.mocked(hasFormDataChanged).mockReturnValue(false);
  });

  describe("basic functionality", () => {
    it("should call useFaultTrackingForm with correct hooks", () => {
      renderHook(() => useEditTimingsForm());

      expect(vi.mocked(useFaultTrackingForm)).toHaveBeenCalledWith(
        expect.any(Function), // useEditTimingsFormState wrapped in useCallback
        useEditTimingsValidation,
      );
    });

    it("should return the result from useFaultTrackingForm plus originalData and resetToOriginal", () => {
      const mockBaseResult = {
        formData: {
          startDate: new Date("2024-02-15"),
          endDate: new Date("2024-02-15"),
          startTime: "10:00",
          startPeriod: "AM" as const,
          endTime: "11:00",
          endPeriod: "AM" as const,
          startSeconds: "",
          endSeconds: "",
          comments: "Test",
        },
        setField: vi.fn(),
        reset: vi.fn(),
        validate: vi.fn(),
        setErrors: vi.fn(),
        errors: { comments: "Required" },
        touched: { comments: true },
        setFieldTouched: vi.fn(),
        isValid: false,
        calculateDuration: vi.fn().mockReturnValue("1h 0m"),
        resetTrigger: 1,
      };

      vi.mocked(useFaultTrackingForm).mockReturnValue(mockBaseResult);

      const { result } = renderHook(() =>
        useEditTimingsForm(mockSelectedAlarm),
      );

      // Should include all the base form properties
      expect(result.current.formData).toBe(mockBaseResult.formData);
      expect(result.current.setField).toBe(mockBaseResult.setField);
      expect(result.current.reset).toBe(mockBaseResult.reset);
      expect(result.current.validate).toBe(mockBaseResult.validate);
      expect(result.current.setErrors).toBe(mockBaseResult.setErrors);
      expect(result.current.errors).toBe(mockBaseResult.errors);
      expect(result.current.touched).toBe(mockBaseResult.touched);
      expect(result.current.setFieldTouched).toBe(
        mockBaseResult.setFieldTouched,
      );
      expect(result.current.isValid).toBe(mockBaseResult.isValid);
      expect(result.current.calculateDuration).toBe(
        mockBaseResult.calculateDuration,
      );
      expect(result.current.resetTrigger).toBe(mockBaseResult.resetTrigger);

      // Should also include the new properties
      expect(result.current.originalData).toBe(mockOriginalData);
      expect(typeof result.current.resetToOriginal).toBe("function");
      expect(typeof result.current.hasChanges).toBe("boolean");
    });

    it("should provide all expected form functionality", () => {
      const { result } = renderHook(() =>
        useEditTimingsForm(mockSelectedAlarm),
      );

      // Verify all expected properties and functions are available
      expect(result.current).toHaveProperty("formData");
      expect(result.current).toHaveProperty("originalData");
      expect(result.current).toHaveProperty("setField");
      expect(result.current).toHaveProperty("reset");
      expect(result.current).toHaveProperty("resetToOriginal");
      expect(result.current).toHaveProperty("validate");
      expect(result.current).toHaveProperty("setErrors");
      expect(result.current).toHaveProperty("errors");
      expect(result.current).toHaveProperty("touched");
      expect(result.current).toHaveProperty("setFieldTouched");
      expect(result.current).toHaveProperty("isValid");
      expect(result.current).toHaveProperty("calculateDuration");
      expect(result.current).toHaveProperty("resetTrigger");
      expect(result.current).toHaveProperty("hasChanges");
    });
  });

  describe("originalData and resetToOriginal", () => {
    it("should return null originalData when no alarm is provided", () => {
      const { result } = renderHook(() => useEditTimingsForm());

      expect(result.current.originalData).toBeNull();
    });

    it("should parse originalData when alarm is provided", () => {
      const { result } = renderHook(() =>
        useEditTimingsForm(mockSelectedAlarm, true, "America/New_York"),
      );

      expect(vi.mocked(parseOriginalTimingData)).toHaveBeenCalledWith(
        mockSelectedAlarm,
        "America/New_York",
      );
      expect(result.current.originalData).toBe(mockOriginalData);
    });

    it("should call parseOriginalTimingData with default timezone when none provided", () => {
      renderHook(() => useEditTimingsForm(mockSelectedAlarm));

      expect(vi.mocked(parseOriginalTimingData)).toHaveBeenCalledWith(
        mockSelectedAlarm,
        undefined,
      );
    });

    it("should provide resetToOriginal function", () => {
      const mockSetField = vi.fn();
      vi.mocked(useFaultTrackingForm).mockReturnValue({
        formData: {
          startDate: null,
          endDate: null,
          startTime: "",
          startPeriod: "AM",
          endTime: "",
          endPeriod: "AM",
          startSeconds: "",
          endSeconds: "",
          comments: "",
        },
        setField: mockSetField,
        reset: vi.fn(),
        validate: vi.fn(),
        setErrors: vi.fn(),
        errors: {},
        touched: {},
        setFieldTouched: vi.fn(),
        isValid: true,
        calculateDuration: vi.fn(),
        resetTrigger: 0,
      });

      const { result } = renderHook(() =>
        useEditTimingsForm(mockSelectedAlarm),
      );

      // Call resetToOriginal
      result.current.resetToOriginal();

      // Should set each field from original data
      expect(mockSetField).toHaveBeenCalledWith(
        "startDate",
        mockOriginalData.startDate,
      );
      expect(mockSetField).toHaveBeenCalledWith(
        "endDate",
        mockOriginalData.endDate,
      );
      expect(mockSetField).toHaveBeenCalledWith(
        "startTime",
        mockOriginalData.startTime,
      );
      expect(mockSetField).toHaveBeenCalledWith(
        "startPeriod",
        mockOriginalData.startPeriod,
      );
      expect(mockSetField).toHaveBeenCalledWith(
        "endTime",
        mockOriginalData.endTime,
      );
      expect(mockSetField).toHaveBeenCalledWith(
        "endPeriod",
        mockOriginalData.endPeriod,
      );
      expect(mockSetField).toHaveBeenCalledWith(
        "startSeconds",
        mockOriginalData.startSeconds,
      );
      expect(mockSetField).toHaveBeenCalledWith(
        "endSeconds",
        mockOriginalData.endSeconds,
      );
    });

    it("should not call setField when originalData is null", () => {
      const mockSetField = vi.fn();
      vi.mocked(useFaultTrackingForm).mockReturnValue({
        formData: {
          startDate: null,
          endDate: null,
          startTime: "",
          startPeriod: "AM",
          endTime: "",
          endPeriod: "AM",
          startSeconds: "",
          endSeconds: "",
          comments: "",
        },
        setField: mockSetField,
        reset: vi.fn(),
        validate: vi.fn(),
        setErrors: vi.fn(),
        errors: {},
        touched: {},
        setFieldTouched: vi.fn(),
        isValid: true,
        calculateDuration: vi.fn(),
        resetTrigger: 0,
      });

      const { result } = renderHook(() => useEditTimingsForm()); // No alarm

      // Call resetToOriginal
      result.current.resetToOriginal();

      // Should not call setField since originalData is null
      expect(mockSetField).not.toHaveBeenCalled();
    });
  });

  describe("hook composition", () => {
    it("should pass the form state hook to useFaultTrackingForm", () => {
      renderHook(() => useEditTimingsForm());

      const [formStateHook] = vi.mocked(useFaultTrackingForm).mock.calls[0];
      expect(typeof formStateHook).toBe("function"); // Wrapped in useCallback
    });

    it("should pass the validation hook to useFaultTrackingForm", () => {
      renderHook(() => useEditTimingsForm());

      const [, validationHook] = vi.mocked(useFaultTrackingForm).mock.calls[0];
      expect(validationHook).toBe(useEditTimingsValidation);
    });

    it("should maintain validation hook identity across re-renders", () => {
      const { rerender } = renderHook(() =>
        useEditTimingsForm(mockSelectedAlarm),
      );

      expect(vi.mocked(useFaultTrackingForm)).toHaveBeenCalledTimes(1);

      rerender();

      // useFaultTrackingForm should be called again on re-render
      expect(vi.mocked(useFaultTrackingForm)).toHaveBeenCalledTimes(2);

      // The validation hook should be the same reference
      const firstCall = vi.mocked(useFaultTrackingForm).mock.calls[0];
      const secondCall = vi.mocked(useFaultTrackingForm).mock.calls[1];

      expect(firstCall[1]).toBe(secondCall[1]); // Same validation hook
      // Form state hook may be recreated due to useCallback dependencies (selectedAlarm, modalOpen, facilityTimezone)
      expect(typeof firstCall[0]).toBe("function");
      expect(typeof secondCall[0]).toBe("function");
    });
  });

  describe("form data handling", () => {
    it("should handle form data updates through setField", () => {
      const mockSetField = vi.fn();
      vi.mocked(useFaultTrackingForm).mockReturnValue({
        formData: {
          startDate: null,
          endDate: null,
          startTime: "",
          startPeriod: "AM",
          endTime: "",
          endPeriod: "AM",
          startSeconds: "",
          endSeconds: "",
          comments: "",
        },
        setField: mockSetField,
        reset: vi.fn(),
        validate: vi.fn(),
        setErrors: vi.fn(),
        errors: {},
        touched: {},
        setFieldTouched: vi.fn(),
        isValid: true,
        calculateDuration: vi.fn(),
        resetTrigger: 0,
      });

      const { result } = renderHook(() => useEditTimingsForm());

      // Call setField
      const testDate = new Date("2024-02-15");
      result.current.setField("startDate", testDate);

      expect(mockSetField).toHaveBeenCalledWith("startDate", testDate);
    });

    it("should handle validation through validate function", () => {
      const mockValidate = vi.fn().mockReturnValue({ comments: "Required" });
      vi.mocked(useFaultTrackingForm).mockReturnValue({
        formData: {
          startDate: new Date("2024-02-15"),
          endDate: new Date("2024-02-15"),
          startTime: "10:00",
          startPeriod: "AM",
          endTime: "11:00",
          endPeriod: "AM",
          startSeconds: "",
          endSeconds: "",
          comments: "",
        },
        setField: vi.fn(),
        reset: vi.fn(),
        validate: mockValidate,
        setErrors: vi.fn(),
        errors: {},
        touched: {},
        setFieldTouched: vi.fn(),
        isValid: false,
        calculateDuration: vi.fn(),
        resetTrigger: 0,
      });

      const { result } = renderHook(() => useEditTimingsForm());

      const errors = result.current.validate();

      expect(mockValidate).toHaveBeenCalled();
      expect(errors).toEqual({ comments: "Required" });
    });

    it("should handle form reset", () => {
      const mockReset = vi.fn();
      vi.mocked(useFaultTrackingForm).mockReturnValue({
        formData: {
          startDate: new Date("2024-02-15"),
          endDate: new Date("2024-02-15"),
          startTime: "10:00",
          startPeriod: "AM",
          endTime: "11:00",
          endPeriod: "AM",
          startSeconds: "",
          endSeconds: "",
          comments: "Test",
        },
        setField: vi.fn(),
        reset: mockReset,
        validate: vi.fn(),
        setErrors: vi.fn(),
        errors: {},
        touched: { comments: true },
        setFieldTouched: vi.fn(),
        isValid: true,
        calculateDuration: vi.fn(),
        resetTrigger: 1,
      });

      const { result } = renderHook(() => useEditTimingsForm());

      result.current.reset();

      expect(mockReset).toHaveBeenCalled();
    });
  });

  describe("validation integration", () => {
    it("should reflect validation state correctly", () => {
      vi.mocked(useFaultTrackingForm).mockReturnValue({
        formData: {
          startDate: null,
          endDate: null,
          startTime: "",
          startPeriod: "AM",
          endTime: "",
          endPeriod: "AM",
          startSeconds: "",
          endSeconds: "",
          comments: "",
        },
        setField: vi.fn(),
        reset: vi.fn(),
        validate: vi.fn(),
        setErrors: vi.fn(),
        errors: {
          startDate: "Start date is required.",
          endDate: "End date is required.",
          comments: "Comments are required.",
        },
        touched: {
          startDate: true,
          comments: true,
        },
        setFieldTouched: vi.fn(),
        isValid: false,
        calculateDuration: vi.fn(),
        resetTrigger: 0,
      });

      const { result } = renderHook(() => useEditTimingsForm());

      expect(result.current.isValid).toBe(false);
      expect(result.current.errors).toEqual({
        startDate: "Start date is required.",
        endDate: "End date is required.",
        comments: "Comments are required.",
      });
      expect(result.current.touched).toEqual({
        startDate: true,
        comments: true,
      });
    });

    it("should handle field touched state", () => {
      const mockSetFieldTouched = vi.fn();
      vi.mocked(useFaultTrackingForm).mockReturnValue({
        formData: {
          startDate: null,
          endDate: null,
          startTime: "",
          startPeriod: "AM",
          endTime: "",
          endPeriod: "AM",
          startSeconds: "",
          endSeconds: "",
          comments: "",
        },
        setField: vi.fn(),
        reset: vi.fn(),
        validate: vi.fn(),
        setErrors: vi.fn(),
        errors: {},
        touched: {},
        setFieldTouched: mockSetFieldTouched,
        isValid: true,
        calculateDuration: vi.fn(),
        resetTrigger: 0,
      });

      const { result } = renderHook(() => useEditTimingsForm());

      result.current.setFieldTouched("startDate");

      expect(mockSetFieldTouched).toHaveBeenCalledWith("startDate");
    });
  });

  describe("duration calculation", () => {
    it("should provide duration calculation", () => {
      const mockCalculateDuration = vi.fn().mockReturnValue("2h 30m");
      vi.mocked(useFaultTrackingForm).mockReturnValue({
        formData: {
          startDate: new Date("2024-02-15"),
          endDate: new Date("2024-02-15"),
          startTime: "10:00",
          startPeriod: "AM",
          endTime: "12:30",
          endPeriod: "PM",
          startSeconds: "",
          endSeconds: "",
          comments: "Test",
        },
        setField: vi.fn(),
        reset: vi.fn(),
        validate: vi.fn(),
        setErrors: vi.fn(),
        errors: {},
        touched: {},
        setFieldTouched: vi.fn(),
        isValid: true,
        calculateDuration: mockCalculateDuration,
        resetTrigger: 0,
      });

      const { result } = renderHook(() => useEditTimingsForm());

      const duration = result.current.calculateDuration();

      expect(mockCalculateDuration).toHaveBeenCalled();
      expect(duration).toBe("2h 30m");
    });

    it("should handle reset trigger changes", () => {
      vi.mocked(useFaultTrackingForm).mockReturnValue({
        formData: {
          startDate: null,
          endDate: null,
          startTime: "",
          startPeriod: "AM",
          endTime: "",
          endPeriod: "AM",
          startSeconds: "",
          endSeconds: "",
          comments: "",
        },
        setField: vi.fn(),
        reset: vi.fn(),
        validate: vi.fn(),
        setErrors: vi.fn(),
        errors: {},
        touched: {},
        setFieldTouched: vi.fn(),
        isValid: true,
        calculateDuration: vi.fn(),
        resetTrigger: 3,
      });

      const { result } = renderHook(() => useEditTimingsForm());

      expect(result.current.resetTrigger).toBe(3);
    });
  });

  describe("hasChanges functionality", () => {
    it("should return false for hasChanges when form data matches original data", () => {
      vi.mocked(hasFormDataChanged).mockReturnValue(false);

      const { result } = renderHook(() =>
        useEditTimingsForm(mockSelectedAlarm),
      );

      expect(result.current.hasChanges).toBe(false);
    });

    it("should return true for hasChanges when form data differs from original data", () => {
      // Use form data that is not completely empty to avoid initialization guard
      const populatedFormData = {
        startDate: new Date("2024-02-15"),
        endDate: new Date("2024-02-15"),
        startTime: "09:00", // Different from alarm data
        startPeriod: "AM" as const,
        endTime: "10:00", // Different from alarm data
        endPeriod: "AM" as const,
        startSeconds: "",
        endSeconds: "",
        comments: "Different comment",
      };

      vi.mocked(useFaultTrackingForm).mockReturnValue({
        formData: populatedFormData,
        setField: vi.fn(),
        reset: vi.fn(),
        validate: vi.fn(),
        setErrors: vi.fn(),
        errors: {},
        touched: {},
        setFieldTouched: vi.fn(),
        isValid: true,
        calculateDuration: vi.fn(),
        resetTrigger: 0,
      });

      vi.mocked(hasFormDataChanged).mockReturnValue(true);

      const { result } = renderHook(() =>
        useEditTimingsForm(mockSelectedAlarm),
      );

      expect(result.current.hasChanges).toBe(true);
    });

    it("should return false for hasChanges when no original data exists", () => {
      const { result } = renderHook(() => useEditTimingsForm()); // No selectedAlarm

      expect(result.current.hasChanges).toBe(false);
    });

    it("should return false for hasChanges during form initialization", () => {
      // Simulate form initialization state: form is empty but alarm has data
      const emptyFormData = {
        startDate: null,
        endDate: null,
        startTime: "",
        startPeriod: "AM" as const,
        endTime: "",
        endPeriod: "AM" as const,
        startSeconds: "",
        endSeconds: "",
        comments: "",
      };

      vi.mocked(useFaultTrackingForm).mockReturnValue({
        formData: emptyFormData,
        setField: vi.fn(),
        reset: vi.fn(),
        validate: vi.fn(),
        setErrors: vi.fn(),
        errors: {},
        touched: {},
        setFieldTouched: vi.fn(),
        isValid: true,
        calculateDuration: vi.fn(),
        resetTrigger: 0,
      });

      // Mock alarm data that parseAlarmToEditTimingsFormState would return
      // Focus on timing data (comments might be empty for unedited alarms)
      vi.mocked(parseAlarmToEditTimingsFormState).mockReturnValue({
        startDate: new Date("2024-02-15"),
        endDate: new Date("2024-02-15"),
        startTime: "10:00",
        startPeriod: "AM",
        endTime: "11:00",
        endPeriod: "AM",
        startSeconds: "",
        endSeconds: "",
        comments: "", // Empty comments for unedited alarm
      });

      const { result } = renderHook(() =>
        useEditTimingsForm(mockSelectedAlarm),
      );

      // Should return false during initialization even though form data differs from alarm data
      expect(result.current.hasChanges).toBe(false);
    });

    it("should call hasFormDataChanged with correct parameters", () => {
      const mockFormData = {
        startDate: new Date("2024-02-15"),
        endDate: new Date("2024-02-15"),
        startTime: "10:00",
        startPeriod: "AM" as const,
        endTime: "11:00",
        endPeriod: "AM" as const,
        startSeconds: "",
        endSeconds: "",
        comments: "Modified comment",
      };

      const mockCurrentAlarmData = {
        startDate: new Date("2024-02-15"),
        endDate: new Date("2024-02-15"),
        startTime: "10:00",
        startPeriod: "AM" as const,
        endTime: "11:00",
        endPeriod: "AM" as const,
        startSeconds: "",
        endSeconds: "",
        comments: "Original comment",
      };

      vi.mocked(useFaultTrackingForm).mockReturnValue({
        formData: mockFormData,
        setField: vi.fn(),
        reset: vi.fn(),
        validate: vi.fn(),
        setErrors: vi.fn(),
        errors: {},
        touched: {},
        setFieldTouched: vi.fn(),
        isValid: true,
        calculateDuration: vi.fn(),
        resetTrigger: 0,
      });

      vi.mocked(parseAlarmToEditTimingsFormState).mockReturnValue(
        mockCurrentAlarmData,
      );

      renderHook(() =>
        useEditTimingsForm(mockSelectedAlarm, true, "America/New_York"),
      );

      expect(vi.mocked(parseAlarmToEditTimingsFormState)).toHaveBeenCalledWith(
        mockSelectedAlarm,
        "America/New_York",
      );

      expect(vi.mocked(hasFormDataChanged)).toHaveBeenCalledWith(
        mockFormData,
        mockCurrentAlarmData,
      );
    });

    it("should use parseAlarmToEditTimingsFormState for current alarm state", () => {
      renderHook(() =>
        useEditTimingsForm(mockSelectedAlarm, true, "America/New_York"),
      );

      expect(vi.mocked(parseAlarmToEditTimingsFormState)).toHaveBeenCalledWith(
        mockSelectedAlarm,
        "America/New_York",
      );
    });

    it("should use default timezone when none provided", () => {
      renderHook(() => useEditTimingsForm(mockSelectedAlarm));

      expect(vi.mocked(parseAlarmToEditTimingsFormState)).toHaveBeenCalledWith(
        mockSelectedAlarm,
        undefined,
      );
    });
  });

  describe("type safety", () => {
    it("should properly type the form data as EditTimingsForm", () => {
      const { result } = renderHook(() => useEditTimingsForm());

      // TypeScript should enforce that formData matches EditTimingsForm interface
      const formData = result.current.formData;

      // These properties should exist and be properly typed
      expect(formData).toHaveProperty("startDate");
      expect(formData).toHaveProperty("endDate");
      expect(formData).toHaveProperty("startTime");
      expect(formData).toHaveProperty("startPeriod");
      expect(formData).toHaveProperty("endTime");
      expect(formData).toHaveProperty("endPeriod");
      expect(formData).toHaveProperty("startSeconds");
      expect(formData).toHaveProperty("endSeconds");
      expect(formData).toHaveProperty("comments");
    });

    it("should enforce EditTimingsForm fields in setField calls", () => {
      const mockSetField = vi.fn();
      vi.mocked(useFaultTrackingForm).mockReturnValue({
        formData: {
          startDate: null,
          endDate: null,
          startTime: "",
          startPeriod: "AM",
          endTime: "",
          endPeriod: "AM",
          startSeconds: "",
          endSeconds: "",
          comments: "",
        },
        setField: mockSetField,
        reset: vi.fn(),
        validate: vi.fn(),
        setErrors: vi.fn(),
        errors: {},
        touched: {},
        setFieldTouched: vi.fn(),
        isValid: true,
        calculateDuration: vi.fn(),
        resetTrigger: 0,
      });

      const { result } = renderHook(() => useEditTimingsForm());

      // These should be valid field names for EditTimingsForm
      result.current.setField("startDate", new Date());
      result.current.setField("startTime", "10:00");
      result.current.setField("startPeriod", "PM");
      result.current.setField("comments", "Test comment");

      expect(mockSetField).toHaveBeenCalledTimes(4);
    });
  });
});

describe("getMinStartDate export", () => {
  it("should re-export getMinStartDate from shared module", () => {
    expect(typeof getMinStartDate).toBe("function");

    const minDate = getMinStartDate();
    expect(minDate).toEqual(new Date("2024-01-15"));
  });
});
