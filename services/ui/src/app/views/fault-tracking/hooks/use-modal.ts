import { useState, useCallback } from "react";

interface ModalState {
  isOpen: boolean;
  toggle: () => void;
  open: () => void;
  close: () => void;
}

/**
 * Generic modal hook that provides state management for modal visibility
 * @param initialState - Initial state of the modal (default: false)
 * @returns Object with modal state and control functions
 */
export function useModal(initialState = false): ModalState {
  const [isOpen, setIsOpen] = useState(initialState);

  const toggle = useCallback(() => setIsOpen((prev) => !prev), []);
  const open = useCallback(() => setIsOpen(true), []);
  const close = useCallback(() => setIsOpen(false), []);

  return {
    isOpen,
    toggle,
    open,
    close,
  };
}
