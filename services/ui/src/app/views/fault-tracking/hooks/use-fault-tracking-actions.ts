import { useCallback, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { useFeatureFlag } from "../../../config/hooks/use-config";

interface FaultTrackingActionsProps {
  toggleEditTimingsModal: () => void;
  toggleCalculationStatusModal: () => void;
  selectedRowsCount: number;
  allSelectedHaveSameInclusionStatus: boolean;
}

export function useFaultTrackingActions({
  toggleEditTimingsModal,
  toggleCalculationStatusModal,
  selectedRowsCount,
  allSelectedHaveSameInclusionStatus,
}: FaultTrackingActionsProps) {
  const { t } = useTranslation();

  const { enabled: editTimingsButtonEnabled } = useFeatureFlag(
    "fault-tracking-edit-timings-button",
  );
  const { enabled: calculationStatusButtonEnabled } = useFeatureFlag(
    "fault-tracking-calculation-status-button",
  );

  const handleEditTimingsClick = useCallback(() => {
    toggleEditTimingsModal();
  }, [toggleEditTimingsModal]);

  const handleCalculationStatusClick = useCallback(() => {
    toggleCalculationStatusModal();
  }, [toggleCalculationStatusModal]);

  const batchActions = useMemo(() => {
    const actions = [];

    if (editTimingsButtonEnabled) {
      actions.push({
        label: t("faultTracking.editAlarmTimings", "Edit Alarm Timings"),
        onClick: handleEditTimingsClick,
        disabled: selectedRowsCount > 1,
        // figure out why tooltip is not working
      });
    }

    if (calculationStatusButtonEnabled) {
      actions.push({
        label: t(
          "faultTracking.updateCalculationStatus",
          "Update Calculation Status",
        ),
        onClick: handleCalculationStatusClick,
        disabled: selectedRowsCount < 1 || !allSelectedHaveSameInclusionStatus,
      });
    }

    return actions;
  }, [
    editTimingsButtonEnabled,
    calculationStatusButtonEnabled,
    t,
    handleEditTimingsClick,
    selectedRowsCount,
    allSelectedHaveSameInclusionStatus,
  ]);

  return {
    batchActions,
  };
}
