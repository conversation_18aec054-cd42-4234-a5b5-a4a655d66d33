import { useMemo } from "react";
import { useTranslation } from "react-i18next";

export interface RemovalReason {
  value: string;
  label: string;
}

export function useRemovalReasons(): RemovalReason[] {
  const { t } = useTranslation();

  return useMemo(() => {
    // static data in abc order
    // in future  could fetch from an API or external config or somethin
    return [
      {
        value: "customer-responsibility",
        label: t(
          "faultTracking.removalReasons.customerResponsibility",
          "Customer Responsibility",
        ),
      },
      {
        value: "equipment-excluded",
        label: t(
          "faultTracking.removalReasons.equipmentExcluded",
          "Equipment Excluded",
        ),
      },
      {
        value: "external-outage",
        label: t(
          "faultTracking.removalReasons.externalOutage",
          "External Outage",
        ),
      },
      {
        value: "fault-excluded",
        label: t(
          "faultTracking.removalReasons.faultExcluded",
          "Fault Excluded",
        ),
      },
      {
        value: "material-quality",
        label: t(
          "faultTracking.removalReasons.materialQuality",
          "Material Quality Poor",
        ),
      },
      {
        value: "planned-maintenance",
        label: t(
          "faultTracking.removalReasons.plannedMaintenance",
          "Planned Maintenance Activity",
        ),
      },
      {
        value: "planned-software",
        label: t(
          "faultTracking.removalReasons.plannedSoftware",
          "Planned Software Update",
        ),
      },
    ];
  }, [t]);
}
