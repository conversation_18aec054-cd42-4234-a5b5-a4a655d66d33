import { describe, it, expect, vi, beforeEach } from "vitest";
import { renderHook, act } from "@testing-library/react";
import {
  useCalculationStatusForm,
  type CalculationStatusFormData,
} from "./use-calculation-status-form";

describe("useCalculationStatusForm", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("initializes with null formData when initialIsIncluded is null", () => {
    const { result } = renderHook(() => useCalculationStatusForm(null));

    expect(result.current.formData).toBeNull();
    expect(result.current.originalValues).toBeNull();
    expect(result.current.hasChanges).toBe(false);
    expect(result.current.isValid).toBe(false);
  });

  it("initializes with correct formData when initialIsIncluded is true", () => {
    const { result } = renderHook(() => useCalculationStatusForm(true));

    const expectedData: CalculationStatusFormData = {
      isIncluded: true,
      removalReason: null,
      comments: "",
    };

    expect(result.current.formData).toEqual(expectedData);
    expect(result.current.originalValues).toEqual(expectedData);
    expect(result.current.hasChanges).toBe(false);
    expect(result.current.isValid).toBe(false); // Invalid because comments are empty
  });

  it("initializes with correct formData when initialIsIncluded is false", () => {
    const { result } = renderHook(() => useCalculationStatusForm(false));

    const expectedData: CalculationStatusFormData = {
      isIncluded: false,
      removalReason: null,
      comments: "",
    };

    expect(result.current.formData).toEqual(expectedData);
    expect(result.current.originalValues).toEqual(expectedData);
    expect(result.current.hasChanges).toBe(false);
    expect(result.current.isValid).toBe(false); // Invalid because comments are empty and removalReason is null
  });

  it("updates isIncluded correctly", () => {
    const { result } = renderHook(() => useCalculationStatusForm(true));

    act(() => {
      result.current.updateIsIncluded(false);
    });

    expect(result.current.formData?.isIncluded).toBe(false);
    expect(result.current.hasChanges).toBe(true);
  });

  it("preserves removalReason when toggling to excluded", () => {
    const { result } = renderHook(() => useCalculationStatusForm(false));

    // First set a removal reason
    act(() => {
      result.current.updateRemovalReason("customer-responsibility");
    });

    // Verify it was set
    expect(result.current.formData?.removalReason).toBe(
      "customer-responsibility",
    );

    // Then toggle to included (should clear removalReason)
    act(() => {
      result.current.updateIsIncluded(true);
    });

    expect(result.current.formData?.removalReason).toBeNull();

    // Set a new removal reason while included (shouldn't matter since it gets cleared)
    act(() => {
      result.current.updateRemovalReason("equipment-excluded");
    });

    // Then toggle back to excluded (should preserve the removalReason that was set)
    act(() => {
      result.current.updateIsIncluded(false);
    });

    expect(result.current.formData?.removalReason).toBe("equipment-excluded");
  });

  it("clears removalReason when toggling to included", () => {
    const { result } = renderHook(() => useCalculationStatusForm(false));

    // First set a removal reason
    act(() => {
      result.current.updateRemovalReason("customer-responsibility");
    });

    // Then toggle to included
    act(() => {
      result.current.updateIsIncluded(true);
    });

    expect(result.current.formData?.removalReason).toBeNull();
  });

  it("updates removalReason correctly", () => {
    const { result } = renderHook(() => useCalculationStatusForm(false));

    act(() => {
      result.current.updateRemovalReason("equipment-excluded");
    });

    expect(result.current.formData?.removalReason).toBe("equipment-excluded");
    expect(result.current.hasChanges).toBe(true);
  });

  it("marks removalReason as touched when updated", () => {
    const { result } = renderHook(() => useCalculationStatusForm(false));

    act(() => {
      result.current.updateRemovalReason("equipment-excluded");
    });

    expect(result.current.fieldErrors.removalReason).toBe(false); // Now has value, so no error
  });

  it("updates comments correctly", () => {
    const { result } = renderHook(() => useCalculationStatusForm(true));

    act(() => {
      result.current.updateComments("Test comment");
    });

    expect(result.current.formData?.comments).toBe("Test comment");
    expect(result.current.hasChanges).toBe(true);
  });

  it("handles comments blur correctly", () => {
    const { result } = renderHook(() => useCalculationStatusForm(true));

    act(() => {
      result.current.handleCommentsBlur();
    });

    // Should mark comments as touched, showing error since it's empty
    expect(result.current.fieldErrors.comments).toBe(true);
  });

  it("validates form correctly for included state", () => {
    const { result } = renderHook(() => useCalculationStatusForm(true));

    // Initially invalid (no comments)
    expect(result.current.isValid).toBe(false);

    // Add comments to make it valid
    act(() => {
      result.current.updateComments("Valid comment");
    });

    expect(result.current.isValid).toBe(true);
  });

  it("validates form correctly for excluded state", () => {
    const { result } = renderHook(() => useCalculationStatusForm(false));

    // Initially invalid (no comments, no removal reason)
    expect(result.current.isValid).toBe(false);

    // Add comments but no removal reason - still invalid
    act(() => {
      result.current.updateComments("Valid comment");
    });
    expect(result.current.isValid).toBe(false);

    // Add removal reason - now valid
    act(() => {
      result.current.updateRemovalReason("customer-responsibility");
    });
    expect(result.current.isValid).toBe(true);
  });

  it("detects changes correctly", () => {
    const { result } = renderHook(() => useCalculationStatusForm(true));

    // Initially no changes
    expect(result.current.hasChanges).toBe(false);

    // Change isIncluded
    act(() => {
      result.current.updateIsIncluded(false);
    });
    expect(result.current.hasChanges).toBe(true);

    // Reset to original
    act(() => {
      result.current.updateIsIncluded(true);
    });
    expect(result.current.hasChanges).toBe(false);

    // Change comments
    act(() => {
      result.current.updateComments("Changed");
    });
    expect(result.current.hasChanges).toBe(true);
  });

  it("resets form to original values", () => {
    const { result } = renderHook(() => useCalculationStatusForm(true));

    // Make some changes
    act(() => {
      result.current.updateIsIncluded(false);
      result.current.updateComments("Changed comment");
      result.current.updateRemovalReason("customer-responsibility");
    });

    expect(result.current.hasChanges).toBe(true);

    // Reset form
    act(() => {
      result.current.resetForm();
    });

    expect(result.current.formData?.isIncluded).toBe(true);
    expect(result.current.formData?.comments).toBe("");
    expect(result.current.formData?.removalReason).toBeNull();
    expect(result.current.hasChanges).toBe(false);
  });

  it("handles field errors correctly for comments", () => {
    const { result } = renderHook(() => useCalculationStatusForm(true));

    // Initially no error (not touched)
    expect(result.current.fieldErrors.comments).toBe(false);

    // Blur without content should show error
    act(() => {
      result.current.handleCommentsBlur();
    });
    expect(result.current.fieldErrors.comments).toBe(true);

    // Add content should clear error
    act(() => {
      result.current.updateComments("Valid comment");
    });
    expect(result.current.fieldErrors.comments).toBe(false);
  });

  it("handles field errors correctly for removal reason", () => {
    const { result } = renderHook(() => useCalculationStatusForm(false));

    // Initially no error (not touched)
    expect(result.current.fieldErrors.removalReason).toBe(false);

    // Touch by selecting a reason
    act(() => {
      result.current.updateRemovalReason("customer-responsibility");
    });
    expect(result.current.fieldErrors.removalReason).toBe(false);

    // Clear the reason while in excluded state should show error when touched
    act(() => {
      result.current.updateRemovalReason("");
    });
    // Since we pass empty string instead of null, need to test with null
    act(() => {
      result.current.updateRemovalReason("customer-responsibility");
      result.current.updateIsIncluded(false);
    });

    // The error should show when excluded and no reason selected after being touched
    expect(result.current.fieldErrors.removalReason).toBe(false); // Has valid reason
  });

  it("resets state when initialIsIncluded changes", () => {
    const { result, rerender } = renderHook(
      ({ initialIsIncluded }) => useCalculationStatusForm(initialIsIncluded),
      { initialProps: { initialIsIncluded: true } },
    );

    // Make some changes
    act(() => {
      result.current.updateComments("Test comment");
    });

    expect(result.current.hasChanges).toBe(true);

    // Change the initial value
    rerender({ initialIsIncluded: false });

    // Should reset to new initial state
    expect(result.current.formData?.isIncluded).toBe(false);
    expect(result.current.formData?.comments).toBe("");
    expect(result.current.hasChanges).toBe(false);
  });

  it("handles whitespace-only comments as invalid", () => {
    const { result } = renderHook(() => useCalculationStatusForm(true));

    act(() => {
      result.current.updateComments("   \n\t   ");
    });

    expect(result.current.isValid).toBe(false);
  });

  it("provides all expected hook returns", () => {
    const { result } = renderHook(() => useCalculationStatusForm(true));

    expect(result.current).toHaveProperty("formData");
    expect(result.current).toHaveProperty("originalValues");
    expect(result.current).toHaveProperty("hasChanges");
    expect(result.current).toHaveProperty("updateIsIncluded");
    expect(result.current).toHaveProperty("updateRemovalReason");
    expect(result.current).toHaveProperty("updateComments");
    expect(result.current).toHaveProperty("handleCommentsBlur");
    expect(result.current).toHaveProperty("resetForm");
    expect(result.current).toHaveProperty("isValid");
    expect(result.current).toHaveProperty("fieldErrors");

    expect(typeof result.current.updateIsIncluded).toBe("function");
    expect(typeof result.current.updateRemovalReason).toBe("function");
    expect(typeof result.current.updateComments).toBe("function");
    expect(typeof result.current.handleCommentsBlur).toBe("function");
    expect(typeof result.current.resetForm).toBe("function");
  });
});
