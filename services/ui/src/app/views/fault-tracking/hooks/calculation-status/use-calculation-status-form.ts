import { useState, useEffect, useMemo } from "react";

export interface CalculationStatusFormData {
  isIncluded: boolean;
  removalReason: string | null;
  comments: string;
}

export function useCalculationStatusForm(initialIsIncluded: boolean | null) {
  const [originalValues, setOriginalValues] =
    useState<CalculationStatusFormData | null>(null);

  const [formData, setFormData] = useState<CalculationStatusFormData | null>(
    null,
  );

  const [touchedFields, setTouchedFields] = useState<{
    comments: boolean;
    removalReason: boolean;
  }>({ comments: false, removalReason: false });

  useEffect(() => {
    if (initialIsIncluded !== null) {
      const initialData = {
        isIncluded: initialIsIncluded,
        removalReason: null,
        comments: "",
      };
      setOriginalValues(initialData);
      setFormData(initialData);
      setTouchedFields({ comments: false, removalReason: false });
    } else {
      setOriginalValues(null);
      setFormData(null);
      setTouchedFields({ comments: false, removalReason: false });
    }
  }, [initialIsIncluded]);

  const hasChanges = useMemo(() => {
    if (!originalValues || !formData) return false;
    return (
      originalValues.isIncluded !== formData.isIncluded ||
      originalValues.removalReason !== formData.removalReason ||
      originalValues.comments !== formData.comments
    );
  }, [originalValues, formData]);

  const updateIsIncluded = (isIncluded: boolean) => {
    if (formData) {
      setFormData({
        ...formData,
        isIncluded,
        removalReason: isIncluded ? null : formData.removalReason,
      });
    }
  };

  const updateRemovalReason = (reason: string) => {
    if (formData) {
      setFormData({ ...formData, removalReason: reason });
    }
    setTouchedFields((prev) => ({ ...prev, removalReason: true }));
  };

  const updateComments = (comments: string) => {
    if (formData) {
      setFormData({ ...formData, comments });
    }
  };

  const handleCommentsBlur = () => {
    setTouchedFields((prev) => ({ ...prev, comments: true }));
  };

  const resetForm = () => {
    if (originalValues) {
      setFormData({ ...originalValues });
    }
  };

  const isValid = useMemo(() => {
    if (!formData) return false;
    if (!formData.comments.trim()) return false;
    if (!formData.isIncluded && formData.removalReason === null) return false;
    return true;
  }, [formData]);

  const fieldErrors = useMemo(() => {
    if (!formData) return { comments: false, removalReason: false };

    return {
      comments: touchedFields.comments && !formData.comments.trim(),
      removalReason:
        touchedFields.removalReason &&
        !formData.isIncluded &&
        formData.removalReason === null,
    };
  }, [formData, touchedFields]);

  return {
    formData,
    originalValues,
    hasChanges,
    updateIsIncluded,
    updateRemovalReason,
    updateComments,
    handleCommentsBlur,
    resetForm,
    isValid,
    fieldErrors,
  };
}
