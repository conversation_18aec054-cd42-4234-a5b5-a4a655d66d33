import { useState } from "react";
import { useTranslation } from "react-i18next";
import { ictApi } from "../../../../api/ict-api";
import { useNotification } from "../../../../components/toast/use-notification";
import { Logger } from "../../../../utils/logger";

import type { FaultAlarm } from "../../types/types";
import type { CalculationStatusFormData } from "./use-calculation-status-form";

const logger = new Logger("useUpdateCalculationStatus");

export interface UpdateCalculationStatusPayload {
  id: string;
  isIncluded: boolean;
  comments: string;
  excludeReason?: string;
  startDateLocal?: string;
  endDateLocal?: string;
}

export function useUpdateCalculationStatus(onSuccess?: () => void) {
  const { t } = useTranslation();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { success, error: showError } = useNotification();

  const updateCalculationStatus = async (
    formData: CalculationStatusFormData,
    selectedAlarms: FaultAlarm[],
  ): Promise<boolean> => {
    if (!formData || selectedAlarms.length === 0) {
      showError(
        t(
          "faultTracking.validation.noAlarmsSelected",
          "No alarms selected for update",
        ),
      );
      return false;
    }

    setIsSubmitting(true);

    try {
      const apiPayloads: UpdateCalculationStatusPayload[] = selectedAlarms.map(
        (alarm) => {
          const basePayload = {
            id: alarm.id,
            isIncluded: formData.isIncluded,
            comments: formData.comments,
          };

          if (formData.isIncluded) {
            // when including an alarm, we need to provide timing information
            // use the current timing from the alarm (either updated or original)
            const startTime =
              alarm.timing?.updatedStartTime || alarm.timing?.startTime;
            const endTime =
              alarm.timing?.updatedEndTime || alarm.timing?.endTime;

            if (!startTime || !endTime) {
              throw new Error(
                `Missing timing information for alarm ${alarm.id}`,
              );
            }

            return {
              ...basePayload,
              startDateLocal: startTime,
              endDateLocal: endTime,
            };
          } else {
            return {
              ...basePayload,
              ...(formData.removalReason && {
                excludeReason: formData.removalReason,
              }),
            };
          }
        },
      );

      logger.info("Submitting calculation status updates", {
        alarmCount: selectedAlarms.length,
        isIncluded: formData.isIncluded,
        excludeReason: formData.removalReason,
        alarmIds: selectedAlarms.map((alarm) => alarm.id),
      });

      const updatePromises = apiPayloads.map(async (payload) => {
        const response = await ictApi.fetchClient.PUT("/availability/alarms", {
          body: payload,
        });
        return response;
      });

      await Promise.all(updatePromises);

      logger.info("Successfully updated calculation status for all alarms", {
        alarmCount: selectedAlarms.length,
        isIncluded: formData.isIncluded,
      });

      const successMessage = formData.isIncluded
        ? t(
            "faultTracking.calculationStatusIncludedSuccessfully",
            "{{count}} alarm{{plural}} successfully included in calculation",
            {
              count: selectedAlarms.length,
              plural: selectedAlarms.length > 1 ? "s" : "",
            },
          )
        : t(
            "faultTracking.calculationStatusExcludedSuccessfully",
            "{{count}} alarm{{plural}} successfully excluded from calculation",
            {
              count: selectedAlarms.length,
              plural: selectedAlarms.length > 1 ? "s" : "",
            },
          );

      success(successMessage);

      if (onSuccess) {
        onSuccess();
      }

      return true;
    } catch (error: unknown) {
      logger.error("Failed to update calculation status", {
        alarmCount: selectedAlarms.length,
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        alarmIds: selectedAlarms.map((alarm) => alarm.id),
      });

      showError(
        t(
          "faultTracking.calculationStatusUpdateFailed",
          "Failed to update calculation status. Please try again.",
        ),
      );
      return false;
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    updateCalculationStatus,
    isSubmitting,
  };
}
