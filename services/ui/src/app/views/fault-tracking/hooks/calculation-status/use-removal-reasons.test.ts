import { describe, it, expect, vi, beforeEach } from "vitest";
import { renderHook } from "@testing-library/react";
import { useRemovalReasons } from "./use-removal-reasons";

// Mock react-i18next
const mockT = vi.fn((_key: string, defaultValue: string) => defaultValue);
vi.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: mockT,
  }),
}));

describe("useRemovalReasons", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("returns an array of removal reasons", () => {
    const { result } = renderHook(() => useRemovalReasons());

    expect(Array.isArray(result.current)).toBe(true);
    expect(result.current.length).toBeGreaterThan(0);
  });

  it("returns reasons with correct structure", () => {
    const { result } = renderHook(() => useRemovalReasons());

    result.current.forEach((reason) => {
      expect(reason).toHaveProperty("value");
      expect(reason).toHaveProperty("label");
      expect(typeof reason.value).toBe("string");
      expect(typeof reason.label).toBe("string");
      expect(reason.value.length).toBeGreaterThan(0);
      expect(reason.label.length).toBeGreaterThan(0);
    });
  });

  it("includes expected removal reasons", () => {
    const { result } = renderHook(() => useRemovalReasons());

    const values = result.current.map((reason) => reason.value);
    const labels = result.current.map((reason) => reason.label);

    // Check for some expected values
    expect(values).toContain("customer-responsibility");
    expect(values).toContain("equipment-excluded");
    expect(values).toContain("external-outage");
    expect(values).toContain("fault-excluded");
    expect(values).toContain("material-quality");
    expect(values).toContain("planned-maintenance");
    expect(values).toContain("planned-software");

    // Check for some expected labels
    expect(labels).toContain("Customer Responsibility");
    expect(labels).toContain("Equipment Excluded");
    expect(labels).toContain("External Outage");
    expect(labels).toContain("Fault Excluded");
  });

  it("uses correct translation keys", () => {
    renderHook(() => useRemovalReasons());

    // Verify translation function was called with expected keys
    expect(mockT).toHaveBeenCalledWith(
      "faultTracking.removalReasons.customerResponsibility",
      "Customer Responsibility",
    );
    expect(mockT).toHaveBeenCalledWith(
      "faultTracking.removalReasons.equipmentExcluded",
      "Equipment Excluded",
    );
    expect(mockT).toHaveBeenCalledWith(
      "faultTracking.removalReasons.externalOutage",
      "External Outage",
    );
    expect(mockT).toHaveBeenCalledWith(
      "faultTracking.removalReasons.faultExcluded",
      "Fault Excluded",
    );
    expect(mockT).toHaveBeenCalledWith(
      "faultTracking.removalReasons.materialQuality",
      "Material Quality Poor",
    );
    expect(mockT).toHaveBeenCalledWith(
      "faultTracking.removalReasons.plannedMaintenance",
      "Planned Maintenance Activity",
    );
    expect(mockT).toHaveBeenCalledWith(
      "faultTracking.removalReasons.plannedSoftware",
      "Planned Software Update",
    );
  });

  it("returns reasons sorted alphabetically", () => {
    const { result } = renderHook(() => useRemovalReasons());

    const labels = result.current.map((reason) => reason.label);
    const sortedLabels = [...labels].sort();

    expect(labels).toEqual(sortedLabels);
  });

  it("memoizes the result", () => {
    const { result, rerender } = renderHook(() => useRemovalReasons());

    const firstResult = result.current;

    // Rerender the hook
    rerender();

    const secondResult = result.current;

    // Should return the same reference due to memoization
    expect(firstResult).toBe(secondResult);
  });

  it("has unique values for all reasons", () => {
    const { result } = renderHook(() => useRemovalReasons());

    const values = result.current.map((reason) => reason.value);
    const uniqueValues = [...new Set(values)];

    expect(values.length).toBe(uniqueValues.length);
  });

  it("has consistent value format", () => {
    const { result } = renderHook(() => useRemovalReasons());

    result.current.forEach((reason) => {
      // Values should be kebab-case
      expect(reason.value).toMatch(/^[a-z]+(-[a-z]+)*$/);
    });
  });

  it("provides reasonable default values", () => {
    // Test that default values are sensible even without translations
    const { result } = renderHook(() => useRemovalReasons());

    result.current.forEach((reason) => {
      // Labels should not be empty or just the translation key
      expect(reason.label).not.toBe("");
      expect(reason.label.startsWith("faultTracking.removalReasons.")).toBe(
        false,
      );

      // Values should be meaningful
      expect(reason.value).not.toBe("");
    });
  });

  it("returns expected number of reasons", () => {
    const { result } = renderHook(() => useRemovalReasons());

    // We expect 7 removal reasons based on the implementation
    expect(result.current).toHaveLength(7);
  });

  it("maintains stable order across re-renders", () => {
    const { result, rerender } = renderHook(() => useRemovalReasons());

    const firstOrder = result.current.map((reason) => reason.value);

    rerender();

    const secondOrder = result.current.map((reason) => reason.value);

    expect(firstOrder).toEqual(secondOrder);
  });

  it("all reasons have non-empty trimmed labels", () => {
    const { result } = renderHook(() => useRemovalReasons());

    result.current.forEach((reason) => {
      expect(reason.label.trim()).toBe(reason.label);
      expect(reason.label.trim().length).toBeGreaterThan(0);
    });
  });

  it("returns consistent result structure", () => {
    const { result } = renderHook(() => useRemovalReasons());

    const originalLength = result.current.length;

    // The array should be consistent across calls
    expect(result.current).toHaveLength(originalLength);
    expect(originalLength).toBeGreaterThan(0);

    // Each item should have the expected structure
    result.current.forEach((reason) => {
      expect(reason).toHaveProperty("value");
      expect(reason).toHaveProperty("label");
    });
  });
});
