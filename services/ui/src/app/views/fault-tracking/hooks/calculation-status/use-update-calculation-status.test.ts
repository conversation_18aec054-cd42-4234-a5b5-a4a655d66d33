import { describe, it, expect, vi, beforeEach } from "vitest";
import { renderHook, act } from "@testing-library/react";
import type { FaultAlarm } from "../../types/types";
import type { CalculationStatusFormData } from "./use-calculation-status-form";

// Mock dependencies
const mockPut = vi.fn();
const mockSuccess = vi.fn();
const mockError = vi.fn();
const mockLoggerInfo = vi.fn();
const mockLoggerError = vi.fn();

vi.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (_key: string, defaultValue: string) => defaultValue,
  }),
}));

vi.mock("../../../../components/toast/use-notification", () => ({
  useNotification: () => ({
    success: mockSuccess,
    error: mockError,
  }),
}));

vi.mock("../../../../utils/logger", () => ({
  Logger: vi.fn(() => ({
    info: mockLoggerInfo,
    error: mockLoggerError,
  })),
}));

vi.mock("../../../../api/ict-api", () => ({
  ictApi: {
    fetchClient: {
      PUT: mockPut,
    },
  },
}));

describe("useUpdateCalculationStatus", () => {
  const mockAlarm: FaultAlarm = {
    id: "test-alarm-id",
    splitId: "test-split-id",
    faultId: "test-fault-id",
    status: "ACTIVE",
    timing: {
      startTime: "2025-01-15T10:30:00.000Z",
      endTime: "2025-01-15T11:45:00.000Z",
      duration: "4500000",
      updatedStartTime: "2025-01-15T10:35:00.000Z",
      updatedEndTime: "2025-01-15T11:50:00.000Z",
      updatedDuration: "4500000",
    },
    location: {
      area: "TestArea",
      section: "TestSection",
      equipment: "TestEquipment",
    },
    title: "Test Alarm",
    description: "Test Description",
    tag: "TEST_TAG",
    reason: "KEPT",
    lastUpdatedUser: "<EMAIL>",
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockPut.mockResolvedValue({ success: true });
  });

  it("should initialize with expected interface", async () => {
    const { useUpdateCalculationStatus } = await import(
      "./use-update-calculation-status"
    );
    const { result } = renderHook(() => useUpdateCalculationStatus());

    expect(result.current).toHaveProperty("updateCalculationStatus");
    expect(result.current).toHaveProperty("isSubmitting");
    expect(typeof result.current.updateCalculationStatus).toBe("function");
    expect(typeof result.current.isSubmitting).toBe("boolean");
    expect(result.current.isSubmitting).toBe(false);
  });

  it("should include timing information when isIncluded is true", async () => {
    const { useUpdateCalculationStatus } = await import(
      "./use-update-calculation-status"
    );
    const { result } = renderHook(() => useUpdateCalculationStatus());

    const formData: CalculationStatusFormData = {
      isIncluded: true,
      removalReason: null,
      comments: "Including alarm with timing",
    };

    await act(async () => {
      await result.current.updateCalculationStatus(formData, [mockAlarm]);
    });

    expect(mockPut).toHaveBeenCalledWith("/availability/alarms", {
      body: {
        id: "test-alarm-id",
        isIncluded: true,
        comments: "Including alarm with timing",
        startDateLocal: "2025-01-15T10:35:00.000Z", // Uses updated timing
        endDateLocal: "2025-01-15T11:50:00.000Z",
      },
    });
  });

  it("should exclude timing information when isIncluded is false", async () => {
    const { useUpdateCalculationStatus } = await import(
      "./use-update-calculation-status"
    );
    const { result } = renderHook(() => useUpdateCalculationStatus());

    const formData: CalculationStatusFormData = {
      isIncluded: false,
      removalReason: "planned-maintenance",
      comments: "Excluding alarm",
    };

    await act(async () => {
      await result.current.updateCalculationStatus(formData, [mockAlarm]);
    });

    expect(mockPut).toHaveBeenCalledWith("/availability/alarms", {
      body: {
        id: "test-alarm-id",
        isIncluded: false,
        comments: "Excluding alarm",
        excludeReason: "planned-maintenance",
      },
    });
  });

  it("should use original timing when updated timing is not available", async () => {
    const { useUpdateCalculationStatus } = await import(
      "./use-update-calculation-status"
    );
    const { result } = renderHook(() => useUpdateCalculationStatus());

    const alarmWithoutUpdatedTiming: FaultAlarm = {
      ...mockAlarm,
      timing: {
        startTime: "2025-01-15T09:00:00.000Z",
        endTime: "2025-01-15T10:00:00.000Z",
        duration: "3600000",
        updatedStartTime: undefined,
        updatedEndTime: undefined,
        updatedDuration: "3600000",
      },
    };

    const formData: CalculationStatusFormData = {
      isIncluded: true,
      removalReason: null,
      comments: "Using original timing",
    };

    await act(async () => {
      await result.current.updateCalculationStatus(formData, [
        alarmWithoutUpdatedTiming,
      ]);
    });

    expect(mockPut).toHaveBeenCalledWith("/availability/alarms", {
      body: {
        id: "test-alarm-id",
        isIncluded: true,
        comments: "Using original timing",
        startDateLocal: "2025-01-15T09:00:00.000Z", // Uses original timing
        endDateLocal: "2025-01-15T10:00:00.000Z",
      },
    });
  });

  it("should throw error when timing information is missing for included alarm", async () => {
    const { useUpdateCalculationStatus } = await import(
      "./use-update-calculation-status"
    );
    const { result } = renderHook(() => useUpdateCalculationStatus());

    const alarmWithoutTiming: FaultAlarm = {
      ...mockAlarm,
      timing: {
        startTime: "",
        endTime: undefined,
        duration: "0",
        updatedStartTime: undefined,
        updatedEndTime: undefined,
        updatedDuration: "0",
      },
    };

    const formData: CalculationStatusFormData = {
      isIncluded: true,
      removalReason: null,
      comments: "Should fail",
    };

    await act(async () => {
      await result.current.updateCalculationStatus(formData, [
        alarmWithoutTiming,
      ]);
    });

    // Should show error notification when timing is missing
    expect(mockError).toHaveBeenCalledWith(
      "Failed to update calculation status. Please try again.",
    );
  });
});
