import { useMemo, useState, useEffect } from "react";
import type { FaultAlarm } from "../types/types";
import { transformFaultData } from "../utils/cell-data-transforms";
import { createFaultTrackingColumns } from "../components/fault-tracking-columns";
import { useConfigSetting } from "../../../config/hooks/use-config";

function createCompositeKey(alarm: FaultAlarm): string {
  return `${alarm.splitId}_${alarm.id}_${alarm.faultId}`;
}

export function useFaultTrackingTable(apiData?: FaultAlarm[]) {
  // Get facility timezone for proper date formatting
  const { setting: timezoneConfig } = useConfigSetting("site-time-zone");
  const facilityTimezone =
    (timezoneConfig?.value as string) ?? "America/New_York";
  const [tableData, setTableData] = useState<FaultAlarm[]>([]);
  const [rawData, setRawData] = useState<FaultAlarm[]>([]);
  const [tableKey, setTableKey] = useState(0);
  const [rowSelection, setRowSelection] = useState<Record<string, boolean>>({});
  const [selectedCompositeKeys, setSelectedCompositeKeys] = useState<
    Set<string>
  >(new Set());

  useEffect(() => {
    if (apiData) {
      const transformedData = transformFaultData(apiData, facilityTimezone);
      setTableData(transformedData);
      setRawData(apiData);

      const newRowSelection: Record<string, boolean> = {};
      apiData.forEach((alarm) => {
        const compositeKey = createCompositeKey(alarm);
        if (selectedCompositeKeys.has(compositeKey)) {
          newRowSelection[compositeKey] = true;
        }
      });
      setRowSelection(newRowSelection);
    }
  }, [apiData, selectedCompositeKeys, facilityTimezone]);

  const selectedFaults = useMemo(() => {
    return rawData.filter((alarm) => {
      const compositeKey = createCompositeKey(alarm);
      return selectedCompositeKeys.has(compositeKey);
    });
  }, [selectedCompositeKeys, rawData]);

  const allSelectedHaveSameInclusionStatus = useMemo(() => {
    if (selectedFaults.length === 0) return false;
    const inclusionStatuses = selectedFaults.map(
      (fault) => fault.status === "KEPT",
    );
    return inclusionStatuses.every((status) => status === inclusionStatuses[0]);
  }, [selectedFaults]);

  const selectedFaultsInclusionState = useMemo(() => {
    if (selectedFaults.length === 0) return null; // No selection
    if (!allSelectedHaveSameInclusionStatus) return null; // Mixed state
    
    // All have same status, return the actual state
    return selectedFaults[0].status === "KEPT";
  }, [selectedFaults, allSelectedHaveSameInclusionStatus]);

  const columns = useMemo(() => createFaultTrackingColumns(), []);

  const getRowId = useMemo(() => createCompositeKey, []);

  const handleRefresh = () => {
    setTableKey((prev) => prev + 1);
  };
  const handleRowSelectionChange = (
    updaterOrValue:
      | Record<string, boolean>
      | ((old: Record<string, boolean>) => Record<string, boolean>),
  ) => {
    const newRowSelection =
      typeof updaterOrValue === "function"
        ? updaterOrValue(rowSelection)
        : updaterOrValue;
    setRowSelection(newRowSelection);

    const newSelectedKeys = new Set<string>();
    Object.entries(newRowSelection).forEach(([compositeKey, isSelected]) => {
      if (isSelected) {
        newSelectedKeys.add(compositeKey);
      }
    });
    setSelectedCompositeKeys(newSelectedKeys);
  };

  const clearSelection = () => {
    setRowSelection({});
    setSelectedCompositeKeys(new Set());
  };

  return {
    // Table data
    tableData,
    setTableData,
    tableKey,

    // Selection
    rowSelection,
    setRowSelection: handleRowSelectionChange,
    selectedFaults,
    allSelectedHaveSameInclusionStatus,
    selectedFaultsInclusionState,
    clearSelection,

    // Configuration
    columns,
    getRowId,

    // Actions
    handleRefresh,
  };
}
