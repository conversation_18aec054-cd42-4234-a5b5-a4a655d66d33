import { describe, it, expect } from "vitest";
import { renderHook, act } from "@testing-library/react";
import { useModal } from "./use-modal";

describe("useModal", () => {
  it("should initialize with default state (closed)", () => {
    const { result } = renderHook(() => useModal());

    expect(result.current.isOpen).toBe(false);
    expect(typeof result.current.toggle).toBe("function");
    expect(typeof result.current.open).toBe("function");
    expect(typeof result.current.close).toBe("function");
  });

  it("should initialize with custom initial state", () => {
    const { result } = renderHook(() => useModal(true));

    expect(result.current.isOpen).toBe(true);
  });

  it("should toggle modal state", () => {
    const { result } = renderHook(() => useModal());

    // Initially closed
    expect(result.current.isOpen).toBe(false);

    // Toggle to open
    act(() => {
      result.current.toggle();
    });
    expect(result.current.isOpen).toBe(true);

    // Toggle to close
    act(() => {
      result.current.toggle();
    });
    expect(result.current.isOpen).toBe(false);
  });

  it("should open modal", () => {
    const { result } = renderHook(() => useModal());

    // Initially closed
    expect(result.current.isOpen).toBe(false);

    // Open modal
    act(() => {
      result.current.open();
    });
    expect(result.current.isOpen).toBe(true);

    // Opening again should keep it open
    act(() => {
      result.current.open();
    });
    expect(result.current.isOpen).toBe(true);
  });

  it("should close modal", () => {
    const { result } = renderHook(() => useModal(true));

    // Initially open
    expect(result.current.isOpen).toBe(true);

    // Close modal
    act(() => {
      result.current.close();
    });
    expect(result.current.isOpen).toBe(false);

    // Closing again should keep it closed
    act(() => {
      result.current.close();
    });
    expect(result.current.isOpen).toBe(false);
  });

  it("should maintain function references between renders", () => {
    const { result, rerender } = renderHook(() => useModal());

    const firstRenderFunctions = {
      toggle: result.current.toggle,
      open: result.current.open,
      close: result.current.close,
    };

    // Trigger a re-render
    rerender();

    const secondRenderFunctions = {
      toggle: result.current.toggle,
      open: result.current.open,
      close: result.current.close,
    };

    // Functions should be stable across renders
    expect(firstRenderFunctions.toggle).toBe(secondRenderFunctions.toggle);
    expect(firstRenderFunctions.open).toBe(secondRenderFunctions.open);
    expect(firstRenderFunctions.close).toBe(secondRenderFunctions.close);
  });

  describe("complex interactions", () => {
    it("should handle multiple operations in sequence", () => {
      const { result } = renderHook(() => useModal());

      // Start closed
      expect(result.current.isOpen).toBe(false);

      // Open, then toggle (should close), then open again
      act(() => {
        result.current.open();
      });
      expect(result.current.isOpen).toBe(true);

      act(() => {
        result.current.toggle();
      });
      expect(result.current.isOpen).toBe(false);

      act(() => {
        result.current.open();
      });
      expect(result.current.isOpen).toBe(true);

      act(() => {
        result.current.close();
      });
      expect(result.current.isOpen).toBe(false);
    });
  });
});
