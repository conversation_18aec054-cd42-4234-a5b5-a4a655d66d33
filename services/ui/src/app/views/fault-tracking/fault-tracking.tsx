import { useTranslation } from "react-i18next";
import { ViewBar } from "../../components/view-bar/view-bar";
import { FaultTrackingTable } from "./components/fault-tracking-table";
import { useFaultTrackingData } from "./hooks/use-fault-tracking-data";
import { useFaultTrackingTable } from "./hooks/use-fault-tracking-table";
import { FullPageContainer } from "../../components/full-page-container/full-page-container";
import { useModal } from "./hooks/use-modal";
import { ManualEntryModal, ManualEntryButton } from "./components/manual-entry";
import { EditTimingsModal } from "./components/edit-timings/edit-timings-modal";
import { useFaultTrackingActions } from "./hooks/use-fault-tracking-actions";
import { CalculationStatusModal } from "./components/calculation-status/calculation-status-modal";

export default function FaultTrackingView() {
  const { t } = useTranslation();
  const manualEntryModal = useModal();
  const editTimingsModal = useModal();
  const calculationStatusModal = useModal();
  const faultData = useFaultTrackingData();
  const tableConfig = useFaultTrackingTable(faultData.data?.data);
  const { batchActions } = useFaultTrackingActions({
    toggleEditTimingsModal: editTimingsModal.toggle,
    toggleCalculationStatusModal: calculationStatusModal.toggle,
    selectedRowsCount: tableConfig.selectedFaults.length,
    allSelectedHaveSameInclusionStatus:
      tableConfig.allSelectedHaveSameInclusionStatus,
  });

  return (
    <div style={{ flex: 1, width: "100%" }}>
      <ViewBar title={t("faultTracking.title", "Fault Tracking")} />
      <FullPageContainer>
        <FaultTrackingTable
          tableKey={tableConfig.tableKey}
          columns={tableConfig.columns}
          data={tableConfig.tableData}
          getRowId={tableConfig.getRowId}
          rowCount={faultData.data?.metadata.totalResults ?? 0}
          isLoading={faultData.isLoading}
          isFetching={faultData.isFetching}
          error={faultData.error}
          pagination={faultData.pagination}
          setPagination={faultData.setPagination}
          sorting={faultData.sorting}
          setSorting={faultData.setSorting}
          setColumnFilters={faultData.setColumnFilters}
          setGlobalFilter={faultData.setGlobalFilter}
          rowSelection={tableConfig.rowSelection}
          onRowSelectionChange={tableConfig.setRowSelection}
          onClearSelection={tableConfig.clearSelection}
          onRefreshClick={() => {
            tableConfig.handleRefresh();
            faultData.refetch();
          }}
          actionBarItems={
            <ManualEntryButton
              toggleManualEntryModal={manualEntryModal.toggle}
            />
          }
          batchActions={batchActions}
        />
      </FullPageContainer>
      <ManualEntryModal
        isManualEntryModalOpen={manualEntryModal.isOpen}
        toggleManualEntryModal={manualEntryModal.toggle}
        onSuccess={() => {
          tableConfig.handleRefresh();
          faultData.refetch();
        }}
      />
      <EditTimingsModal
        isEditTimingsModalOpen={editTimingsModal.isOpen}
        toggleEditTimingsModal={editTimingsModal.toggle}
        selectedAlarm={tableConfig.selectedFaults[0] || null}
        onSuccess={() => {
          tableConfig.handleRefresh();
          faultData.refetch();
        }}
      />
      <CalculationStatusModal
        isCalculationStatusModalOpen={calculationStatusModal.isOpen}
        toggleCalculationStatusModal={calculationStatusModal.toggle}
        selectedAlarms={tableConfig.selectedFaults}
        isIncluded={tableConfig.selectedFaultsInclusionState}
        onSuccess={() => {
          tableConfig.handleRefresh();
          faultData.refetch();
        }}
      />
    </div>
  );
}
