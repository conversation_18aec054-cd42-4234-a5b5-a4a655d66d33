import { describe, it, expect, vi, beforeEach } from "vitest";
import { render, screen, fireEvent } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import "@testing-library/jest-dom/vitest";
import { EditTimingsModal } from "./edit-timings-modal";

// Mock the translation hook
vi.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (_key: string, fallback: string) => fallback,
  }),
}));

// Mock the config hook
vi.mock("../../../../config/hooks/use-config", () => ({
  useConfigSetting: vi.fn(),
}));

// Mock the notification hook
const mockSuccess = vi.fn();
vi.mock("../../../../components/toast/use-notification", () => ({
  useNotification: () => ({
    success: mockSuccess,
  }),
}));

// Mock the logger
vi.mock("../../../../utils/logger", () => ({
  Logger: vi.fn().mockImplementation(() => ({
    info: vi.fn(),
  })),
}));

// Mock the edit timings form hook
const mockFormHook = {
  formData: {
    startDate: new Date("2024-02-15"),
    endDate: new Date("2024-02-15"),
    startTime: "10:00",
    startPeriod: "AM" as const,
    endTime: "11:00",
    endPeriod: "AM" as const,
    startSeconds: "",
    endSeconds: "",
    comments: "Test comment",
  },
  originalData: null,
  reset: vi.fn(),
  resetToOriginal: vi.fn(),
  validate: vi.fn(),
  setErrors: vi.fn(),
  isValid: true,
  hasChanges: true,
  setField: vi.fn(),
  setFieldTouched: vi.fn(),
  calculateDuration: vi.fn(),
  touched: {},
  resetTrigger: 0,
  errors: {},
};

vi.mock("../../hooks/edit-timings/use-edit-timings-form", () => ({
  useEditTimingsForm: () => mockFormHook,
  getMinStartDate: () => new Date("2024-01-01"),
}));

// Mock the update alarm timings hook
const mockUpdateAlarmTimings = vi.fn();
const mockUpdateHook = {
  updateAlarmTimings: mockUpdateAlarmTimings,
  isSubmitting: false,
};

vi.mock("../../hooks/edit-timings/use-update-alarm-timings", () => ({
  useUpdateAlarmTimings: vi.fn(() => mockUpdateHook),
}));

import { useConfigSetting } from "../../../../config/hooks/use-config";
import { useUpdateAlarmTimings } from "../../hooks/edit-timings/use-update-alarm-timings";

describe("EditTimingsModal", () => {
  const mockToggleModal = vi.fn();

  const mockSelectedAlarm = {
    id: "test-alarm-123",
    splitId: "SPLIT-001",
    faultId: "FAULT-001",
    title: "Test Alarm",
    description: "Test alarm description",
    tag: "TAG001",
    status: "KEPT",
    reason: "Test reason",
    comments: "Test comment",
    location: {
      area: "Area A",
      section: "Section 1",
      equipment: "Equipment 1",
    },
    timing: {
      startTime: "2024-02-15T10:00:00.000Z",
      endTime: "2024-02-15T11:00:00.000Z",
      duration: "01:00:00",
      updatedStartTime: "2024-02-15T10:00:00.000Z",
      updatedEndTime: "2024-02-15T11:00:00.000Z",
      origStartTime: "2024-02-15T10:00:00.000Z",
      origEndTime: "2024-02-15T11:00:00.000Z",
      origDuration: "01:00:00",
    },
  };

  const defaultProps = {
    isEditTimingsModalOpen: true,
    toggleEditTimingsModal: mockToggleModal,
    selectedAlarm: mockSelectedAlarm,
  };

  beforeEach(() => {
    vi.clearAllMocks();

    // Setup default config setting mock
    vi.mocked(useConfigSetting).mockReturnValue({
      setting: {
        id: "site-time-zone",
        name: "Site Time Zone",
        value: "America/New_York",
        dataType: "string",
      },
      isLoading: false,
      error: null,
    });

    // Setup update alarm timings hook mock
    mockUpdateAlarmTimings.mockResolvedValue(true);
    mockUpdateHook.isSubmitting = false;
    vi.mocked(useUpdateAlarmTimings).mockReturnValue(mockUpdateHook);

    // Reset notification mock
    mockSuccess.mockClear();

    // Reset form hook mocks
    mockFormHook.validate.mockReturnValue({});
    mockFormHook.isValid = true;
    mockFormHook.hasChanges = true;
  });

  describe("rendering", () => {
    it("should render the modal when open", () => {
      render(<EditTimingsModal {...defaultProps} />);

      expect(screen.getByText("Edit Alarm Timings")).toBeInTheDocument();
      expect(
        screen.getByText(
          "Adjust the date range, start and end time, the duration will update automatically.",
        ),
      ).toBeInTheDocument();
    });

    it("should respect the open prop", () => {
      const { rerender } = render(
        <EditTimingsModal {...defaultProps} isEditTimingsModalOpen={false} />,
      );

      // First render with closed - modal may exist but should not be interactable
      // Let's just verify we can control the open state

      // Now render with open
      rerender(
        <EditTimingsModal {...defaultProps} isEditTimingsModalOpen={true} />,
      );

      // Should be able to find interactive elements when open
      expect(screen.getByText("Edit Alarm Timings")).toBeInTheDocument();
      expect(screen.getByText("Save")).toBeInTheDocument();
      expect(screen.getByText("Cancel")).toBeInTheDocument();
    });

    it("should render save and cancel buttons", () => {
      render(<EditTimingsModal {...defaultProps} />);

      expect(screen.getByText("Save")).toBeInTheDocument();
      expect(screen.getByText("Cancel")).toBeInTheDocument();
    });

    it("should enable save button when form is valid", () => {
      mockFormHook.isValid = true;
      render(<EditTimingsModal {...defaultProps} />);

      const saveButton = screen.getByText("Save");
      expect(saveButton).not.toBeDisabled();
    });

    it("should disable save button when form is invalid", () => {
      mockFormHook.isValid = false;
      render(<EditTimingsModal {...defaultProps} />);

      const saveButton = screen.getByText("Save");
      expect(saveButton).toBeDisabled();
    });

    it("should disable save button when no changes have been made", () => {
      mockFormHook.isValid = true;
      mockFormHook.hasChanges = false;
      render(<EditTimingsModal {...defaultProps} />);

      const saveButton = screen.getByText("Save");
      expect(saveButton).toBeDisabled();
    });
  });

  describe("form submission", () => {
    it("should handle successful form submission", async () => {
      const user = userEvent.setup();
      render(<EditTimingsModal {...defaultProps} />);

      const saveButton = screen.getByText("Save");
      await user.click(saveButton);

      expect(mockFormHook.validate).toHaveBeenCalled();
      expect(mockFormHook.setErrors).toHaveBeenCalledWith({});
      expect(mockUpdateAlarmTimings).toHaveBeenCalledWith(
        mockFormHook.formData,
        mockSelectedAlarm,
      );
    });

    it("should handle form validation errors", async () => {
      const user = userEvent.setup();
      const validationErrors = {
        startDate: "Start date is required",
        comments: "Comments are required",
      };

      mockFormHook.validate.mockReturnValue(validationErrors);
      render(<EditTimingsModal {...defaultProps} />);

      const saveButton = screen.getByText("Save");
      await user.click(saveButton);

      expect(mockFormHook.validate).toHaveBeenCalled();
      expect(mockFormHook.setErrors).toHaveBeenCalledWith(validationErrors);
      expect(mockUpdateAlarmTimings).not.toHaveBeenCalled();
    });

    it("should handle no selected alarm", async () => {
      const user = userEvent.setup();
      render(<EditTimingsModal {...defaultProps} selectedAlarm={null} />);

      const saveButton = screen.getByText("Save");
      await user.click(saveButton);

      expect(mockFormHook.validate).toHaveBeenCalled();
      expect(mockUpdateAlarmTimings).not.toHaveBeenCalled();
    });

    it("should clear errors on successful submission", async () => {
      const user = userEvent.setup();
      render(<EditTimingsModal {...defaultProps} />);

      const saveButton = screen.getByText("Save");
      await user.click(saveButton);

      expect(mockFormHook.setErrors).toHaveBeenCalledWith({});
    });

    it("should disable save button during submission", async () => {
      mockUpdateHook.isSubmitting = true;
      vi.mocked(useUpdateAlarmTimings).mockReturnValue(mockUpdateHook);

      render(<EditTimingsModal {...defaultProps} />);

      const saveButton = screen.getByText("Save");
      expect(saveButton).toBeDisabled();
    });
  });

  describe("modal interactions", () => {
    it("should handle cancel button click", async () => {
      const user = userEvent.setup();
      render(<EditTimingsModal {...defaultProps} />);

      const cancelButton = screen.getByText("Cancel");
      await user.click(cancelButton);

      expect(mockFormHook.setErrors).toHaveBeenCalledWith({});
      expect(mockToggleModal).toHaveBeenCalled();
    });

    it("should handle modal close (X button)", () => {
      render(<EditTimingsModal {...defaultProps} />);

      // Simulate close event (onRequestClose)
      const modal = screen.getByRole("dialog");
      fireEvent.keyDown(modal, { key: "Escape" });

      // The actual close behavior depends on Carbon's Modal implementation
      // We can verify our handler would be called
      expect(mockFormHook.setErrors).toHaveBeenCalledWith({});
      expect(mockToggleModal).toHaveBeenCalled();
    });
  });

  describe("configuration handling", () => {
    it("should pass timezone config to useEditTimingsForm", () => {
      vi.mocked(useConfigSetting).mockReturnValue({
        setting: {
          id: "site-time-zone",
          name: "Site Time Zone",
          value: "Europe/London",
          dataType: "string",
        },
        isLoading: false,
        error: null,
      });

      render(<EditTimingsModal {...defaultProps} />);

      // Verify that the useEditTimingsForm is called with the correct timezone
      // The timezone is passed to the form hook, which handles the timezone logic
      expect(useConfigSetting).toHaveBeenCalledWith("site-time-zone");
    });

    it("should use default timezone when config is not available", () => {
      vi.mocked(useConfigSetting).mockReturnValue({
        setting: undefined,
        isLoading: false,
        error: null,
      });

      render(<EditTimingsModal {...defaultProps} />);

      // Verify config is called, fallback timezone will be handled by the hook
      expect(useConfigSetting).toHaveBeenCalledWith("site-time-zone");
    });
  });

  describe("accessibility", () => {
    it("should have proper modal structure", () => {
      render(<EditTimingsModal {...defaultProps} />);

      const modal = screen.getByRole("dialog");
      expect(modal).toBeInTheDocument();
      expect(modal).toHaveAttribute("aria-modal", "true");
    });

    it("should have proper button roles", () => {
      render(<EditTimingsModal {...defaultProps} />);

      const saveButton = screen.getByRole("button", { name: "Save" });
      const cancelButton = screen.getByRole("button", { name: "Cancel" });

      expect(saveButton).toBeInTheDocument();
      expect(cancelButton).toBeInTheDocument();
    });

    it("should properly disable save button when invalid", () => {
      mockFormHook.isValid = false;
      render(<EditTimingsModal {...defaultProps} />);

      const saveButton = screen.getByRole("button", { name: "Save" });
      expect(saveButton).toBeDisabled();
    });
  });

  describe("form integration", () => {
    it("should call form validation on submit", async () => {
      const user = userEvent.setup();
      render(<EditTimingsModal {...defaultProps} />);

      const saveButton = screen.getByText("Save");
      await user.click(saveButton);

      expect(mockFormHook.validate).toHaveBeenCalledTimes(1);
    });

    it("should clear errors on cancel", async () => {
      const user = userEvent.setup();
      render(<EditTimingsModal {...defaultProps} />);

      const cancelButton = screen.getByText("Cancel");
      await user.click(cancelButton);

      expect(mockFormHook.setErrors).toHaveBeenCalledWith({});
    });

    it("should call update hook on successful submission", async () => {
      const user = userEvent.setup();
      render(<EditTimingsModal {...defaultProps} />);

      const saveButton = screen.getByText("Save");
      await user.click(saveButton);

      expect(mockUpdateAlarmTimings).toHaveBeenCalledWith(
        mockFormHook.formData,
        mockSelectedAlarm,
      );
    });

    it("should respect form validity state", () => {
      // Test valid form with changes
      mockFormHook.isValid = true;
      mockFormHook.hasChanges = true;
      const { rerender } = render(<EditTimingsModal {...defaultProps} />);

      let saveButton = screen.getByText("Save");
      expect(saveButton).not.toBeDisabled();

      // Test invalid form
      mockFormHook.isValid = false;
      rerender(<EditTimingsModal {...defaultProps} />);

      saveButton = screen.getByText("Save");
      expect(saveButton).toBeDisabled();

      // Test valid form but no changes
      mockFormHook.isValid = true;
      mockFormHook.hasChanges = false;
      rerender(<EditTimingsModal {...defaultProps} />);

      saveButton = screen.getByText("Save");
      expect(saveButton).toBeDisabled();
    });
  });

  describe("error handling", () => {
    it("should call validation before attempting update", async () => {
      const user = userEvent.setup();

      render(<EditTimingsModal {...defaultProps} />);

      const saveButton = screen.getByText("Save");
      await user.click(saveButton);

      // Validation should be called first
      expect(mockFormHook.validate).toHaveBeenCalled();
      // Update hook should be called after validation passes
      expect(mockUpdateAlarmTimings).toHaveBeenCalled();
    });
  });
});
