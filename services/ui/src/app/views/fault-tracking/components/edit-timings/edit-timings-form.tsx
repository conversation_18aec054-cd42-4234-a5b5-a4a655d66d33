import { Form, Stack } from "@carbon/react";
import { useEditTimingsForm } from "../../hooks/edit-timings/use-edit-timings-form";
import {
  TimePickerField,
  SecondsInputField,
  DateRangePicker,
  FormTextAreaField,
} from "../shared";
import styles from "./edit-timings-form.module.scss";
import { DurationDisplayField } from "../shared/duration-display-field";
import { OriginalValuesDisplay } from "./original-values-display";
import { useTranslation } from "react-i18next";

interface EditTimingsFormProps {
  formData: ReturnType<typeof useEditTimingsForm>["formData"];
  originalData: ReturnType<typeof useEditTimingsForm>["originalData"];
  setField: ReturnType<typeof useEditTimingsForm>["setField"];
  resetToOriginal: ReturnType<typeof useEditTimingsForm>["resetToOriginal"];
  errors: Partial<
    Record<keyof ReturnType<typeof useEditTimingsForm>["formData"], string>
  >;
  minStartDate: Date;
  validate: ReturnType<typeof useEditTimingsForm>["validate"];
  setErrors: (
    errors: Partial<
      Record<keyof ReturnType<typeof useEditTimingsForm>["formData"], string>
    >,
  ) => void;
  touched: Partial<
    Record<keyof ReturnType<typeof useEditTimingsForm>["formData"], boolean>
  >;
  setFieldTouched: (
    field: keyof ReturnType<typeof useEditTimingsForm>["formData"],
  ) => void;
  calculateDuration: ReturnType<typeof useEditTimingsForm>["calculateDuration"];
  resetTrigger?: number;
}

export function EditTimingsForm({
  formData,
  originalData,
  setField,
  resetToOriginal,
  errors,
  minStartDate,
  validate,
  touched,
  setFieldTouched,
  calculateDuration,
  resetTrigger = 0,
}: EditTimingsFormProps) {
  const handleBlur = (field: keyof typeof formData) => {
    setFieldTouched(field);
    validate();
  };

  const { t } = useTranslation();

  return (
    <Form>
      <Stack gap={"1rem"}>
        <Stack orientation="horizontal" className={styles.formRow}>
          <DateRangePicker
            startDate={formData.startDate}
            endDate={formData.endDate}
            onStartDateChange={(date) => setField("startDate", date)}
            onEndDateChange={(date) => setField("endDate", date)}
            minStartDate={minStartDate}
            resetTrigger={resetTrigger}
            errors={{
              startDate: errors.startDate,
              endDate: errors.endDate,
            }}
            touched={{
              startDate: touched.startDate,
              endDate: touched.endDate,
            }}
          />
        </Stack>
        <Stack orientation="horizontal" className={styles.formRow}>
          <Stack orientation="horizontal">
            <TimePickerField
              id="start-time-picker"
              label={t("faultTracking.startTime", "Start Time")}
              value={formData.startTime}
              period={formData.startPeriod}
              onTimeChange={(value) => setField("startTime", value)}
              onPeriodChange={(value) => setField("startPeriod", value)}
              error={errors.startTime}
              touched={touched.startTime}
              onTimeBlur={() => handleBlur("startTime")}
              onPeriodBlur={() => handleBlur("startPeriod")}
            />
            <SecondsInputField
              id="start-seconds"
              value={formData.startSeconds}
              onChange={(value) => setField("startSeconds", value)}
              error={errors.startSeconds}
              touched={touched.startSeconds}
              onBlur={() => handleBlur("startSeconds")}
              label={t(
                "faultTracking.secondsMilliseconds",
                "Seconds & Milliseconds",
              )}
            />
          </Stack>

          <Stack orientation="horizontal">
            <TimePickerField
              id="end-time-picker"
              label={t("faultTracking.endTime", "End Time")}
              value={formData.endTime}
              period={formData.endPeriod}
              onTimeChange={(value) => setField("endTime", value)}
              onPeriodChange={(value) => setField("endPeriod", value)}
              error={errors.endTime}
              touched={touched.endTime}
              onTimeBlur={() => handleBlur("endTime")}
              onPeriodBlur={() => handleBlur("endPeriod")}
            />
            <SecondsInputField
              id="end-seconds"
              value={formData.endSeconds}
              onChange={(value) => setField("endSeconds", value)}
              error={errors.endSeconds}
              touched={touched.endSeconds}
              onBlur={() => handleBlur("endSeconds")}
              label={t(
                "faultTracking.secondsMilliseconds",
                "Seconds & Milliseconds",
              )}
            />
          </Stack>
        </Stack>

        <DurationDisplayField calculateDuration={calculateDuration} />
        <OriginalValuesDisplay
          originalData={originalData}
          currentFormData={formData}
          onResetToOriginal={resetToOriginal}
        />
        <FormTextAreaField
          id="comments"
          fieldName="comments"
          labelKey="faultTracking.alarmComments"
          labelDefault={t("faultTracking.alarmComments", "Alarm Comments")}
          placeholderKey="faultTracking.enterComments"
          placeholderDefault={t(
            "faultTracking.enterComments",
            "Enter comments",
          )}
          value={formData.comments}
          onChange={(value) => setField("comments", value)}
          onBlur={() => handleBlur("comments")}
          error={errors.comments}
          touched={touched.comments}
        />
      </Stack>
    </Form>
  );
}
