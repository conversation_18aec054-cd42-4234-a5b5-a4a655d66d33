import { describe, it, expect, vi } from "vitest";
import { createFaultTrackingColumns } from "./fault-tracking-columns";
import type { FaultAlarm } from "../types/types";

// Mock the utilities and Carbon components
vi.mock("../utils/time-formatting", () => ({
  formatFaultTrackingDuration: vi.fn((duration) => `formatted-${duration}`),
}));

vi.mock("../utils/cell-data-transforms", () => ({
  getStatusColor: vi.fn((status) =>
    status.toLowerCase() === "included" ? "blue" : "outline",
  ),
}));

vi.mock("@carbon/react", () => ({
  Tag: vi.fn(({ children, title, type, size }) => (
    <span data-testid="tag" data-type={type} data-size={size} title={title}>
      {children}
    </span>
  )),
}));

describe("createFaultTrackingColumns", () => {
  const mockFaultData: FaultAlarm = {
    faultId: "FAULT-001",
    splitId: "SPLIT-001",
    title: "Test fault title",
    description: "Test fault description",
    tag: "TAG001",
    status: "Included",
    reason: "Equipment malfunction",
    id: "user123",
    comments:
      "This is a very long comment that should be truncated when displayed in the table cell",
    location: {
      area: "Area A",
      section: "Section 1",
      equipment: "Equipment 1",
    },
    timing: {
      startTime: "01/01/2024 10:00 AM",
      duration: "01:00:00",
      origStartTime: "01/01/2024 10:00 AM",
      origEndTime: "01/01/2024 11:00 AM",
      origDuration: "01:00:00",
      updatedStartTime: "01/01/2024 10:05 AM",
      updatedEndTime: "01/01/2024 11:05 AM",
      updatedDuration: "01:00:00",
    },
  };

  it("should create all expected columns", () => {
    const columns = createFaultTrackingColumns();

    // Verify we have a reasonable number of columns
    expect(columns.length).toBeGreaterThan(10);

    // Check that key columns are present
    const keyColumns = [
      "Section Area",
      "Section Name",
      "Equipment Name",
      "Alarm ID",
      "Removal Status",
      "Alarm Description",
      "Alarm Tag",
      "Alarm Start Time",
      "Alarm End Time",
      "Alarm Duration",
      "Removal Reason",
      "Updated Start Time",
      "Updated End Time",
      "Updated Duration",
    ];

    keyColumns.forEach((expectedHeader) => {
      const column = columns.find((col) => col.header === expectedHeader);
      expect(column).toBeDefined();
    });
  });

  it("should configure location columns correctly", () => {
    const columns = createFaultTrackingColumns();

    const areaColumn = columns.find((col) => col.header === "Section Area");
    const sectionColumn = columns.find((col) => col.header === "Section Name");
    const equipmentColumn = columns.find(
      (col) => col.header === "Equipment Name",
    );

    expect(areaColumn?.accessorKey).toBe("location.area");
    expect(sectionColumn?.accessorKey).toBe("location.section");
    expect(equipmentColumn?.accessorKey).toBe("location.equipment");
  });

  it("should render status column with Tag component and correct color", () => {
    const columns = createFaultTrackingColumns();
    const statusColumn = columns.find((col) => col.header === "Removal Status");

    expect(statusColumn).toBeDefined();
    expect(statusColumn?.cell).toBeDefined();

    if (statusColumn?.cell && typeof statusColumn.cell === "function") {
      const mockInfo = {
        getValue: () => "Included",
        row: { original: mockFaultData },
      } as any;

      const result = statusColumn.cell(mockInfo);
      expect(result).toBeDefined();
    }
  });

  it("should have no non-sortable columns (except where explicitly disabled)", () => {
    const columns = createFaultTrackingColumns();

    // Currently no columns have sorting explicitly disabled
    // This test ensures we maintain this state
    const nonSortableColumns = columns.filter(
      (col) => col.enableSorting === false,
    );
    expect(nonSortableColumns).toHaveLength(0);
  });

  it("should format duration columns using formatFaultTrackingDuration", () => {
    const columns = createFaultTrackingColumns();

    const origDurationColumn = columns.find(
      (col) => col.header === "Alarm Duration",
    );
    const updatedDurationColumn = columns.find(
      (col) => col.header === "Updated Duration",
    );

    expect(origDurationColumn?.cell).toBeDefined();
    expect(updatedDurationColumn?.cell).toBeDefined();

    if (
      origDurationColumn?.cell &&
      typeof origDurationColumn.cell === "function"
    ) {
      const mockInfo = {
        getValue: () => "01:30:00",
        row: { original: mockFaultData },
      } as any;

      const result = origDurationColumn.cell(mockInfo);
      expect(result).toBe("formatted-01:30:00");
    }
  });

  it("should configure timing columns to access nested properties", () => {
    const columns = createFaultTrackingColumns();

    const timingColumns = [
      { header: "Alarm Start Time", accessor: "timing.origStartTime" },
      { header: "Alarm End Time", accessor: "timing.origEndTime" },
      { header: "Alarm Duration", accessor: "timing.origDuration" },
      { header: "Updated Start Time", accessor: "timing.updatedStartTime" },
      { header: "Updated End Time", accessor: "timing.updatedEndTime" },
      { header: "Updated Duration", accessor: "timing.updatedDuration" },
    ];

    timingColumns.forEach(({ header, accessor }) => {
      const column = columns.find((col) => col.header === header);
      expect(column?.accessorKey).toBe(accessor);
    });
  });

  it("should configure comments column with proper truncation styling", () => {
    const columns = createFaultTrackingColumns();
    const commentsColumn = columns.find((col) => col.header === "Comments");

    if (commentsColumn) {
      // Comments column should be sortable (enableSorting is undefined, which means true by default)
      expect(commentsColumn.enableSorting).toBeUndefined();
      expect(commentsColumn.cell).toBeDefined();

      if (commentsColumn.cell && typeof commentsColumn.cell === "function") {
        const mockInfo = {
          getValue: () => "This is a long comment",
          row: { original: mockFaultData },
        } as any;

        const result = commentsColumn.cell(mockInfo);
        expect(result).toBeDefined();

        // Check if it's a React element with the expected structure
        if (
          typeof result === "object" &&
          result !== null &&
          "props" in result
        ) {
          const props = (result as any).props;
          expect(props.title).toBe("This is a long comment");
          expect(props.style).toEqual({
            maxWidth: "200px",
            overflow: "hidden",
            textOverflow: "ellipsis",
            whiteSpace: "nowrap",
            display: "block",
          });
        }
      }
    } else {
      // If no comments column found, just ensure we have some columns
      expect(columns.length).toBeGreaterThan(0);
    }
  });

  it("should access simple properties correctly", () => {
    const columns = createFaultTrackingColumns();

    // Test known simple columns that should exist
    const alarmIdColumn = columns.find((col) => col.header === "Alarm ID");
    if (alarmIdColumn) {
      expect(alarmIdColumn.accessorKey).toBe("faultId");
    }

    const descriptionColumn = columns.find(
      (col) => col.header === "Alarm Description",
    );
    if (descriptionColumn) {
      expect(descriptionColumn.accessorKey).toBe("description");
    }

    const tagColumn = columns.find((col) => col.header === "Alarm Tag");
    if (tagColumn) {
      expect(tagColumn.accessorKey).toBe("tag");
    }

    // Verify we have at least these basic columns
    expect(columns.length).toBeGreaterThan(3);
  });

  it("should return consistent column structure", () => {
    const columns = createFaultTrackingColumns();

    // Verify all columns have the required structure
    columns.forEach((column) => {
      expect(column).toHaveProperty("header");
      expect(column.header).toBeDefined();
      expect(typeof column.header).toBe("string");
      expect(column.header!.length).toBeGreaterThan(0);
    });

    // Verify no undefined or null columns
    expect(columns.every((col) => col !== null && col !== undefined)).toBe(
      true,
    );
  });

  it("should handle status column with different status values", () => {
    const columns = createFaultTrackingColumns();
    const statusColumn = columns.find((col) => col.header === "Removal Status");

    if (statusColumn?.cell && typeof statusColumn.cell === "function") {
      // Test "Included" status
      const includedInfo = {
        getValue: () => "Included",
        row: { original: mockFaultData },
      } as any;

      const includedResult = statusColumn.cell(includedInfo);
      expect(includedResult).toBeDefined();

      // Test "Excluded" status
      const excludedInfo = {
        getValue: () => "Excluded",
        row: { original: mockFaultData },
      } as any;

      const excludedResult = statusColumn.cell(excludedInfo);
      expect(excludedResult).toBeDefined();
    }
  });

  it("should return array of column definitions", () => {
    const columns = createFaultTrackingColumns();

    expect(Array.isArray(columns)).toBe(true);
    expect(columns.length).toBeGreaterThan(0);

    // Each column should have at least a header
    columns.forEach((column) => {
      expect(column).toHaveProperty("header");
      expect(typeof column.header).toBe("string");
    });
  });
});
