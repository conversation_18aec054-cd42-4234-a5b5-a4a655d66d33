import { Form, Stack } from "@carbon/react";
import { useRef } from "react";
import { useManualEntryForm } from "../../hooks/manual-entry/use-manual-entry-form";
import {
  TimePickerField,
  SecondsInputField,
  DateRangePicker,
  FormTextAreaField,
} from "../shared";
import styles from "./manual-entry-form.module.scss";
import { IncludedDefaultToggle } from "./included-default-toggle";
import { DurationDisplayField } from "../shared/duration-display-field";
import {
  SectionEquipmentSelector,
  SectionEquipmentSelectorRef,
} from "./section-equipment-selector";
import { useTranslation } from "react-i18next";

interface ManualEntryFormProps {
  formData: ReturnType<typeof useManualEntryForm>["formData"];
  setField: ReturnType<typeof useManualEntryForm>["setField"];
  errors: Partial<
    Record<keyof ReturnType<typeof useManualEntryForm>["formData"], string>
  >;
  minStartDate: Date;
  validate: ReturnType<typeof useManualEntryForm>["validate"];
  setErrors: (
    errors: Partial<
      Record<keyof ReturnType<typeof useManualEntryForm>["formData"], string>
    >,
  ) => void;
  touched: Partial<
    Record<keyof ReturnType<typeof useManualEntryForm>["formData"], boolean>
  >;
  setFieldTouched: (
    field: keyof ReturnType<typeof useManualEntryForm>["formData"],
  ) => void;
  calculateDuration: ReturnType<typeof useManualEntryForm>["calculateDuration"];
  resetTrigger?: number;
}

export function ManualEntryForm({
  formData,
  setField,
  errors,
  minStartDate,
  validate,
  touched,
  setFieldTouched,
  calculateDuration,
  resetTrigger = 0,
}: ManualEntryFormProps) {
  const selectorRef = useRef<SectionEquipmentSelectorRef>(null);
  const handleBlur = (field: keyof typeof formData) => {
    setFieldTouched(field);
    validate();
  };

  const { t } = useTranslation();

  return (
    <Form>
      <Stack gap={"1rem"}>
        <IncludedDefaultToggle />
        <Stack orientation="horizontal" className={styles.formRow}>
          <DateRangePicker
            startDate={formData.startDate}
            endDate={formData.endDate}
            onStartDateChange={(date) => setField("startDate", date)}
            onEndDateChange={(date) => setField("endDate", date)}
            minStartDate={minStartDate}
            resetTrigger={resetTrigger}
            errors={{
              startDate: errors.startDate,
              endDate: errors.endDate,
            }}
            touched={{
              startDate: touched.startDate,
              endDate: touched.endDate,
            }}
          />
        </Stack>
        <div className={styles.formRow}>
          <div className={styles.timeSection}>
            <TimePickerField
              id="start-time-picker"
              label={t("faultTracking.startTime", "Start Time")}
              value={formData.startTime}
              period={formData.startPeriod}
              onTimeChange={(value) => setField("startTime", value)}
              onPeriodChange={(value) => setField("startPeriod", value)}
              error={errors.startTime}
              touched={touched.startTime}
              onTimeBlur={() => handleBlur("startTime")}
              onPeriodBlur={() => handleBlur("startPeriod")}
              selectedDate={formData.startDate}
            />
            <SecondsInputField
              id="start-seconds"
              value={formData.startSeconds}
              onChange={(value) => setField("startSeconds", value)}
              error={errors.startSeconds}
              touched={touched.startSeconds}
              onBlur={() => handleBlur("startSeconds")}
              label={t(
                "faultTracking.secondsMilliseconds",
                "Seconds & Milliseconds",
              )}
            />
          </div>

          <div className={styles.timeSection}>
            <TimePickerField
              id="end-time-picker"
              label={t("faultTracking.endTime", "End Time")}
              value={formData.endTime}
              period={formData.endPeriod}
              onTimeChange={(value) => setField("endTime", value)}
              onPeriodChange={(value) => setField("endPeriod", value)}
              error={errors.endTime}
              touched={touched.endTime}
              onTimeBlur={() => handleBlur("endTime")}
              onPeriodBlur={() => handleBlur("endPeriod")}
              selectedDate={formData.endDate}
            />
            <SecondsInputField
              id="end-seconds"
              value={formData.endSeconds}
              onChange={(value) => setField("endSeconds", value)}
              error={errors.endSeconds}
              touched={touched.endSeconds}
              onBlur={() => handleBlur("endSeconds")}
              label={t(
                "faultTracking.secondsMilliseconds",
                "Seconds & Milliseconds",
              )}
            />
          </div>
        </div>

        <DurationDisplayField calculateDuration={calculateDuration} />
        <Stack orientation="horizontal" className={styles.formRow}>
          <SectionEquipmentSelector
            ref={selectorRef}
            onSectionChange={(section) => {
              setField("section", section);
            }}
            onEquipmentChange={(equipment) => {
              setField("equipment", equipment);
            }}
            onSectionBlur={() => setFieldTouched("section")}
            onEquipmentBlur={() => setFieldTouched("equipment")}
            sectionError={errors.section}
            equipmentError={errors.equipment}
            sectionTouched={touched.section}
            equipmentTouched={touched.equipment}
            resetTrigger={resetTrigger}
          />
        </Stack>
        <FormTextAreaField
          id="description"
          fieldName="description"
          labelKey="faultTracking.alarmDescription"
          labelDefault="Alarm Description"
          placeholderKey="faultTracking.enterDescription"
          placeholderDefault="Enter description"
          value={formData.description}
          onChange={(value) => setField("description", value)}
          onBlur={() => handleBlur("description")}
          error={errors.description}
          touched={touched.description}
        />

        <FormTextAreaField
          id="comments"
          fieldName="comments"
          labelKey="faultTracking.alarmComments"
          labelDefault="Alarm Comments"
          placeholderKey="faultTracking.enterComments"
          placeholderDefault="Enter comments"
          value={formData.comments}
          onChange={(value) => setField("comments", value)}
          onBlur={() => handleBlur("comments")}
          error={errors.comments}
          touched={touched.comments}
        />
      </Stack>
    </Form>
  );
}
