import { describe, it, expect, vi, beforeEach } from "vitest";
import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import "@testing-library/jest-dom";
import { CalculationStatusModal } from "./calculation-status-modal";
import type { FaultAlarm } from "../../types/types";

// Mock react-i18next
const mockT = vi.fn((_key: string, defaultValue: string) => defaultValue);
vi.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: mockT,
  }),
}));

// Mock Carbon Modal
vi.mock("@carbon/react", () => ({
  Modal: ({
    open,
    onRequestClose,
    onRequestSubmit,
    onSecondarySubmit,
    primaryButtonText,
    secondaryButtonText,
    modalHeading,
    modalLabel,
    primaryButtonDisabled,
    children,
    ...props
  }: any) => {
    if (!open) return null;

    return (
      <div data-testid="modal" {...props}>
        <div data-testid="modal-label">{modalLabel}</div>
        <h2 data-testid="modal-heading">{modalHeading}</h2>
        <div data-testid="modal-content">{children}</div>
        <div data-testid="modal-actions">
          <button data-testid="secondary-button" onClick={onSecondarySubmit}>
            {secondaryButtonText}
          </button>
          <button
            data-testid="primary-button"
            onClick={onRequestSubmit}
            disabled={primaryButtonDisabled}
          >
            {primaryButtonText}
          </button>
        </div>
        <button data-testid="close-button" onClick={onRequestClose}>
          Close
        </button>
      </div>
    );
  },
}));

// Mock the child components
vi.mock("./selected-alarms-label", () => ({
  SelectedAlarmsLabel: ({ selectedAlarms }: any) => (
    <div data-testid="selected-alarms-label">
      Selected {selectedAlarms.length} alarm(s)
    </div>
  ),
}));

vi.mock("./calculation-status-form", () => ({
  CalculationStatusForm: ({
    isIncluded,
    selectedAlarms,
    onFormChange,
  }: any) => {
    // Simulate form changes for testing
    const handleChange = () => {
      onFormChange(true, {
        formData: {
          isIncluded,
          removalReason: isIncluded ? null : "customer-responsibility",
          comments: "Test comment",
        },
        isValid: true,
      });
    };

    return (
      <div data-testid="calculation-status-form">
        <div data-testid="form-inclusion-state">
          {isIncluded ? "Included" : "Excluded"}
        </div>
        <div data-testid="selected-alarms-count">
          {selectedAlarms ? selectedAlarms.length : 0} alarm(s)
        </div>
        <button data-testid="trigger-form-change" onClick={handleChange}>
          Trigger Form Change
        </button>
      </div>
    );
  },
}));

// Mock useUpdateCalculationStatus hook
const mockUpdateCalculationStatus = vi.fn().mockResolvedValue(true);
vi.mock("../../hooks/calculation-status/use-update-calculation-status", () => ({
  useUpdateCalculationStatus: vi.fn(() => ({
    updateCalculationStatus: mockUpdateCalculationStatus,
    isSubmitting: false,
  })),
}));

describe("CalculationStatusModal", () => {
  const mockToggleCalculationStatusModal = vi.fn();

  const mockSelectedAlarms: FaultAlarm[] = [
    {
      faultId: "alarm-1",
      status: "KEPT",
    } as FaultAlarm,
    {
      faultId: "alarm-2",
      status: "KEPT",
    } as FaultAlarm,
  ];

  const defaultProps = {
    isCalculationStatusModalOpen: true,
    toggleCalculationStatusModal: mockToggleCalculationStatusModal,
    selectedAlarms: mockSelectedAlarms,
    isIncluded: true as boolean | null,
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockUpdateCalculationStatus.mockClear();
  });

  it("does not render when modal is closed", () => {
    render(
      <CalculationStatusModal
        {...defaultProps}
        isCalculationStatusModalOpen={false}
      />,
    );

    expect(screen.queryByTestId("modal")).not.toBeInTheDocument();
  });

  it("renders modal when open", () => {
    render(<CalculationStatusModal {...defaultProps} />);

    expect(screen.getByTestId("modal")).toBeInTheDocument();
    expect(screen.getByTestId("modal-heading")).toHaveTextContent(
      "Update Calculation Status",
    );
    expect(screen.getByTestId("selected-alarms-label")).toBeInTheDocument();
  });

  it("displays selected alarms label", () => {
    render(<CalculationStatusModal {...defaultProps} />);

    const label = screen.getByTestId("selected-alarms-label");
    expect(label).toHaveTextContent("Selected 2 alarm(s)");
  });

  it("renders form when alarms are selected and inclusion state is defined", () => {
    render(<CalculationStatusModal {...defaultProps} />);

    expect(screen.getByTestId("calculation-status-form")).toBeInTheDocument();
    expect(screen.getByTestId("form-inclusion-state")).toHaveTextContent(
      "Included",
    );
  });

  it("renders form for excluded state", () => {
    render(<CalculationStatusModal {...defaultProps} isIncluded={false} />);

    expect(screen.getByTestId("calculation-status-form")).toBeInTheDocument();
    expect(screen.getByTestId("form-inclusion-state")).toHaveTextContent(
      "Excluded",
    );
  });

  it("shows no alarms message when no alarms selected", () => {
    render(<CalculationStatusModal {...defaultProps} selectedAlarms={[]} />);

    expect(screen.getByText("No alarms selected")).toBeInTheDocument();
    expect(
      screen.queryByTestId("calculation-status-form"),
    ).not.toBeInTheDocument();
  });

  it("shows mixed selection message when inclusion state is null", () => {
    render(<CalculationStatusModal {...defaultProps} isIncluded={null} />);

    expect(
      screen.getByText(
        "Mixed selection state - please select alarms with the same inclusion status",
      ),
    ).toBeInTheDocument();
    expect(
      screen.queryByTestId("calculation-status-form"),
    ).not.toBeInTheDocument();
  });

  it("has Save and Cancel buttons", () => {
    render(<CalculationStatusModal {...defaultProps} />);

    expect(screen.getByTestId("primary-button")).toHaveTextContent("Save");
    expect(screen.getByTestId("secondary-button")).toHaveTextContent("Cancel");
  });

  it("calls toggleCalculationStatusModal when Cancel button is clicked", async () => {
    render(<CalculationStatusModal {...defaultProps} />);

    const cancelButton = screen.getByTestId("secondary-button");
    await userEvent.click(cancelButton);

    expect(mockToggleCalculationStatusModal).toHaveBeenCalledTimes(1);
  });

  it("calls toggleCalculationStatusModal when close button is clicked", async () => {
    render(<CalculationStatusModal {...defaultProps} />);

    const closeButton = screen.getByTestId("close-button");
    await userEvent.click(closeButton);

    expect(mockToggleCalculationStatusModal).toHaveBeenCalledTimes(1);
  });

  it("disables Save button initially when no form changes", () => {
    render(<CalculationStatusModal {...defaultProps} />);

    const saveButton = screen.getByTestId("primary-button");
    expect(saveButton).toBeDisabled();
  });

  it("disables Save button when no alarms selected", () => {
    render(<CalculationStatusModal {...defaultProps} selectedAlarms={[]} />);

    const saveButton = screen.getByTestId("primary-button");
    expect(saveButton).toBeDisabled();
  });

  it("disables Save button when inclusion state is null", () => {
    render(<CalculationStatusModal {...defaultProps} isIncluded={null} />);

    const saveButton = screen.getByTestId("primary-button");
    expect(saveButton).toBeDisabled();
  });

  it("enables Save button when form has valid changes", async () => {
    render(<CalculationStatusModal {...defaultProps} />);

    const saveButton = screen.getByTestId("primary-button");
    expect(saveButton).toBeDisabled();

    // Trigger form change
    const triggerButton = screen.getByTestId("trigger-form-change");
    await userEvent.click(triggerButton);

    expect(saveButton).toBeEnabled();
  });

  it("calls API update when Save button is clicked", async () => {
    render(<CalculationStatusModal {...defaultProps} />);

    // Enable the save button by triggering form changes
    const triggerButton = screen.getByTestId("trigger-form-change");
    await userEvent.click(triggerButton);

    const saveButton = screen.getByTestId("primary-button");
    await userEvent.click(saveButton);

    // Basic test that the API function exists and can be called
    // (More detailed testing would be in the hook's own test file)
    expect(mockUpdateCalculationStatus).toHaveBeenCalled();
  });

  it("does not call API when Save button is clicked without form changes", async () => {
    render(<CalculationStatusModal {...defaultProps} />);

    // Try to click save button without making any changes
    // Note: The button should be disabled, but this tests the handleSubmit logic as well
    const saveButton = screen.getByTestId("primary-button");

    // Button should be disabled
    expect(saveButton).toBeDisabled();

    // Even if we could somehow click it, the API should not be called
    // (This is testing the handleSubmit function's validation)
    expect(mockUpdateCalculationStatus).not.toHaveBeenCalled();
  });

  it("updates form state when form changes", async () => {
    render(<CalculationStatusModal {...defaultProps} />);

    // Initially Save button should be disabled
    const saveButton = screen.getByTestId("primary-button");
    expect(saveButton).toBeDisabled();

    // Trigger form change
    const triggerButton = screen.getByTestId("trigger-form-change");
    await userEvent.click(triggerButton);

    // Now Save button should be enabled
    expect(saveButton).toBeEnabled();
  });

  it("renders form only when modal is open", () => {
    const { rerender } = render(
      <CalculationStatusModal
        {...defaultProps}
        isCalculationStatusModalOpen={false}
      />,
    );

    expect(
      screen.queryByTestId("calculation-status-form"),
    ).not.toBeInTheDocument();

    rerender(<CalculationStatusModal {...defaultProps} />);

    expect(screen.getByTestId("calculation-status-form")).toBeInTheDocument();
  });

  it("translates button labels correctly", () => {
    render(<CalculationStatusModal {...defaultProps} />);

    expect(mockT).toHaveBeenCalledWith("faultTracking.save", "Save");
    expect(mockT).toHaveBeenCalledWith("faultTracking.cancel", "Cancel");
    expect(mockT).toHaveBeenCalledWith(
      "faultTracking.updateCalculationStatus",
      "Update Calculation Status",
    );
  });
});
