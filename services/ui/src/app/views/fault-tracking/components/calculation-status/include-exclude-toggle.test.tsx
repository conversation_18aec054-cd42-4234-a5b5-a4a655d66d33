import { describe, it, expect, vi, beforeEach } from "vitest";
import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import "@testing-library/jest-dom";
import { IncludeExcludeToggle } from "./include-exclude-toggle";

// Mock react-i18next
const mockT = vi.fn((_key: string, defaultValue: string) => defaultValue);
vi.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: mockT,
  }),
}));

// Mock Carbon Toggle
vi.mock("@carbon/react", () => ({
  Toggle: ({
    id,
    labelA,
    labelB,
    labelText,
    toggled,
    onToggle,
    ...props
  }: any) => (
    <div data-testid="toggle-container" {...props}>
      <label htmlFor={id} data-testid="toggle-label">
        {labelText}
      </label>
      <div data-testid="toggle-labels">
        <span data-testid="label-a">{labelA}</span>
        <span data-testid="label-b">{labelB}</span>
      </div>
      <input
        id={id}
        data-testid="toggle-input"
        type="checkbox"
        checked={toggled}
        onChange={(e) => onToggle(e.target.checked)}
      />
      <div data-testid="toggle-state">{toggled ? "On" : "Off"}</div>
    </div>
  ),
}));

describe("IncludeExcludeToggle", () => {
  const mockOnChange = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("renders toggle with correct labels", () => {
    render(<IncludeExcludeToggle isIncluded={true} onChange={mockOnChange} />);

    expect(screen.getByTestId("toggle-container")).toBeInTheDocument();
    expect(screen.getByTestId("toggle-label")).toHaveTextContent(
      "System Availability Calculation",
    );
    expect(screen.getByTestId("label-a")).toHaveTextContent("Excluded");
    expect(screen.getByTestId("label-b")).toHaveTextContent("Included");
  });

  it("shows toggled state when isIncluded is true", () => {
    render(<IncludeExcludeToggle isIncluded={true} onChange={mockOnChange} />);

    const toggleInput = screen.getByTestId("toggle-input");
    expect(toggleInput).toBeChecked();
    expect(screen.getByTestId("toggle-state")).toHaveTextContent("On");
  });

  it("shows untoggled state when isIncluded is false", () => {
    render(<IncludeExcludeToggle isIncluded={false} onChange={mockOnChange} />);

    const toggleInput = screen.getByTestId("toggle-input");
    expect(toggleInput).not.toBeChecked();
    expect(screen.getByTestId("toggle-state")).toHaveTextContent("Off");
  });

  it("calls onChange with true when toggled from false to true", async () => {
    render(<IncludeExcludeToggle isIncluded={false} onChange={mockOnChange} />);

    const toggleInput = screen.getByTestId("toggle-input");
    await userEvent.click(toggleInput);

    expect(mockOnChange).toHaveBeenCalledWith(true);
    expect(mockOnChange).toHaveBeenCalledTimes(1);
  });

  it("calls onChange with false when toggled from true to false", async () => {
    render(<IncludeExcludeToggle isIncluded={true} onChange={mockOnChange} />);

    const toggleInput = screen.getByTestId("toggle-input");
    await userEvent.click(toggleInput);

    expect(mockOnChange).toHaveBeenCalledWith(false);
    expect(mockOnChange).toHaveBeenCalledTimes(1);
  });

  it("works without onChange callback", async () => {
    // Should not throw an error when onChange is not provided
    expect(() => {
      render(<IncludeExcludeToggle isIncluded={true} />);
    }).not.toThrow();

    const toggleInput = screen.getByTestId("toggle-input");

    // Should not throw when clicked
    await expect(userEvent.click(toggleInput)).resolves.not.toThrow();
  });

  it("works with undefined onChange callback", async () => {
    render(<IncludeExcludeToggle isIncluded={true} onChange={undefined} />);

    const toggleInput = screen.getByTestId("toggle-input");

    // Should not throw when clicked
    await expect(userEvent.click(toggleInput)).resolves.not.toThrow();
  });

  it("uses correct translation keys", () => {
    render(<IncludeExcludeToggle isIncluded={true} onChange={mockOnChange} />);

    expect(mockT).toHaveBeenCalledWith("faultTracking.included", "Included");
    expect(mockT).toHaveBeenCalledWith("faultTracking.excluded", "Excluded");
    expect(mockT).toHaveBeenCalledWith(
      "faultTracking.systemAvailabilityCalculation",
      "System Availability Calculation",
    );
  });

  it("has correct toggle id", () => {
    render(<IncludeExcludeToggle isIncluded={true} onChange={mockOnChange} />);

    const toggleInput = screen.getByTestId("toggle-input");
    expect(toggleInput).toHaveAttribute("id", "include-exclude-toggle");
  });

  it("maintains toggle state correctly across multiple interactions", async () => {
    const { rerender } = render(
      <IncludeExcludeToggle isIncluded={false} onChange={mockOnChange} />,
    );

    const toggleInput = screen.getByTestId("toggle-input");

    // Initial state
    expect(toggleInput).not.toBeChecked();

    // Click to toggle
    await userEvent.click(toggleInput);
    expect(mockOnChange).toHaveBeenCalledWith(true);

    // Simulate parent component updating the prop
    rerender(
      <IncludeExcludeToggle isIncluded={true} onChange={mockOnChange} />,
    );

    // Verify new state
    expect(toggleInput).toBeChecked();

    // Click again
    await userEvent.click(toggleInput);
    expect(mockOnChange).toHaveBeenCalledWith(false);
    expect(mockOnChange).toHaveBeenCalledTimes(2);
  });

  it("passes through additional props to Toggle component", () => {
    render(<IncludeExcludeToggle isIncluded={true} onChange={mockOnChange} />);

    // The mock Toggle component should receive the props
    expect(screen.getByTestId("toggle-container")).toBeInTheDocument();
  });
});
