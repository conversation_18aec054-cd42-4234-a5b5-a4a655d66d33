import React, { useEffect } from "react";
import { Form, Stack } from "@carbon/react";
import { IncludeExcludeToggle } from "./include-exclude-toggle";
import {
  useCalculationStatusForm,
  type CalculationStatusFormData,
} from "../../hooks/calculation-status/use-calculation-status-form";
import { RemovalReasonRadioGroup } from "./removal-reason-radio-group";
import { FormTextAreaField } from "../shared";
import styles from "./calculation-status-form.module.scss";
import { useTranslation } from "react-i18next";

interface FormChangeData {
  formData: CalculationStatusFormData | null;
  isValid: boolean;
}

export function CalculationStatusForm({
  isIncluded,
  onFormChange,
}: {
  isIncluded: boolean;
  onFormChange?: (hasChanges: boolean, formData: FormChangeData) => void;
}) {
  const { t } = useTranslation();
  const {
    formData,
    hasChanges,
    updateIsIncluded,
    updateRemovalReason,
    updateComments,
    handleCommentsBlur,
    isValid,
    fieldErrors,
  } = useCalculationStatusForm(isIncluded);

  useEffect(() => {
    if (onFormChange) {
      onFormChange(hasChanges, { formData, isValid });
    }
  }, [hasChanges, formData, isValid, onFormChange]);

  if (!formData) return null;

  return (
    <div className={styles.formContainer}>
      <Form>
        <Stack gap={"1rem"}>
          <IncludeExcludeToggle
            isIncluded={formData.isIncluded}
            onChange={updateIsIncluded}
          />
          <div
            className={`${styles.removalReasonContainerTransition} ${formData.isIncluded ? styles.hidden : ""}`}
          >
            <RemovalReasonRadioGroup
              value={formData.removalReason}
              onChange={updateRemovalReason}
              hasError={fieldErrors.removalReason}
            />
          </div>
          <FormTextAreaField
            id="calculation-status-comment"
            labelKey="faultTracking.comments"
            labelDefault="Comments"
            fieldName="calculation-status-comment"
            placeholderKey="faultTracking.enterComments"
            placeholderDefault="Enter comments"
            value={formData.comments}
            onChange={updateComments}
            onBlur={handleCommentsBlur}
            required
            touched={fieldErrors.comments}
            error={
              fieldErrors.comments
                ? t(
                    "faultTracking.validation.commentsRequired",
                    "Comments are required",
                  )
                : undefined
            }
          />
        </Stack>
      </Form>
    </div>
  );
}
