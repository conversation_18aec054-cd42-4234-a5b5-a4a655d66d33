import { describe, it, expect, vi, beforeEach } from "vitest";
import { render, screen } from "@testing-library/react";
import "@testing-library/jest-dom";
import { SelectedAlarmsLabel } from "./selected-alarms-label";
import type { FaultAlarm } from "../../types/types";

// Mock CSS modules
vi.mock("./selected-alarms-label.module.scss", () => ({
  default: {
    container: "container",
    content: "content",
    contentTitle: "contentTitle",
  },
}));

// Mock react-i18next
const mockT = vi.fn((_key: string, defaultValue: string, options?: any) => {
  if (options) {
    // Handle pluralization and interpolation
    let result = defaultValue;
    if (options.count !== undefined) {
      result = result.replace("{{count}}", options.count.toString());
    }
    if (options.plural !== undefined) {
      result = result.replace("{{plural}}", options.plural);
    }
    if (options.ids !== undefined) {
      result = result.replace("{{ids}}", options.ids);
    }
    return result;
  }
  return defaultValue;
});

vi.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: mockT,
  }),
}));

describe("SelectedAlarmsLabel", () => {
  const createMockAlarm = (faultId: string): FaultAlarm =>
    ({
      faultId,
      status: "KEPT",
    }) as FaultAlarm;

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("displays 'No Alarms Selected' when no alarms are provided", () => {
    render(<SelectedAlarmsLabel selectedAlarms={[]} />);

    expect(screen.getByText("No Alarms Selected")).toBeInTheDocument();
    expect(mockT).toHaveBeenCalledWith(
      "faultTracking.noAlarmsSelected",
      "No Alarms Selected",
    );
  });

  it("displays single alarm with correct singular form", () => {
    const singleAlarm = [createMockAlarm("alarm-001")];

    render(<SelectedAlarmsLabel selectedAlarms={singleAlarm} />);

    expect(
      screen.getByText("Selected 1 alarm - alarm-001"),
    ).toBeInTheDocument();
    expect(mockT).toHaveBeenCalledWith(
      "faultTracking.selectedAlarmsLabel",
      "Selected {{count}} alarm{{plural}} - {{ids}}",
      {
        count: 1,
        plural: "",
        ids: "alarm-001",
      },
    );
  });

  it("displays multiple alarms with correct plural form", () => {
    const multipleAlarms = [
      createMockAlarm("alarm-001"),
      createMockAlarm("alarm-002"),
      createMockAlarm("alarm-003"),
    ];

    render(<SelectedAlarmsLabel selectedAlarms={multipleAlarms} />);

    expect(
      screen.getByText("Selected 3 alarms - alarm-001, alarm-002, alarm-003"),
    ).toBeInTheDocument();
    expect(mockT).toHaveBeenCalledWith(
      "faultTracking.selectedAlarmsLabel",
      "Selected {{count}} alarm{{plural}} - {{ids}}",
      {
        count: 3,
        plural: "s",
        ids: "alarm-001, alarm-002, alarm-003",
      },
    );
  });

  it("displays exactly 4 alarms without truncation", () => {
    const fourAlarms = [
      createMockAlarm("alarm-001"),
      createMockAlarm("alarm-002"),
      createMockAlarm("alarm-003"),
      createMockAlarm("alarm-004"),
    ];

    render(<SelectedAlarmsLabel selectedAlarms={fourAlarms} />);

    expect(
      screen.getByText(
        "Selected 4 alarms - alarm-001, alarm-002, alarm-003, alarm-004",
      ),
    ).toBeInTheDocument();
    expect(mockT).toHaveBeenCalledWith(
      "faultTracking.selectedAlarmsLabel",
      "Selected {{count}} alarm{{plural}} - {{ids}}",
      {
        count: 4,
        plural: "s",
        ids: "alarm-001, alarm-002, alarm-003, alarm-004",
      },
    );
  });

  it("truncates 5 or more alarms with ellipsis format", () => {
    const fiveAlarms = [
      createMockAlarm("alarm-001"),
      createMockAlarm("alarm-002"),
      createMockAlarm("alarm-003"),
      createMockAlarm("alarm-004"),
      createMockAlarm("alarm-005"),
    ];

    render(<SelectedAlarmsLabel selectedAlarms={fiveAlarms} />);

    expect(
      screen.getByText(
        "Selected 5 alarms - alarm-001, alarm-002, alarm-003, ... , alarm-005",
      ),
    ).toBeInTheDocument();
    expect(mockT).toHaveBeenCalledWith(
      "faultTracking.selectedAlarmsLabel",
      "Selected {{count}} alarm{{plural}} - {{ids}}",
      {
        count: 5,
        plural: "s",
        ids: "alarm-001, alarm-002, alarm-003, ... , alarm-005",
      },
    );
  });

  it("truncates many alarms showing first 3 and last one", () => {
    const manyAlarms = [
      createMockAlarm("alarm-001"),
      createMockAlarm("alarm-002"),
      createMockAlarm("alarm-003"),
      createMockAlarm("alarm-004"),
      createMockAlarm("alarm-005"),
      createMockAlarm("alarm-006"),
      createMockAlarm("alarm-007"),
      createMockAlarm("alarm-008"),
      createMockAlarm("alarm-009"),
      createMockAlarm("alarm-010"),
    ];

    render(<SelectedAlarmsLabel selectedAlarms={manyAlarms} />);

    expect(
      screen.getByText(
        "Selected 10 alarms - alarm-001, alarm-002, alarm-003, ... , alarm-010",
      ),
    ).toBeInTheDocument();
    expect(mockT).toHaveBeenCalledWith(
      "faultTracking.selectedAlarmsLabel",
      "Selected {{count}} alarm{{plural}} - {{ids}}",
      {
        count: 10,
        plural: "s",
        ids: "alarm-001, alarm-002, alarm-003, ... , alarm-010",
      },
    );
  });

  it("handles alarms with different ID formats", () => {
    const mixedAlarms = [
      createMockAlarm("ALM-001"),
      createMockAlarm("fault_123"),
      createMockAlarm("emergency-alert-456"),
    ];

    render(<SelectedAlarmsLabel selectedAlarms={mixedAlarms} />);

    expect(
      screen.getByText(
        "Selected 3 alarms - ALM-001, fault_123, emergency-alert-456",
      ),
    ).toBeInTheDocument();
    expect(mockT).toHaveBeenCalledWith(
      "faultTracking.selectedAlarmsLabel",
      "Selected {{count}} alarm{{plural}} - {{ids}}",
      {
        count: 3,
        plural: "s",
        ids: "ALM-001, fault_123, emergency-alert-456",
      },
    );
  });

  it("renders in a styled container", () => {
    const { container } = render(
      <SelectedAlarmsLabel selectedAlarms={[createMockAlarm("test-alarm")]} />,
    );

    // The component should render in a div container with CSS module styling
    expect(container.firstChild?.nodeType).toBe(Node.ELEMENT_NODE);
    expect(container.firstChild).toHaveClass("container");
  });

  it("updates when selectedAlarms prop changes", () => {
    const initialAlarms = [createMockAlarm("alarm-001")];
    const { rerender } = render(
      <SelectedAlarmsLabel selectedAlarms={initialAlarms} />,
    );

    expect(
      screen.getByText("Selected 1 alarm - alarm-001"),
    ).toBeInTheDocument();

    const updatedAlarms = [
      createMockAlarm("alarm-001"),
      createMockAlarm("alarm-002"),
    ];
    rerender(<SelectedAlarmsLabel selectedAlarms={updatedAlarms} />);

    expect(
      screen.getByText("Selected 2 alarms - alarm-001, alarm-002"),
    ).toBeInTheDocument();
  });

  it("memoizes the label computation", () => {
    const alarms = [createMockAlarm("alarm-001")];
    const { rerender } = render(
      <SelectedAlarmsLabel selectedAlarms={alarms} />,
    );

    const initialCallCount = mockT.mock.calls.length;

    // Rerender with same props - should not call t() again due to memoization
    rerender(<SelectedAlarmsLabel selectedAlarms={alarms} />);

    expect(mockT.mock.calls.length).toBe(initialCallCount);
  });

  it("uses correct translation keys", () => {
    render(<SelectedAlarmsLabel selectedAlarms={[]} />);
    expect(mockT).toHaveBeenCalledWith(
      "faultTracking.noAlarmsSelected",
      "No Alarms Selected",
    );

    vi.clearAllMocks();

    render(<SelectedAlarmsLabel selectedAlarms={[createMockAlarm("test")]} />);
    expect(mockT).toHaveBeenCalledWith(
      "faultTracking.selectedAlarmsLabel",
      "Selected {{count}} alarm{{plural}} - {{ids}}",
      expect.any(Object),
    );
  });

  it("shows tooltip with all alarm IDs when 5 or more alarms are selected", () => {
    const fiveAlarms = [
      createMockAlarm("alarm-001"),
      createMockAlarm("alarm-002"),
      createMockAlarm("alarm-003"),
      createMockAlarm("alarm-004"),
      createMockAlarm("alarm-005"),
    ];

    render(<SelectedAlarmsLabel selectedAlarms={fiveAlarms} />);

    // Check that tooltip content translation is called
    expect(mockT).toHaveBeenCalledWith(
      "faultTracking.selectedAlarmIds",
      "Selected Alarm IDs:",
    );

    // Check that all alarm IDs are rendered in the tooltip
    expect(screen.getByText("alarm-001")).toBeInTheDocument();
    expect(screen.getByText("alarm-002")).toBeInTheDocument();
    expect(screen.getByText("alarm-003")).toBeInTheDocument();
    expect(screen.getByText("alarm-004")).toBeInTheDocument();
    expect(screen.getByText("alarm-005")).toBeInTheDocument();
  });

  it("does not show tooltip when fewer than 5 alarms are selected", () => {
    const fourAlarms = [
      createMockAlarm("alarm-001"),
      createMockAlarm("alarm-002"),
      createMockAlarm("alarm-003"),
      createMockAlarm("alarm-004"),
    ];

    render(<SelectedAlarmsLabel selectedAlarms={fourAlarms} />);

    // Tooltip translation should not be called for < 5 alarms
    expect(mockT).not.toHaveBeenCalledWith(
      "faultTracking.selectedAlarmIds",
      "Selected Alarm IDs:",
    );
  });
});
