import { Modal } from "@carbon/react";
import { useTranslation } from "react-i18next";
import { useState } from "react";
import { FaultAlarm } from "../../types/types";
import { SelectedAlarmsLabel } from "./selected-alarms-label";
import { CalculationStatusForm } from "./calculation-status-form";
import { useUpdateCalculationStatus } from "../../hooks/calculation-status/use-update-calculation-status";
import type { CalculationStatusFormData } from "../../hooks/calculation-status/use-calculation-status-form";

interface FormChangeData {
  formData: CalculationStatusFormData | null;
  isValid: boolean;
}

export function CalculationStatusModal({
  isCalculationStatusModalOpen,
  toggleCalculationStatusModal,
  selectedAlarms,
  isIncluded,
  onSuccess,
}: {
  isCalculationStatusModalOpen: boolean;
  toggleCalculationStatusModal: () => void;
  selectedAlarms: FaultAlarm[];
  isIncluded: boolean | null;
  onSuccess?: () => void;
}) {
  const { t } = useTranslation();
  const [hasFormChanges, setHasFormChanges] = useState(false);
  const [formData, setFormData] = useState<CalculationStatusFormData | null>(
    null,
  );
  const [isFormValid, setIsFormValid] = useState(false);

  const { updateCalculationStatus, isSubmitting } = useUpdateCalculationStatus(
    () => {
      toggleCalculationStatusModal();
      if (onSuccess) {
        onSuccess();
      }
    },
  );

  const handleCancel = () => {
    toggleCalculationStatusModal();
  };

  const handleSubmit = async () => {
    if (
      !formData ||
      !isFormValid ||
      selectedAlarms.length === 0 ||
      !hasFormChanges
    ) {
      return;
    }

    await updateCalculationStatus(formData, selectedAlarms);
  };

  const handleFormChange = (hasChanges: boolean, data: FormChangeData) => {
    setHasFormChanges(hasChanges);
    setFormData(data.formData);
    setIsFormValid(data.isValid);
  };

  return (
    <Modal
      open={isCalculationStatusModalOpen}
      onRequestClose={toggleCalculationStatusModal}
      onRequestSubmit={handleSubmit}
      onSecondarySubmit={handleCancel}
      primaryButtonText={t("faultTracking.save", "Save")}
      secondaryButtonText={t("faultTracking.cancel", "Cancel")}
      modalHeading={t(
        "faultTracking.updateCalculationStatus",
        "Update Calculation Status",
      )}
      modalLabel={<SelectedAlarmsLabel selectedAlarms={selectedAlarms} />}
      primaryButtonDisabled={
        selectedAlarms.length === 0 ||
        isIncluded === null ||
        !hasFormChanges ||
        !isFormValid ||
        isSubmitting
      }
    >
      {selectedAlarms.length === 0 ? (
        <p>{t("faultTracking.noAlarmsSelected", "No alarms selected")}</p>
      ) : isIncluded !== null ? (
        isCalculationStatusModalOpen && (
          <CalculationStatusForm
            isIncluded={isIncluded}
            onFormChange={handleFormChange}
          />
        )
      ) : (
        <p>
          {t(
            "faultTracking.mixedSelectionState",
            "Mixed selection state - please select alarms with the same inclusion status",
          )}
        </p>
      )}
    </Modal>
  );
}
