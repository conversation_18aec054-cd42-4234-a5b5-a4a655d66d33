import { useTranslation } from "react-i18next";
import { FaultAlarm } from "../../types/types";
import { useMemo } from "react";
import {
  Toggletip,
  ToggletipButton,
  ToggletipContent,
  ToggletipLabel,
} from "@carbon/react";
import { Information } from "@carbon/icons-react";
import styles from "./selected-alarms-label.module.scss";

export function SelectedAlarmsLabel({
  selectedAlarms,
}: {
  selectedAlarms: FaultAlarm[];
}) {
  const { t } = useTranslation();

  const label = useMemo(() => {
    if (selectedAlarms.length === 0) {
      return t("faultTracking.noAlarmsSelected", "No Alarms Selected");
    }

    const allIds = selectedAlarms.map((alarm) => alarm.faultId);
    let alarmIds: string;

    if (allIds.length >= 5) {
      const firstThree = allIds.slice(0, 3);
      const lastOne = allIds[allIds.length - 1];
      alarmIds = `${firstThree.join(", ")}, ... , ${lastOne}`;
    } else {
      alarmIds = allIds.join(", ");
    }

    return t(
      "faultTracking.selectedAlarmsLabel",
      "Selected {{count}} alarm{{plural}} - {{ids}}",
      {
        count: selectedAlarms.length,
        plural: selectedAlarms.length > 1 ? "s" : "",
        ids: alarmIds,
      },
    );
  }, [selectedAlarms, t]);

  return (
    <div className={styles.container}>
      <ToggletipLabel>{label}</ToggletipLabel>
      {selectedAlarms.length >= 5 && (
        <Toggletip autoAlign>
          <ToggletipButton
            label={t("faultTracking.showAllAlarmIds", "Show all alarm IDs")}
          >
            <Information />
          </ToggletipButton>
          <ToggletipContent>
            <div className={styles.content}>
              <h6 className={styles.contentTitle}>
                {t("faultTracking.selectedAlarmIds", "Selected Alarm IDs:")}
              </h6>
              <ul>
                {selectedAlarms.map((alarm) => (
                  <li key={alarm.faultId}>{alarm.faultId}</li>
                ))}
              </ul>
            </div>
          </ToggletipContent>
        </Toggletip>
      )}
    </div>
  );
}
