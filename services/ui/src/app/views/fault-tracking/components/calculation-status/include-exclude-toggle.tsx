import { Toggle } from "@carbon/react";
import { useTranslation } from "react-i18next";

export function IncludeExcludeToggle({ 
  isIncluded, 
  onChange 
}: { 
  isIncluded: boolean;
  onChange?: (isIncluded: boolean) => void;
}) {
  const { t } = useTranslation();
  return (
    <Toggle
      id="include-exclude-toggle"
      labelB={t("faultTracking.included", "Included")}
      labelA={t("faultTracking.excluded", "Excluded")}
      labelText={t(
        "faultTracking.systemAvailabilityCalculation",
        "System Availability Calculation",
      )}
      toggled={isIncluded}
      onToggle={(checked) => {
        if (onChange) {
          onChange(checked);
        }
      }}
    />
  );
}
