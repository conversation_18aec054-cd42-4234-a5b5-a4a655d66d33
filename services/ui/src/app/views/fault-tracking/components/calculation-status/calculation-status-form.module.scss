.formContainer {
  transition: all 250ms ease-out;
  overflow: visible;
  padding: 8px 0;
  
  :global(.cds--form) {
    transition: all 250ms ease-out;
  }
  
  :global(.cds--stack) {
    transition: all 250ms ease-out;
  }
}

.removalReasonContainer {
  animation: slideInFade 300ms ease-out forwards;
  transform-origin: top;
  overflow: hidden;

  :global(.cds--radio-button-wrapper) {
    transition: all 150ms ease-out;
  }

  :global(.cds--radio-button__label) {
    transition: color 150ms ease-out;
  }
}

.removalReasonContainerTransition {
  transition: all 300ms ease-out;
  transform-origin: top;
  overflow: visible;
  max-height: 500px;
  opacity: 1;
  transform: translateY(0) scaleY(1);

  &.hidden {
    max-height: 0;
    opacity: 0;
    transform: translateY(-8px) scaleY(0.95);
    margin-bottom: 0;
  }

  :global(.cds--radio-button-wrapper) {
    transition: all 150ms ease-out;
  }

  :global(.cds--radio-button__label) {
    transition: color 150ms ease-out;
  }
}

@keyframes slideInFade {
  0% {
    opacity: 0;
    transform: translateY(-8px) scaleY(0.95);
    max-height: 0;
  }
  100% {
    opacity: 1;
    transform: translateY(0) scaleY(1);
    max-height: 500px; 
  }
}

@keyframes slideOutFade {
  0% {
    opacity: 1;
    transform: translateY(0) scaleY(1);
    max-height: 500px;
  }
  100% {
    opacity: 0;
    transform: translateY(-8px) scaleY(0.95);
    max-height: 0;
  }
}

.removalReasonContainerLeaving {
  animation: slideOutFade 200ms ease-out forwards;
  transform-origin: top;
}
