import { describe, it, expect, vi, beforeEach } from "vitest";
import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import "@testing-library/jest-dom";
import { RemovalReasonRadioGroup } from "./removal-reason-radio-group";

// Mock react-i18next
const mockT = vi.fn((_key: string, defaultValue: string) => defaultValue);
vi.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: mockT,
  }),
}));

// Mock Carbon Radio components
vi.mock("@carbon/react", () => ({
  RadioButtonGroup: ({
    legendText,
    name,
    valueSelected,
    onChange,
    required,
    invalid,
    orientation,
    children,
    ...props
  }: any) => (
    <fieldset
      data-testid="radio-button-group"
      data-invalid={invalid}
      data-required={required}
      data-orientation={orientation}
      {...props}
    >
      <legend data-testid="radio-legend">{legendText}</legend>
      <div
        data-testid="radio-container"
        data-name={name}
        data-value-selected={valueSelected}
        onChange={(e: any) => onChange(e.target.value)}
      >
        {children}
      </div>
    </fieldset>
  ),
  RadioButton: ({ id, value, labelText, ...props }: any) => (
    <label data-testid={`radio-option-${value}`} {...props}>
      <input
        id={id}
        data-testid={`radio-input-${value}`}
        type="radio"
        name="removal-reason"
        value={value}
      />
      <span data-testid={`radio-label-${value}`}>{labelText}</span>
    </label>
  ),
}));

// Mock the useRemovalReasons hook
const mockUseRemovalReasons = vi.fn();
vi.mock("../../hooks/calculation-status/use-removal-reasons", () => ({
  useRemovalReasons: () => mockUseRemovalReasons(),
}));

describe("RemovalReasonRadioGroup", () => {
  const mockOnChange = vi.fn();

  const defaultReasons = [
    { value: "customer-responsibility", label: "Customer Responsibility" },
    { value: "equipment-excluded", label: "Equipment Excluded" },
    { value: "external-outage", label: "External Outage" },
    { value: "fault-excluded", label: "Fault Excluded" },
  ];

  const defaultProps = {
    value: null,
    onChange: mockOnChange,
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockUseRemovalReasons.mockReturnValue(defaultReasons);
  });

  it("renders radio button group with correct legend", () => {
    render(<RemovalReasonRadioGroup {...defaultProps} />);

    expect(screen.getByTestId("radio-button-group")).toBeInTheDocument();
    expect(screen.getByTestId("radio-legend")).toHaveTextContent(
      "Edit Removal Reason",
    );
    expect(mockT).toHaveBeenCalledWith(
      "faultTracking.editRemovalReason",
      "Edit Removal Reason",
    );
  });

  it("renders all removal reason options", () => {
    render(<RemovalReasonRadioGroup {...defaultProps} />);

    defaultReasons.forEach((reason) => {
      expect(
        screen.getByTestId(`radio-option-${reason.value}`),
      ).toBeInTheDocument();
      expect(
        screen.getByTestId(`radio-label-${reason.value}`),
      ).toHaveTextContent(reason.label);
      expect(
        screen.getByTestId(`radio-input-${reason.value}`),
      ).toBeInTheDocument();
    });
  });

  it("sets correct radio button group properties", () => {
    render(<RemovalReasonRadioGroup {...defaultProps} />);

    const container = screen.getByTestId("radio-container");
    expect(container).toHaveAttribute("data-name", "removal-reason");
    expect(container).toHaveAttribute("data-value-selected", "");

    const group = screen.getByTestId("radio-button-group");
    expect(group).toHaveAttribute("data-required", "true");
    expect(group).toHaveAttribute("data-orientation", "vertical");
  });

  it("displays selected value correctly", () => {
    render(
      <RemovalReasonRadioGroup
        {...defaultProps}
        value="customer-responsibility"
      />,
    );

    const container = screen.getByTestId("radio-container");
    expect(container).toHaveAttribute(
      "data-value-selected",
      "customer-responsibility",
    );
  });

  it("handles null value by setting empty string", () => {
    render(<RemovalReasonRadioGroup {...defaultProps} value={null} />);

    const container = screen.getByTestId("radio-container");
    expect(container).toHaveAttribute("data-value-selected", "");
  });

  it("calls onChange when a radio button is selected", async () => {
    render(<RemovalReasonRadioGroup {...defaultProps} />);

    const radioInput = screen.getByTestId(
      "radio-input-customer-responsibility",
    );
    await userEvent.click(radioInput);

    // The mock Carbon component will trigger onChange with the value
    // In the real component, Carbon handles the onChange event
    expect(mockOnChange).toHaveBeenCalled();
  });

  it("shows error state when hasError is true", () => {
    render(<RemovalReasonRadioGroup {...defaultProps} hasError={true} />);

    const group = screen.getByTestId("radio-button-group");
    expect(group).toHaveAttribute("data-invalid", "true");
  });

  it("does not show error state when hasError is false", () => {
    render(<RemovalReasonRadioGroup {...defaultProps} hasError={false} />);

    const group = screen.getByTestId("radio-button-group");
    expect(group).toHaveAttribute("data-invalid", "false");
  });

  it("does not show error state when hasError is undefined", () => {
    render(<RemovalReasonRadioGroup {...defaultProps} hasError={undefined} />);

    const group = screen.getByTestId("radio-button-group");
    // When hasError is undefined, the data-invalid attribute should not be present or be null
    const invalidAttr = group.getAttribute("data-invalid");
    expect(invalidAttr === null || invalidAttr === "false").toBe(true);
  });

  it("renders with different removal reasons from hook", () => {
    const customReasons = [
      { value: "planned-maintenance", label: "Planned Maintenance Activity" },
      { value: "material-quality", label: "Material Quality Poor" },
    ];
    mockUseRemovalReasons.mockReturnValue(customReasons);

    render(<RemovalReasonRadioGroup {...defaultProps} />);

    customReasons.forEach((reason) => {
      expect(
        screen.getByTestId(`radio-option-${reason.value}`),
      ).toBeInTheDocument();
      expect(
        screen.getByTestId(`radio-label-${reason.value}`),
      ).toHaveTextContent(reason.label);
    });

    // Original reasons should not be present
    expect(
      screen.queryByTestId("radio-option-customer-responsibility"),
    ).not.toBeInTheDocument();
  });

  it("handles empty reasons array gracefully", () => {
    mockUseRemovalReasons.mockReturnValue([]);

    render(<RemovalReasonRadioGroup {...defaultProps} />);

    expect(screen.getByTestId("radio-button-group")).toBeInTheDocument();
    expect(screen.getByTestId("radio-legend")).toHaveTextContent(
      "Edit Removal Reason",
    );

    // No radio options should be rendered
    defaultReasons.forEach((reason) => {
      expect(
        screen.queryByTestId(`radio-option-${reason.value}`),
      ).not.toBeInTheDocument();
    });
  });

  it("works without onChange callback", () => {
    expect(() => {
      render(
        <RemovalReasonRadioGroup value={null} onChange={undefined as any} />,
      );
    }).not.toThrow();
  });

  it("handles string and non-string selections correctly", () => {
    render(<RemovalReasonRadioGroup {...defaultProps} />);

    // The component has logic to check if selection is string before calling onChange
    // This tests the onChange handler in the component
    const container = screen.getByTestId("radio-container");

    // Simulate Carbon's onChange with string value
    container.dispatchEvent(new Event("change", { bubbles: true }));

    // The mock should handle the change event
    expect(container).toBeInTheDocument();
  });

  it("maps reason values to radio button IDs correctly", () => {
    render(<RemovalReasonRadioGroup {...defaultProps} />);

    defaultReasons.forEach((reason) => {
      const radioInput = screen.getByTestId(`radio-input-${reason.value}`);
      expect(radioInput).toHaveAttribute("id", reason.value);
      expect(radioInput).toHaveAttribute("value", reason.value);
    });
  });

  it("sets all radio buttons to same name for grouping", () => {
    render(<RemovalReasonRadioGroup {...defaultProps} />);

    defaultReasons.forEach((reason) => {
      const radioInput = screen.getByTestId(`radio-input-${reason.value}`);
      expect(radioInput).toHaveAttribute("name", "removal-reason");
    });
  });

  it("uses vertical orientation", () => {
    render(<RemovalReasonRadioGroup {...defaultProps} />);

    const group = screen.getByTestId("radio-button-group");
    expect(group).toHaveAttribute("data-orientation", "vertical");
  });
});
