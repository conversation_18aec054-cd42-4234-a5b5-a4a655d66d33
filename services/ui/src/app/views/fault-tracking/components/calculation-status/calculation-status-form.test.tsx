import { describe, it, expect, vi, beforeEach } from "vitest";
import { render, screen, fireEvent } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import "@testing-library/jest-dom";
import { CalculationStatusForm } from "./calculation-status-form";

// Mock react-i18next
const mockT = vi.fn((_key: string, defaultValue: string) => defaultValue);
vi.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: mockT,
  }),
}));

// Mock Carbon components
vi.mock("@carbon/react", () => ({
  Form: ({ children, ...props }: any) => (
    <form data-testid="calculation-status-form" {...props}>
      {children}
    </form>
  ),
  Stack: ({ children, gap, ...props }: any) => (
    <div data-testid="stack" data-gap={gap} {...props}>
      {children}
    </div>
  ),
}));

// Mock the child components
vi.mock("./include-exclude-toggle", () => ({
  IncludeExcludeToggle: ({ isIncluded, onChange }: any) => (
    <div data-testid="include-exclude-toggle">
      <button data-testid="toggle-button" onClick={() => onChange(!isIncluded)}>
        {isIncluded ? "Included" : "Excluded"}
      </button>
    </div>
  ),
}));

vi.mock("./removal-reason-radio-group", () => ({
  RemovalReasonRadioGroup: ({ value, onChange, hasError }: any) => (
    <div data-testid="removal-reason-radio-group">
      <div data-testid={hasError ? "radio-group-error" : "radio-group-normal"}>
        <button
          data-testid="reason-customer-responsibility"
          onClick={() => onChange("customer-responsibility")}
        >
          Customer Responsibility
        </button>
        <button
          data-testid="reason-equipment-excluded"
          onClick={() => onChange("equipment-excluded")}
        >
          Equipment Excluded
        </button>
        <div data-testid="selected-value">{value || "none"}</div>
      </div>
    </div>
  ),
}));

vi.mock("../shared", () => ({
  FormTextAreaField: ({
    id,
    labelDefault,
    value,
    onChange,
    onBlur,
    required,
    touched,
    error,
  }: any) => (
    <div data-testid="form-textarea-field">
      <label htmlFor={`${id}-input`}>{labelDefault}</label>
      <textarea
        id={`${id}-input`}
        data-testid={`${id}-input`}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        onBlur={onBlur}
        aria-required={required}
        aria-invalid={touched}
      />
      {error && <div data-testid={`${id}-error`}>{error}</div>}
    </div>
  ),
}));

// Mock the CSS module
vi.mock("./calculation-status-form.module.scss", () => ({
  default: {
    formContainer: "mocked-form-container",
    removalReasonContainer: "mocked-removal-reason-container",
    removalReasonContainerTransition:
      "mocked-removal-reason-container-transition",
    hidden: "mocked-hidden",
  },
}));

// Mock the custom hook
const mockUseCalculationStatusForm = vi.fn();
vi.mock("../../hooks/calculation-status/use-calculation-status-form", () => ({
  useCalculationStatusForm: () => mockUseCalculationStatusForm(),
}));

describe("CalculationStatusForm", () => {
  const mockOnFormChange = vi.fn();

  const defaultHookReturn = {
    formData: {
      isIncluded: true,
      removalReason: null,
      comments: "",
    },
    hasChanges: false,
    updateIsIncluded: vi.fn(),
    updateRemovalReason: vi.fn(),
    updateComments: vi.fn(),
    handleCommentsBlur: vi.fn(),
    isValid: false,
    fieldErrors: {
      comments: false,
      removalReason: false,
    },
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockUseCalculationStatusForm.mockReturnValue(defaultHookReturn);
  });

  it("renders form with include toggle and comments field", () => {
    render(
      <CalculationStatusForm
        isIncluded={true}
        onFormChange={mockOnFormChange}
      />,
    );

    expect(screen.getByTestId("calculation-status-form")).toBeInTheDocument();
    expect(screen.getByTestId("include-exclude-toggle")).toBeInTheDocument();
    expect(screen.getByTestId("form-textarea-field")).toBeInTheDocument();
    expect(screen.getByLabelText("Comments")).toBeInTheDocument();
  });

  it("shows removal reason radio group when excluded", () => {
    mockUseCalculationStatusForm.mockReturnValue({
      ...defaultHookReturn,
      formData: {
        isIncluded: false,
        removalReason: null,
        comments: "",
      },
    });

    render(
      <CalculationStatusForm
        isIncluded={false}
        onFormChange={mockOnFormChange}
      />,
    );

    expect(
      screen.getByTestId("removal-reason-radio-group"),
    ).toBeInTheDocument();
  });

  it("hides removal reason radio group when included", () => {
    render(
      <CalculationStatusForm
        isIncluded={true}
        onFormChange={mockOnFormChange}
      />,
    );

    const container = screen.getByTestId(
      "removal-reason-radio-group",
    ).parentElement;
    expect(container).toHaveClass("mocked-hidden");
  });

  it("calls updateIsIncluded when toggle is clicked", async () => {
    const mockUpdateIsIncluded = vi.fn();
    mockUseCalculationStatusForm.mockReturnValue({
      ...defaultHookReturn,
      updateIsIncluded: mockUpdateIsIncluded,
    });

    render(
      <CalculationStatusForm
        isIncluded={true}
        onFormChange={mockOnFormChange}
      />,
    );

    const toggleButton = screen.getByTestId("toggle-button");
    await userEvent.click(toggleButton);

    expect(mockUpdateIsIncluded).toHaveBeenCalledWith(false);
  });

  it("calls updateRemovalReason when reason is selected", async () => {
    const mockUpdateRemovalReason = vi.fn();
    mockUseCalculationStatusForm.mockReturnValue({
      ...defaultHookReturn,
      formData: {
        isIncluded: false,
        removalReason: null,
        comments: "",
      },
      updateRemovalReason: mockUpdateRemovalReason,
    });

    render(
      <CalculationStatusForm
        isIncluded={false}
        onFormChange={mockOnFormChange}
      />,
    );

    const reasonButton = screen.getByTestId("reason-customer-responsibility");
    await userEvent.click(reasonButton);

    expect(mockUpdateRemovalReason).toHaveBeenCalledWith(
      "customer-responsibility",
    );
  });

  it("calls updateComments when comments field changes", async () => {
    const mockUpdateComments = vi.fn();
    mockUseCalculationStatusForm.mockReturnValue({
      ...defaultHookReturn,
      updateComments: mockUpdateComments,
    });

    render(
      <CalculationStatusForm
        isIncluded={true}
        onFormChange={mockOnFormChange}
      />,
    );

    const commentsInput = screen.getByTestId(
      "calculation-status-comment-input",
    );

    // Type text which will call updateComments for each character
    await userEvent.type(commentsInput, "Test comment");

    // Verify updateComments was called (userEvent.type calls it for each character)
    expect(mockUpdateComments).toHaveBeenCalled();
    expect(mockUpdateComments).toHaveBeenCalledWith("t"); // Last character
    expect(mockUpdateComments).toHaveBeenCalledTimes(12); // "Test comment" is 12 characters
  });

  it("calls handleCommentsBlur when comments field loses focus", () => {
    const mockHandleCommentsBlur = vi.fn();
    mockUseCalculationStatusForm.mockReturnValue({
      ...defaultHookReturn,
      handleCommentsBlur: mockHandleCommentsBlur,
    });

    render(
      <CalculationStatusForm
        isIncluded={true}
        onFormChange={mockOnFormChange}
      />,
    );

    const commentsInput = screen.getByTestId(
      "calculation-status-comment-input",
    );
    fireEvent.blur(commentsInput);

    expect(mockHandleCommentsBlur).toHaveBeenCalled();
  });

  it("shows error message for comments when field has error", () => {
    mockUseCalculationStatusForm.mockReturnValue({
      ...defaultHookReturn,
      fieldErrors: {
        comments: true,
        removalReason: false,
      },
    });

    render(
      <CalculationStatusForm
        isIncluded={true}
        onFormChange={mockOnFormChange}
      />,
    );

    expect(
      screen.getByTestId("calculation-status-comment-error"),
    ).toBeInTheDocument();
    expect(screen.getByText("Comments are required")).toBeInTheDocument();
  });

  it("shows error state for removal reason when field has error", () => {
    mockUseCalculationStatusForm.mockReturnValue({
      ...defaultHookReturn,
      formData: {
        isIncluded: false,
        removalReason: null,
        comments: "",
      },
      fieldErrors: {
        comments: false,
        removalReason: true,
      },
    });

    render(
      <CalculationStatusForm
        isIncluded={false}
        onFormChange={mockOnFormChange}
      />,
    );

    expect(screen.getByTestId("radio-group-error")).toBeInTheDocument();
  });

  it("calls onFormChange when form state changes", () => {
    mockUseCalculationStatusForm.mockReturnValue({
      ...defaultHookReturn,
      hasChanges: true,
      isValid: true,
      formData: {
        isIncluded: true,
        removalReason: null,
        comments: "Some comment",
      },
    });

    render(
      <CalculationStatusForm
        isIncluded={true}
        onFormChange={mockOnFormChange}
      />,
    );

    expect(mockOnFormChange).toHaveBeenCalledWith(true, {
      formData: {
        isIncluded: true,
        removalReason: null,
        comments: "Some comment",
      },
      isValid: true,
    });
  });

  it("returns null when formData is null", () => {
    mockUseCalculationStatusForm.mockReturnValue({
      ...defaultHookReturn,
      formData: null,
    });

    const { container } = render(
      <CalculationStatusForm
        isIncluded={true}
        onFormChange={mockOnFormChange}
      />,
    );

    expect(container.firstChild).toBeNull();
  });

  it("applies CSS module class to removal reason container", () => {
    mockUseCalculationStatusForm.mockReturnValue({
      ...defaultHookReturn,
      formData: {
        isIncluded: false,
        removalReason: null,
        comments: "",
      },
    });

    render(
      <CalculationStatusForm
        isIncluded={false}
        onFormChange={mockOnFormChange}
      />,
    );

    const container = screen.getByTestId(
      "removal-reason-radio-group",
    ).parentElement;
    expect(container).toHaveClass("mocked-removal-reason-container-transition");
  });
});
