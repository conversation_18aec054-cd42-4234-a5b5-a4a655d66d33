import { RadioButtonGroup, RadioButton } from "@carbon/react";
import { useTranslation } from "react-i18next";
import { useRemovalReasons } from "../../hooks/calculation-status/use-removal-reasons";

export function RemovalReasonRadioGroup({
  value,
  onChange,
  hasError,
}: {
  value: string | null;
  onChange: (reason: string) => void;
  hasError?: boolean;
}) {
  const { t } = useTranslation();
  const removalReasons = useRemovalReasons();

  return (
    <RadioButtonGroup
      legendText={t("faultTracking.editRemovalReason", "Edit Removal Reason")}
      name="removal-reason"
      valueSelected={value || ""}
      onChange={(selection) => {
        if (selection && typeof selection === "string") {
          onChange(selection);
        }
      }}
      required
      invalid={hasError}
      orientation="vertical"
    >
      {removalReasons.map((reason) => (
        <RadioButton
          key={reason.value}
          id={reason.value}
          value={reason.value}
          labelText={reason.label}
        />
      ))}
    </RadioButtonGroup>
  );
}
