import { describe, it, expect } from "vitest";
import { formatEditTimingsForAPI } from "./edit-timings-api-formatter";
import type { EditTimingsForm } from "../hooks/edit-timings/use-edit-timings-form";
import type { FaultAlarm } from "../types/types";

const createBaseForm = (): EditTimingsForm => ({
  startDate: new Date("2024-02-15"),
  endDate: new Date("2024-02-15"),
  startTime: "10:30",
  startPeriod: "AM" as const,
  endTime: "11:45",
  endPeriod: "AM" as const,
  startSeconds: "15:500",
  endSeconds: "30:250",
  comments: "Test comments",
});

const createMockAlarm = (): FaultAlarm => ({
  id: "test-alarm-123",
  splitId: "SPLIT-001",
  faultId: "FAULT-001",
  title: "Test Alarm",
  description: "Test Description",
  tag: "TAG001",
  status: "KEPT",
  reason: "Test reason",
  comments: "Initial comments",
  location: {
    area: "Area A",
    section: "Section 1",
    equipment: "Equipment 1",
  },
  timing: {
    startTime: "2024-02-15T10:30:00.000Z",
    endTime: "2024-02-15T11:45:00.000Z",
    updatedStartTime: "2024-02-15T10:30:00.000Z",
    updatedEndTime: "2024-02-15T11:45:00.000Z",
    origStartTime: "2024-02-15T10:30:00.000Z",
    origEndTime: "2024-02-15T11:45:00.000Z",
    origDuration: "01:15:00",
    duration: "01:15:00",
  },
});

describe("formatEditTimingsForAPI", () => {
  describe("successful formatting", () => {
    it("should format complete form data with start and end times", () => {
      const formData = createBaseForm();

      const result = formatEditTimingsForAPI(
        formData,
        createMockAlarm(),
        "America/New_York",
      );

      expect(result).toEqual({
        id: "test-alarm-123",
        startDateLocal: expect.stringMatching(
          /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/,
        ),
        endDateLocal: expect.stringMatching(
          /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/,
        ),
        isIncluded: true,
        comments: "Test comments",
      });

      // Verify the ISO strings are valid
      expect(new Date(result.startDateLocal!)).toBeInstanceOf(Date);
      expect(new Date(result.endDateLocal!)).toBeInstanceOf(Date);
    });

    it("should handle PM times correctly", () => {
      const formData = createBaseForm();
      formData.startTime = "02:30";
      formData.startPeriod = "PM";
      formData.endTime = "04:45";
      formData.endPeriod = "PM";

      const result = formatEditTimingsForAPI(
        formData,
        createMockAlarm(),
        "America/New_York",
      );

      expect(result.startDateLocal).toBeDefined();
      expect(result.endDateLocal).toBeDefined();
      expect(result.comments).toBe("Test comments");
    });

    it("should handle midnight and noon correctly", () => {
      const formData = createBaseForm();
      formData.startTime = "12:00";
      formData.startPeriod = "AM"; // Midnight
      formData.endTime = "12:00";
      formData.endPeriod = "PM"; // Noon

      const result = formatEditTimingsForAPI(
        formData,
        createMockAlarm(),
        "America/New_York",
      );

      expect(result.startDateLocal).toBeDefined();
      expect(result.endDateLocal).toBeDefined();
    });

    it("should handle empty seconds", () => {
      const formData = createBaseForm();
      formData.startSeconds = "";
      formData.endSeconds = "";

      const result = formatEditTimingsForAPI(
        formData,
        createMockAlarm(),
        "America/New_York",
      );

      expect(result.startDateLocal).toBeDefined();
      expect(result.endDateLocal).toBeDefined();
      expect(result.comments).toBe("Test comments");
    });

    it("should handle invalid seconds format gracefully", () => {
      const formData = createBaseForm();
      formData.startSeconds = "invalid";
      formData.endSeconds = "also-invalid";

      const result = formatEditTimingsForAPI(
        formData,
        createMockAlarm(),
        "America/New_York",
      );

      expect(result.startDateLocal).toBeDefined();
      expect(result.endDateLocal).toBeDefined();
    });

    it("should handle empty comments", () => {
      const formData = createBaseForm();
      formData.comments = "";

      const result = formatEditTimingsForAPI(
        formData,
        createMockAlarm(),
        "America/New_York",
      );

      expect(result.comments).toBe("");
    });

    it("should use default timezone when not specified", () => {
      const formData = createBaseForm();

      const result = formatEditTimingsForAPI(formData, createMockAlarm()); // No timezone specified

      expect(result.startDateLocal).toBeDefined();
      expect(result.endDateLocal).toBeDefined();
      expect(result.comments).toBe("Test comments");
    });

    it("should handle different timezones", () => {
      const formData = createBaseForm();

      const resultEST = formatEditTimingsForAPI(
        formData,
        createMockAlarm(),
        "America/New_York",
      );
      const resultPST = formatEditTimingsForAPI(
        formData,
        createMockAlarm(),
        "America/Los_Angeles",
      );

      expect(resultEST.startDateLocal).toBeDefined();
      expect(resultPST.startDateLocal).toBeDefined();
      // The times should be different due to timezone conversion
      expect(resultEST.startDateLocal).not.toBe(resultPST.startDateLocal);
    });
  });

  describe("error handling", () => {
    it("should throw error when start date is missing", () => {
      const formData = createBaseForm();
      formData.startDate = null;

      expect(() =>
        formatEditTimingsForAPI(formData, createMockAlarm()),
      ).toThrow("Start date and end date are required");
    });

    it("should throw error when end date is missing", () => {
      const formData = createBaseForm();
      formData.endDate = null;

      expect(() =>
        formatEditTimingsForAPI(formData, createMockAlarm()),
      ).toThrow("Start date and end date are required");
    });

    it("should throw error when both dates are missing", () => {
      const formData = createBaseForm();
      formData.startDate = null;
      formData.endDate = null;

      expect(() =>
        formatEditTimingsForAPI(formData, createMockAlarm()),
      ).toThrow("Start date and end date are required");
    });
  });

  describe("data structure validation", () => {
    it("should only include expected fields in output", () => {
      const formData = createBaseForm();

      const result = formatEditTimingsForAPI(formData, createMockAlarm());

      // Should only have these 5 fields
      expect(Object.keys(result)).toEqual([
        "id",
        "startDateLocal",
        "endDateLocal",
        "isIncluded",
        "comments",
      ]);

      // Should NOT include these fields from manual entry
      expect(result).not.toHaveProperty("description");
      expect(result).not.toHaveProperty("equipment");
      expect(result).not.toHaveProperty("section");
      expect(result).not.toHaveProperty("area");
      expect(result).not.toHaveProperty("section");
    });

    it("should have ISO string format for date fields", () => {
      const formData = createBaseForm();

      const result = formatEditTimingsForAPI(formData, createMockAlarm());

      // Verify ISO 8601 format with timezone (ends with Z for UTC)
      expect(result.startDateLocal).toMatch(
        /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/,
      );
      expect(result.endDateLocal).toMatch(
        /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/,
      );

      // Verify they can be parsed back to Date objects
      expect(new Date(result.startDateLocal!).getTime()).not.toBeNaN();
      expect(new Date(result.endDateLocal!).getTime()).not.toBeNaN();
    });
  });
});
