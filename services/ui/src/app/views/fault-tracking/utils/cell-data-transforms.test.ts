import { describe, it, expect, beforeAll, afterAll } from "vitest";
import { transformFaultData, getStatusColor } from "./cell-data-transforms";
import type { FaultAlarm } from "../types/types";
import { Settings } from "luxon";

describe("data-transforms", () => {
  // Mock timezone to ensure consistent test results across environments
  // This prevents test failures when running in CI/CD pipelines or different developer machines
  beforeAll(() => {
    Settings.defaultZone = "America/New_York"; // EST/EDT timezone (UTC-5/-4)
  });

  afterAll(() => {
    Settings.defaultZone = "system"; // Reset to system timezone after tests
  });

  const mockRawData: FaultAlarm[] = [
    {
      id: "1",
      splitId: "SPLIT-001",
      faultId: "FAULT-001",
      title: "Test fault 1",
      description: "Test fault 1",
      tag: "TAG001",
      status: "KEPT", // Should transform to "Included"
      reason: "Test reason",
      location: { area: "A", section: "S1", equipment: "EQ1" },
      timing: {
        startTime: "2024-01-01T10:00:15.123Z",
        endTime: "2024-01-01T11:00:30.456Z",
        duration: "01:00:00",
        origStartTime: "2024-01-01 10:00 AM", // Alarm format without seconds
        origEndTime: "2024-01-01 11:00 AM", // Alarm format without seconds
        origDuration: "01:00:00",
        updatedStartTime: "2024-01-01T10:05:45.789Z", // ISO format with milliseconds
        updatedEndTime: "2024-01-01T11:05:20.012Z", // ISO format with milliseconds
        updatedDuration: "01:00:00",
      },
    },
    {
      id: "2",
      splitId: "SPLIT-002",
      faultId: "FAULT-002",
      title: "Test fault 2",
      description: "Test fault 2",
      tag: "TAG002",
      status: "[4] Equipment Excluded", // Should transform to "Excluded"
      reason: "Test reason",
      location: { area: "B", section: "S2", equipment: "EQ2" },
      timing: {
        startTime: "2024-01-02T12:00:00.999Z",
        endTime: "2024-01-02T13:00:00.000Z",
        duration: "01:00:00",
        origStartTime: "2024-01-02 12:00:30 PM", // Alarm format with seconds only
        origEndTime: "2024-01-02 1:00 PM", // Alarm format without seconds
        origDuration: "01:00:00",
      },
    },
  ];

  describe("transformFaultData", () => {
    it("should transform KEPT status to Included", () => {
      const result = transformFaultData(mockRawData, "America/New_York");

      expect(result[0].status).toBe("Included");
    });

    it("should transform non-KEPT status to 'Excluded'", () => {
      const result = transformFaultData(mockRawData, "America/New_York");

      expect(result[1].status).toBe("Excluded");
    });

    it("should format datetime strings using smart formatting", () => {
      const result = transformFaultData(mockRawData, "America/New_York");

      // Alarm format without seconds should format without seconds
      expect(result[0].timing.origStartTime).toBe("2024-01-01 10:00 AM");
      expect(result[0].timing.origEndTime).toBe("2024-01-01 11:00 AM");

      // ISO format with milliseconds should include seconds and milliseconds (UTC to EST conversion)
      expect(result[0].timing.updatedStartTime).toBe(
        "2024-01-01 5:05:45.789 AM",
      );
      expect(result[0].timing.updatedEndTime).toBe("2024-01-01 6:05:20.012 AM");
    });

    it("should handle different datetime formatting scenarios", () => {
      const result = transformFaultData(mockRawData, "America/New_York");

      // Alarm format with seconds only (no milliseconds)
      expect(result[1].timing.origStartTime).toBe("2024-01-02 12:00:30 PM");

      // Alarm format without seconds or milliseconds
      expect(result[1].timing.origEndTime).toBe("2024-01-02 1:00 PM");

      // Missing values should return empty strings
      expect(result[1].timing.updatedStartTime).toBe("");
      expect(result[1].timing.updatedEndTime).toBe("");
    });

    it("should preserve other fields unchanged", () => {
      const result = transformFaultData(mockRawData, "America/New_York");

      expect(result[0].faultId).toBe("FAULT-001");
      expect(result[0].splitId).toBe("SPLIT-001");
      expect(result[0].description).toBe("Test fault 1");
      expect(result[0].tag).toBe("TAG001");
      expect(result[0].location).toEqual({
        area: "A",
        section: "S1",
        equipment: "EQ1",
      });
      expect(result[0].timing.origDuration).toBe("01:00:00");
    });

    it("should handle empty array", () => {
      const result = transformFaultData([], "America/New_York");

      expect(result).toEqual([]);
    });

    it("should handle multiple status transformations", () => {
      const testData: FaultAlarm[] = [
        {
          ...mockRawData[0],
          status: "KEPT",
        },
        {
          ...mockRawData[0],
          status: "[2] Status Excluded",
        },
        {
          ...mockRawData[0],
          status: "[6] Alarm Less than Diagnostic Time",
        },
      ];

      const result = transformFaultData(testData, "America/New_York");

      expect(result[0].status).toBe("Included");
      expect(result[1].status).toBe("Excluded");
      expect(result[2].status).toBe("Excluded");
    });

    it("should handle edge cases in datetime formatting", () => {
      const testData: FaultAlarm[] = [
        {
          ...mockRawData[0],
          timing: {
            ...mockRawData[0].timing,
            origStartTime: "2024-01-01T10:00:00Z", // ISO with zero seconds and milliseconds
            origEndTime: "2024-01-01 11:00:00 AM", // Alarm format with seconds but no milliseconds
            updatedStartTime: "2024-01-01T12:30:15Z", // ISO with seconds but no milliseconds
            updatedEndTime: "invalid-date-string", // Invalid date should fallback
          },
        },
      ];

      const result = transformFaultData(testData, "America/New_York");

      // No seconds or milliseconds should show time without seconds (UTC to EST conversion)
      expect(result[0].timing.origStartTime).toBe("2024-01-01 5:00 AM");

      // Alarm format with seconds only
      expect(result[0].timing.origEndTime).toBe("2024-01-01 11:00:00 AM");

      // ISO format with seconds only (UTC to EST conversion)
      expect(result[0].timing.updatedStartTime).toBe("2024-01-01 7:30:15 AM");

      // Invalid date should use fallback (strip milliseconds with regex)
      expect(result[0].timing.updatedEndTime).toBe("invalid-date-string");
    });

    it("should handle undefined timing object", () => {
      const testData: FaultAlarm[] = [
        {
          ...mockRawData[0],
          timing: undefined as any,
        },
      ];

      const result = transformFaultData(testData, "America/New_York");

      expect(result[0].timing).toBeDefined();
      expect(result[0].timing.origStartTime).toBe("");
      expect(result[0].timing.origEndTime).toBe("");
      expect(result[0].timing.updatedStartTime).toBe("");
      expect(result[0].timing.updatedEndTime).toBe("");
    });

    it("should use default timezone when not specified", () => {
      const result = transformFaultData(mockRawData);

      // Should use default "America/New_York" timezone (EST/EDT, UTC-5/-4)
      expect(result[0].timing.updatedStartTime).toBe(
        "2024-01-01 5:05:45.789 AM",
      );
    });

    it("should respect different timezone parameters", () => {
      const testData: FaultAlarm[] = [
        {
          ...mockRawData[0],
          timing: {
            ...mockRawData[0].timing,
            updatedStartTime: "2024-01-01T10:00:00Z", // UTC time
          },
        },
      ];

      // Test with Eastern Time (UTC-5 in winter)
      const resultEastern = transformFaultData(testData, "America/New_York");
      expect(resultEastern[0].timing.updatedStartTime).toBe(
        "2024-01-01 5:00 AM",
      );

      // Test with Central Time (UTC-6 in winter)
      const resultCentral = transformFaultData(testData, "America/Chicago");
      expect(resultCentral[0].timing.updatedStartTime).toBe(
        "2024-01-01 4:00 AM",
      );

      // Test with Pacific Time (UTC-8 in winter)
      const resultPacific = transformFaultData(testData, "America/Los_Angeles");
      expect(resultPacific[0].timing.updatedStartTime).toBe(
        "2024-01-01 2:00 AM",
      );
    });
  });

  describe("getStatusColor", () => {
    it("should return blue for included status", () => {
      expect(getStatusColor("Included")).toBe("blue");
      expect(getStatusColor("included")).toBe("blue");
      expect(getStatusColor("INCLUDED")).toBe("blue");
    });

    it("should return outline for other statuses", () => {
      expect(getStatusColor("Excluded")).toBe("outline");
      expect(getStatusColor("Manual Override")).toBe("outline");
      expect(getStatusColor("")).toBe("outline");
    });
  });
});
