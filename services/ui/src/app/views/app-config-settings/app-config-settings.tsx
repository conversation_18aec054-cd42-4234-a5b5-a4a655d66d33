import {
  <PERSON><PERSON>,
  Column,
  <PERSON>rid,
  <PERSON><PERSON>,
  InlineNot<PERSON>,
  Modal,
  ModalBody,
  ModalFooter,
} from "@carbon/react";
import { createColumnHelper } from "@tanstack/react-table";
import { useMemo, useState } from "react";
import { Datagrid } from "../../components/datagrid";
import { deleteConfigSetting, useConfig } from "../../config/hooks/use-config";
import classes from "./app-config-settings.module.css";
import { AppConfigSetting, AppConfigSettingSource } from "@ict/sdk/types";
import { ViewAside } from "../../components/view-aside/view-aside";
import { AppConfigSettingDetail } from "./app-config-setting-detail";
import { TrashCan } from "@carbon/icons-react";
import { RowDelete } from "@carbon/react/icons";
import { useRoles } from "../../auth/hooks/use-roles";
import ReactDOM from "react-dom";
import { toastManager } from "../../components/toast/toast-container";
import { useTranslation } from "react-i18next";

export const AppConfigSettingsView = () => {
  const { data: appConfigSettings, isLoading, error } = useConfig();
  const { hasConfiguratorAccess } = useRoles();
  const { t } = useTranslation();

  const [settingsToDelete, setSettingsToDelete] = useState<
    AppConfigSetting[] | undefined
  >(undefined);
  const [selectedSettingId, setSelectedSettingId] = useState<
    string | undefined
  >(undefined);

  // determines if the expanded view is open for a new setting value
  const [isNewSettingOpen, setIsNewSettingOpen] = useState(false);

  // state for row selection
  const [rowSelection, setRowSelection] = useState<Record<string, boolean>>({});

  // filter config settings down to remove feature flags since there's a dedicated view
  const filteredConfigSettings: AppConfigSetting[] = useMemo(() => {
    return (appConfigSettings as AppConfigSetting[]).filter((setting) => {
      return setting.group !== "feature-flags";
    });
  }, [appConfigSettings]);

  // Define column helper for type safety
  const columnHelper = createColumnHelper<AppConfigSetting>();

  // Handle deletion of multiple config settings
  const handleDeleteSettings = async () => {
    if (!settingsToDelete || settingsToDelete.length === 0) return;

    try {
      const results = await Promise.allSettled(
        settingsToDelete.map((setting) =>
          deleteConfigSetting(
            { ...setting, source: AppConfigSettingSource.default },
            true,
          ),
        ),
      );

      const successful: AppConfigSetting[] = [];
      const failed: { setting: AppConfigSetting; error: Error }[] = [];

      results.forEach((result, index) => {
        if (result.status === "fulfilled" && !result.value.error) {
          successful.push(settingsToDelete[index]);
        } else {
          const error =
            result.status === "rejected" ? result.reason : result.value.error;
          failed.push({ setting: settingsToDelete[index], error });
        }
      });

      // Show appropriate toasts based on results
      if (successful.length > 0) {
        const message =
          successful.length === 1
            ? t("deleteSettings.successSingle")
            : t("deleteSettings.successMultiple", { count: successful.length });

        toastManager.addToast({
          id: "delete-settings-success",
          title: "Success",
          message,
          type: "success",
          duration: 5000,
        });
      }

      if (failed.length > 0) {
        const message =
          failed.length === 1
            ? t("deleteSettings.errorSingle", { name: failed[0].setting.name })
            : t("deleteSettings.errorMultiple", {
                count: failed.length,
                names: failed.map((f) => f.setting.name).join(", "),
              });

        toastManager.addToast({
          id: "delete-settings-error",
          title: "Error",
          message,
          type: "error",
        });
      }

      // Close modal and refresh data if any deletions succeeded
      if (successful.length > 0) {
        setSettingsToDelete(undefined);
        setRowSelection({});
      }
    } catch (error) {
      // Handle any unexpected errors during the deletion process
      console.error("Unexpected error during settings deletion:", error);
      toastManager.addToast({
        id: "delete-settings-unexpected-error",
        title: "Error",
        message: t("deleteSettings.unexpectedError"),
        type: "error",
      });
    }
  };

  // Define columns for the table
  const columns = useMemo(
    () => [
      columnHelper.accessor("name", {
        header: "Name",
        minSize: 90,
      }),
      columnHelper.accessor("description", {
        header: "Description",
        minSize: 180,
      }),
      columnHelper.accessor("group", {
        header: "Group",
        maxSize: 100,
        cell: (value) => value.getValue(),
      }),
      columnHelper.accessor("value", {
        id: "setting-value",
        header: "Value",
        cell: (props) => (
          <span>
            {`${props.row.original.dataType === "json" ? "View JSON in details" : props.getValue()}`}
          </span>
        ),
      }),
      columnHelper.accessor((row) => `${row.source}`, {
        id: "display-source",
        header: "Source",
        maxSize: 50,
      }),
      columnHelper.display({
        id: "details-button",
        size: 40,
        cell: ({ row }) => {
          return (
            <Button
              size="sm"
              onClick={(e) => {
                e.preventDefault();
                setSelectedSettingId(row.original.id);
              }}
            >
              Details
            </Button>
          );
        },
      }),
      columnHelper.display({
        id: "delete-setting",
        header: "",
        size: 20,
        cell: ({ row }) => {
          function deleteSetting() {
            setSettingsToDelete([row.original as AppConfigSetting]);
          }
          if (!hasConfiguratorAccess) {
            return null;
          }
          return (
            <Button
              kind="danger--ghost"
              style={{ paddingLeft: "0px", paddingRight: "0px" }}
              size="sm"
              onClick={() => {
                deleteSetting();
              }}
            >
              <RowDelete />
            </Button>
          );
        },
      }),
    ],
    [columnHelper],
  );

  // Handle error state
  if (error) {
    return (
      <InlineNotification
        kind="error"
        title="Error"
        subtitle={`Error loading application config settings: ${
          (error as Error)?.message || String(error)
        }`}
      />
    );
  }

  return (
    <div className={classes.container}>
      <Grid>
        <Column lg={16} md={8} sm={4}>
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              marginBottom: "1rem",
            }}
          >
            <Heading>Application Config Settings</Heading>
            <Button onClick={() => setIsNewSettingOpen(true)}>
              Add Setting
            </Button>
          </div>
          <div className={classes.tableContainer}>
            <div style={{ height: "calc(100vh - 180px)", width: "100%" }}>
              <Datagrid
                columns={columns}
                data={filteredConfigSettings}
                mode="client"
                initialPagination={{ pageIndex: 0, pageSize: 100 }}
                initialDensity="compact"
                initialSorting={[{ id: "name", desc: false }]}
                initialColumnVisibility={{ source: false }}
                isLoading={isLoading}
                enableSelection={true}
                rowSelection={rowSelection}
                onRowSelectionChange={setRowSelection}
                batchActions={[
                  {
                    label: "Delete",
                    icon: TrashCan,
                    onClick: () => {
                      const settingsToDelete = Object.keys(rowSelection)
                        .filter((objectKey) => rowSelection[objectKey] === true)
                        .map(
                          (rowIndex) =>
                            filteredConfigSettings[
                              rowIndex as unknown as number
                            ],
                        );
                      setSettingsToDelete(settingsToDelete);
                    },
                  },
                ]}
              />
            </div>
          </div>
        </Column>
      </Grid>
      <ViewAside
        className={classes.viewAside}
        isVisible={!!selectedSettingId || isNewSettingOpen}
      >
        {(selectedSettingId || isNewSettingOpen) && (
          <AppConfigSettingDetail
            settingId={selectedSettingId}
            onClose={() => {
              setSelectedSettingId(undefined);
              setIsNewSettingOpen(false);
            }}
          />
        )}
      </ViewAside>
      {settingsToDelete &&
        ReactDOM.createPortal(
          <Modal
            open={settingsToDelete !== undefined}
            onRequestClose={() => {
              setSettingsToDelete(undefined);
            }}
            passiveModal={true}
          >
            <ModalBody>
              <h1>
                {settingsToDelete?.length === 1
                  ? t("deleteSettings.modalTitle")
                  : t("deleteSettings.modalTitlePlural")}
              </h1>
              <br />
              <span>
                {t("deleteSettings.modalConfirmation")}{" "}
                {settingsToDelete?.length === 1
                  ? t("deleteSettings.modalConfirmationSingle")
                  : t("deleteSettings.modalConfirmationPlural")}
                ? {t("deleteSettings.modalWarning")}
                <br />
                <br />
                <table
                  style={{
                    width: "100%",
                    borderCollapse: "collapse",
                    marginTop: "1em",
                  }}
                >
                  <thead>
                    <tr>
                      <th
                        style={{
                          textAlign: "left",
                          borderBottom: "1px solid #ccc",
                          padding: "4px",
                        }}
                      >
                        {t("deleteSettings.tableHeaders.name")}
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {settingsToDelete?.map((setting, idx) => (
                      <tr key={setting.id ?? idx}>
                        <td
                          style={{
                            padding: "4px",
                            borderBottom: "1px solid #eee",
                          }}
                        >
                          {setting.name}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
                <br />
                <br />
              </span>
            </ModalBody>
            <ModalFooter>
              <Button
                kind="secondary"
                onClick={() => {
                  setSettingsToDelete(undefined);
                }}
              >
                Cancel
              </Button>
              <Button kind="danger" onClick={handleDeleteSettings}>
                Delete
              </Button>
            </ModalFooter>
          </Modal>,
          document.body,
        )}
    </div>
  );
};

export default AppConfigSettingsView;
