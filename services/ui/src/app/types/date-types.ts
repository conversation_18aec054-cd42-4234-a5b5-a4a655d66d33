/**
 * A date period is a predefined range of dates.
 */
export enum DatePeriod {
  today = "today",
  yesterday = "yesterday",
  thisWeek = "thisWeek",
  lastWeek = "lastWeek",
  thisMonth = "thisMonth",
  lastMonth = "lastMonth",
  thisYear = "thisYear",
  last7days = "last7days",
  last14days = "last14days",
  last30days = "last30days",
  last60days = "last60days",
}

export const allDateRangeOptions = [
  { value: DatePeriod.today, label: "Today" },
  { value: DatePeriod.yesterday, label: "Yesterday" },
  { value: DatePeriod.last7days, label: "Last 7 Days" },
  { value: DatePeriod.last14days, label: "Last 14 Days" },
  { value: DatePeriod.last30days, label: "Last 30 Days" },
  { value: DatePeriod.thisWeek, label: "This Week" },
  { value: DatePeriod.lastWeek, label: "Last Week" },
  { value: DatePeriod.thisMonth, label: "This Month" },
  { value: DatePeriod.lastMonth, label: "Last Month" },
];

export interface DatePeriodOption {
  id: DatePeriod;
  text: string;
  label: string;
}

/**
 * A date range is a range of dates.
 */
export type DateRange = {
  startDate: Date;
  endDate: Date;
};

/**
 * A combination of DatePeriod and DateRange to represent the date range
 * for a view.
 */
export type DatePeriodRange = DateRange | DatePeriod;
