import {
  ChartTypes,
  ComboChart,
  ComboChartOptions,
  ScaleTypes,
} from "@carbon/charts-react";
import "@carbon/charts/styles.css";
import { useTheme } from "@carbon/react";
import { ThemeMode } from "../../layout/theme";
import { toSiteDate } from "../../utils";
import styles from "./combo-chart-component.module.css";
import { formatCamelCaseToTitleCase } from "../../utils/string-util";
import { UnitUtil, type UnitType } from "../../utils/unit-util";
import { ComboChartComponentProps } from "./types";
import { useMeasure } from "@uidotdev/usehooks";
import { Logger } from "../../utils/logger";
import { getDashboardColorById } from "../echarts/utils/carbon-colors";
export const ComboChartComponent = ({
  chartData,
  title,
  chartStyle = "line",
  showAverageLine = false,
  showTargetLine = false,
  showLegend = true,
  targetValue = 0,
  timezone = "America/New_York",
  dateFormat = { month: "short", day: "numeric" },
  color,
}: ComboChartComponentProps) => {
  const { theme } = useTheme();
  const isDark = theme === ThemeMode.DARK;
  const [ref, { height }] = useMeasure();

  if (!chartData || chartData.series.length === 0) {
    return <div>No Chart Data Available</div>;
  }

  // Get the unit from the first series in the chart data
  // Assuming all series in the chart have the same unit
  const unit = chartData.series[0]?.unit as UnitType;

  const logger = new Logger("ComboChartComponent");

  const combinedData = chartData?.series?.flatMap((series) =>
    series.data.map((point) => {
      const siteDate = toSiteDate(point?.unit, timezone);
      const formattedDate = siteDate.toLocaleString(dateFormat);

      return {
        date: siteDate.isValid ? formattedDate : point?.unit,
        value: point?.value,
        group:
          formatCamelCaseToTitleCase(series.id) ||
          `Series ${chartData.series.indexOf(series)}`,
        unit: series.unit as UnitType, // Store the unit with each data point
      };
    }),
  );

  logger.debug("combinedData", combinedData);

  // Get all unique dataset identifiers
  const allDatasets = chartData.series.map(
    (series, index) =>
      formatCamelCaseToTitleCase(series.id) || `Series ${index}`,
  );

  // Determine the Carbon chart type based on our chart style
  let carbonChartType: ChartTypes;
  let requiresGroupedFormat = false;

  switch (chartStyle) {
    case "area":
      carbonChartType = ChartTypes.AREA;
      break;
    case "column":
      carbonChartType = ChartTypes.GROUPED_BAR;
      requiresGroupedFormat = true;
      break;
    case "stacked-column":
      carbonChartType = ChartTypes.STACKED_BAR;
      requiresGroupedFormat = true;
      break;
    case "stacked-row":
      carbonChartType = ChartTypes.STACKED_BAR;
      requiresGroupedFormat = true;
      break;
    case "stacked-area":
      carbonChartType = ChartTypes.STACKED_AREA;
      requiresGroupedFormat = true;
      break;
    default:
      carbonChartType = ChartTypes.LINE;
      break;
  }

  // For grouped or stacked formats, combine all datasets into a single chart type
  const comboChartTypes = requiresGroupedFormat
    ? [
        {
          type: carbonChartType,
          correspondingDatasets: allDatasets,
        },
      ]
    : chartData.series.map((series, index) => ({
        // For non-grouped formats, create individual chart types per series
        type: carbonChartType,
        correspondingDatasets: [
          formatCamelCaseToTitleCase(series.id) || `Series ${index}`,
        ],
      }));

  // Determine axis title
  const axisTitle = unit && UnitUtil.getUnitLabel(unit);

  // Build color scale object if color is provided
  let colorScale = {};

  if (color) {
    if (Array.isArray(color)) {
      colorScale = allDatasets.reduce((acc, datasetName) => {
        const resolvedColor = getDashboardColorById(
          datasetName.toLowerCase().replace(/\s+/g, ""),
          isDark,
        );
        return {
          ...acc,
          [datasetName]: resolvedColor ?? "#cccccc", // fallback color if not found
        };
      }, {});
    } else {
      colorScale = allDatasets.reduce((acc, datasetName) => {
        return {
          ...acc,
          [datasetName]: color,
        };
      }, {});
    }
  }

  // Calculate average from the first series if showAverageLine is true
  let averageValue: number | undefined;
  if (showAverageLine && chartData.series.length > 0) {
    const firstSeriesData = chartData.series[0].data;
    if (firstSeriesData.length > 0) {
      const sum = firstSeriesData.reduce(
        (total, point) => total + point.value,
        0,
      );
      averageValue = sum / firstSeriesData.length;
    }
  }

  // Create thresholds array for average and target lines
  const thresholds = [];

  if (showAverageLine && averageValue !== undefined) {
    thresholds.push({
      value: averageValue,
      label: `Average: ${UnitUtil.formatValue(averageValue, unit)}`,
      fillColor: isDark ? "#78a9ff" : "#0043ce", // Blue color for average line
    });
  }

  if (showTargetLine && targetValue !== undefined) {
    thresholds.push({
      value: targetValue,
      label: `Target: ${UnitUtil.formatValue(targetValue, unit)}`,
      fillColor: isDark ? "#ff8389" : "#da1e28", // Red color for target line
    });
  }

  const options: ComboChartOptions = {
    title: title,
    animations: true,
    toolbar: {
      enabled: false,
    },
    legend: {
      enabled: showLegend,
    },
    color: {
      scale: colorScale,
    },
    axes:
      chartStyle !== "stacked-row"
        ? {
            left: {
              mapsTo: "value",
              title: axisTitle,
              scaleType: ScaleTypes.LINEAR,
              includeZero: true,
              stacked:
                chartStyle === "stacked-column" ||
                chartStyle === "stacked-area",
              // Add thresholds if there are any
              thresholds: thresholds.length > 0 ? thresholds : undefined,
            },
            bottom: {
              mapsTo: "date",
              scaleType: ScaleTypes.LABELS,
              title: "Date",
            },
          }
        : {
            left: {
              mapsTo: "date",
              title: axisTitle,
              scaleType: ScaleTypes.LABELS,
              includeZero: true,
            },
            bottom: {
              stacked: true,
              mapsTo: "value",
            },
          },
    comboChartTypes: comboChartTypes,
    curve: "curveMonotoneX",
    height: height ? `${height - 5}px` : undefined,
    width: "100%",
    theme: isDark ? "g100" : "g10",
    data: {
      groupMapsTo: "group",
    },
    tooltip: {
      valueFormatter: (value: number | string, _label: string): string => {
        // Convert to number if it's a string
        const numericValue =
          typeof value === "string" ? parseFloat(value) : value;

        if (!Number.isFinite(numericValue)) return String(value);

        // Format with the unit using our utility
        return UnitUtil.formatValue(numericValue, unit, undefined, false);
      },
    },
  };

  // Create a key that includes all properties that should trigger a re-render
  const chartKey = JSON.stringify({
    chartStyle,
    color,
    showAverageLine,
    showTargetLine,
    targetValue,
    showLegend,
  });

  return (
    <div className={styles.chartContainer} ref={ref}>
      <ComboChart key={chartKey} data={combinedData} options={options} />
    </div>
  );
};

export default ComboChartComponent;
