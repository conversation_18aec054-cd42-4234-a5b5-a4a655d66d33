import { vi } from "vitest";
import { render, screen } from "../../../test-utils";
import {
  CategoryChartComponent,
  type DataQueryResult,
} from "./category-chart-component";
import { ThemeMode } from "../../layout/theme";

// Mock the Carbon Chart components
vi.mock("@carbon/charts-react", () => ({
  ComboChart: ({ data, options }: any) => (
    <div data-testid="combo-chart">
      <div data-testid="chart-data">{JSON.stringify(data)}</div>
      <div data-testid="chart-options">{JSON.stringify(options)}</div>
    </div>
  ),
  ChartTypes: {
    GROUPED_BAR: "grouped-bar",
    STACKED_BAR: "stacked-bar",
  },
  ScaleTypes: {
    LINEAR: "linear",
    LABELS: "labels",
  },
}));

const mockedUseTheme = vi.hoisted(() =>
  vi.fn(() => ({ theme: ThemeMode.LIGHT })),
);

vi.mock("@carbon/react", async () => {
  const actual = await vi.importActual("@carbon/react");
  return {
    ...actual,
    useTheme: mockedUseTheme,
    GlobalTheme: ({ children }: { children: React.ReactNode }) => children,
  };
});

describe("CategoryChartComponent", () => {
  const mockDataQueryResult: DataQueryResult = {
    type: "categorySeries",
    fields: [
      { name: "category", type: "string" },
      { name: "value1", type: "number", unit: "count" },
      { name: "value2", type: "number", unit: "count" },
    ],
    rows: [
      { category: "Category A", value1: 30, value2: 20 },
      { category: "Category B", value1: 70, value2: 40 },
      { category: "Category C", value1: 50, value2: 60 },
    ],
    validation: { isValid: true },
    metadata: {},
  };

  it("renders with default props", () => {
    render(<CategoryChartComponent dataQueryResult={mockDataQueryResult} />);

    // Check that combo chart is rendered
    expect(screen.getByTestId("combo-chart")).toBeInTheDocument();

    // Check data transformation
    const chartData = JSON.parse(
      screen.getByTestId("chart-data").textContent || "[]",
    );
    expect(chartData.length).toBe(6); // 3 categories × 2 value fields
    // Data is reversed by default for horizontal orientation
    expect(chartData[0].category).toBe("Category C");
    expect(chartData[0].value).toBe(50);
    expect(chartData[0].group).toBe("Value1");
  });

  it("renders message when no data is available", () => {
    const emptyDataQueryResult: DataQueryResult = {
      type: "categorySeries",
      fields: [],
      rows: [],
      validation: { isValid: false },
      metadata: {},
    };
    render(<CategoryChartComponent dataQueryResult={emptyDataQueryResult} />);
    expect(screen.getByText("No Chart Data Available")).toBeInTheDocument();
  });

  it("renders with custom title", () => {
    render(
      <CategoryChartComponent
        dataQueryResult={mockDataQueryResult}
        title="Custom Title"
      />,
    );

    const chartOptions = JSON.parse(
      screen.getByTestId("chart-options").textContent || "{}",
    );
    expect(chartOptions.title).toBe("Custom Title");
  });

  it("uses the g10 theme when light theme is active", () => {
    render(<CategoryChartComponent dataQueryResult={mockDataQueryResult} />);

    const chartOptions = JSON.parse(
      screen.getByTestId("chart-options").textContent || "{}",
    );
    expect(chartOptions.theme).toBe("g10");
  });

  it("uses the g100 theme when dark theme is active", () => {
    // Override the mock for this specific test
    mockedUseTheme.mockReturnValueOnce({ theme: ThemeMode.DARK });

    render(<CategoryChartComponent dataQueryResult={mockDataQueryResult} />);

    const chartOptions = JSON.parse(
      screen.getByTestId("chart-options").textContent || "{}",
    );
    expect(chartOptions.theme).toBe("g100");
  });

  it("renders with horizontal orientation", () => {
    render(
      <CategoryChartComponent
        dataQueryResult={mockDataQueryResult}
        orientation="horizontal"
      />,
    );

    const chartOptions = JSON.parse(
      screen.getByTestId("chart-options").textContent || "{}",
    );
    expect(chartOptions.axes.left.mapsTo).toBe("category");
    expect(chartOptions.axes.bottom.mapsTo).toBe("value");
  });
});
