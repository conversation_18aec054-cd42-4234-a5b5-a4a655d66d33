import {
  ChartTypes,
  ComboChart,
  ScaleTypes,
  type ComboChartOptions,
} from "@carbon/charts-react";
import "@carbon/charts/styles.css";
import { useTheme } from "@carbon/react";
import { useMeasure } from "@uidotdev/usehooks";
import { ThemeMode } from "../../layout/theme";
import { formatCamelCaseToTitleCase } from "../../utils/string-util";
import { UnitUtil, type UnitType } from "../../utils/unit-util";
import { ChartStyle } from "../combo-chart/types";
import styles from "./category-chart-component.module.css";

import { components } from "../../../../../../libs/shared/ict-sdk/generated/openapi-react-query";

export type DataQueryResult = components["schemas"]["DataQueryResult"];

export interface CategoryChartComponentProps {
  dataQueryResult: DataQueryResult;
  title?: string;
  height?: string;
  chartStyle?: ChartStyle;
  showLegend?: boolean;
  color?: string;
  orientation?: "horizontal" | "vertical";
  reverseData?: boolean;
}

export const CategoryChartComponent = ({
  dataQueryResult,
  title,
  height,
  chartStyle = "stacked-column",
  showLegend = true,
  color,
  orientation = "horizontal",
  reverseData,
}: CategoryChartComponentProps) => {
  const { theme } = useTheme();
  const isDark = theme === ThemeMode.DARK;
  const [ref, { height: containerHeight }] = useMeasure();

  if (
    !dataQueryResult ||
    !dataQueryResult.fields ||
    !dataQueryResult.rows ||
    dataQueryResult.rows.length === 0 ||
    !dataQueryResult.validation?.isValid
  ) {
    return <div>No Chart Data Available</div>;
  }

  // Find the category field (string field named "category" or the first string field)
  const categoryField =
    dataQueryResult.fields.find(
      (field) => field.type === "string" && field.name === "category",
    ) || dataQueryResult.fields[0];

  // Get all numeric fields that are not the category field
  const valueFields = dataQueryResult.fields.filter(
    (field) => field.name !== categoryField?.name && field.type === "number",
  );

  if (!categoryField || valueFields.length === 0) {
    return <div>Invalid Chart Data Structure</div>;
  }

  // Get the unit from the first value field
  const unit = valueFields[0]?.unit as UnitType;

  // Default reverseData to true for horizontal charts, false for vertical
  const shouldReverseData = reverseData ?? orientation === "horizontal";

  // Transform data for category-based charts
  let combinedData = valueFields.flatMap((field) =>
    dataQueryResult.rows.map((row) => ({
      category: String(row[categoryField?.name || "category"] || ""),
      value: Number(row[field.name]) || 0,
      group: formatCamelCaseToTitleCase(field.name) || field.name,
    })),
  );

  // Reverse data order if requested (useful for horizontal charts where index 0 should be at top)
  if (shouldReverseData) {
    // Get unique categories in their original order
    const uniqueCategories = Array.from(
      new Set(combinedData.map((item) => item.category)),
    );

    // Reverse the category order
    const reversedCategories = [...uniqueCategories].reverse();

    // Sort data to match reversed category order
    combinedData = combinedData.sort((a, b) => {
      const aIndex = reversedCategories.indexOf(a.category);
      const bIndex = reversedCategories.indexOf(b.category);
      return aIndex - bIndex;
    });
  }

  // Get all unique dataset identifiers for color scaling
  const allDatasets = valueFields.map(
    (field) => formatCamelCaseToTitleCase(field.name) || field.name,
  );

  // Determine the Carbon chart type based on chart style
  let carbonChartType: ChartTypes;
  switch (chartStyle) {
    case "column":
      carbonChartType = ChartTypes.GROUPED_BAR;
      break;
    case "stacked-column":
    default:
      carbonChartType = ChartTypes.STACKED_BAR;
      break;
  }

  // Build color scale object if color is provided
  let colorScale = {};
  if (color) {
    colorScale = allDatasets.reduce((acc, datasetName) => {
      return {
        ...acc,
        [datasetName]: color,
      };
    }, {});
  }

  // Determine axis configuration based on orientation
  const isHorizontal = orientation === "horizontal";
  const axisTitle = unit && UnitUtil.getUnitLabel(unit);

  const options: ComboChartOptions = {
    title: title,
    animations: true,
    toolbar: {
      enabled: false,
    },
    legend: {
      enabled: showLegend,
    },
    color: color
      ? {
          scale: colorScale,
        }
      : undefined,
    axes: isHorizontal
      ? {
          // Horizontal: categories on left (Y-axis), values on bottom (X-axis)
          left: {
            mapsTo: "category",
            scaleType: ScaleTypes.LABELS,
            title: "Category",
          },
          bottom: {
            mapsTo: "value",
            scaleType: ScaleTypes.LINEAR,
            includeZero: true,
            stacked: chartStyle === "stacked-column",
            title: axisTitle,
          },
        }
      : {
          // Vertical: values on left (Y-axis), categories on bottom (X-axis)
          left: {
            mapsTo: "value",
            scaleType: ScaleTypes.LINEAR,
            includeZero: true,
            stacked: chartStyle === "stacked-column",
            title: axisTitle,
          },
          bottom: {
            mapsTo: "category",
            scaleType: ScaleTypes.LABELS,
            title: "Category",
          },
        },
    comboChartTypes: [
      {
        type: carbonChartType,
        correspondingDatasets: allDatasets,
      },
    ],
    height: height || (containerHeight ? `${containerHeight - 5}px` : "400px"),
    width: "100%",
    theme: isDark ? "g100" : "g10",
    data: {
      groupMapsTo: "group",
    },
    tooltip: {
      valueFormatter: (value: number | string): string => {
        const numericValue =
          typeof value === "string" ? parseFloat(value) : value;

        if (!Number.isFinite(numericValue)) return String(value);
        return UnitUtil.formatValue(numericValue, unit, undefined, false);
      },
    },
  };

  // Create a key that includes all properties that should trigger a re-render
  const chartKey = JSON.stringify({
    chartStyle,
    color,
    showLegend,
    orientation,
    reverseData: shouldReverseData,
    dataHash: dataQueryResult.rows.length, // Simple hash to detect data changes
  });

  return (
    <div className={styles.chartContainer} ref={ref}>
      <ComboChart key={chartKey} data={combinedData} options={options} />
    </div>
  );
};

export default CategoryChartComponent;
