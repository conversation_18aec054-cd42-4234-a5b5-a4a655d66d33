import { useTheme } from "@carbon/react";
import type { EChartsOption, SeriesOption } from "echarts";
import type { MarkLine1DDataItemOption } from "echarts/types/src/component/marker/MarkLineModel";
import type { CallbackDataParams } from "echarts/types/src/util/types";
import ReactECharts from "echarts-for-react";
import { DateTime } from "luxon";
import { ThemeMode } from "../../../layout/theme";
import { toSiteDate } from "../../../utils";
import { formatCamelCaseToTitleCase } from "../../../utils/string-util";
import "../themes/dark-theme";
import { getColorByIndex } from "../utils/carbon-colors";
import styles from "./chart-component.module.css";
import { useTranslation } from "react-i18next";

export interface ChartDataPoint {
  unit: string; // x-axis: date/time
  value: number; // y-axis: value
}

export interface ChartSeries {
  data: ChartDataPoint[];
  unit?: string;
  id?: string; // Add id field for unique identification
}

export interface ChartData {
  series: ChartSeries[];
}

export interface ChartComponentProps {
  chartData: ChartData;
  chartStyle?:
    | "line"
    | "area"
    | "column"
    | "stacked-area"
    | "stacked-column"
    | "stacked-row";
  lineColor?: string | string[];
  dateFormat?: Intl.DateTimeFormatOptions;
  precision?: number;
  showUnit?: boolean;
  showAverageLine?: boolean;
  showTargetLine?: boolean;
  targetValue?: number;
  timezone?: string;
}

export const ChartComponent = ({
  chartData,
  chartStyle = "line",
  lineColor,
  dateFormat = { month: "short", day: "numeric" },
  precision = 2,
  showUnit = false,
  showAverageLine = false,
  showTargetLine = false,
  targetValue,
  timezone = "America/New_York",
}: ChartComponentProps) => {
  const { theme } = useTheme();
  const isDark = theme === ThemeMode.DARK;

  const { t } = useTranslation();

  if (!chartData || !chartData.series || chartData.series.length === 0) {
    return (
      <div>{t("chartComponent.noChartData", "No Chart Data Available")}</div>
    );
  }

  // Replace the single series data preparation with multiple series handling
  const formattedDatasets = chartData.series.map((series) => ({
    id: series.id,
    source: series.data.map((item) => [
      item.unit, // x-axis: date/time
      item.value, // y-axis: value
    ]),
  }));

  // Use the unit from first series as default if showing units
  const defaultUnitString = chartData.series[0]?.unit || "";

  // Calculate average if needed (using first series for now)
  let averageValue = null;
  if (showAverageLine && chartData.series[0]?.data.length > 0) {
    const sum = chartData.series[0].data.reduce(
      (acc, item) => acc + item.value,
      0,
    );
    averageValue = sum / chartData.series[0].data.length;
  }

  // Get color for a specific series index
  const getSeriesColor = (index: number): string => {
    // If lineColor is provided, use it first
    if (Array.isArray(lineColor) && lineColor[index]) {
      return lineColor[index];
    }
    if (typeof lineColor === "string" && index === 0) {
      return lineColor;
    }

    // Otherwise use Carbon colors
    return getColorByIndex(index, isDark);
  };

  // Configure base series styling
  const getSeriesConfig = (index: number) => {
    const isStacked =
      chartStyle === "stacked-area" || chartStyle === "stacked-column";
    const seriesColor = getSeriesColor(index);

    return {
      type:
        chartStyle === "column" || chartStyle === "stacked-column"
          ? "bar"
          : "line",
      smooth: true,
      datasetId: chartData.series[index].id,
      // Set stack property for stacked charts
      ...(isStacked && { stack: "total" }),
      lineStyle: {
        color: seriesColor,
        width: 2,
      },
      symbolSize: 6,
      itemStyle: {
        color: seriesColor,
      },
      // Add areaStyle for area charts
      ...((chartStyle === "area" || chartStyle === "stacked-area") && {
        areaStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: `${seriesColor}4D`, // 30% opacity
              },
              {
                offset: 1,
                color: "rgba(22, 119, 255, 0)",
              },
            ],
          },
          // For stacked area, we need solid colors with opacity
          ...(chartStyle === "stacked-area" && {
            opacity: 0.7,
            color: seriesColor,
          }),
        },
      }),
      encode: {
        x: 0,
        y: 1,
      },
    };
  };

  // Configure markLines for average and target
  const markLines: MarkLine1DDataItemOption[] = [];

  if (showAverageLine && averageValue !== null) {
    const formattedAvg = averageValue.toFixed(precision);
    const avgLabel = showUnit
      ? defaultUnitString.toLowerCase() === "percent"
        ? t("formatAvg.percentage", "Avg: {{avg}}%", { avg: formattedAvg })
        : t("formatAvg.defaultUnitString", "Avg: {{avg}} {{unit}}", {
            avg: formattedAvg,
            unit: defaultUnitString,
          })
      : t("formatAvg.noUnit", "Avg: {{avg}}", { avg: formattedAvg });

    markLines.push({
      name: "Average",
      yAxis: averageValue,
      label: {
        formatter: avgLabel,
        position: "insideEndTop",
        color: isDark ? "#fff" : "#000",
      },
      lineStyle: {
        type: "dashed",
        color: "#5470c6",
      },
    });
  }

  if (showTargetLine && targetValue !== undefined) {
    const formattedTarget = targetValue.toFixed(precision);
    const targetLabel = showUnit
      ? defaultUnitString.toLowerCase() === "percent"
        ? t("formatTarget.percentage", "Target: {{target}}%", {
            target: formattedTarget,
          })
        : t("formatTarget.defaultUnitString", "Target: {{target}} {{unit}}", {
            target: formattedTarget,
            unit: defaultUnitString,
          })
      : t("formatTarget.noUnit", "Target: {{target}}", {
          target: formattedTarget,
        });

    markLines.push({
      name: "Target",
      yAxis: targetValue,
      label: {
        formatter: targetLabel,
        position: "insideEndTop",
        color: isDark ? "#fff" : "#000",
      },
      lineStyle: {
        type: "dashed",
        color: "#91cc75",
      },
    });
  }

  // Update ECharts options
  const echartsOption: EChartsOption = {
    dataset: formattedDatasets,
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      top: 10,
      containLabel: true,
    },
    xAxis: {
      type: "category" as const,
      axisLabel: {
        formatter: (value: string) => {
          const date = toSiteDate(value, timezone);
          return date.toLocaleString(dateFormat);
        },
      },
    },
    yAxis: {
      type: "value" as const,
      axisLabel: {
        formatter: (value: number) => {
          if (showUnit && defaultUnitString.toLowerCase() === "percent") {
            return `${value}%`;
          }
          return String(value);
        },
      },
    },
    tooltip: {
      trigger: "axis",
      formatter: (params: unknown) => {
        const paramArray = Array.isArray(params) ? params : [params];
        if (!paramArray || paramArray.length === 0) return "";

        const firstParam = paramArray[0] as CallbackDataParams;
        if (!firstParam.value || !Array.isArray(firstParam.value)) return "";

        const date = toSiteDate(firstParam.value[0] as string, timezone);

        // For stacked charts, calculate total
        let total = 0;
        if (chartStyle === "stacked-area" || chartStyle === "stacked-column") {
          total = paramArray.reduce((sum: number, param: unknown) => {
            const typedParam = param as CallbackDataParams;
            if (!typedParam.value || !Array.isArray(typedParam.value))
              return sum;
            const value =
              typeof typedParam.value[1] === "number" ? typedParam.value[1] : 0;
            return sum + value;
          }, 0);
        }

        const valueLines = paramArray
          .map((param: unknown, index: number) => {
            const typedParam = param as CallbackDataParams;
            if (!typedParam.value || !Array.isArray(typedParam.value))
              return "";
            const value =
              typeof typedParam.value[1] === "number" ? typedParam.value[1] : 0;
            const formattedValue = value.toFixed(precision);
            const series = chartData.series[index];
            const unitStr = series?.unit || defaultUnitString;

            // Use the imported utility function instead
            const rawSeriesName = series?.id || `Series ${index + 1}`;
            const seriesName = formatCamelCaseToTitleCase(rawSeriesName);

            const seriesColor = getSeriesColor(index);

            let valueDisplay = formattedValue;
            if (showUnit) {
              valueDisplay =
                unitStr.toLowerCase() === "percent"
                  ? `${formattedValue}%`
                  : `${formattedValue} ${unitStr}`;
            }

            return `<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:${seriesColor};"></span> ${seriesName}: ${valueDisplay}`;
          })
          .join("<br/>");

        // Add total for stacked charts
        let result = `${date.toLocaleString(DateTime.DATETIME_SHORT)}<br/>${valueLines}`;
        if (
          (chartStyle === "stacked-area" || chartStyle === "stacked-column") &&
          paramArray.length > 1
        ) {
          const totalFormatted = total.toFixed(precision);
          let totalDisplay = totalFormatted;
          if (showUnit) {
            totalDisplay =
              defaultUnitString.toLowerCase() === "percent"
                ? t("formatTotal.percentage", "{{totalFormatted}}%", {
                    totalFormatted: totalFormatted,
                  })
                : t(
                    "formatTotal.defaultUnitString",
                    "{{totalFormatted}} {{defaultUnitString}}",
                    {
                      totalFormatted: totalFormatted,
                      defaultUnitString: defaultUnitString,
                    },
                  );
          }
          result += `<br/><b>${totalDisplay}</b>`;
        }

        return result;
      },
    },
    series: chartData.series.map((_, index) => {
      const config = getSeriesConfig(index);
      return {
        ...config,
        markLine:
          index === 0 && markLines.length > 0
            ? {
                data: markLines,
                animation: false,
                symbol: ["none", "none"],
              }
            : undefined,
      };
    }) as SeriesOption[],
  };

  return (
    <div className={styles.chartContainer}>
      <ReactECharts
        theme={isDark ? "dark_theme" : "light"}
        option={echartsOption}
        notMerge={true}
        lazyUpdate={true}
        style={{ height: "100%", width: "100%" }}
      />
    </div>
  );
};

export default ChartComponent;
