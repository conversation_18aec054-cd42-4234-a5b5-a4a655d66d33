import { useState, useRef, useCallback, useEffect } from "react";
import classes from "./view-aside.module.css";

type ViewAsideProps = {
  isVisible: boolean;
  children?: React.ReactNode;
  style?: React.CSSProperties;
  className?: string;
  minWidth?: number;
  maxWidth?: number;
  defaultWidth?: number;
};

export const ViewAside = ({
  isVisible,
  children,
  style = {},
  className = "",
  minWidth = 200,
  maxWidth = 1000,
  defaultWidth = 320,
}: ViewAsideProps) => {
  const [width, setWidth] = useState(style?.width || defaultWidth);
  const [isResizing, setIsResizing] = useState(false);
  const asideRef = useRef<HTMLElement>(null);

  const startResize = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    setIsResizing(true);
  }, []);

  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (!isResizing || !asideRef.current) return;

      const newWidth = window.innerWidth - e.clientX;

      // Constrain width within min/max bounds
      const constrainedWidth = Math.max(minWidth, Math.min(maxWidth, newWidth));
      setWidth(constrainedWidth);
    },
    [isResizing, minWidth, maxWidth],
  );

  const stopResize = useCallback(() => {
    setIsResizing(false);
  }, []);

  useEffect(() => {
    if (isResizing) {
      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", stopResize);
      document.body.style.cursor = "col-resize";
      document.body.style.userSelect = "none";
    } else {
      document.removeEventListener("mousemove", handleMouseMove);
      document.removeEventListener("mouseup", stopResize);
      document.body.style.cursor = "";
      document.body.style.userSelect = "";
    }

    return () => {
      document.removeEventListener("mousemove", handleMouseMove);
      document.removeEventListener("mouseup", stopResize);
      document.body.style.cursor = "";
      document.body.style.userSelect = "";
    };
  }, [isResizing, handleMouseMove, stopResize]);

  return (
    <aside
      ref={asideRef}
      data-testid="view-aside"
      className={`${classes.viewAside} ${className} ${isVisible ? classes.visible : classes.hidden} ${!isResizing ? classes.animateWidthTransition : ""}`}
      style={{ ...style, width: isVisible ? width : 0 }}
    >
      <div className={classes.content}>{children}</div>
      <div
        className={classes.resizeHandle}
        onMouseDown={startResize}
        data-testid="resize-handle"
      />
    </aside>
  );
};
