import { Select, SelectItem, TextInput } from "@carbon/react";
import { components } from "../../../../../../libs/shared/ict-sdk/generated/openapi-react-query";
import { DateTimeGranularityValues } from "@ict/sdk/types";

// Local type definition for DataQueryProperty until OpenAPI schema is updated
export type DataQueryProperty = components["schemas"]["DataQueryProperty"] & {
  groupableFields?: string[]; // Add groupableFields support for group_by type
};

interface DataQueryPropertyInputProps {
  property: DataQueryProperty;
  value?: string | number;
  onChange: (value: string | number) => void;
  disabled?: boolean;
}

export const DataQueryPropertyInput = ({
  property,
  value,
  onChange,
  disabled = false,
}: DataQueryPropertyInputProps) => {
  const currentValue = value ?? property.defaultValue;

  const handleChange = (newValue: string | number) => {
    onChange(newValue);
  };

  // Render appropriate input based on property type
  switch (property.type) {
    case "time_bucket":
      return (
        <Select
          id={`property-${property.id}`}
          labelText={`${property.id} (Time Granularity)`}
          value={String(currentValue)}
          onChange={(e) => handleChange(e.target.value)}
          disabled={disabled}
        >
          {DateTimeGranularityValues.map((value) => (
            <SelectItem key={value} value={value} text={value} />
          ))}
        </Select>
      );

    case "sort":
      return (
        <Select
          id={`property-${property.id}`}
          labelText={`${property.id} (Sort Order)`}
          value={String(currentValue)}
          onChange={(e) => handleChange(e.target.value)}
          disabled={disabled}
        >
          <SelectItem value="ASC" text="Ascending" />
          <SelectItem value="DESC" text="Descending" />
        </Select>
      );

    case "limit":
      return (
        <TextInput
          id={`property-${property.id}`}
          labelText={`${property.id} (Limit)`}
          type="number"
          value={String(currentValue)}
          onChange={(e) => {
            const numValue = parseInt(e.target.value) || 0;
            handleChange(numValue);
          }}
          disabled={disabled}
          helperText="Number limit for query results"
        />
      );

    case "group_by":
      return (
        <Select
          id={`property-${property.id}`}
          labelText={`${property.id} (Group By)`}
          value={String(currentValue)}
          onChange={(e) => handleChange(e.target.value)}
          disabled={
            disabled ||
            !property.groupableFields ||
            property.groupableFields.length === 0
          }
          helperText="Select a field to group by"
        >
          <SelectItem value="" text="Select a field..." />
          {property.groupableFields?.map((field) => (
            <SelectItem key={field} value={field} text={field} />
          )) || []}
        </Select>
      );

    default:
      return (
        <TextInput
          id={`unnamed-property`}
          labelText={`Unnamed Property`}
          value={String(currentValue)}
          onChange={(e) => handleChange(e.target.value)}
          disabled={disabled}
          helperText={`Property type: unknown`}
        />
      );
  }
};
