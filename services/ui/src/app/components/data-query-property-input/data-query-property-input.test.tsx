import "@testing-library/jest-dom";
import { screen, fireEvent } from "@testing-library/react";
import { render } from "../../../test-utils/render";
import {
  DataQueryPropertyInput,
  type DataQueryProperty,
} from "./data-query-property-input";

describe("DataQueryPropertyInput", () => {
  const mockOnChange = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("time_bucket property", () => {
    const timeBucketProperty: DataQueryProperty = {
      id: "time_granularity",
      type: "time_bucket",
      defaultValue: "DAY",
    };

    it("renders a select dropdown for time_bucket type", () => {
      render(
        <DataQueryPropertyInput
          property={timeBucketProperty}
          onChange={mockOnChange}
        />,
      );

      expect(screen.getByRole("combobox")).toBeInTheDocument();
      // Check that the select has the correct label text
      expect(
        screen.getByText("time_granularity (Time Granularity)"),
      ).toBeInTheDocument();
    });

    it("displays all time granularity options", () => {
      render(
        <DataQueryPropertyInput
          property={timeBucketProperty}
          onChange={mockOnChange}
        />,
      );

      const select = screen.getByRole("combobox");

      // Check options are available
      const options = select.querySelectorAll("option");
      const optionValues = Array.from(options).map(
        (option) => option.textContent,
      );

      expect(optionValues).toContain("DAY");
      expect(optionValues).toContain("WEEK");
      expect(optionValues).toContain("MONTH");
      expect(optionValues).toContain("YEAR");
    });

    it("calls onChange when time granularity is changed", () => {
      render(
        <DataQueryPropertyInput
          property={timeBucketProperty}
          onChange={mockOnChange}
        />,
      );

      const select = screen.getByRole("combobox");
      fireEvent.change(select, { target: { value: "MONTH" } });

      expect(mockOnChange).toHaveBeenCalledWith("MONTH");
    });

    it("uses provided value over default", () => {
      render(
        <DataQueryPropertyInput
          property={timeBucketProperty}
          value="YEAR"
          onChange={mockOnChange}
        />,
      );

      const select = screen.getByRole("combobox");
      // Check that YEAR is selected (even if display shows differently)
      expect(select).toHaveValue("YEAR");
    });
  });

  describe("sort property", () => {
    const sortProperty: DataQueryProperty = {
      id: "sort_direction",
      type: "sort",
      defaultValue: "ASC",
    };

    it("renders a select dropdown for sort type", () => {
      render(
        <DataQueryPropertyInput
          property={sortProperty}
          onChange={mockOnChange}
        />,
      );

      expect(screen.getByRole("combobox")).toBeInTheDocument();
      expect(
        screen.getByText("sort_direction (Sort Order)"),
      ).toBeInTheDocument();
    });

    it("displays ascending and descending options", () => {
      render(
        <DataQueryPropertyInput
          property={sortProperty}
          onChange={mockOnChange}
        />,
      );

      const select = screen.getByRole("combobox");
      const options = select.querySelectorAll("option");
      const optionValues = Array.from(options).map(
        (option) => option.textContent,
      );

      expect(optionValues).toContain("Ascending");
      expect(optionValues).toContain("Descending");
    });

    it("calls onChange when sort order is changed", () => {
      render(
        <DataQueryPropertyInput
          property={sortProperty}
          onChange={mockOnChange}
        />,
      );

      const select = screen.getByRole("combobox");
      fireEvent.change(select, { target: { value: "DESC" } });

      expect(mockOnChange).toHaveBeenCalledWith("DESC");
    });
  });

  describe("limit property", () => {
    const limitProperty: DataQueryProperty = {
      id: "result_limit",
      type: "limit",
      defaultValue: 100,
    };

    it("renders a number input for limit type", () => {
      render(
        <DataQueryPropertyInput
          property={limitProperty}
          onChange={mockOnChange}
        />,
      );

      const input = screen.getByRole("spinbutton");
      expect(input).toBeInTheDocument();
      expect(input).toHaveAttribute("type", "number");
      expect(input).toHaveDisplayValue("100");
      expect(screen.getByText("result_limit (Limit)")).toBeInTheDocument();
      expect(
        screen.getByText("Number limit for query results"),
      ).toBeInTheDocument();
    });

    it("calls onChange with number when limit is changed", () => {
      render(
        <DataQueryPropertyInput
          property={limitProperty}
          onChange={mockOnChange}
        />,
      );

      const input = screen.getByRole("spinbutton");
      fireEvent.change(input, { target: { value: "250" } });

      expect(mockOnChange).toHaveBeenCalledWith(250);
    });

    it("handles non-numeric input gracefully", () => {
      render(
        <DataQueryPropertyInput
          property={limitProperty}
          onChange={mockOnChange}
        />,
      );

      const input = screen.getByRole("spinbutton");
      fireEvent.change(input, { target: { value: "abc" } });

      expect(mockOnChange).toHaveBeenCalledWith(0);
    });

    it("uses provided numeric value", () => {
      render(
        <DataQueryPropertyInput
          property={limitProperty}
          value={500}
          onChange={mockOnChange}
        />,
      );

      expect(screen.getByDisplayValue("500")).toBeInTheDocument();
    });
  });

  describe("group_by property", () => {
    const groupByProperty: DataQueryProperty = {
      id: "group_field",
      type: "group_by",
      defaultValue: "category",
      groupableFields: ["category", "department", "location"],
    };

    it("renders a select for group_by type", () => {
      render(
        <DataQueryPropertyInput
          property={groupByProperty}
          onChange={mockOnChange}
        />,
      );

      const select = screen.getByRole("combobox");
      expect(select).toBeInTheDocument();
      expect(screen.getByText("group_field (Group By)")).toBeInTheDocument();
      expect(
        screen.getByText("Select a field to group by"),
      ).toBeInTheDocument();
    });

    it("calls onChange when group by field is changed", () => {
      render(
        <DataQueryPropertyInput
          property={groupByProperty}
          onChange={mockOnChange}
        />,
      );

      const select = screen.getByRole("combobox");
      fireEvent.change(select, { target: { value: "department" } });

      expect(mockOnChange).toHaveBeenCalledWith("department");
    });

    it("uses provided string value", () => {
      render(
        <DataQueryPropertyInput
          property={groupByProperty}
          value="location"
          onChange={mockOnChange}
        />,
      );

      const select = screen.getByRole("combobox");
      expect(select).toHaveValue("location");
    });
  });

  describe("disabled state", () => {
    const testProperty: DataQueryProperty = {
      id: "test_property",
      type: "sort",
      defaultValue: "ASC",
    };

    it("disables input when disabled prop is true", () => {
      render(
        <DataQueryPropertyInput
          property={testProperty}
          onChange={mockOnChange}
          disabled={true}
        />,
      );

      const input = screen.getByRole("combobox");
      expect(input).toBeDisabled();
    });

    it("enables input when disabled prop is false", () => {
      render(
        <DataQueryPropertyInput
          property={testProperty}
          onChange={mockOnChange}
          disabled={false}
        />,
      );

      const input = screen.getByRole("combobox");
      expect(input).not.toBeDisabled();
    });

    it("enables input by default when disabled prop is not provided", () => {
      render(
        <DataQueryPropertyInput
          property={testProperty}
          onChange={mockOnChange}
        />,
      );

      const input = screen.getByRole("combobox");
      expect(input).not.toBeDisabled();
    });
  });

  describe("edge cases", () => {
    it("handles unknown property type gracefully", () => {
      const unknownProperty = {
        id: "unknown_prop",
        type: "unknown" as any,
        defaultValue: "test",
      } as DataQueryProperty;

      render(
        <DataQueryPropertyInput
          property={unknownProperty}
          onChange={mockOnChange}
        />,
      );

      // Should render fallback text input
      const input = screen.getByRole("textbox");
      expect(input).toBeInTheDocument();
      expect(input).toHaveDisplayValue("test");
      expect(screen.getByText("Unnamed Property")).toBeInTheDocument();
      expect(screen.getByText("Property type: unknown")).toBeInTheDocument();
    });

    it("handles empty string values", () => {
      const emptyProperty: DataQueryProperty = {
        id: "empty_prop",
        type: "group_by",
        defaultValue: "",
        groupableFields: ["field1", "field2"],
      };

      render(
        <DataQueryPropertyInput
          property={emptyProperty}
          onChange={mockOnChange}
        />,
      );

      const select = screen.getByRole("combobox");
      expect(select).toHaveValue("");
    });

    it("handles undefined value by using default", () => {
      const testProperty: DataQueryProperty = {
        id: "test_prop",
        type: "sort",
        defaultValue: "DESC",
      };

      render(
        <DataQueryPropertyInput
          property={testProperty}
          value={undefined}
          onChange={mockOnChange}
        />,
      );

      const select = screen.getByRole("combobox");
      expect(select).toHaveValue("DESC");
    });
  });
});
