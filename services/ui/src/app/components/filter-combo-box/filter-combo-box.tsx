import { ComboBox, FilterableMultiSelect } from "@carbon/react";
import type { ReactElement } from "react";
import { forwardRef, useImperativeHandle } from "react";
import { components } from "../../../../../../libs/shared/ict-sdk/generated/openapi-react-query";
import { ictApi } from "../../api/ict-api";
import styles from "./filter-combo-box.module.css";

type DataQueryResponse = components["schemas"]["DataQueryResult"];

export interface SelectionEvent {
  /** ID of the filter that should handle this selection */
  filterId: string;
  /** The value that was selected/clicked */
  selectedValue: string;
  /** Optional action hint - defaults to 'toggle' */
  action?: "select" | "deselect" | "toggle";
  /** Optional metadata about the selection */
  metadata?: {
    /** Source widget type that generated this event */
    sourceWidget?: string;
    [key: string]: unknown;
  };
}

export interface FilterSelection {
  value: string | string[] | null;
  metadata: {
    isCategory: boolean;
  };
}

export interface FilterComboBoxProps {
  /** Start date for the filter query in ISO string format */
  startDate: string;
  /** End date for the filter query in ISO string format */
  endDate: string;
  /** Whether to allow multiple selections */
  allowMultiSelect: boolean;
  /** ID of the data query to fetch values for */
  dataQueryId: string;
  /** Callback function called when selection changes */
  onSelectionChange?: (selection: FilterSelection) => void;
  /** Currently selected value(s) */
  selectedValue?: string | string[] | null;
  /** Optional placeholder text */
  placeholder?: string;
  /** Optional title text */
  titleText?: string;
  /** Optional helper text */
  helperText?: string;
  /** Whether the component is disabled */
  disabled?: boolean;
  /** Size of the component */
  size?: "sm" | "md" | "lg";
  /** Custom className */
  className?: string;
}

/**
 * Methods available on FilterComboBox ref for external control
 *
 * These methods enable the event-driven cross-filter architecture by allowing
 * external components (like Dashboard) to control the FilterComboBox programmatically.
 */
export interface FilterComboBoxRef {
  /**
   * Handle a selection event from a widget
   *
   * This processes selection events from widgets and applies the appropriate
   * selection logic (toggle, select, deselect) based on the current state.
   */
  handleSelectionEvent: (event: SelectionEvent) => void;
  /** Get current selection values */
  getCurrentSelection: () => string | string[] | null;
  /** Clear all selections */
  clearSelection: () => void;
}

interface ComboBoxItem {
  id: string;
  text: string;
}

export const FilterComboBox = forwardRef<
  FilterComboBoxRef,
  FilterComboBoxProps
>(
  (
    {
      startDate,
      endDate,
      allowMultiSelect,
      dataQueryId,
      onSelectionChange,
      selectedValue,
      placeholder = "Select...",
      titleText,
      helperText,
      disabled = false,
      size = "md",
      className,
    },
    ref,
  ): ReactElement => {
    // Fetch filter values from the API
    const { data, error, isLoading } = ictApi.client.useQuery(
      "get",
      "/data/{dataQueryId}/value",
      {
        params: {
          path: { dataQueryId },
          query: {
            start_date: startDate,
            end_date: endDate,
          },
        },
      },
      {
        enabled: !!dataQueryId && !!startDate && !!endDate,
      },
    );

    // Process the API response to create items and extract metadata
    const { processedItems, isCategory } = (() => {
      if (!data) {
        return { processedItems: [], isCategory: false };
      }

      const responseData = data as DataQueryResponse;
      const value = responseData.rows;
      const metadata = responseData.metadata;
      const isCategoryFilter = Boolean(metadata?.isCategoryFilter);

      if (!value) {
        return { processedItems: [], isCategory: isCategoryFilter };
      }

      // Handle array of values
      if (Array.isArray(value)) {
        const items = value.map((item) => ({
          id: String(item?.value),
          text: String(item?.value),
        }));
        return { processedItems: items, isCategory: isCategoryFilter };
      }

      // Handle single value (string or number)
      const items = [
        {
          id: String(value),
          text: String(value),
        },
      ];
      return { processedItems: items, isCategory: isCategoryFilter };
    })();

    // Convert selectedValue to selected items
    const getSelectedItems = () => {
      if (!selectedValue || processedItems.length === 0) {
        return { selectedItem: null, selectedItems: [] };
      }

      if (allowMultiSelect) {
        // Handle multi-select case
        const valueArray = Array.isArray(selectedValue)
          ? selectedValue
          : [selectedValue];
        const selectedItems = processedItems.filter((item) =>
          valueArray.includes(item.id),
        );
        return { selectedItem: null, selectedItems };
      } else {
        // Handle single-select case
        const valueString = Array.isArray(selectedValue)
          ? selectedValue[0]
          : selectedValue;
        const selectedItem =
          processedItems.find((item) => item.id === valueString) || null;
        return { selectedItem, selectedItems: [] };
      }
    };

    const { selectedItem, selectedItems } = getSelectedItems();

    // Selection event handling logic
    const applySelectionLogic = (
      currentSelection: string | string[] | null,
      event: SelectionEvent,
    ): string | string[] | null => {
      const { selectedValue, action = "toggle" } = event;

      if (allowMultiSelect) {
        // Multi-select logic
        const currentArray = Array.isArray(currentSelection)
          ? currentSelection
          : currentSelection
            ? [currentSelection]
            : [];

        const isCurrentlySelected = currentArray.includes(selectedValue);

        switch (action) {
          case "select":
            return isCurrentlySelected
              ? currentArray
              : [...currentArray, selectedValue];
          case "deselect":
            return currentArray.filter((val) => val !== selectedValue);
          case "toggle":
          default:
            return isCurrentlySelected
              ? currentArray.filter((val) => val !== selectedValue)
              : [...currentArray, selectedValue];
        }
      } else {
        // Single-select logic
        const currentValue = Array.isArray(currentSelection)
          ? currentSelection[0]
          : currentSelection;
        const isCurrentlySelected = currentValue === selectedValue;

        switch (action) {
          case "select":
            return selectedValue;
          case "deselect":
            return isCurrentlySelected ? null : currentValue;
          case "toggle":
          default:
            return isCurrentlySelected ? null : selectedValue;
        }
      }
    };

    // Expose methods via ref
    useImperativeHandle(
      ref,
      () => ({
        handleSelectionEvent: (event: SelectionEvent) => {
          const newSelection = applySelectionLogic(
            selectedValue || null,
            event,
          );

          if (newSelection !== selectedValue) {
            const selection: FilterSelection = {
              value: newSelection,
              metadata: { isCategory: true }, // Selection events are typically for category filters
            };

            onSelectionChange?.(selection);
          }
        },

        getCurrentSelection: () => selectedValue || null,

        clearSelection: () => {
          const selection: FilterSelection = {
            value: null,
            metadata: { isCategory: true },
          };
          onSelectionChange?.(selection);
        },
      }),
      [selectedValue, allowMultiSelect, dataQueryId, onSelectionChange],
    );

    // Handle selection change for single select ComboBox
    const handleComboBoxChange = (data: {
      selectedItem: ComboBoxItem | null | undefined;
    }) => {
      const value = data.selectedItem ? data.selectedItem.id : null;
      const selection: FilterSelection = {
        value,
        metadata: {
          isCategory,
        },
      };
      onSelectionChange?.(selection);
    };

    // Handle selection change for multi-select FilterableMultiSelect
    const handleMultiSelectChange = ({
      selectedItems,
    }: {
      selectedItems: ComboBoxItem[];
    }) => {
      const valueArray = selectedItems.map((item) => item.id);
      const value = valueArray.length > 0 ? valueArray : null;
      const selection: FilterSelection = {
        value,
        metadata: {
          isCategory,
        },
      };
      onSelectionChange?.(selection);
    };

    // Show loading state
    if (isLoading) {
      return (
        <div className={`${styles.container} ${className || ""}`}>
          {allowMultiSelect ? (
            <FilterableMultiSelect
              id={`filter-combo-box-${dataQueryId}`}
              titleText={titleText}
              placeholder="Loading..."
              items={[]}
              itemToString={() => ""}
              onChange={() => {}}
              disabled={true}
              size={size}
              helperText={helperText}
            />
          ) : (
            <ComboBox
              id={`filter-combo-box-${dataQueryId}`}
              titleText={titleText}
              placeholder="Loading..."
              items={[]}
              itemToString={() => ""}
              onChange={() => {}}
              disabled={true}
              size={size}
              helperText={helperText}
            />
          )}
        </div>
      );
    }

    // Show error state
    if (error) {
      const errorMessage = "Failed to load filter values";

      return (
        <div className={`${styles.container} ${className || ""}`}>
          {allowMultiSelect ? (
            <FilterableMultiSelect
              id={`filter-combo-box-${dataQueryId}`}
              titleText={titleText}
              placeholder={errorMessage}
              items={[]}
              itemToString={() => ""}
              onChange={() => {}}
              disabled={true}
              size={size}
              helperText={helperText}
              invalid={true}
              invalidText={errorMessage}
            />
          ) : (
            <ComboBox
              id={`filter-combo-box-${dataQueryId}`}
              titleText={titleText}
              placeholder={errorMessage}
              items={[]}
              itemToString={() => ""}
              onChange={() => {}}
              disabled={true}
              size={size}
              helperText={helperText}
              invalid={true}
              invalidText={errorMessage}
            />
          )}
        </div>
      );
    }

    // Render the appropriate component based on allowMultiSelect
    return (
      <div className={`${styles.container} ${className || ""}`}>
        {allowMultiSelect ? (
          <FilterableMultiSelect
            id={`filter-combo-box-${dataQueryId}`}
            titleText={titleText}
            placeholder={placeholder}
            items={processedItems}
            itemToString={(item) => item?.text || ""}
            onChange={handleMultiSelectChange}
            initialSelectedItems={selectedItems}
            disabled={disabled || processedItems.length === 0}
            size={size}
            helperText={helperText}
            selectionFeedback="top-after-reopen"
          />
        ) : (
          <ComboBox
            id={`filter-combo-box-${dataQueryId}`}
            titleText={titleText}
            placeholder={placeholder}
            items={processedItems}
            itemToString={(item) => item?.text || ""}
            onChange={handleComboBoxChange}
            selectedItem={selectedItem}
            disabled={disabled || processedItems.length === 0}
            size={size}
            helperText={helperText}
          />
        )}
      </div>
    );
  },
);

// Add display name for debugging
FilterComboBox.displayName = "FilterComboBox";

export default FilterComboBox;
