/**
 * FilterComboBox component styles
 */

.container {
  width: 100%;
  min-width: 200px;
}

/* Ensure consistent styling with other form components */
.container :global(.cds--list-box) {
  max-width: 100%;
}

/* Loading state styles */
.container :global(.cds--list-box--disabled) .cds--list-box__field {
  cursor: not-allowed;
}

/* Error state styles */
.container :global(.cds--list-box--invalid) {
  /* Error styles are handled by Carbon Design System */
}
