import { render, screen } from "../../../test-utils";
import { FilterComboBox } from "./filter-combo-box";

// Mock the ictApi
vi.mock("../../api/ict-api", () => ({
  ictApi: {
    client: {
      useQuery: vi.fn(() => ({
        data: null,
        error: null,
        isLoading: false,
      })),
    },
  },
}));

describe("FilterComboBox", () => {
  const defaultProps = {
    startDate: "2024-01-01T00:00:00.000Z",
    endDate: "2024-01-31T23:59:59.999Z",
    allowMultiSelect: false,
    dataQueryId: "test-query-id",
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("renders single select ComboBox when allowMultiSelect is false", () => {
    render(<FilterComboBox {...defaultProps} />);

    // Look for the combobox element
    const combobox = screen.getByRole("combobox");
    expect(combobox).toBeInTheDocument();
  });

  it("renders FilterableMultiSelect when allowMultiSelect is true", () => {
    render(<FilterComboBox {...defaultProps} allowMultiSelect={true} />);

    // Look for the combobox element (FilterableMultiSelect also uses combobox role)
    const combobox = screen.getByRole("combobox");
    expect(combobox).toBeInTheDocument();
  });

  it("displays custom title text when provided", () => {
    const titleText = "Select Filter Value";
    render(<FilterComboBox {...defaultProps} titleText={titleText} />);

    expect(screen.getByText(titleText)).toBeInTheDocument();
  });

  it("displays custom placeholder when provided", () => {
    const placeholder = "Choose an option...";
    render(<FilterComboBox {...defaultProps} placeholder={placeholder} />);

    const combobox = screen.getByRole("combobox");
    expect(combobox).toHaveAttribute("placeholder", placeholder);
  });

  it("is disabled when disabled prop is true", () => {
    render(<FilterComboBox {...defaultProps} disabled={true} />);

    const combobox = screen.getByRole("combobox");
    expect(combobox).toBeDisabled();
  });
});
