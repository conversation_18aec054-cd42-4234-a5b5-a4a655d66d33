import { describe, it, expect } from "vitest";
import {
  dashboardSummaryInfo,
  dashboardSummaryDefinitions,
} from "./dashboard-resolver-data";

// Mock TFunction
const t = ((key: string) => `t(${key})`) as any;

describe("dashboardSummaryInfo", () => {
  it("should return the expected summary info array", () => {
    const result = dashboardSummaryInfo(t);

    expect(result).toHaveLength(1);
    expect(result[0]).toMatchObject({
      id: "summary-movements",
      label: "t(Movements Summary)",
      description: "High-level summary of movements",
      dms_ids: ["inventory"],
    });
  });

  it("should return an array", () => {
    const result = dashboardSummaryInfo(t);
    expect(Array.isArray(result)).toBe(true);
  });

  it("should include description and dms_ids", () => {
    const [info] = dashboardSummaryInfo(t);
    expect(info.description).toBe("High-level summary of movements");
    expect(info.dms_ids).toEqual(["inventory"]);
  });

  it("should not include unexpected properties", () => {
    const [info] = dashboardSummaryInfo(t);
    expect(info).not.toHaveProperty("foo");
    expect(info).not.toHaveProperty("bar");
  });
});

describe("dashboardSummaryDefinitions", () => {
  it("should contain the expected definition", () => {
    expect(dashboardSummaryDefinitions).toHaveLength(1);
    expect(dashboardSummaryDefinitions[0]).toMatchObject({
      id: "summary-movements",
      endpoint: "/movements/summary",
      unit: "movements",
    });
  });

  it("should map summary-movements to the correct endpoint", () => {
    const [def] = dashboardSummaryDefinitions;
    expect(def.id).toBe("summary-movements");
    expect(def.endpoint).toBe("/movements/summary");
  });

  it("should have the correct unit value", () => {
    const [def] = dashboardSummaryDefinitions;
    expect(def.unit).toBe("movements");
  });

  it("should not include unexpected properties", () => {
    const [def] = dashboardSummaryDefinitions;
    expect(def).not.toHaveProperty("extra");
  });
});
