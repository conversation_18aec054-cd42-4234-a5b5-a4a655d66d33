import { describe, it, expect, beforeEach, vi } from "vitest";
import { DashboardSummaryResolver } from "./dashboard-resolver";

const { fetchQueryMock, queryOptionsMock } = vi.hoisted(() => {
  return {
    fetchQueryMock: vi.fn(),
    queryOptionsMock: vi.fn((method, endpoint, opts) => ({
      method,
      endpoint,
      opts,
    })),
  };
});

// --- Mocks ---
vi.mock("../../../utils", () => ({
  Logger: vi.fn().mockImplementation(() => ({
    info: vi.fn(),
    error: vi.fn(),
  })),
}));

vi.mock("../../../config/i18n/i18n", () => ({
  default: { t: (key: string) => `t(${key})` },
  i18nInitPromise: Promise.resolve(),
}));

vi.mock("../../ict-api", () => ({
  ictApi: {
    client: { queryOptions: queryOptionsMock },
    queryClient: { fetchQuery: fetchQueryMock },
  },
}));

describe("DashboardSummaryResolver", () => {
  let resolver: DashboardSummaryResolver;

  const filters = {
    dms_id: "inventory",
    datePeriodRange: {
      startDate: new Date("2025-08-25T00:00:00Z"),
      endDate: new Date("2025-09-01T23:59:59Z"),
    },
    startDate: "2025-08-25T00:00:00Z",
    endDate: "2025-09-01T23:59:59Z",
  };

  const startDate = new Date("2025-01-01T00:00:00Z");
  const endDate = new Date("2025-01-31T23:59:59Z");

  beforeEach(() => {
    resolver = new DashboardSummaryResolver();
    vi.clearAllMocks();
  });

  it("should return translated summary info", async () => {
    const infos = await resolver.getSummaryInfo();
    expect(Array.isArray(infos)).toBe(true);
    expect(infos[0]).toHaveProperty("label");
  });

  it("should throw error if summary definition not found", async () => {
    await expect(
      resolver.getSummary(
        "summary-movements",
        { ...filters, dms_id: "" }, // missing dms_id
        new Date("2025-08-25T00:00:00Z"),
        new Date("2025-09-01T23:59:59Z"),
      ),
    ).rejects.toThrow(/'dms_id' is required/);
  });

  it("should return 204 if fetchQuery returns empty", async () => {
    fetchQueryMock.mockResolvedValueOnce([]);

    const result = await resolver.getSummary(
      "summary-movements",
      filters,
      startDate,
      endDate,
    );

    expect(result).toMatchObject({
      result: "204",
      id: "summary-movements",
    });
  });

  it("should return 200 with data if fetchQuery has values", async () => {
    fetchQueryMock.mockResolvedValueOnce([
      { name: "inbound", value: { totalMovements: 10, percentage: 60 } },
    ]);

    const result = await resolver.getSummary(
      "summary-movements",
      filters,
      startDate,
      endDate,
    );

    expect(result.result).toBe("200");

    if (result.result === "200") {
      expect(result.data).toEqual([
        { name: "inbound", value: { totalMovements: 10, percentage: 60 } },
      ]);
    }
  });

  it("should normalize response values to correct types", async () => {
    fetchQueryMock.mockResolvedValueOnce([
      { name: 123, value: { totalMovements: "20", percentage: "75" } },
    ]);

    const result = await resolver.getSummary(
      "summary-movements",
      filters,
      startDate,
      endDate,
    );

    expect(result.result).toBe("200");

    if (result.result === "200") {
      // Here we assume backend always returns already normalized numbers & strings correctly —
      // if you want type conversion, you'd do it in resolver
      expect(result.data).toEqual([
        { name: 123, value: { totalMovements: "20", percentage: "75" } },
      ]);
    }
  });

  it("should throw error if summary info not found", async () => {
    await expect(
      resolver.getSummary("invalid-id" as any, filters, startDate, endDate),
    ).rejects.toThrow(/Dashboard summary info not found/);
  });

  it("should call queryOptions with correct params in buildUrl", async () => {
    fetchQueryMock.mockResolvedValueOnce([]);

    await resolver.getSummary("summary-movements", filters, startDate, endDate);

    expect(queryOptionsMock).toHaveBeenCalledWith(
      "get",
      expect.anything(),
      expect.objectContaining({
        params: {
          query: expect.objectContaining({
            start_date: startDate.toISOString(),
            end_date: endDate.toISOString(),
            dms_id: "inventory",
          }),
        },
      }),
    );
  });

  it("should handle response with multiple records correctly", async () => {
    fetchQueryMock.mockResolvedValueOnce([
      { name: "inbound", value: { totalMovements: 15, percentage: 60 } },
      { name: "outbound", value: { totalMovements: 10, percentage: 40 } },
    ]);

    const result = await resolver.getSummary(
      "summary-movements",
      filters,
      startDate,
      endDate,
    );

    expect(result.result).toBe("200");
    if (result.result === "200") {
      expect(result.data).toHaveLength(2);
      expect(result.data[1]).toEqual({
        name: "outbound",
        value: { totalMovements: 10, percentage: 40 },
      });
    }
  });
});
