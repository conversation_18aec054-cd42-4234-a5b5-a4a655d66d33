import type { DashboardSummaryMapDefinition } from "./dashboard-resolver-types";
import type { DashboardSummaryInfo } from "./dashboard-resolver-types";
import { TFunction } from "i18next";

export const dashboardSummaryInfo: (t: TFunction) => DashboardSummaryInfo[] = (
  t,
) => [
  {
    id: "summary-movements",
    label: t("Movements Summary"),
    description: "High-level summary of movements",
    dms_ids: ["inventory"],
  },
];

export const dashboardSummaryDefinitions: DashboardSummaryMapDefinition[] = [
  {
    id: "summary-movements",
    endpoint: "/movements/summary",
    unit: "movements",
  },
];
