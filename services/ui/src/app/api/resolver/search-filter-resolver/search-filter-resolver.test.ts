import { vi } from "vitest";
import i18n from "../../../../test-utils/i18n-testing";
import { SearchFilterResolver } from "./search-filter-resolver";
import {
  searchFilterDefinitions,
  searchFilterInfo,
} from "./search-filter-resolver-data";

const startDate = new Date("2023-01-01");
const endDate = new Date("2023-01-31");

vi.mock("../../../config/i18n/i18n", () => ({
  __esModule: true,
  default: { t: (key: string, options?: any) => options?.defaultValue || key },
  i18nInitPromise: Promise.resolve(),
}));

vi.mock("../../ict-api", () => ({
  ictApi: {
    client: {
      queryOptions: vi.fn().mockReturnValue({
        queryKey: ["test-key"],
      }),
    },
    queryClient: {
      fetchQuery: vi.fn(),
    },
  },
}));

vi.mock("../../../utils", () => ({
  Logger: class {
    info = vi.fn();
    warn = vi.fn();
  },
}));

vi.mock("../../hooks/use-dates", () => ({
  useDates: () => ({
    startDate,
    endDate,
  }),
}));

describe("SearchFilterResolver", () => {
  let filterResolver: SearchFilterResolver;

  beforeEach(() => {
    vi.clearAllMocks();
    filterResolver = new SearchFilterResolver();
  });

  describe("getFilter", () => {
    it("should return groupBy values if definition has groupBy", async () => {
      // Assume the first entry in searchFilterDefinitions and searchFilterInfo match by id
      const filterId = searchFilterDefinitions.find((d) => d.groupBy)
        ?.id as any;
      const info = searchFilterInfo(i18n.t).find((i) => i.id === filterId);

      const result = await filterResolver.getFilter(filterId);

      expect(result.result).toBe("200");
      expect(result.id).toBe(filterId);
      expect(result.label).toBe(info?.label);
      expect(result.value).toEqual(
        searchFilterDefinitions.find((d) => d.id === filterId)?.groupBy,
      );
    });

    it("should throw if filter info not found", async () => {
      await expect(
        filterResolver.getFilter("not-found" as any),
      ).rejects.toThrow("Search Filter info not found for id: not-found");
    });

    it("should throw if definition not found", async () => {
      await expect(
        filterResolver.getFilter("non-existent" as any),
      ).rejects.toThrow("Search Filter info not found for id: non-existent");
    });
  });

  describe("getSearchFilterInfo", () => {
    it("should return filter info", async () => {
      const t = i18n.t;
      const result = await filterResolver.getSearchFilterInfo();
      expect(Array.isArray(result)).toBe(true);
      expect(result).toEqual(searchFilterInfo(t));
    });
  });
});
