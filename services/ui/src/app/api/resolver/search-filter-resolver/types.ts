import { SearchFilterTypes } from "./search-filter-types";

/**
 * Search Filter allowed value types.
 */
export type SearchFilterValue = string[];

/**
 * Information used to render the metric kpi data in the widget.
 */
export interface SearchFilter {
  id: SearchFilterTypes;
  label: string;
  value: SearchFilterValue;
}

export interface SearchFilterInfo {
  id: SearchFilterTypes;
  label: string;
  description: string;
  enableSearch?: boolean;
  filters?: string[];
  apiKey?: string;
  apiSearchKey?: string;
}

/**
 * Information needed to render a metric kpi when no data is available.
 */
export type SearchFilterNoDataResponse = SearchFilter & {
  result: "204";
};

/**
 * Information needed to render a metric kpi when data is available.
 */
export type SearchFilterDataResponse = SearchFilter & {
  result: "200";
};

/**
 *  Search Filter response
 */
export type SearchFilterResponse =
  | SearchFilterNoDataResponse
  | SearchFilterDataResponse;
