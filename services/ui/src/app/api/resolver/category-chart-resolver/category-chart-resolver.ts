import type { PathsWithMethod } from "openapi-typescript-helpers";
import { Logger } from "../../../utils";
import type { ApiFilters } from "../../api.types";
import { ictApi } from "../../ict-api";
import type { paths } from "@ict/sdk/openapi-react-query";
import {
  categoryChartDefinitions,
  categoryChartInfo,
} from "./category-chart-resolver-data";
import type {
  CategoryChartMapDefinition,
  CategoryChartSeriesData,
  ChartData,
  ChartInfo,
  ChartSeriesDataResponse,
} from "./category-chart-resolver-types";
import type { CategoryChartType } from "./category-chart-types";

export class ChartResolver {
  private logger;
  constructor() {
    this.logger = new Logger("ChartResolver");
  }

  private buildQueryParams(
    startDate: Date,
    endDate: Date,
    filters: ApiFilters,
  ): Record<string, unknown> {
    const queryParams: Record<string, unknown> = {
      start_date: startDate.toISOString(),
      end_date: endDate.toISOString(),
    };

    // Add groupBy if it exists in filters
    if (filters.groupBy && typeof filters.groupBy === "string") {
      queryParams.group_by_column = filters.groupBy;
      queryParams.dms_id = filters.dmsId;
      queryParams.unit_type = filters.unit_type;
      queryParams.unit_id = filters.unit_id;
    }

    if (
      filters.unit_type &&
      (filters.groupBy === "loadunit" || filters.groupBy === "sku")
    ) {
      queryParams.group_by_column = filters.unit_type;
    }

    return queryParams;
  }

  public async getCategoryChart(
    id: CategoryChartType,
    filters: ApiFilters,
    startDate: Date,
    endDate: Date,
  ): Promise<ChartSeriesDataResponse> {
    this.logger.info("ChartResolver::getChart", id);
    const info = categoryChartInfo.find((info) => info.id === id);
    if (!info) {
      throw new Error(`Chart info not found for id: ${id}`);
    }

    const definition = categoryChartDefinitions.find(
      (definition) => definition.id === id,
    );
    this.logger.info("ChartResolver", definition);
    if (!definition) {
      throw new Error(`Metric definition not found for id: ${id}`);
    }

    const url = definition.endpoint;
    const queryParams = this.buildQueryParams(
      startDate,
      endDate,
      filters,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    ) as any;

    const clientUrl = ictApi.client.queryOptions(
      "get",
      url as PathsWithMethod<paths, "get">,
      {
        params: { query: queryParams },
      },
    );

    this.logger.info("ChartResolver api POST request", url, queryParams);
    const response = await ictApi.queryClient.fetchQuery(clientUrl);

    this.logger.info("ChartResolver::getChart - response ", response);

    // Error response
    // if (!response.success) return { success: false, error: response.error };

    // // no data response
    if (!response) return { success: true, data: null };

    // For request-body type, use the category data conversion
    const categoryData = this.convertToCategoryResponse(response, definition);

    const chartData: ChartData = {
      id: id,
      series: categoryData,
    };

    this.logger.info("ChartResolver::getChart - seriesData ", categoryData);

    const res: ChartSeriesDataResponse = {
      success: true,
      data: chartData,
    };

    this.logger.info("ChartResolver::getChart - res ", res);

    // data response
    return res;
  }

  public async getCategoryChartInfo(): Promise<ChartInfo[]> {
    return categoryChartInfo;
  }

  /**
   * Converts API response to category data format
   * Used for category-based charts with request-body type
   */
  private convertToCategoryResponse(
    response: unknown,
    definition: CategoryChartMapDefinition,
  ): CategoryChartSeriesData[] {
    this.logger.info("convertToCategoryResponse", response);

    const data: CategoryChartSeriesData[] = [];

    // Type guard to ensure response is an object
    if (!response || typeof response !== "object") {
      this.logger.warn("Invalid response format - not an object");
      return [];
    }

    const seriesId = definition.series[0].id;
    const series = (response as Record<string, unknown>)[seriesId];

    if (!Array.isArray(series)) {
      this.logger.warn("Invalid series data format");
      return [];
    }

    // Handle the first shape where we have multiple series in an object
    if (
      series.length === 1 &&
      typeof series[0] === "object" &&
      !("name" in series[0])
    ) {
      const seriesObject = series[0];
      for (const [key, points] of Object.entries(seriesObject)) {
        data.push({
          id: key,
          unit: definition.unit,
          data: (points as { name: string; value: number }[]).map((point) => ({
            unit: point.name,
            value: point.value,
          })),
        });
      }
      return data;
    }

    // Handle the second shape where we have a single series of points
    data.push({
      id: seriesId,
      unit: definition.unit,
      data: series.map((point) => ({
        unit: point.name,
        value: point.value,
      })),
    });

    return data;
  }
}

export const categoryChartResolver = new ChartResolver();
