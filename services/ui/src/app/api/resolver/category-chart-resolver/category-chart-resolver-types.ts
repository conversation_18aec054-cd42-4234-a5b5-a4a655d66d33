import type {
  CategoryChartEndpoint,
  CategoryChartType,
} from "./category-chart-types";

/**
 * This is the data structure we get back from the api for all
 * series data type endpoint.
 */
export type ApiSeriesDataResponse = {
  name: string;
  value: number;
};
export type ApiSeriesResponse = {
  [key: string]: ApiSeriesDataResponse[];
};

/**
 * series data from the api.
 */
export type CategoryChartSeriesDataPoints = {
  unit: string;
  value: number;
};

/**
 * definition for a single trend data set.
 */
export type CategoryChartSeriesData = {
  id: string;
  unit: string;
  data: CategoryChartSeriesDataPoints[];
};

/**
 *  definition for time series data.
 */
export type TimeChartSeries = {
  /**
   * The series id from the api.
   */
  id: string;
  data: CategoryChartSeriesDataPoints[];
};

/**
 * Category data point for category-based charts
 */
export type CategoryDataPoint = {
  name: string;
  value: number;
};

export type ChartData = CategoryChartData;

/**
 * API response type that can contain either time series or category chart data
 */
export type ChartSeriesDataResponse = ApiResult<ChartData>;

export type ChartSeriesDefinition = {
  id: string;
};

export type CategoryChartMapDefinition = {
  id: CategoryChartType;
  endpoint: CategoryChartEndpoint;
  unit: string;
  series: ChartSeriesDefinition[];
};

/**
 * Category chart data structure for charts that use categories instead of time series
 */
export type CategoryChartData = {
  id: CategoryChartType;
  series: CategoryChartSeriesData[];
};

/**
 * Chart information
 */
export type ChartInfo = {
  id: CategoryChartType;
  title: string;
  description: string;
  groupBy?: string[];
  dmsId?: string[];
};

export interface SuccessResponse<T> {
  success: true;
  data: T;
}

interface NoData {
  success: true;
  data: null;
}

interface ErrorResult {
  success: false;
  error: Error;
}

export type ApiResult<T> = SuccessResponse<T> | NoData | ErrorResult;
