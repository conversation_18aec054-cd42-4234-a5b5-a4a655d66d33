import { describe, expect, it } from "vitest";
import {
  categoryChartDefinitions,
  categoryChartInfo,
} from "./category-chart-resolver-data";

describe("time-chart-resolver-data", () => {
  describe("categoryChartInfo", () => {
    it("should contain faults chart info", () => {
      expect(categoryChartInfo).toBeInstanceOf(Array);
      const faultsInfo = categoryChartInfo.find(
        (info) => info.id === "dms-movements-by-day",
      );
      expect(faultsInfo).toBeDefined();
    });
  });

  describe("categoryChartDefinitions", () => {
    it("should contain faults chart definition", () => {
      expect(categoryChartDefinitions).toBeInstanceOf(Array);
      const faultsDefinition = categoryChartDefinitions.find(
        (def) => def.id === "dms-movements-by-day",
      );
      expect(faultsDefinition).toBeDefined();
    });
  });
});
