import { ApiEndpoint, ApiRoute } from '@ui-adk/types/api-routes';
import { components } from '@ict/sdk/sdk-types';

export type InventoryForecastTestData<TData> = {
  data: TData;
  endpoint: ApiEndpoint;
};

export default class InventoryForecastMockData {
  public static List(): InventoryForecastTestData<components['schemas']['InventoryForecastListingData']> {
    return {
      data: {
        data: [
          {
            sku: 'DUB1005DBM',
            current: {
              reserveStorage: 1625,
              forwardPick: 18,
            },
            projected: {
              pendingReplenishment: 0,
              pendingPicks: 0,
              allocatedOrders: 0,
              projectedForwardPick: 18,
            },
            forecast: {
              averageReplenishment: 16.133333333333333,
              averageDemand: 257,
              demandTomorrow: 719,
              knownDemand: 0,
              forwardPickTomorrow: -701,
              twoDayDemand: 0,
              twoDayForwardPick: -701,
            },
          },
        ],
        metadata: {
          page: 0,
          limit: 50,
          totalResults: 1,
        },
      },
      endpoint: {
        path: 'inventory/forecast/list',
      },
    };
  }

  public static Locations(sku: string): InventoryForecastTestData<components['schemas']['InventoryForecastSkuLocationAreas']> {
    return {
      data: {
        data: [
          {
            area: 'Reserve Storage',
            details: [
              {
                locationId: 'CNV122503',
                locationType: 'BULK',
                containerId: 'BCV992',
                containerCount: 1,
                quantity: 96,
                containerQuantity: 96,
                skuSize: 'NS',
                conditionCode: 'UNRESTRICTED',
                zone: 'CNVZ',
                lastActivityDate: '2025-02-11T13:13:06',
              },
            ],
          },
          {
            area: 'Forward Pick',
            details: [
              {
                locationId: 'M11-GTP-04',
                locationType: 'STAGE',
                containerId: 'BA004176',
                containerCount: 1,
                quantity: 6,
                skuSize: 'NS',
                conditionCode: 'UNRESTRICTED',
                zone: 'GTP04',
                lastActivityDate: '2025-04-21T06:21:22',
              },
            ],
          },
        ],
      },
      endpoint: {
        path: `inventory/forecast/${sku}/locations` as ApiRoute,
      },
    };
  }

  public static Forecasting(sku: string): InventoryForecastTestData<components['schemas']['InventorySkuForecastDetails']> {
    return {
      data: {
        confidence: 159.772,
        knownDemand: {
          quantity: 0,
          percentage: 0,
        },
        shortTermDaily: 2565.2923,
        shortTermDailyDays: 88,
        longTermDaily: 1150.7915,
        longTermDailyDays: 360,
        nonZeroDemand: 98.4615,
        zeroDemandIntermittency: 1.3594,
        predictedDemandSeries: [
          {
            name: '2025-03-01T00:00:00.000Z',
            value: 0,
          },
          {
            name: '2025-03-02T00:00:00.000Z',
            value: 50,
          },
          {
            name: '2025-03-03T00:00:00.000Z',
            value: 100,
          },
          {
            name: '2025-03-04T00:00:00.000Z',
            value: 250,
          },
          {
            name: '2025-03-05T00:00:00.000Z',
            value: 200,
          },
          {
            name: '2025-03-06T00:00:00.000Z',
            value: 150,
          },
          {
            name: '2025-03-07T00:00:00.000Z',
            value: 260,
          },
        ],
        actualDemandSeries: [
          {
            name: '2025-03-01T00:00:00.000Z',
            value: 0,
          },
          {
            name: '2025-03-02T00:00:00.000Z',
            value: 10,
          },
          {
            name: '2025-03-03T00:00:00.000Z',
            value: 12,
          },
          {
            name: '2025-03-04T00:00:00.000Z',
            value: 16,
          },
          {
            name: '2025-03-05T00:00:00.000Z',
            value: 11,
          },
          {
            name: '2025-03-06T00:00:00.000Z',
            value: 8,
          },
          {
            name: '2025-03-07T00:00:00.000Z',
            value: 19,
          },
        ],
        confidenceLowSeries: [
          {
            name: '2025-03-01T00:00:00.000Z',
            value: 0,
          },
          {
            name: '2025-03-02T00:00:00.000Z',
            value: 50,
          },
          {
            name: '2025-03-03T00:00:00.000Z',
            value: 100,
          },
          {
            name: '2025-03-04T00:00:00.000Z',
            value: 250,
          },
          {
            name: '2025-03-05T00:00:00.000Z',
            value: 200,
          },
          {
            name: '2025-03-06T00:00:00.000Z',
            value: 150,
          },
          {
            name: '2025-03-07T00:00:00.000Z',
            value: 260,
          },
        ],
        confidenceHighSeries: [
          {
            name: '2025-03-01T00:00:00.000Z',
            value: 0,
          },
          {
            name: '2025-03-02T00:00:00.000Z',
            value: 50,
          },
          {
            name: '2025-03-03T00:00:00.000Z',
            value: 100,
          },
          {
            name: '2025-03-04T00:00:00.000Z',
            value: 250,
          },
          {
            name: '2025-03-05T00:00:00.000Z',
            value: 200,
          },
          {
            name: '2025-03-06T00:00:00.000Z',
            value: 150,
          },
          {
            name: '2025-03-07T00:00:00.000Z',
            value: 260,
          },
        ],
      },
      endpoint: {
        path: `inventory/forecast/${sku}` as ApiRoute,
      },
    };
  }

  public static Orders(sku: string): InventoryForecastTestData<components['schemas']['InventoryForecastSkuOrders']> {
    return {
      data: {
        skuId: 'TBN5002BKOSFA',
        openOrderCount: 12,
        data: [
          {
            skuId: 'TBN5002BKOSFA',
            orderId: '0008108099',
            priority: '5',
            allocationDate: '2025-04-22T07:10:08',
            shipDate: '2025-04-22T00:00:00',
            orderLines: 5,
            allocatedQty: 6,
          },
        ],
      },
      endpoint: {
        path: `inventory/forecast/${sku}/orders` as ApiRoute,
      },
    };
  }
}
