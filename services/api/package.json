{"name": "control-tower-api", "version": "0.0.1", "description": "Monorepo for control tower api", "private": true, "workspaces": {"packages": ["apps/api/*", "libs/api/*", "libs/shared/*"]}, "type": "module", "engineStrict": true, "engines": {"node": ">=22.0.0"}, "resolutions": {"openapi-backend": "5.12.0", "openapi-client-axios-typegen": "7.6.2", "@types/express": "4.17.23"}, "scripts": {"setup": "yarn install", "reset": "nx reset", "build": "nx run-many --target=build", "spec": "nx run-many --target=spec", "lint": "nx run-many --target=lint", "test": "nx run-many --target=test --exclude=test-suite", "smoke-test-suite": "nx run test-suite:test", "clean": "npx rimraf --glob **/node_modules **/build", "terraform-lint": "cd infrastructure && terraform fmt --recursive", "merge-spec": "yarn spec && npx openapi-merge-cli --config ./docs/ict-openapi-config.json", "generate-sdk": "yarn build && yarn merge-spec && yarn workspace @ict/sdk-foundations generate", "publish-sdk": "yarn workspace @ict/sdk-foundations publish-sdk", "postinstall": "node -e \"process.env.NO_POSTINSTALL===1&&process.exit(1)\" || husky install", "watch": "nx run-many --target=watch --parallel=20 --output-style=stream --exclude mock-data-api ict-tableau-management-api", "ai:watch": "nx run ict-ai-api:watch", "admin:watch": "nx run ict-admin-api:watch", "availability:watch": "nx run ict-availability-api:watch", "data:watch": "nx run ict-data-api:watch", "config:watch": "nx run ict-config-api:watch", "dataexplorer:watch": "nx run ict-dataexplorer-api:watch", "diagnostics:watch": "nx run ict-diagnostics-api:watch", "equipment:watch": "nx run ict-equipment-api:watch", "inventory:watch": "nx run ict-inventory-api:watch", "movement:watch": "nx run ict-movement-api:watch", "operators:watch": "nx run ict-operators-api:watch", "orders:watch": "nx run ict-orders-api:watch", "workstation:watch": "nx run ict-workstation-api:watch", "tableau:watch": "nx run ict-tableau-proxy:watch", "mock:watch": "nx run mock-data-api:watch", "db:setup": "./setup-local-db.sh dev", "db:reset": "./setup-local-db.sh dev true", "db:generate": "cd libs/api/ict-api-schema && yarn db:build-insert && yarn db:build-insert-process-flow", "db:help": "./setup-local-db.sh --help"}, "repository": "**************:dematic/controltower/control-tower.git", "author": "", "license": "ISC", "devDependencies": {"@apidevtools/swagger-parser": "^12.0.0", "@nrwl/cli": "15.9.7", "@nx/esbuild": "18.3.5", "@nx/jest": "18.3.5", "@nx/js": "18.3.5", "@nx/node": "18.3.5", "@open-wc/eslint-config": "13.0.0", "@swc-node/register": "1.10.10", "@swc/core": "1.12.1", "@swc/helpers": "0.5.17", "@types/chai": "5.2.2", "@types/chai-as-promised": "7.1.8", "@types/chai-http": "4.2.4", "@types/express": "4.17.23", "@types/express-http-proxy": "1.6.7", "@types/fast-redact": "3.0.4", "@types/jest": "30.0.0", "@types/luxon": "3.7.1", "@types/mocha": "10.0.10", "@types/node": "22.15.31", "@types/node-fetch": "^2.6.13", "@types/semver": "7.7.0", "@types/shelljs": "0.8.17", "@types/sinon": "17.0.4", "@types/supertest": "6.0.3", "@types/uuid": "10.0.0", "@typescript-eslint/eslint-plugin": "8.44.0", "chai-as-promised": "7.1.2", "chai-http": "4.4.0", "eslint": "9.35.0", "eslint-config-prettier": "10.1.8", "eslint-plugin-import": "^2.30.0", "eslint-plugin-n": "15.0.0", "eslint-plugin-prettier": "5.5.4", "eta": "3.5.0", "gts": "5.3.1", "husky": "8.0.3", "jest": "30.1.3", "jest-environment-node": "30.1.2", "lint-staged": "14.0.1", "mocha": "10.8.2", "mocha-junit-reporter": "2.2.1", "multer": "1.4.5-lts.2", "npm-run-all2": "5.0.2", "nx": "18.3.5", "openapi-client-axios-typegen": "7.7.0", "openapi-merge-cli": "1.3.2", "openapi-typescript": "7.6.1", "pino-pretty": "10.3.1", "prettier": "3.6.2", "semver": "7.7.2", "shelljs": "0.10.0", "sonar-scanner": "3.1.0", "ts-jest": "29.4.1", "yaml": "1.10.2", "yargs": "16.2.0"}, "packageManager": "yarn@4.7.0", "dependencies": {"@google-cloud/bigquery": "^8.1.1", "@google-cloud/pubsub": "4.8.0", "@google-cloud/secret-manager": "^6.0.0", "@google-cloud/storage": "^6.12.0", "@ict/sdk-foundations": "workspace:^", "@opentelemetry/api": "^1.9.0", "@opentelemetry/auto-instrumentations-node": "^0.52.1", "@types/handlebars-helpers": "^0.5.6", "@typescript-eslint/eslint-plugin": "^8.44.0", "@typescript-eslint/parser": "^8.44.0", "ajv": "^8.16.0", "axios": "^1.9.0", "chai": "4.5.0", "class-validator": "^0.14.1", "csv-parser": "^3.2.0", "dotenv-flow": "^4.1.0", "esbuild": "^0.25.0", "esbuild-plugin-tsc": "^0.5.0", "eta": "^3.4.0", "express": "^4.19.2", "express-http-context": "^2.0.1", "express-http-proxy": "^2.0.0", "express-oauth2-jwt-bearer": "^1.6.0", "express-validator": "^7.1.0", "fast-redact": "^3.5.0", "google-auth-library": "^9.15.1", "gts": "^5.3.1", "handlebars": "^4.7.8", "handlebars-helpers": "^0.10.0", "ict-api-foundations": "^1.0.0", "ict-api-schema": "^1.0.0", "luxon": "^3.4.4", "mocha": "^10.5.1", "moment": "^2.30.1", "multer": "^1.4.5-lts.1", "neo4j-driver": "^5.21.0", "nodemon": "^2.0.22", "openapi": "^1.0.1", "opentelemetry-instrumentation-typeorm": "^0.41.0", "pg": "^8.12.0", "prettier": "^3.3.2", "redis": "^4.6.14", "reflect-metadata": "^0.1.14", "sinon": "20.0.0", "sql-highlight": "^6.0.0", "supertest": "6.3.4", "ts-node": "^10.9.2", "tslib": "^2.6.3", "tsoa": "^6.6.0", "typedi": "^0.10.0", "typeorm": "^0.3.20", "typescript": "5.8.3", "uuid": "^13.0.0", "winston": "^3.13.0", "xlsx": "^0.18.5", "yaml": "^2.4.5"}}