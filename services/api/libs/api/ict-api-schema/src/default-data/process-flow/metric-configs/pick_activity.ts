import {MetricConfig} from '../types/metric-configuration';

/**
 * Metrics configuration for pick activity operations
 * Note: This file contains a selection of metrics from the original pick_activity.py
 * Additional metrics should be added following the same pattern
 */
const metrics: MetricConfig[] = [
  // Workstations - Source Container Arrivals Total Count
  {
    metric_config_name: 'workstations_source_container_arrival_count',
    aggregation: 'count',
    config_type: 'node',
    description:
      'Total source container arrivals across all workstations in the last hour.',
    fact_type: 'pick_activity',
    graph_operation: 'area_node',
    match_conditions: {
      event_code: '^Arrival$',
      induction_zone_code: '^M.*GTP.*D1$',
    },
    metric_type: 'source_container_arrival_count',
    node_name: 'workstations',
    label: 'Area',
    redis_operation: 'event_set',
    time_window: '60m_set',
    views: ['facility'],
  },

  // Workstations - Source Container Arrival Rate
  {
    metric_config_name: 'workstations_source_container_arrival_rate',
    aggregation: 'hourly_rate',
    config_type: 'node',
    description:
      'Hourly rate of source container arrivals across all workstations based on the last 15 minutes.',
    fact_type: 'pick_activity',
    graph_operation: 'area_node',
    match_conditions: {
      event_code: '^Arrival$',
      induction_zone_code: '^M.*GTP.*D1$',
    },
    metric_type: 'source_container_arrival_rate',
    metric_units: '/hr',
    node_name: 'workstations',
    label: 'Area',
    redis_operation: 'event_set',
    time_window: '15m_set',
    views: ['facility'],
  },

  // Workstations - Destination Container Arrivals Total Count
  {
    metric_config_name: 'workstations_destination_container_arrival_count',
    aggregation: 'count',
    config_type: 'node',
    description:
      'Total destination container arrivals across all workstations in the last hour.',
    fact_type: 'pick_activity',
    graph_operation: 'area_node',
    match_conditions: {
      event_code: '^Arrival$',
      induction_zone_code: '^M.*GTP.*(B[1-5]|F[1-6])$',
    },
    metric_type: 'destination_container_arrival_count',
    node_name: 'workstations',
    label: 'Area',
    redis_operation: 'event_set',
    time_window: '60m_set',
    views: ['facility'],
  },

  // Workstations - Destination Container Arrival Rate
  {
    metric_config_name: 'workstations_destination_container_arrival_rate',
    aggregation: 'hourly_rate',
    config_type: 'node',
    description:
      'Hourly rate of destination container arrivals across all workstations based on the last 15 minutes.',
    fact_type: 'pick_activity',
    graph_operation: 'area_node',
    match_conditions: {
      event_code: '^Arrival$',
      induction_zone_code: '^M.*GTP.*(B[1-5]|F[1-6])$',
    },
    metric_type: 'destination_container_arrival_rate',
    metric_units: '/hr',
    node_name: 'workstations',
    label: 'Area',
    redis_operation: 'event_set',
    time_window: '15m_set',
    views: ['facility'],
  },

  // Workstations - Total Arrivals - Used to calculate arrivals_per_fault
  {
    metric_config_name: 'workstations_arrivals_per_fault_numerator',
    aggregation: 'ratio',
    config_type: 'node',
    description:
      'Total arrivals per fault across all workstations in the last hour.',
    fact_type: 'pick_activity',
    graph_operation: 'area_node',
    match_conditions: {
      event_code: '^Arrival$',
      induction_zone_code: '^M.*GTP.*(B[1-5]|F[1-6]|D1)$',
    },
    metric_type: 'arrivals_per_fault_numerator',
    node_name: 'workstations',
    label: 'Area',
    redis_operation: 'event_set',
    time_window: '60m_set',
    views: ['facility'],
  },

  // Workstations - Active Operators - Count
  {
    metric_config_name: 'workstations_active_operators_increment',
    aggregation: 'count',
    config_type: 'node',
    description:
      'Total active operators across all workstations in the last hour.',
    fact_type: 'pick_activity',
    graph_operation: 'area_node',
    match_conditions: {
      workstation_code: '-GTP-',
      event_code: '^Logon$',
    },
    metric_type: 'active_operators',
    node_name: 'workstations',
    label: 'Area',
    redis_operation: 'distinct_item_count',
    redis_params: {
      identifier: '{workstation_code}',
    },
    time_window: '60m_set',
    views: ['facility'],
  },

  {
    metric_config_name: 'workstations_active_operators_decrement',
    aggregation: 'count',
    config_type: 'node',
    description:
      'Total active operators across all workstations in the last hour.',
    fact_type: 'pick_activity',
    graph_operation: 'area_node',
    match_conditions: {
      event_code: '^Logoff$',
    },
    metric_type: 'active_operators',
    node_name: 'workstations',
    label: 'Area',
    redis_operation: 'distinct_item_subtract',
    redis_params: {
      identifier: '{workstation_code}',
    },
    time_window: '60m_set',
    views: ['facility'],
  },

  // Workstations - Occupied Source Locations - Count
  {
    metric_config_name: 'workstations_occupied_source_locations_increment',
    aggregation: 'count',
    config_type: 'node',
    description:
      'Total occupied source locations across all workstations in the last hour.',
    fact_type: 'pick_activity',
    graph_operation: 'area_node',
    match_conditions: {
      event_code: '^Arrival$',
      induction_zone_code: '^M.*GTP.*D1$',
    },
    metric_type: 'occupied_source_locations',
    node_name: 'workstations',
    label: 'Area',
    redis_operation: 'distinct_item_count',
    redis_params: {
      identifier: '{workstation_code}',
    },
    time_window: '60m_set',
    views: ['facility'],
  },

  {
    metric_config_name: 'workstations_occupied_source_locations_decrement',
    aggregation: 'count',
    config_type: 'node',
    description:
      'Total occupied source locations across all workstations in the last hour.',
    fact_type: 'pick_activity',
    graph_operation: 'area_node',
    match_conditions: {
      event_code: '^(?!Arrival$).+$',
      induction_zone_code: '^M.*GTP.*D1$',
    },
    metric_type: 'occupied_source_locations',
    node_name: 'workstations',
    label: 'Area',
    redis_operation: 'distinct_item_subtract',
    redis_params: {
      identifier: '{workstation_code}',
    },
    time_window: '60m_set',
    views: ['facility'],
  },

  // Workstations - Occupied Destination Locations - Count
  {
    metric_config_name: 'workstations_occupied_destination_locations_increment',
    aggregation: 'count',
    config_type: 'node',
    description:
      'Total occupied destination locations across all workstations in the last hour.',
    fact_type: 'pick_activity',
    graph_operation: 'area_node',
    match_conditions: {
      event_code: '^Arrival$',
      induction_zone_code: '^M.*GTP.*(B[1-5]|F[1-6])$',
    },
    metric_type: 'occupied_destination_locations',
    node_name: 'workstations',
    label: 'Area',
    redis_operation: 'distinct_item_count',
    redis_params: {
      identifier: '{workstation_code}',
    },
    time_window: '60m_set',
    views: ['facility'],
  },

  {
    metric_config_name: 'workstations_occupied_destination_locations_decrement',
    aggregation: 'count',
    config_type: 'node',
    description:
      'Total occupied destination locations across all workstations in the last hour.',
    fact_type: 'pick_activity',
    graph_operation: 'area_node',
    match_conditions: {
      event_code: '^(?!Arrival$).+$',
      induction_zone_code: '^M.*GTP.*(B[1-5]|F[1-6])$',
      workstation_code: '^M.*GTP-\\d{2}$',
    },
    metric_type: 'occupied_destination_locations',
    node_name: 'workstations',
    label: 'Area',
    redis_operation: 'distinct_item_subtract',
    redis_params: {
      identifier: '{workstation_code}',
    },
    time_window: '60m_set',
    views: ['facility'],
  },

  // Workstations - Logged In Time Start - Used to calculate logged_in_time
  {
    metric_config_name: 'workstations_logged_in_time_start',
    aggregation: 'sum',
    config_type: 'node',
    description:
      'Total time operators are logged in across all workstations in the last hour.',
    fact_type: 'pick_activity',
    graph_operation: 'area_node',
    match_conditions: {
      event_code: '^Logon$',
      workstation_code: '^M.*GTP-\\d{2}$',
    },
    metric_type: 'logged_in_time',
    metric_units: 'mins',
    node_name: 'workstations',
    label: 'Area',
    redis_operation: 'cycle_time_start',
    redis_params: {
      instance_id: '{workstation_code}',
    },
    time_window: '60m_set',
    views: ['facility'],
  },

  // Workstations - Logged In Time Stop - Used to calculate logged_in_time
  {
    metric_config_name: 'workstations_logged_in_time_stop',
    aggregation: 'sum',
    config_type: 'node',
    description:
      'Total time operators are logged in across all workstations in the last hour.',
    fact_type: 'pick_activity',
    graph_operation: 'area_node',
    match_conditions: {
      event_code: '^Logoff$',
      workstation_code: '^M.*GTP-\\d{2}$',
    },
    metric_type: 'logged_in_time',
    metric_units: 'mins',
    node_name: 'workstations',
    label: 'Area',
    redis_operation: 'cycle_time_stop',
    redis_params: {
      instance_id: '{workstation_code}',
    },
    time_window: '60m_set',
    views: ['facility'],
  },

  // Workstations - Occupied Time Start - Used to calculate occupied_time
  {
    metric_config_name: 'workstations_occupied_time_start',
    aggregation: 'sum',
    config_type: 'node',
    description:
      'Total time one or more source destinations at a workstation are occupied in the last hour.',
    fact_type: 'pick_activity',
    graph_operation: 'area_node',
    match_conditions: {
      event_code: '^Arrival$',
      induction_zone_code: '^M.*GTP.*D1$',
    },
    metric_type: 'occupied_time',
    metric_units: 'mins',
    node_name: 'workstations',
    label: 'Area',
    redis_operation: 'cycle_time_start',
    redis_params: {
      instance_id: '{induction_zone_code}',
    },
    time_window: '60m_set',
    views: ['facility'],
  },

  // Workstations - Occupied Time Stop - Used to calculate occupied_time
  {
    metric_config_name: 'workstations_occupied_time_stop',
    aggregation: 'sum',
    config_type: 'node',
    description:
      'Total time one or more source destinations at a workstation are occupied in the last hour.',
    fact_type: 'pick_activity',
    graph_operation: 'area_node',
    match_conditions: {
      event_code: '^(Departure|Release)$',
      induction_zone_code: '^M.*GTP.*D1$',
    },
    metric_type: 'occupied_time',
    metric_units: 'mins',
    node_name: 'workstations',
    label: 'Area',
    redis_operation: 'cycle_time_stop',
    redis_params: {
      instance_id: '{induction_zone_code}',
    },
    time_window: '60m_set',
    views: ['facility'],
  },

  // Workstations - Active Time Start - Used to calculate active_time
  {
    metric_config_name: 'workstations_active_time_start',
    aggregation: 'sum',
    config_type: 'node',
    description:
      'Total time one or more source destinations at a workstation are occupied while an operator is logged in or a terminal is active in the last hour.',
    fact_type: 'pick_activity',
    graph_operation: 'area_node',
    match_conditions: {
      event_code: '^Arrival$',
      induction_zone_code: '^M.*GTP.*D1$',
      operator_code: '^.+$',
    },
    metric_type: 'active_time',
    metric_units: 'mins',
    node_name: 'workstations',
    label: 'Area',
    redis_operation: 'cycle_time_start',
    redis_params: {
      instance_id: '{induction_zone_code}',
    },
    time_window: '60m_set',
    views: ['facility'],
  },

  // Workstations - Active Time Stop - Used to calculate active_time
  {
    metric_config_name: 'workstations_active_time_stop',
    aggregation: 'sum',
    config_type: 'node',
    description:
      'Total time one or more source destinations at a workstation are occupied while an operator is logged in or a terminal is active in the last hour.',
    fact_type: 'pick_activity',
    graph_operation: 'area_node',
    match_conditions: {
      event_code: '^(Release|Departure|Logoff)$',
      induction_zone_code: '^M.*GTP.*D1$',
    },
    metric_type: 'active_time',
    metric_units: 'mins',
    node_name: 'workstations',
    label: 'Area',
    redis_operation: 'cycle_time_stop',
    redis_params: {
      instance_id: '{induction_zone_code}',
    },
    time_window: '60m_set',
    views: ['facility'],
  },

  // Workstations - Idle Time Start - Used to calculate idle_time
  {
    metric_config_name: 'workstations_idle_time_start',
    aggregation: 'sum',
    config_type: 'node',
    description:
      'Total time an operator is logged in to a workstation or a terminal is active and no source destinations are occupied in the last hour.',
    fact_type: 'pick_activity',
    graph_operation: 'area_node',
    match_conditions: {
      event_code: '^(Departure|Release)$',
      operator_code: '^.+$',
      induction_zone_code: '^M.*GTP.*(D1|(B[1-5]|F[1-6]))$', // includes source and destination locations
    },
    metric_type: 'idle_time',
    metric_units: 'mins',
    node_name: 'workstations',
    label: 'Area',
    redis_operation: 'cycle_time_start',
    redis_params: {
      instance_id: '{induction_zone_code}',
    },
    time_window: '60m_set',
    views: ['facility'],
  },

  // Workstations - Idle Time Stop - Used to calculate idle_time
  {
    metric_config_name: 'workstations_idle_time_stop',
    aggregation: 'sum',
    config_type: 'node',
    description:
      'Total time an operator is logged in to a workstation or a terminal is active and no source destinations are occupied in the last hour.',
    fact_type: 'pick_activity',
    graph_operation: 'area_node',
    match_conditions: {
      event_code: '^(Arrival|Logoff)$',
      induction_zone_code: '^M.*GTP.*(D1|(B[1-5]|F[1-6]))$', // includes source and destination locations
    },
    metric_type: 'idle_time',
    metric_units: 'mins',
    node_name: 'workstations',
    label: 'Area',
    redis_operation: 'cycle_time_stop',
    redis_params: {
      instance_id: '{induction_zone_code}',
    },
    time_window: '60m_set',
    views: ['facility'],
  },

  // Workstations - Starved Time Start - Used to calculate starved_time
  {
    metric_config_name: 'workstations_starved_time_start',
    aggregation: 'sum',
    config_type: 'node',
    description:
      'Total time an operator is present or logged in to a workstation when no Source Container is present in the last hour.',
    fact_type: 'pick_activity',
    graph_operation: 'area_node',
    match_conditions: {
      handling_unit_code: '^None$',
      operator_code: '^.+$',
    },
    metric_type: 'starved_time',
    metric_units: 'mins',
    node_name: 'workstations',
    label: 'Area',
    redis_operation: 'cycle_time_start',
    redis_params: {
      instance_id: '{workstation_code}',
    },
    time_window: '60m_set',
    views: ['facility'],
  },

  // Workstations - Starved Time Stop - Used to calculate starved_time
  {
    metric_config_name: 'workstations_starved_time_stop',
    aggregation: 'sum',
    config_type: 'node',
    description:
      'Total time an operator is present or logged in to a workstation when no Source Container is present in the last hour.',
    fact_type: 'pick_activity',
    graph_operation: 'area_node',
    match_conditions: {
      or_condition: [{handling_unit_code: '^.+$'}, {event_code: '^Logoff$'}],
    },
    metric_type: 'starved_time',
    metric_units: 'mins',
    node_name: 'workstations',
    label: 'Area',
    redis_operation: 'cycle_time_stop',
    redis_params: {
      instance_id: '{workstation_code}',
    },
    time_window: '60m_set',
    views: ['facility'],
  },

  // Workstations - Blocked Time Start - Used to calculate blocked_time
  {
    metric_config_name: 'workstations_blocked_time_start',
    aggregation: 'sum',
    config_type: 'node',
    description:
      'Total time a source load unit is present between "Release" and "Departure" events and an operator is logged in to a workstation in the last hour.',
    fact_type: 'pick_activity',
    graph_operation: 'area_node',
    match_conditions: {
      event_code: '^Release$',
      induction_zone_code: '^M.*GTP.*D1$',
      operator_code: '^.+$',
    },
    metric_type: 'blocked_time',
    metric_units: 'mins',
    node_name: 'workstations',
    label: 'Area',
    redis_operation: 'cycle_time_start',
    redis_params: {
      instance_id: '{induction_zone_code}',
    },
    time_window: '60m_set',
    views: ['facility'],
  },

  // Workstations - Blocked Time Stop - Used to calculate blocked_time
  {
    metric_config_name: 'workstations_blocked_time_stop',
    aggregation: 'sum',
    config_type: 'node',
    description:
      'Total time a source load unit is present between "Release" and "Departure" events and an operator is logged in to a workstation in the last hour.',
    fact_type: 'pick_activity',
    graph_operation: 'area_node',
    match_conditions: {
      event_code: '^(Logoff|Departure)$',
      induction_zone_code: '^M.*GTP.*D1$',
    },
    metric_type: 'blocked_time',
    metric_units: 'mins',
    node_name: 'workstations',
    label: 'Area',
    redis_operation: 'cycle_time_stop',
    redis_params: {
      instance_id: '{induction_zone_code}',
    },
    time_window: '60m_set',
    views: ['facility'],
  },

  // Work Station Node Metrics
  // Workstation - Source Container Arrival Rate
  {
    metric_config_name: 'workstation_source_container_arrival_rate',
    aggregation: 'hourly_rate',
    config_type: 'node',
    description:
      'Hourly rate of source container arrivals in this station based on the last hour.',
    fact_type: 'pick_activity',
    graph_operation: 'area_node',
    label: 'Station',
    match_conditions: {
      event_code: '^Arrival$',
      induction_zone_code: '^M.*GTP.*D1$',
      workstation_code: '^M.*GTP-\\d{2}$',
    },
    metric_type: 'source_container_arrival_rate',
    metric_units: '/hr',
    node_name: '{workstation_code}',
    parent_nodes: ['workstations'],
    redis_operation: 'event_set',
    redis_params: {
      ttl: 900,
    },
    time_window: '15m_set',
    views: ['workstations'],
  },
  // Workstation - Source Container Arrivals - Count
  {
    metric_config_name: 'workstation_source_container_arrival_count',
    aggregation: 'count',
    config_type: 'node',
    description:
      'Total source container arrivals in this station in the last hour.',
    fact_type: 'pick_activity',
    graph_operation: 'area_node',
    label: 'Station',
    match_conditions: {
      event_code: '^Arrival$',
      induction_zone_code: '^M.*GTP.*D1$',
      workstation_code: '^M.*GTP-\\d{2}$',
    },
    metric_type: 'source_container_arrivals',
    node_name: '{workstation_code}',
    parent_nodes: ['workstations'],
    redis_operation: 'event_set',
    time_window: '60m_set',
    views: ['workstations'],
  },

  // Workstation Occupied Source Locations
  {
    metric_config_name: 'workstation_occupied_source_locations_increment',
    aggregation: 'count',
    config_type: 'node',
    description:
      'Total occupied source locations in this station in the last hour.',
    fact_type: 'pick_activity',
    graph_operation: 'area_node',
    label: 'Station',
    match_conditions: {
      event_code: '^Arrival$',
      induction_zone_code: '^M.*GTP.*D1$',
      workstation_code: '^M.*GTP-\\d{2}$',
    },
    metric_type: 'occupied_source_locations',
    node_name: '{workstation_code}',
    parent_nodes: ['workstations'],
    redis_operation: 'distinct_item_count',
    redis_params: {
      identifier: '{workstation_code}',
    },
    time_window: '60m_set',
    views: ['workstations'],
  },

  {
    metric_config_name: 'workstation_occupied_source_locations_decrement',
    aggregation: 'count',
    config_type: 'node',
    description:
      'Total occupied source locations in this station in the last hour.',
    fact_type: 'pick_activity',
    graph_operation: 'area_node',
    label: 'Station',
    match_conditions: {
      event_code: '^(?!Arrival$).+$',
      induction_zone_code: '^M.*GTP.*D1$',
      workstation_code: '^M.*GTP-\\d{2}$',
    },
    metric_type: 'occupied_source_locations',
    node_name: '{workstation_code}',
    parent_nodes: ['workstations'],
    redis_operation: 'distinct_item_subtract',
    redis_params: {
      identifier: '{workstation_code}',
    },
    time_window: '60m_set',
    views: ['workstations'],
  },

  // Workstation - Occupied Destination Locations - Count
  {
    metric_config_name: 'workstation_occupied_destination_locations_increment',
    aggregation: 'count',
    config_type: 'node',
    description:
      'Total occupied destination locations in this station in the last hour.',
    fact_type: 'pick_activity',
    graph_operation: 'area_node',
    label: 'Station',
    match_conditions: {
      event_code: '^Arrival$',
      induction_zone_code: '^M.*GTP.*(B[1-5]|F[1-6])$',
      workstation_code: '^M.*GTP-\\d{2}$',
    },
    metric_type: 'occupied_destination_locations',
    node_name: '{workstation_code}',
    parent_nodes: ['workstations'],
    redis_operation: 'distinct_item_count',
    redis_params: {
      identifier: '{workstation_code}',
    },
    time_window: '60m_set',
    views: ['workstations'],
  },

  {
    metric_config_name: 'workstation_occupied_destination_locations_decrement',
    aggregation: 'count',
    config_type: 'node',
    description:
      'Total occupied destination locations in this station in the last hour.',
    fact_type: 'pick_activity',
    graph_operation: 'area_node',
    label: 'Station',
    match_conditions: {
      event_code: '^(?!Arrival$).+$',
      induction_zone_code: '^M.*GTP.*(B[1-5]|F[1-6])$',
      workstation_code: '^M.*GTP-\\d{2}$',
    },
    metric_type: 'occupied_destination_locations',
    node_name: '{workstation_code}',
    parent_nodes: ['workstations'],
    redis_operation: 'distinct_item_subtract',
    redis_params: {
      identifier: '{workstation_code}',
    },
    time_window: '60m_set',
    views: ['workstations'],
  },

  // Workstations - Active Operators - Count
  {
    metric_config_name: 'workstation_active_operators_increment',
    aggregation: 'count',
    config_type: 'node',
    description: 'Total active operators in this station in the last hour.',
    fact_type: 'pick_activity',
    graph_operation: 'area_node',
    label: 'Station',
    match_conditions: {
      event_code: '^Logon$',
      workstation_code: '^M.*GTP-\\d{2}$',
    },
    metric_type: 'active_operators',
    node_name: '{workstation_code}',
    parent_nodes: ['workstations'],
    redis_operation: 'distinct_item_count',
    redis_params: {
      identifier: '{workstation_code}',
    },
    time_window: '60m_set',
    views: ['workstations'],
  },
  {
    metric_config_name: 'workstation_active_operators_decrement',
    aggregation: 'count',
    config_type: 'node',
    description: 'Total active operators in this station in the last hour.',
    fact_type: 'pick_activity',
    graph_operation: 'area_node',
    label: 'Station',
    match_conditions: {
      event_code: '^Logoff$',
      workstation_code: '^M.*GTP-\\d{2}$',
    },
    metric_type: 'active_operators',
    node_name: '{workstation_code}',
    parent_nodes: ['workstations'],
    redis_operation: 'distinct_item_subtract',
    redis_params: {
      identifier: '{workstation_code}',
    },
    time_window: '60m_set',
    views: ['workstations'],
  },
  {
    metric_config_name:
      'workstation_destination_position_pick_lines_ratio_numerator',
    aggregation: 'destination_position_ratio',
    config_type: 'node',
    description:
      'Ratio of pick order lines completed to destination containers in each of the available positions at this station in the last hour.',
    fact_type: 'pick_activity',
    graph_operation: 'area_node',
    label: 'Station',
    match_conditions: {
      induction_zone_code: '^M.*GTP.*(B[1-5]|F[1-6])$',
      zone_code: '^.+$',
      workflow_code: 'PICK',
      workstation_code: '^M.*GTP-\\d{2}$',
    },
    metric_type: '{induction_zone_code}_destination_position_ratio',
    node_name: '{workstation_code}',
    parent_nodes: ['workstations'],
    redis_operation: 'event_set',
    redis_params: {
      denominator_node: '{zone_code}',
    },
    time_window: '60m_set',
    views: ['workstations'],
  },
  {
    metric_config_name:
      'workstation_destination_position_pick_lines_ratio_denominator',
    aggregation: 'destination_position_ratio',
    config_type: 'node',
    description:
      'Ratio of pick order lines completed to destination containers in each of the available positions at this station in the last hour.',
    fact_type: 'pick_activity',
    graph_operation: 'area_node',
    label: 'Station',
    match_conditions: {
      induction_zone_code: '^M.*GTP.*(B[1-5]|F[1-6])$',
      zone_code: '^.+$',
      workflow_code: 'PICK',
      workstation_code: '^M.*GTP-\\d{2}$',
    },
    metric_type: 'destination_position_ratio_denominator',
    node_name: '{workstation_code}',
    parent_nodes: ['workstations'],
    redis_operation: 'event_set',
    time_window: '60m_set',
    views: ['workstations'],
  },
  //= ========================================
  // Destination Location Metrics
  {
    metric_config_name:
      'edge_from_workstation_source_location_to_destination_location',
    config_type: 'complete-edge',
    description:
      'Hourly rate of units moving from the station source location to the destination location based on the last 15 minutes.',
    fact_type: 'pick_activity',
    graph_operation: 'area_edge',
    inbound_area: '{induction_zone_code}',
    inbound_parent_nodes: ['{workstation_code}'],
    label: 'StationLocation',
    match_conditions: {
      event_code: '^Arrival$',
      induction_zone_code: '^{workstation_code}(B[1-5]|F[1-6])$',
      workstation_code: '^M.*GTP-\\d{2}$',
    },
    outbound_area: '{workstation_code}D1',
    outbound_node_label: 'StationLocation',
    outbound_parent_nodes: ['{workstation_code}'],
    redis_operation: 'event_set',
    views: ['{workstation_code}'],
  },

  // Destination Location - Occupied
  {
    metric_config_name: 'station_location_occupied',
    aggregation: 'static',
    config_type: 'node',
    description: 'Whether this location is occupied or unoccupied.',
    fact_type: 'pick_activity',
    graph_operation: 'area_node',
    label: 'StationLocation',
    match_conditions: {
      event_code: '^Arrival$',
      induction_zone_code: '^{workstation_code}(B[1-5]|F[1-6]|D1)$',
      workstation_code: '^M.*GTP-\\d{2}$',
    },
    metric_type: 'occupied_location',
    node_name: '{induction_zone_code}',
    parent_nodes: ['{workstation_code}'],
    redis_operation: 'store_static_value',
    redis_params: {
      value: 'Yes',
    },
    time_window: 'value',
    views: ['{workstation_code}'],
  },

  // Destination Location - Unoccupied
  {
    metric_config_name: 'station_location_unoccupied',
    aggregation: 'static',
    config_type: 'node',
    description: 'Whether this location is occupied or unoccupied.',
    fact_type: 'pick_activity',
    graph_operation: 'area_node',
    label: 'StationLocation',
    match_conditions: {
      event_code: '^(?!Arrival$).+$',
      induction_zone_code: '^{workstation_code}(B[1-5]|F[1-6]|D1)$',
      workstation_code: '^M.*GTP-\\d{2}$',
    },
    metric_type: 'occupied_location',
    node_name: '{induction_zone_code}',
    parent_nodes: ['{workstation_code}'],
    redis_operation: 'store_static_value',
    redis_params: {
      value: 'No',
    },
    time_window: 'value',
    views: ['{workstation_code}'],
  },

  // Destination Location - Arrival Rate
  {
    metric_config_name: 'destination_location_arrival_rate',
    aggregation: 'hourly_rate',
    config_type: 'node',
    description:
      'Hourly rate of units arriving at this location based on the last 15 minutes.',
    fact_type: 'pick_activity',
    graph_operation: 'area_node',
    label: 'StationLocation',
    match_conditions: {
      event_code: '^Arrival$',
      induction_zone_code: '^{workstation_code}(B[1-5]|F[1-6])$',
      workstation_code: '^M.*GTP-\\d{2}$',
    },
    metric_type: 'destination_container_arrival_rate',
    metric_units: '/hr',
    node_name: '{induction_zone_code}',
    parent_nodes: ['{workstation_code}'],
    redis_operation: 'event_set',
    redis_params: {
      ttl: 900,
    },
    time_window: '15m_set',
    views: ['{workstation_code}'],
  },

  // Destination Location Arrivals Total Count
  {
    metric_config_name: 'destination_location_arrival_count',
    aggregation: 'count',
    config_type: 'node',
    description: 'Total units arriving at this location in the last hour.',
    fact_type: 'pick_activity',
    graph_operation: 'area_node',
    label: 'StationLocation',
    match_conditions: {
      event_code: '^Arrival$',
      induction_zone_code: '^{workstation_code}(B[1-5]|F[1-6])$',
      workstation_code: '^M.*GTP-\\d{2}$',
    },
    metric_type: 'destination_container_arrivals',
    node_name: '{induction_zone_code}',
    parent_nodes: ['{workstation_code}'],
    redis_operation: 'event_set',
    time_window: '60m_set',
    views: ['{workstation_code}'],
  },

  // Destination Location - Active Operator
  {
    metric_config_name: 'station_location_active_operator',
    aggregation: 'static',
    config_type: 'node',
    description:
      "Whether or not an operator is active at this location's station.",
    fact_type: 'pick_activity',
    graph_operation: 'area_node',
    label: 'StationLocation',
    match_conditions: {
      operator_code: '^.+$',
      induction_zone_code: '^{workstation_code}(B[1-5]|F[1-6]|D1)$',
      workstation_code: '^M.*GTP-\\d{2}$',
    },
    metric_type: 'active_operator',
    node_name: '{induction_zone_code}',
    parent_nodes: ['{workstation_code}'],
    redis_operation: 'store_static_value',
    redis_params: {
      value: 'Yes',
    },
    time_window: 'value',
    views: ['{workstation_code}'],
  },

  // Destination Location - Container ID
  {
    metric_config_name: 'station_location_container_id',
    aggregation: 'static',
    config_type: 'node',
    description: 'The ID of the container that is currently at this location.',
    fact_type: 'pick_activity',
    graph_operation: 'area_node',
    label: 'StationLocation',
    match_conditions: {
      event_code: '^Arrival$',
      induction_zone_code: '^{workstation_code}(B[1-5]|F[1-6]|D1)$',
      workstation_code: '^M.*GTP-\\d{2}$',
      handling_unit_code: '^.+$',
    },
    metric_type: 'container_id',
    node_name: '{induction_zone_code}',
    parent_nodes: ['{workstation_code}'],
    redis_operation: 'store_static_value',
    redis_params: {
      value: '{handling_unit_code}',
    },
    time_window: 'value',
    views: ['{workstation_code}'],
  },
  {
    metric_config_name: 'station_location_container_id_remove',
    aggregation: 'static',
    config_type: 'node',
    description: 'The ID of the container that is currently at this location.',
    fact_type: 'pick_activity',
    graph_operation: 'area_node',
    label: 'StationLocation',
    match_conditions: {
      event_code: '^(?!Arrival$).+$',
      induction_zone_code: '^{workstation_code}(B[1-5]|F[1-6]|D1)$',
      workstation_code: '^M.*GTP-\\d{2}$',
    },
    metric_type: 'container_id',
    node_name: '{induction_zone_code}',
    parent_nodes: ['{workstation_code}'],
    redis_operation: 'store_static_value',
    redis_params: {
      value: '',
    },
    time_window: 'value',
    views: ['{workstation_code}'],
  },

  // Destination Location - Inactive Operator
  {
    metric_config_name: 'station_location_inactive_operator',
    aggregation: 'static',
    config_type: 'node',
    description:
      "Whether or not an operator is active at this location's station.",
    fact_type: 'pick_activity',
    graph_operation: 'area_node',
    label: 'StationLocation',
    match_conditions: {
      operator_code: '^None$',
      induction_zone_code: '^{workstation_code}(B[1-5]|F[1-6]|D1)$',
      workstation_code: '^M.*GTP-\\d{2}$',
    },
    metric_type: 'active_operator',
    node_name: '{induction_zone_code}',
    parent_nodes: ['{workstation_code}'],
    redis_operation: 'store_static_value',
    redis_params: {
      value: 'No',
    },
    time_window: 'value',
    views: ['{workstation_code}'],
  },

  // Destination Location - Set Load Unit Type
  {
    metric_config_name: 'station_location_load_unit_type',
    aggregation: 'static',
    config_type: 'node',
    description:
      'The type of load unit (carton, pallet, tote, etc.) that is currently at this location.',
    fact_type: 'pick_activity',
    graph_operation: 'area_node',
    label: 'StationLocation',
    match_conditions: {
      event_code: '^Arrival$',
      handling_unit_type: '^.+$',
      induction_zone_code: '^{workstation_code}(B[1-5]|F[1-6]|D1)$',
      workstation_code: '^M.*GTP-\\d{2}$',
    },
    metric_type: 'load_unit_type',
    node_name: '{induction_zone_code}',
    parent_nodes: ['{workstation_code}'],
    redis_operation: 'store_static_value',
    redis_params: {
      value: '{handling_unit_type}',
    },
    time_window: 'value',
    views: ['{workstation_code}'],
  },

  // Destination Location - Unset Load Unit Type
  {
    metric_config_name: 'station_location_load_unit_type_remove',
    aggregation: 'static',
    config_type: 'node',
    description:
      'The type of load unit (carton, pallet, tote, etc.) that is currently at this location.',
    fact_type: 'pick_activity',
    graph_operation: 'area_node',
    label: 'StationLocation',
    match_conditions: {
      event_code: '^(?!Arrival$).+$',
      induction_zone_code: '^{workstation_code}(B[1-5]|F[1-6]|D1)$',
      workstation_code: '^M.*GTP-\\d{2}$',
    },
    metric_type: 'load_unit_type',
    node_name: '{induction_zone_code}',
    parent_nodes: ['{workstation_code}'],
    redis_operation: 'store_static_value',
    redis_params: {
      value: '',
    },
    time_window: 'value',
    views: ['{workstation_code}'],
  },

  // Destination Location - Occupied Time Start
  {
    metric_config_name: 'station_location_occupied_time_start',
    aggregation: 'sum',
    config_type: 'node',
    description: 'Total time this location is occupied.',
    fact_type: 'pick_activity',
    graph_operation: 'area_node',
    label: 'StationLocation',
    match_conditions: {
      event_code: '^Arrival$',
      induction_zone_code: '^{workstation_code}(B[1-5]|F[1-6]|D1)$',
      workstation_code: '^M.*GTP-\\d{2}$',
    },
    metric_type: 'occupied_time',
    metric_units: 'mins',
    node_name: '{induction_zone_code}',
    parent_nodes: ['{workstation_code}'],
    redis_operation: 'cycle_time_start',
    redis_params: {
      instance_id: '{induction_zone_code}',
    },
    time_window: '60m_set',
    views: ['{workstation_code}'],
  },

  // Destination Location - Occupied Time Stop
  {
    metric_config_name: 'station_location_occupied_time_stop',
    aggregation: 'sum',
    config_type: 'node',
    description: 'Total time this location is occupied.',
    fact_type: 'pick_activity',
    graph_operation: 'area_node',
    label: 'StationLocation',
    match_conditions: {
      event_code: '^(Release|Departure)$',
      induction_zone_code: '^{workstation_code}(B[1-5]|F[1-6]|D1)$',
      workstation_code: '^M.*GTP-\\d{2}$',
    },
    metric_type: 'occupied_time',
    metric_units: 'mins',
    node_name: '{induction_zone_code}',
    parent_nodes: ['{workstation_code}'],
    redis_operation: 'cycle_time_stop',
    redis_params: {
      instance_id: '{induction_zone_code}',
    },
    time_window: '60m_set',
    views: ['{workstation_code}'],
  },
  {
    metric_config_name: 'station_location_active_time_start',
    aggregation: 'sum',
    config_type: 'node',
    description: 'Total time an operator is active at this location.',
    fact_type: 'pick_activity',
    graph_operation: 'area_node',
    label: 'StationLocation',
    match_conditions: {
      event_code: '^Arrival$',
      induction_zone_code: '^{workstation_code}(B[1-5]|F[1-6]|D1)$',
      operator_code: '^.+$',
      workstation_code: '^M.*GTP-\\d{2}$',
    },
    metric_type: 'active_time',
    metric_units: 'mins',
    node_name: '{induction_zone_code}',
    parent_nodes: ['{workstation_code}'],
    redis_operation: 'cycle_time_start',
    redis_params: {
      instance_id: '{induction_zone_code}',
    },
    time_window: '60m_set',
    views: ['{workstation_code}'],
  },
  {
    metric_config_name: 'station_location_active_time_stop',
    aggregation: 'sum',
    config_type: 'node',
    description: 'Total time an operator is active at this location.',
    fact_type: 'pick_activity',
    graph_operation: 'area_node',
    label: 'StationLocation',
    match_conditions: {
      or_condition: [
        {operator_code: '^None$'},
        {event_code: '^(Release|Departure)$'},
      ],
      induction_zone_code: '^{workstation_code}(B[1-5]|F[1-6]|D1)$',
      workstation_code: '^M.*GTP-\\d{2}$',
    },
    metric_type: 'active_time',
    metric_units: 'mins',
    node_name: '{induction_zone_code}',
    parent_nodes: ['{workstation_code}'],
    redis_operation: 'cycle_time_stop',
    redis_params: {
      instance_id: '{induction_zone_code}',
    },
    time_window: '60m_set',
    views: ['{workstation_code}'],
  },
  {
    metric_config_name: 'station_location_idle_time_start',
    aggregation: 'sum',
    config_type: 'node',
    description: 'Total time an operator is idle at this location.',
    fact_type: 'pick_activity',
    graph_operation: 'area_node',
    label: 'StationLocation',
    match_conditions: {
      event_code: '^(Departure|Release)$',
      induction_zone_code: '^{workstation_code}(B[1-5]|F[1-6]|D1)$',
      operator_code: '^.+$',
      workstation_code: '^M.*GTP-\\d{2}$',
    },
    metric_type: 'idle_time',
    metric_units: 'mins',
    node_name: '{induction_zone_code}',
    parent_nodes: ['{workstation_code}'],
    redis_operation: 'cycle_time_start',
    redis_params: {
      instance_id: '{induction_zone_code}',
    },
    time_window: '60m_set',
    views: ['{workstation_code}'],
  },
  {
    metric_config_name: 'station_location_idle_time_stop',
    aggregation: 'sum',
    config_type: 'node',
    description: 'Total time an operator is idle at this location.',
    fact_type: 'pick_activity',
    graph_operation: 'area_node',
    label: 'StationLocation',
    match_conditions: {
      event_code: '^Arrival$',
      induction_zone_code: '^{workstation_code}(B[1-5]|F[1-6]|D1)$',
      workstation_code: '^M.*GTP-\\d{2}$',
    },
    metric_type: 'idle_time',
    metric_units: 'mins',
    node_name: '{induction_zone_code}',
    parent_nodes: ['{workstation_code}'],
    redis_operation: 'cycle_time_stop',
    redis_params: {
      instance_id: '{induction_zone_code}',
    },
    time_window: '60m_set',
    views: ['{workstation_code}'],
  },
  {
    metric_config_name:
      'destination_location_destination_position_ratio_numerator',
    aggregation: 'destination_position_ratio',
    config_type: 'node',
    description:
      'Ratio of pick order lines completed to destination containers in each of the available positions at this station in the last hour.',
    fact_type: 'pick_activity',
    graph_operation: 'area_node',
    label: 'StationLocation',
    match_conditions: {
      induction_zone_code: '^{workstation_code}(B[1-5]|F[1-6])$',
      zone_code: '^.+$',
      workflow_code: 'PICK',
      workstation_code: '^M.*-GTP-\\d{2}$',
    },
    metric_type: 'destination_position_ratio',
    node_name: '{induction_zone_code}',
    parent_nodes: ['{workstation_code}'],
    redis_operation: 'event_set',
    time_window: '60m_set',
    views: ['{workstation_code}'],
  },
  // Source Location - Arrival Rate
  {
    metric_config_name: 'source_location_arrival_rate',
    aggregation: 'hourly_rate',
    config_type: 'node',
    description:
      'Hourly rate of units arriving at this source location based on the last 15 minutes.',
    fact_type: 'pick_activity',
    graph_operation: 'area_node',
    label: 'StationLocation',
    match_conditions: {
      event_code: '^Arrival$',
      induction_zone_code: '^{workstation_code}D1$',
      workstation_code: '^M.*GTP-\\d{2}$',
    },
    metric_type: 'source_container_arrival_rate',
    metric_units: '/hr',
    node_name: '{induction_zone_code}',
    parent_nodes: ['{workstation_code}'],
    redis_operation: 'event_set',
    redis_params: {
      ttl: 900,
    },
    time_window: '15m_set',
    views: ['{workstation_code}'],
  },

  // Source Location Arrivals Total Count
  {
    metric_config_name: 'source_location_arrival_count',
    aggregation: 'count',
    config_type: 'node',
    description:
      'Total units arriving at this source location in the last hour.',
    fact_type: 'pick_activity',
    graph_operation: 'area_node',
    label: 'StationLocation',
    match_conditions: {
      event_code: '^Arrival$',
      induction_zone_code: '^{workstation_code}D1$',
      workstation_code: '^M.*GTP-\\d{2}$',
    },
    metric_type: 'source_container_arrivals',
    node_name: '{induction_zone_code}',
    parent_nodes: ['{workstation_code}'],
    redis_operation: 'event_set',
    time_window: '60m_set',
    views: ['{workstation_code}'],
  },
];

export default metrics;
