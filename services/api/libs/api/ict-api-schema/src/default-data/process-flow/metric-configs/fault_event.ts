import {MetricConfig} from '../types/metric-configuration';

/**
 * Metrics configuration for fault events
 */
const faultEventMetricConfigs: MetricConfig[] = [
  // Multishuttle Active Devices
  {
    metric_config_name: 'multishuttle_active_devices_lift',
    aggregation: 'count',
    config_type: 'node',
    description: 'Number of active lift devices within the DMS picking buffer.',
    fact_type: 'fault_event',
    graph_operation: 'area_node',
    match_conditions: {
      availability_status: '^AU$',
      device_id_code: '^MSAI.*E[RL].*LO.*$',
    },
    metric_type: 'active_lift_devices',
    node_name: 'multishuttle',
    label: 'Area',
    redis_operation: 'event_set',
    time_window: '60m_set',
    views: ['facility'],
  },

  {
    metric_config_name: 'multishuttle_active_devices_shuttle',
    aggregation: 'count',
    config_type: 'node',
    description:
      'Number of active shuttle devices within the DMS picking buffer.',
    fact_type: 'fault_event',
    graph_operation: 'area_node',
    match_conditions: {
      availability_status: '^AU$',
      device_id_code: '^MSAI.*LV.*SH.*$',
    },
    metric_type: 'active_shuttle_devices',
    node_name: 'multishuttle',
    label: 'Area',
    redis_operation: 'event_set',
    time_window: '60m_set',
    views: ['facility'],
  },

  {
    metric_config_name: 'multishuttle_device_status',
    aggregation: 'static',
    config_type: 'node',
    description: 'Status of the shuttle device.',
    fact_type: 'fault_event',
    graph_operation: 'shuttle_node',
    match_conditions: {
      device_id_code: '^MSAI.*LV.*SH.*$',
    },
    metric_type: 'device_status',
    node_name: '{device_id_code}',
    label: 'Shuttle',
    redis_operation: 'store_static_value',
    redis_params: {
      value: '{availability_status}',
    },
    time_window: 'value',
    views: ['MSAI{aisle_code}'],
  },

  {
    metric_config_name: 'active_devices',
    aggregation: 'count',
    config_type: 'node',
    description:
      'Number of active devices per aisle within the DMS Picking Buffer.',
    fact_type: 'fault_event',
    graph_operation: 'area_node',
    label: 'Aisle',
    match_conditions: {
      aisle_code: '^.+$',
      or_condition: [
        {availability_status: '^AU$'},
        {availability_status: '^Automatic Mode$'},
      ],
    },
    metric_type: 'active_devices',
    node_name: 'MSAI{aisle_code}',
    parent_nodes: ['multishuttle'],
    redis_operation: 'event_set',
    time_window: '60m_set',
    views: ['facility', 'MSAI{aisle_code}'],
  },
];

export default faultEventMetricConfigs;
