import {DefaultSettingDefinition} from '../default-setting-definition.ts';

export const DefaultFacilityProcessFlowGraphConfig: DefaultSettingDefinition = {
  name: 'ict-facility-process-flow-graph-config',
  dataType: 'json',
  description:
    'Stores configuration values for how to render the nodes, and edges within the graph and customize functionality within the graph.',
  defaultValueJson: {},
  jsonSchema: {
    $schema: 'http://json-schema.org/draft-07/schema#',
    title: 'Process Flow Graph Configuration Settings',
    type: 'object',
    additionalProperties: {
      type: 'object',
      additionalProperties: {
        type: 'object',
        properties: {
          metrics: {
            type: 'array',
            items: {
              type: 'string',
            },
          },
          hasChildren: {
            type: 'boolean',
          },
        },
        required: ['metrics', 'hasChildren'],
        additionalProperties: false,
      },
    },
  },
};

export const DefaultFacilityProcessFlowExcludedNodes: DefaultSettingDefinition =
  {
    name: 'ict-facility-process-flow-excluded-nodes',
    dataType: 'json',
    description:
      'The nodes that will be excluded from the facility process flow page. These nodes may still exist in neo4j, but wont be passed to the UI.',
    defaultValueJson: {
      facility: [],
    },
    jsonSchema: {
      $schema: 'http://json-schema.org/draft-07/schema#',
      title: 'Excluded Nodes Configuration',
      description: 'Configuration for nodes to exclude from different views',
      type: 'object',
      properties: {
        facility: {
          type: 'array',
          description: 'List of node names to exclude from the facility view',
          items: {
            type: 'string',
            description: 'Node name to exclude (e.g., MSAI00)',
          },
          uniqueItems: true,
        },
      },
      additionalProperties: {
        type: 'array',
        description: 'List of node names to exclude from this view',
        items: {
          type: 'string',
          description: 'Node name to exclude',
        },
        uniqueItems: true,
      },
    },
  };
