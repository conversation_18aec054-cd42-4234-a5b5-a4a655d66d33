import {DefaultSettingDefinition} from '../default-setting-definition.ts';
import {
  DefaultFacilityProcessFlowExcludedNodes,
  DefaultFacilityProcessFlowGraphConfig,
} from './default-facility-process-flow-graph-config.ts';
import {DefaultFacilityProcessFlowPollingInterval} from './default-facility-process-flow-polling-interval.ts';
import {DefaultProcessFlowDetailPanelLayout} from './default-facility-process-flow-detail-panel-layout.ts';

export const DefaultFacilityProcessFlowSettings: DefaultSettingDefinition[] = [
  DefaultFacilityProcessFlowGraphConfig,
  DefaultFacilityProcessFlowPollingInterval,
  DefaultProcessFlowDetailPanelLayout,
  DefaultFacilityProcessFlowExcludedNodes,
] as const;
