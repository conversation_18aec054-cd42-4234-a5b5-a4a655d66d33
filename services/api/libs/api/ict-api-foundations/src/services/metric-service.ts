import axios from 'axios';
import {Metric} from '../defs/metric-def.ts';
import {DiService} from '../di/type-di.ts';
import {<PERSON><PERSON>ogger} from '../log/winston-logger.ts';
import {ContextService} from '../context/context-service.ts';

interface ProcessedMetricResponse {
  lastProcessedTime: string;
  metrics: Metric[];
}
@DiService()
export class MetricService {
  constructor(
    private context: ContextService,
    private logger: WinstonLogger,
  ) {}

  /**
   * Retrieves metrics from Redis and calculates rate changes if applicable
   */
  public async getMetrics(metrics: Metric[]): Promise<ProcessedMetricResponse> {
    try {
      const apiUrl = `${process.env.METRIC_PROCESSOR_METRICS_API_URL || 'localhost'}/metrics/`;
      const metricIds = metrics.map((m: Metric) => m.id);
      const response = await axios.post(
        apiUrl,
        {
          metric_ids: metricIds,
          tenant_id: this.context.datasetId, // pass the tenant_id to get the last_processes_time for the facility
        },
        {
          headers: {'Content-Type': 'application/json'},
        },
      );

      const processedMetricResults = this.processResults(
        metrics,
        response.data.metrics,
      );

      return {
        lastProcessedTime: response.data.last_processed_time,
        metrics: processedMetricResults,
      };
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      console.error('Error sending request:', error.message);
      throw error;
    }
  }

  /**
   * Processes the results from Redis and builds the final metric response.
   * Will try to parse the value as a number if possible.
   */
  private processResults(metrics: Metric[], results: Metric[]): Metric[] {
    return metrics.map((metric, index) => {
      const val = results[index].value;
      let parsedValue: number | string | null;
      if (val === null || val === undefined) {
        parsedValue = null;
      } else if (typeof val === 'number') {
        parsedValue = val;
      } else if (!Number.isNaN(parseFloat(val))) {
        parsedValue = parseFloat(val);
      } else {
        parsedValue = val;
      }
      return {
        ...metric,
        value: parsedValue,
        type: metric.type ? metric.type : 'normal',
      };
    });
  }
}
