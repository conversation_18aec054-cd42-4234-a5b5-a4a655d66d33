import sinon from 'sinon';
import {expect} from 'chai';
import {
  ContextService,
  WinstonLogger,
  MetricConfigStore,
  MetricConfigFact,
} from 'ict-api-foundations';
import type {MetricConfigDetail} from 'ict-api-foundations';
import {
  DefaultMetricConfigurationEntity,
  CustomMetricConfigurationEntity,
} from 'ict-api-schema';

describe('MetricConfigStore', () => {
  describe('getMetricConfigSummaries', () => {
    it('should return both default and custom metric configurations', async () => {
      const context = new ContextService();
      const logger = new WinstonLogger(context);
      const metricConfigStore = new MetricConfigStore(context, logger);

      const mockDefaultConfig = new DefaultMetricConfigurationEntity();
      mockDefaultConfig.id = '1';
      mockDefaultConfig.metricConfigName = 'test-metric';
      mockDefaultConfig.configType = 'node';
      mockDefaultConfig.nodeName = 'test-node';
      mockDefaultConfig.factType = 'test-fact';
      mockDefaultConfig.enabled = {default: true};
      mockDefaultConfig.active = {default: true};

      const mockCustomConfig = new CustomMetricConfigurationEntity();
      mockCustomConfig.id = '2';
      mockCustomConfig.metricConfigName = 'test-metric';
      mockCustomConfig.configType = 'node';
      mockCustomConfig.nodeName = 'custom-node';
      mockCustomConfig.factType = 'custom-fact';
      mockCustomConfig.active = true;
      mockCustomConfig.facilityId = 'test-facility';

      const mockEntities = [mockDefaultConfig, mockCustomConfig];

      sinon
        .stub(metricConfigStore, 'getMetricConfigSummaries')
        .resolves(mockEntities);

      const filters = {
        metricName: 'test-metric',
        configType: 'node',
        entityType: 'process-flow',
      };

      const result = await metricConfigStore.getMetricConfigSummaries(filters);

      expect(result).to.deep.equal(mockEntities);
    });

    it('should handle table not found error', async () => {
      const context = new ContextService();
      const logger = new WinstonLogger(context);
      const metricConfigStore = new MetricConfigStore(context, logger);

      sinon
        .stub(metricConfigStore, 'getMetricConfigSummaries')
        .rejects(new Error('Failed to get metric configurations'));

      const filters = {
        metricName: 'test-metric',
        configType: 'node',
        entityType: 'process-flow',
      };

      try {
        await metricConfigStore.getMetricConfigSummaries(filters);
        expect.fail('Should have thrown an error');
      } catch (error: unknown) {
        if (!(error instanceof Error)) {
          throw error;
        }
        expect(error.message).to.equal('Failed to get metric configurations');
      }
    });

    it('should handle database authentication failure', async () => {
      const context = new ContextService();
      const logger = new WinstonLogger(context);
      const metricConfigStore = new MetricConfigStore(context, logger);

      sinon
        .stub(metricConfigStore, 'getMetricConfigSummaries')
        .rejects(new Error('Failed to get metric configurations'));

      const filters = {
        metricName: 'test-metric',
        configType: 'node',
        entityType: 'process-flow',
      };

      try {
        await metricConfigStore.getMetricConfigSummaries(filters);
        expect.fail('Should have thrown an error');
      } catch (error: unknown) {
        if (!(error instanceof Error)) {
          throw error;
        }
        expect(error.message).to.equal('Failed to get metric configurations');
      }
    });

    it('should handle invalid column name error', async () => {
      const context = new ContextService();
      const logger = new WinstonLogger(context);
      const metricConfigStore = new MetricConfigStore(context, logger);

      sinon
        .stub(metricConfigStore, 'getMetricConfigSummaries')
        .rejects(new Error('Failed to get metric configurations'));

      const filters = {
        metricName: 'test-metric',
        configType: 'node',
        entityType: 'process-flow',
      };

      try {
        await metricConfigStore.getMetricConfigSummaries(filters);
        expect.fail('Should have thrown an error');
      } catch (error: unknown) {
        if (!(error instanceof Error)) {
          throw error;
        }
        expect(error.message).to.equal('Failed to get metric configurations');
      }
    });
  });

  describe('putMetricConfig', () => {
    let context: ContextService;
    let logger: WinstonLogger;
    let metricConfigStore: MetricConfigStore;
    let mockEntityManager: any;
    let mockCustomRepo: any;

    beforeEach(() => {
      context = new ContextService();
      logger = new WinstonLogger(context);
      metricConfigStore = new MetricConfigStore(context, logger);

      // Mock the database provider and entity manager
      mockEntityManager = {
        getRepository: sinon.stub(),
      };

      mockCustomRepo = {
        findOne: sinon.stub(),
        save: sinon.stub(),
      };

      mockEntityManager.getRepository.returns(mockCustomRepo);

      // Mock the dbProvider
      sinon.stub(metricConfigStore, 'dbProvider').get(() => ({
        processFlowPostgres: {
          datasource: {
            manager: mockEntityManager,
          },
        },
      }));

      // Mock facility maps and selectedFacilityId for last_updated_timestamp key creation
      sinon.stub(context, 'facilityMaps').get(() => [
        {
          id: 'test-facility',
          name: 'Test Facility',
          tenantId: 'test-tenant',
          facilityId: 'test-facility-id',
          dataset: 'test-dataset',
          default: true,
        },
      ]);
      sinon.stub(context, 'selectedFacilityId').get(() => 'test-facility');
    });

    afterEach(() => {
      sinon.restore();
    });

    it('should create a new node metric configuration when none exists', async () => {
      const input: MetricConfigDetail = {
        metricConfigName: 'test-node-metric',
        configType: 'node',
        views: ['facility', 'multishuttle'],
        matchConditions: {eventType: 'test-event'},
        factType: 'test-fact',
        sourceSystem: 'diq',
        displayName: 'Test Node Metric',
        thresholdConfig: {
          threshold_type: 'numerical',
          target_value: 50,
          low_critical_range: 0,
          high_critical_range: 100,
        },
        exampleMessage: {test: 'data'},
        enabled: true,
        active: true,
        isCustom: true,
        nodeName: 'test-node',
        metricType: 'stockTime',
        timeWindow: '60m_set',
        aggregation: 'sum',
        redisOperation: 'event_set',
        metricUnits: '/hr',
        graphOperation: 'area_node',
        redisParams: {rowHash: '{rowHash}'},
        label: 'Area',
        parentNodes: ['facility'],
      };

      const facilityId = 'test-facility';

      // Mock that no existing config is found
      mockCustomRepo.findOne.resolves(null);

      // Mock default repository (no default config exists)
      const mockDefaultRepo = {
        findOne: sinon.stub().resolves(null),
        save: sinon.stub(),
      };

      mockEntityManager.getRepository
        .withArgs(CustomMetricConfigurationEntity)
        .returns(mockCustomRepo);
      mockEntityManager.getRepository
        .withArgs(DefaultMetricConfigurationEntity)
        .returns(mockDefaultRepo);

      // Mock the save operation
      const savedConfig = new CustomMetricConfigurationEntity();
      Object.assign(savedConfig, {
        id: 'new-config-id',
        ...input,
        facilityId,
      });
      mockCustomRepo.save.resolves(savedConfig);

      const result = await metricConfigStore.putMetricConfig(input, facilityId);

      expect(result.isNew).to.be.true;
      expect(result.config).to.deep.equal(savedConfig);
      expect(
        mockCustomRepo.findOne.calledOnceWith({
          where: {
            metricConfigName: input.metricConfigName,
            facilityId,
          },
        }),
      ).to.be.true;
      expect(mockCustomRepo.save.calledOnce).to.be.true;
    });

    it('should update an existing node metric configuration', async () => {
      const input: MetricConfigDetail = {
        metricConfigName: 'existing-node-metric',
        configType: 'node',
        views: ['facility', 'multishuttle'],
        matchConditions: {eventType: 'test-event'},
        factType: 'updated-fact',
        sourceSystem: 'diq',
        displayName: 'Updated Node Metric',
        thresholdConfig: {
          threshold_type: 'numerical',
          target_value: 50,
          low_warning_range: 10,
          high_warning_range: 90,
        },
        exampleMessage: {updated: 'data'},
        enabled: false,
        active: false,
        isCustom: true,
        nodeName: 'updated-node',
        metricType: 'updatedType',
        timeWindow: '60m_set',
        aggregation: 'avg',
        redisOperation: 'event_set',
        metricUnits: '/min',
        graphOperation: 'area_node',
        redisParams: {updatedHash: '{updatedHash}'},
        label: 'Area',
        parentNodes: ['facility', 'zone'],
      };

      const facilityId = 'test-facility';

      // Mock existing config
      const existingConfig = new CustomMetricConfigurationEntity();
      existingConfig.id = 'existing-config-id';
      existingConfig.metricConfigName = input.metricConfigName;
      existingConfig.facilityId = facilityId;
      existingConfig.factType = 'old-fact';
      existingConfig.displayName = 'Old Display Name';

      mockCustomRepo.findOne.resolves(existingConfig);

      // Mock the save operation with updated config
      const updatedConfig = new CustomMetricConfigurationEntity();
      Object.assign(updatedConfig, {
        ...existingConfig,
        ...input,
        facilityId,
      });
      mockCustomRepo.save.resolves(updatedConfig);

      const result = await metricConfigStore.putMetricConfig(input, facilityId);

      expect(result.isNew).to.be.false;
      expect(result.config).to.deep.equal(updatedConfig);
      expect(
        mockCustomRepo.findOne.calledOnceWith({
          where: {
            metricConfigName: input.metricConfigName,
            facilityId,
          },
        }),
      ).to.be.true;
      expect(mockCustomRepo.save.calledOnceWith(existingConfig)).to.be.true;
    });

    it('should create a new inbound-edge metric configuration', async () => {
      const input: MetricConfigDetail = {
        metricConfigName: 'test-inbound-metric',
        configType: 'inbound-edge',
        views: ['facility', 'multishuttle'],
        matchConditions: {eventType: 'arrival'},
        factType: 'inbound-fact',
        sourceSystem: 'diq',
        displayName: 'Test Inbound Metric',
        enabled: true,
        active: true,
        isCustom: true,
        huId: 'handlingUnitCode',
        inboundArea: 'multishuttle',
        redisOperation: 'event_set',
        metricUnits: 'units/hr',
        inboundParentNodes: ['facility'],
        graphOperation: 'area_edge',
        redisParams: {huId: '{handlingUnitCode}'},
        label: 'Area',
        parentNodes: ['facility'],
      };

      const facilityId = 'test-facility';

      mockCustomRepo.findOne.resolves(null);

      const savedConfig = new CustomMetricConfigurationEntity();
      Object.assign(savedConfig, {
        id: 'new-inbound-config-id',
        ...input,
        facilityId,
      });
      mockCustomRepo.save.resolves(savedConfig);

      const result = await metricConfigStore.putMetricConfig(input, facilityId);

      expect(result.isNew).to.be.true;
      expect(result.config).to.deep.equal(savedConfig);
    });

    it('should create a new outbound-edge metric configuration', async () => {
      const input: MetricConfigDetail = {
        metricConfigName: 'test-outbound-metric',
        configType: 'outbound-edge',
        views: ['facility', 'multishuttle'],
        matchConditions: {eventType: 'departure'},
        factType: 'outbound-fact',
        sourceSystem: 'diq',
        displayName: 'Test Outbound Metric',
        enabled: true,
        active: true,
        isCustom: true,
        outboundArea: 'multishuttle',
        huId: 'handlingUnitCode',
        units: 'handlingUnit',
        outboundParentNodes: ['facility'],
        graphOperation: 'area_edge',
        redisParams: {huId: '{handlingUnitCode}'},
        label: 'Area',
        parentNodes: ['facility'],
      };

      const facilityId = 'test-facility';

      mockCustomRepo.findOne.resolves(null);

      const savedConfig = new CustomMetricConfigurationEntity();
      Object.assign(savedConfig, {
        id: 'new-outbound-config-id',
        ...input,
        facilityId,
      });
      mockCustomRepo.save.resolves(savedConfig);

      const result = await metricConfigStore.putMetricConfig(input, facilityId);

      expect(result.isNew).to.be.true;
      expect(result.config).to.deep.equal(savedConfig);
    });

    it('should create a new complete-edge metric configuration', async () => {
      const input: MetricConfigDetail = {
        metricConfigName: 'test-complete-metric',
        configType: 'complete-edge',
        views: ['facility', 'multishuttle'],
        matchConditions: {eventType: 'complete'},
        factType: 'complete-fact',
        sourceSystem: 'diq',
        displayName: 'Test Complete Metric',
        enabled: true,
        active: true,
        isCustom: true,
        inboundArea: 'multishuttle',
        outboundArea: 'sortation',
        redisOperation: 'event_set',
        outboundNodeLabel: 'SortArea',
        inboundParentNodes: ['facility'],
        outboundParentNodes: ['facility'],
        metricUnits: 'units/hr',
        graphOperation: 'area_edge',
        redisParams: {edgeId: '{edgeId}'},
        label: 'Area',
        parentNodes: ['facility'],
      };

      const facilityId = 'test-facility';

      mockCustomRepo.findOne.resolves(null);

      const savedConfig = new CustomMetricConfigurationEntity();
      Object.assign(savedConfig, {
        id: 'new-complete-config-id',
        ...input,
        facilityId,
      });
      mockCustomRepo.save.resolves(savedConfig);

      const result = await metricConfigStore.putMetricConfig(input, facilityId);

      expect(result.isNew).to.be.true;
      expect(result.config).to.deep.equal(savedConfig);
    });

    it('should handle database save errors', async () => {
      const input: MetricConfigDetail = {
        metricConfigName: 'test-error-metric',
        configType: 'node',
        views: ['facility'],
        matchConditions: {eventType: 'test'},
        factType: 'test-fact',
        sourceSystem: 'diq',
        displayName: 'Test Error Metric',
        enabled: true,
        active: true,
        isCustom: true,
        nodeName: 'test-node',
        metricType: 'testType',
        timeWindow: '60m_set',
        aggregation: 'sum',
        redisOperation: 'event_set',
        graphOperation: 'area_node',
      };

      const facilityId = 'test-facility';

      mockCustomRepo.findOne.resolves(null);
      mockCustomRepo.save.rejects(new Error('Database connection failed'));

      try {
        await metricConfigStore.putMetricConfig(input, facilityId);
        expect.fail('Should have thrown an error');
      } catch (error: unknown) {
        if (!(error instanceof Error)) {
          throw error;
        }
        expect(error.message).to.equal('Database connection failed');
      }
    });

    it('should handle database find errors', async () => {
      const input: MetricConfigDetail = {
        metricConfigName: 'test-find-error-metric',
        configType: 'node',
        views: ['facility'],
        matchConditions: {eventType: 'test'},
        factType: 'test-fact',
        sourceSystem: 'diq',
        displayName: 'Test Find Error Metric',
        enabled: true,
        active: true,
        isCustom: true,
        nodeName: 'test-node',
        metricType: 'testType',
        timeWindow: '60m_set',
        aggregation: 'sum',
        redisOperation: 'event_set',
        graphOperation: 'area_node',
      };

      const facilityId = 'test-facility';

      mockCustomRepo.findOne.rejects(new Error('Database query failed'));

      try {
        await metricConfigStore.putMetricConfig(input, facilityId);
        expect.fail('Should have thrown an error');
      } catch (error: unknown) {
        if (!(error instanceof Error)) {
          throw error;
        }
        expect(error.message).to.equal('Database query failed');
      }
    });

    it('should handle configuration with minimal required fields', async () => {
      const input: MetricConfigDetail = {
        metricConfigName: 'minimal-metric',
        configType: 'node',
        views: ['facility'],
        matchConditions: {eventType: 'test'},
        factType: 'test-fact',
        sourceSystem: 'diq',
        displayName: 'Minimal Metric',
        isCustom: true,
        nodeName: 'test-node',
        metricType: 'testType',
        timeWindow: '60m_set',
        aggregation: 'sum',
        redisOperation: 'event_set',
        graphOperation: 'area_node',
      };

      const facilityId = 'test-facility';

      mockCustomRepo.findOne.resolves(null);

      const savedConfig = new CustomMetricConfigurationEntity();
      Object.assign(savedConfig, {
        id: 'minimal-config-id',
        ...input,
        facilityId,
      });
      mockCustomRepo.save.resolves(savedConfig);

      const result = await metricConfigStore.putMetricConfig(input, facilityId);

      expect(result.isNew).to.be.true;
      expect(result.config).to.deep.equal(savedConfig);
    });
  });

  describe('getMetricConfigDetail', () => {
    let context: ContextService;
    let logger: WinstonLogger;
    let metricConfigStore: MetricConfigStore;
    let mockEntityManager: any;
    let mockDefaultRepo: any;
    let mockCustomRepo: any;

    beforeEach(() => {
      context = new ContextService();
      logger = new WinstonLogger(context);
      metricConfigStore = new MetricConfigStore(context, logger);

      // Mock the database provider and entity manager
      mockEntityManager = {
        getRepository: sinon.stub(),
      };

      mockDefaultRepo = {
        findOne: sinon.stub(),
      };

      mockCustomRepo = {
        findOne: sinon.stub(),
      };

      mockEntityManager.getRepository
        .withArgs(DefaultMetricConfigurationEntity)
        .returns(mockDefaultRepo);
      mockEntityManager.getRepository
        .withArgs(CustomMetricConfigurationEntity)
        .returns(mockCustomRepo);

      // Mock the dbProvider
      sinon.stub(metricConfigStore, 'dbProvider').get(() => ({
        processFlowPostgres: {
          datasource: {
            manager: mockEntityManager,
          },
        },
      }));
    });

    afterEach(() => {
      sinon.restore();
    });

    it('should return both default and custom configurations when both exist', async () => {
      const metricName = 'test-metric';
      const facilityId = 'test-facility';

      const mockDefaultConfig = new DefaultMetricConfigurationEntity();
      Object.assign(mockDefaultConfig, {
        id: 'default-1',
        metricConfigName: metricName,
        configType: 'node',
        factType: 'test-fact',
        enabled: {default: true},
        active: {default: true},
      });

      const mockCustomConfig = new CustomMetricConfigurationEntity();
      Object.assign(mockCustomConfig, {
        id: 'custom-1',
        metricConfigName: metricName,
        configType: 'node',
        factType: 'test-fact',
        facilityId,
        enabled: true,
        active: true,
      });

      mockDefaultRepo.findOne.resolves(mockDefaultConfig);
      mockCustomRepo.findOne.resolves(mockCustomConfig);

      const result = await metricConfigStore.getMetricConfigDetail(
        metricName,
        facilityId,
        undefined,
      );

      expect(result.default).to.deep.equal(mockDefaultConfig);
      expect(result.custom).to.deep.equal(mockCustomConfig);
      expect(
        mockDefaultRepo.findOne.calledWith({
          where: {metricConfigName: metricName},
        }),
      ).to.be.true;
      expect(
        mockCustomRepo.findOne.calledWith({
          where: {metricConfigName: metricName, facilityId},
        }),
      ).to.be.true;
    });

    it('should return only default configuration when configType is "default"', async () => {
      const metricName = 'test-metric';
      const facilityId = 'test-facility';

      const mockDefaultConfig = new DefaultMetricConfigurationEntity();
      Object.assign(mockDefaultConfig, {
        id: 'default-1',
        metricConfigName: metricName,
        configType: 'node',
        factType: 'test-fact',
        enabled: {default: true},
        active: {default: true},
      });

      mockDefaultRepo.findOne.resolves(mockDefaultConfig);

      const result = await metricConfigStore.getMetricConfigDetail(
        metricName,
        facilityId,
        'default',
      );

      expect(result.default).to.deep.equal(mockDefaultConfig);
      expect(result.custom).to.be.undefined;
      expect(mockDefaultRepo.findOne.calledOnce).to.be.true;
      expect(mockCustomRepo.findOne.called).to.be.false;
    });

    it('should return only custom configuration when configType is "custom"', async () => {
      const metricName = 'test-metric';
      const facilityId = 'test-facility';

      const mockCustomConfig = new CustomMetricConfigurationEntity();
      Object.assign(mockCustomConfig, {
        id: 'custom-1',
        metricConfigName: metricName,
        configType: 'node',
        factType: 'test-fact',
        facilityId,
        enabled: true,
        active: true,
      });

      mockCustomRepo.findOne.resolves(mockCustomConfig);

      const result = await metricConfigStore.getMetricConfigDetail(
        metricName,
        facilityId,
        'custom',
      );

      expect(result.default).to.be.undefined;
      expect(result.custom).to.deep.equal(mockCustomConfig);
      expect(mockDefaultRepo.findOne.called).to.be.false;
      expect(mockCustomRepo.findOne.calledOnce).to.be.true;
    });

    it('should return empty result when no configurations exist', async () => {
      const metricName = 'nonexistent-metric';
      const facilityId = 'test-facility';

      mockDefaultRepo.findOne.resolves(null);
      mockCustomRepo.findOne.resolves(null);

      const result = await metricConfigStore.getMetricConfigDetail(
        metricName,
        facilityId,
        undefined,
      );

      expect(result.default).to.be.undefined;
      expect(result.custom).to.be.undefined;
    });
  });

  describe('getMetricConfigFacts', () => {
    it('should return metric configuration facts with statistics', async () => {
      const context = new ContextService();
      const logger = new WinstonLogger(context);
      const metricConfigStore = new MetricConfigStore(context, logger);

      const facilityId = 'test-facility';
      const mockQueryResults = [
        {
          factType: 'inventory',
          totalConfigs: 5,
          enabledConfigs: 3,
          active: true,
        },
        {
          factType: 'shipping',
          totalConfigs: 2,
          enabledConfigs: 1,
          active: false,
        },
      ];

      const expectedResults: MetricConfigFact[] = [
        {
          factType: 'inventory',
          totalConfigs: 5,
          enabledConfigs: 3,
          active: true,
        },
        {
          factType: 'shipping',
          totalConfigs: 2,
          enabledConfigs: 1,
          active: false,
        },
      ];

      // Mock the database provider and entity manager
      const mockEntityManager = {
        query: sinon.stub().resolves(mockQueryResults),
      };

      const mockDbProvider = {
        processFlowPostgres: {
          datasource: {
            manager: mockEntityManager,
          },
        },
      };

      sinon.stub(metricConfigStore, 'dbProvider').get(() => mockDbProvider);

      const result = await metricConfigStore.getMetricConfigFacts(facilityId);

      expect(result).to.deep.equal(expectedResults);
      sinon.assert.calledOnce(mockEntityManager.query);
      sinon.assert.calledWith(mockEntityManager.query, sinon.match.string, [
        facilityId,
      ]);
    });

    it('should handle empty results', async () => {
      const context = new ContextService();
      const logger = new WinstonLogger(context);
      const metricConfigStore = new MetricConfigStore(context, logger);

      const facilityId = 'test-facility';
      const mockQueryResults: any[] = [];

      // Mock the database provider and entity manager
      const mockEntityManager = {
        query: sinon.stub().resolves(mockQueryResults),
      };

      const mockDbProvider = {
        processFlowPostgres: {
          datasource: {
            manager: mockEntityManager,
          },
        },
      };

      sinon.stub(metricConfigStore, 'dbProvider').get(() => mockDbProvider);

      const result = await metricConfigStore.getMetricConfigFacts(facilityId);

      expect(result).to.be.an('array').that.is.empty;
      sinon.assert.calledOnce(mockEntityManager.query);
    });

    it('should return database results with proper types', async () => {
      const context = new ContextService();
      const logger = new WinstonLogger(context);
      const metricConfigStore = new MetricConfigStore(context, logger);

      const facilityId = 'test-facility';
      const mockQueryResults = [
        {
          factType: 'fault_event',
          totalConfigs: 10,
          enabledConfigs: 8,
          active: true,
        },
      ];

      const expectedResult: MetricConfigFact[] = [
        {
          factType: 'fault_event',
          totalConfigs: 10,
          enabledConfigs: 8,
          active: true,
        },
      ];

      // Mock the database provider and entity manager
      const mockEntityManager = {
        query: sinon.stub().resolves(mockQueryResults),
      };

      const mockDbProvider = {
        processFlowPostgres: {
          datasource: {
            manager: mockEntityManager,
          },
        },
      };

      sinon.stub(metricConfigStore, 'dbProvider').get(() => mockDbProvider);

      const result = await metricConfigStore.getMetricConfigFacts(facilityId);

      expect(result).to.deep.equal(expectedResult);
      expect(result[0].factType).to.equal('fault_event');
      expect(result[0].totalConfigs).to.be.a('number').and.equal(10);
      expect(result[0].enabledConfigs).to.be.a('number').and.equal(8);
      expect(result[0].active).to.be.a('boolean').and.equal(true);
    });
  });

  describe('deleteMetricConfig', () => {
    let context: ContextService;
    let logger: WinstonLogger;
    let metricConfigStore: MetricConfigStore;
    let mockEntityManager: any;
    let mockCustomRepo: any;

    beforeEach(() => {
      context = new ContextService();
      logger = new WinstonLogger(context);
      metricConfigStore = new MetricConfigStore(context, logger);

      // Mock the database provider and entity manager
      mockEntityManager = {
        getRepository: sinon.stub(),
      };

      mockCustomRepo = {
        findOne: sinon.stub(),
        delete: sinon.stub(),
      };

      mockEntityManager.getRepository.returns(mockCustomRepo);

      // Mock the dbProvider
      sinon.stub(metricConfigStore, 'dbProvider').get(() => ({
        processFlowPostgres: {
          datasource: {
            manager: mockEntityManager,
          },
        },
      }));
    });

    afterEach(() => {
      sinon.restore();
    });

    it('should delete existing metric configuration and return the deleted entity', async () => {
      const configId = 'test-config-id';
      const existingConfig = new CustomMetricConfigurationEntity();
      Object.assign(existingConfig, {
        id: configId,
        metricConfigName: 'test-metric',
        facilityId: 'test-facility',
        configType: 'node',
        factType: 'test-fact',
        enabled: true,
        active: true,
      });

      mockCustomRepo.findOne.resolves(existingConfig);
      mockCustomRepo.delete.resolves({affected: 1});

      const result = await metricConfigStore.deleteMetricConfig(configId);

      expect(result).to.deep.equal(existingConfig);
      expect(
        mockCustomRepo.findOne.calledOnceWith({
          where: {id: configId},
        }),
      ).to.be.true;
      expect(mockCustomRepo.delete.calledOnceWith(configId)).to.be.true;
    });

    it('should return null when metric configuration does not exist', async () => {
      const configId = 'nonexistent-config-id';

      mockCustomRepo.findOne.resolves(null);

      const result = await metricConfigStore.deleteMetricConfig(configId);

      expect(result).to.be.null;
      expect(
        mockCustomRepo.findOne.calledOnceWith({
          where: {id: configId},
        }),
      ).to.be.true;
      expect(mockCustomRepo.delete.called).to.be.false;
    });

    it('should handle database find errors', async () => {
      const configId = 'test-config-id';

      mockCustomRepo.findOne.rejects(new Error('Database connection failed'));

      try {
        await metricConfigStore.deleteMetricConfig(configId);
        expect.fail('Should have thrown an error');
      } catch (error: unknown) {
        if (!(error instanceof Error)) {
          throw error;
        }
        expect(error.message).to.equal('Database connection failed');
      }
    });

    it('should handle database delete errors', async () => {
      const configId = 'test-config-id';
      const existingConfig = new CustomMetricConfigurationEntity();
      Object.assign(existingConfig, {
        id: configId,
        metricConfigName: 'test-metric',
        facilityId: 'test-facility',
      });

      mockCustomRepo.findOne.resolves(existingConfig);
      mockCustomRepo.delete.rejects(new Error('Delete operation failed'));

      try {
        await metricConfigStore.deleteMetricConfig(configId);
        expect.fail('Should have thrown an error');
      } catch (error: unknown) {
        if (!(error instanceof Error)) {
          throw error;
        }
        expect(error.message).to.equal('Delete operation failed');
      }
    });

    it('should return deleted entity even if no rows were affected', async () => {
      const configId = 'test-config-id';
      const existingConfig = new CustomMetricConfigurationEntity();
      Object.assign(existingConfig, {
        id: configId,
        metricConfigName: 'test-metric',
        facilityId: 'test-facility',
      });

      mockCustomRepo.findOne.resolves(existingConfig);
      mockCustomRepo.delete.resolves({affected: 0});

      const result = await metricConfigStore.deleteMetricConfig(configId);

      expect(result).to.deep.equal(existingConfig);
      expect(mockCustomRepo.delete.calledOnceWith(configId)).to.be.true;
    });
  });

  describe('getMetricConfigs', () => {
    let context: ContextService;
    let logger: WinstonLogger;
    let metricConfigStore: MetricConfigStore;
    let mockEntityManager: any;
    let mockDefaultRepo: any;
    let mockCustomRepo: any;

    beforeEach(() => {
      context = new ContextService();
      logger = new WinstonLogger(context);
      metricConfigStore = new MetricConfigStore(context, logger);

      // Mock the database provider and entity manager
      mockEntityManager = {
        getRepository: sinon.stub(),
      };

      mockDefaultRepo = {
        find: sinon.stub(),
      };

      mockCustomRepo = {
        find: sinon.stub(),
      };

      mockEntityManager.getRepository
        .withArgs(DefaultMetricConfigurationEntity)
        .returns(mockDefaultRepo);
      mockEntityManager.getRepository
        .withArgs(CustomMetricConfigurationEntity)
        .returns(mockCustomRepo);

      // Mock the dbProvider
      sinon.stub(metricConfigStore, 'dbProvider').get(() => ({
        processFlowPostgres: {
          datasource: {
            manager: mockEntityManager,
          },
        },
      }));
    });

    afterEach(() => {
      sinon.restore();
    });

    it('should return custom configs when they exist and are enabled', async () => {
      const facilityId = 'test-facility';
      const metricNames = ['metric1', 'metric2'];

      const mockCustomConfig = new CustomMetricConfigurationEntity();
      mockCustomConfig.metricConfigName = 'metric1';
      mockCustomConfig.facilityId = facilityId;
      mockCustomConfig.enabled = true;
      mockCustomConfig.thresholdConfig = {
        threshold_type: 'numerical',
        target_value: 100,
        low_warning_range: 10,
        high_warning_range: 10,
      };

      mockCustomRepo.find.resolves([mockCustomConfig]);
      mockDefaultRepo.find.resolves([]);

      const result = await metricConfigStore.getMetricConfigs(
        metricNames,
        facilityId,
      );

      expect(result.size).to.equal(2);
      expect(result.get('metric1')).to.deep.equal(mockCustomConfig);
      expect(result.get('metric2')).to.be.null;
    });

    it('should return default configs when no custom configs exist', async () => {
      const facilityId = 'test-facility';
      const metricNames = ['metric1'];

      const mockDefaultConfig = new DefaultMetricConfigurationEntity();
      mockDefaultConfig.metricConfigName = 'metric1';
      mockDefaultConfig.enabled = {[facilityId]: true};
      mockDefaultConfig.thresholdConfig = {
        threshold_type: 'numerical',
        target_value: 100,
        low_warning_range: 10,
        high_warning_range: 10,
      };

      mockCustomRepo.find.resolves([]);
      mockDefaultRepo.find.resolves([mockDefaultConfig]);

      const result = await metricConfigStore.getMetricConfigs(
        metricNames,
        facilityId,
      );

      expect(result.size).to.equal(1);
      expect(result.get('metric1')).to.deep.equal(mockDefaultConfig);
    });

    it('should prefer custom configs over default configs', async () => {
      const facilityId = 'test-facility';
      const metricNames = ['metric1'];

      const mockCustomConfig = new CustomMetricConfigurationEntity();
      mockCustomConfig.metricConfigName = 'metric1';
      mockCustomConfig.facilityId = facilityId;
      mockCustomConfig.enabled = true;
      mockCustomConfig.thresholdConfig = {
        threshold_type: 'numerical',
        target_value: 200, // Different from default
        low_warning_range: 20,
        high_warning_range: 20,
      };

      const mockDefaultConfig = new DefaultMetricConfigurationEntity();
      mockDefaultConfig.metricConfigName = 'metric1';
      mockDefaultConfig.enabled = {[facilityId]: true};
      mockDefaultConfig.thresholdConfig = {
        threshold_type: 'numerical',
        target_value: 100,
        low_warning_range: 10,
        high_warning_range: 10,
      };

      mockCustomRepo.find.resolves([mockCustomConfig]);
      mockDefaultRepo.find.resolves([mockDefaultConfig]);

      const result = await metricConfigStore.getMetricConfigs(
        metricNames,
        facilityId,
      );

      expect(result.size).to.equal(1);
      expect(result.get('metric1')).to.deep.equal(mockCustomConfig);
      expect(result.get('metric1')?.thresholdConfig?.target_value).to.equal(
        200,
      );
    });

    it('should skip disabled custom configs and use default configs', async () => {
      const facilityId = 'test-facility';
      const metricNames = ['metric1'];

      const mockCustomConfig = new CustomMetricConfigurationEntity();
      mockCustomConfig.metricConfigName = 'metric1';
      mockCustomConfig.facilityId = facilityId;
      mockCustomConfig.enabled = false; // Disabled custom config
      mockCustomConfig.thresholdConfig = {
        threshold_type: 'numerical',
        target_value: 200,
        low_warning_range: 20,
        high_warning_range: 20,
      };

      const mockDefaultConfig = new DefaultMetricConfigurationEntity();
      mockDefaultConfig.metricConfigName = 'metric1';
      mockDefaultConfig.enabled = {[facilityId]: true};
      mockDefaultConfig.thresholdConfig = {
        threshold_type: 'numerical',
        target_value: 100,
        low_warning_range: 10,
        high_warning_range: 10,
      };

      mockCustomRepo.find.resolves([]); // No enabled custom configs returned
      mockDefaultRepo.find.resolves([mockDefaultConfig]);

      const result = await metricConfigStore.getMetricConfigs(
        metricNames,
        facilityId,
      );

      expect(result.size).to.equal(1);
      expect(result.get('metric1')).to.deep.equal(mockDefaultConfig);
      expect(result.get('metric1')?.thresholdConfig?.target_value).to.equal(
        100,
      );
    });

    it('should handle HSTORE enabled field for default configs', async () => {
      const facilityId = 'test-facility';
      const metricNames = ['metric1'];

      const mockDefaultConfig = new DefaultMetricConfigurationEntity();
      mockDefaultConfig.metricConfigName = 'metric1';
      mockDefaultConfig.enabled = {[facilityId]: false}; // Disabled for this facility
      mockDefaultConfig.thresholdConfig = {
        threshold_type: 'numerical',
        target_value: 100,
        low_warning_range: 10,
        high_warning_range: 10,
      };

      mockCustomRepo.find.resolves([]);
      mockDefaultRepo.find.resolves([mockDefaultConfig]);

      const result = await metricConfigStore.getMetricConfigs(
        metricNames,
        facilityId,
      );

      expect(result.size).to.equal(1);
      expect(result.get('metric1')).to.be.null; // Should be null because disabled
    });

    it('should handle null/undefined enabled field for default configs', async () => {
      const facilityId = 'test-facility';
      const metricNames = ['metric1'];

      const mockDefaultConfig = new DefaultMetricConfigurationEntity();
      mockDefaultConfig.metricConfigName = 'metric1';
      mockDefaultConfig.enabled = null as any; // Null enabled field
      mockDefaultConfig.thresholdConfig = {
        threshold_type: 'numerical',
        target_value: 100,
        low_warning_range: 10,
        high_warning_range: 10,
      };

      mockCustomRepo.find.resolves([]);
      mockDefaultRepo.find.resolves([mockDefaultConfig]);

      const result = await metricConfigStore.getMetricConfigs(
        metricNames,
        facilityId,
      );

      expect(result.size).to.equal(1);
      expect(result.get('metric1')).to.deep.equal(mockDefaultConfig); // Should be included (enabled by default)
    });

    it('should handle empty metric names array', async () => {
      const facilityId = 'test-facility';
      const metricNames: string[] = [];

      mockCustomRepo.find.resolves([]);
      mockDefaultRepo.find.resolves([]);

      const result = await metricConfigStore.getMetricConfigs(
        metricNames,
        facilityId,
      );

      expect(result.size).to.equal(0);
    });

    it('should handle multiple metrics with mixed scenarios', async () => {
      const facilityId = 'test-facility';
      const metricNames = ['metric1', 'metric2', 'metric3', 'metric4'];

      // Custom config for metric1 (enabled)
      const mockCustomConfig1 = new CustomMetricConfigurationEntity();
      mockCustomConfig1.metricConfigName = 'metric1';
      mockCustomConfig1.facilityId = facilityId;
      mockCustomConfig1.enabled = true;
      mockCustomConfig1.thresholdConfig = {
        threshold_type: 'numerical',
        target_value: 100,
      };

      // Default config for metric2 (enabled)
      const mockDefaultConfig2 = new DefaultMetricConfigurationEntity();
      mockDefaultConfig2.metricConfigName = 'metric2';
      mockDefaultConfig2.enabled = {[facilityId]: 'true'} as any;
      mockDefaultConfig2.thresholdConfig = {
        threshold_type: 'numerical',
        target_value: 150,
      };

      // Default config for metric3 (enabled)
      const mockDefaultConfig3 = new DefaultMetricConfigurationEntity();
      mockDefaultConfig3.metricConfigName = 'metric3';
      mockDefaultConfig3.enabled = {[facilityId]: 'true'} as any;
      mockDefaultConfig3.thresholdConfig = {
        threshold_type: 'numerical',
        target_value: 300,
      };

      // Default config for metric4 (disabled)
      const mockDefaultConfig4 = new DefaultMetricConfigurationEntity();
      mockDefaultConfig4.metricConfigName = 'metric4';
      mockDefaultConfig4.enabled = {[facilityId]: 'false'} as any;
      mockDefaultConfig4.thresholdConfig = {
        threshold_type: 'numerical',
        target_value: 400,
      };

      mockCustomRepo.find.resolves([mockCustomConfig1]);
      mockDefaultRepo.find.resolves([
        mockDefaultConfig2,
        mockDefaultConfig3,
        mockDefaultConfig4,
      ]);

      const result = await metricConfigStore.getMetricConfigs(
        metricNames,
        facilityId,
      );

      expect(result.size).to.equal(4);
      expect(result.get('metric1')).to.deep.equal(mockCustomConfig1); // Custom enabled
      expect(result.get('metric2')).to.deep.equal(mockDefaultConfig2); // Default enabled
      expect(result.get('metric3')).to.deep.equal(mockDefaultConfig3); // Default enabled
      expect(result.get('metric4')).to.be.null; // Default disabled
    });
  });

  describe('getMetricConfigDetailById', () => {
    let context: ContextService;
    let logger: WinstonLogger;
    let metricConfigStore: MetricConfigStore;
    let mockEntityManager: any;
    let mockDefaultRepo: any;
    let mockCustomRepo: any;

    beforeEach(() => {
      context = new ContextService();
      logger = new WinstonLogger(context);
      metricConfigStore = new MetricConfigStore(context, logger);

      // Mock the database provider and entity manager
      mockEntityManager = {
        getRepository: sinon.stub(),
      };

      mockDefaultRepo = {
        findOne: sinon.stub(),
      };

      mockCustomRepo = {
        findOne: sinon.stub(),
      };

      mockEntityManager.getRepository
        .withArgs(DefaultMetricConfigurationEntity)
        .returns(mockDefaultRepo);
      mockEntityManager.getRepository
        .withArgs(CustomMetricConfigurationEntity)
        .returns(mockCustomRepo);

      // Mock the dbProvider
      sinon.stub(metricConfigStore, 'dbProvider').get(() => ({
        processFlowPostgres: {
          datasource: {
            manager: mockEntityManager,
          },
        },
      }));
    });

    afterEach(() => {
      sinon.restore();
    });

    it('should return both default and custom configurations when both exist', async () => {
      const metricId = 'test-metric-id';
      const facilityId = 'test-facility';

      const mockDefaultConfig = new DefaultMetricConfigurationEntity();
      Object.assign(mockDefaultConfig, {
        id: metricId,
        metricConfigName: 'test-metric',
        configType: 'node',
        factType: 'test-fact',
        enabled: {default: true},
        active: {default: true},
      });

      const mockCustomConfig = new CustomMetricConfigurationEntity();
      Object.assign(mockCustomConfig, {
        id: metricId,
        metricConfigName: 'test-metric',
        configType: 'node',
        factType: 'test-fact',
        facilityId,
        enabled: true,
        active: true,
      });

      mockDefaultRepo.findOne.resolves(mockDefaultConfig);
      mockCustomRepo.findOne.resolves(mockCustomConfig);

      const result = await metricConfigStore.getMetricConfigDetailById(
        metricId,
        facilityId,
        undefined,
      );

      expect(result.default).to.deep.equal(mockDefaultConfig);
      expect(result.custom).to.deep.equal(mockCustomConfig);
      expect(
        mockDefaultRepo.findOne.calledWith({
          where: {id: metricId},
        }),
      ).to.be.true;
      expect(
        mockCustomRepo.findOne.calledWith({
          where: {id: metricId, facilityId},
        }),
      ).to.be.true;
    });

    it('should return only default configuration when configType is "default"', async () => {
      const metricId = 'test-metric-id';
      const facilityId = 'test-facility';

      const mockDefaultConfig = new DefaultMetricConfigurationEntity();
      Object.assign(mockDefaultConfig, {
        id: metricId,
        metricConfigName: 'test-metric',
        configType: 'node',
        factType: 'test-fact',
        enabled: {default: true},
        active: {default: true},
      });

      mockDefaultRepo.findOne.resolves(mockDefaultConfig);

      const result = await metricConfigStore.getMetricConfigDetailById(
        metricId,
        facilityId,
        'default',
      );

      expect(result.default).to.deep.equal(mockDefaultConfig);
      expect(result.custom).to.be.undefined;
      expect(mockDefaultRepo.findOne.calledOnce).to.be.true;
      expect(mockCustomRepo.findOne.called).to.be.false;
    });

    it('should return only custom configuration when configType is "custom"', async () => {
      const metricId = 'test-metric-id';
      const facilityId = 'test-facility';

      const mockCustomConfig = new CustomMetricConfigurationEntity();
      Object.assign(mockCustomConfig, {
        id: metricId,
        metricConfigName: 'test-metric',
        configType: 'node',
        factType: 'test-fact',
        facilityId,
        enabled: true,
        active: true,
      });

      mockCustomRepo.findOne.resolves(mockCustomConfig);

      const result = await metricConfigStore.getMetricConfigDetailById(
        metricId,
        facilityId,
        'custom',
      );

      expect(result.default).to.be.undefined;
      expect(result.custom).to.deep.equal(mockCustomConfig);
      expect(mockDefaultRepo.findOne.called).to.be.false;
      expect(mockCustomRepo.findOne.calledOnce).to.be.true;
    });

    it('should return empty result when no configurations exist', async () => {
      const metricId = 'nonexistent-metric-id';
      const facilityId = 'test-facility';

      mockDefaultRepo.findOne.resolves(null);
      mockCustomRepo.findOne.resolves(null);

      const result = await metricConfigStore.getMetricConfigDetailById(
        metricId,
        facilityId,
        undefined,
      );

      expect(result.default).to.be.undefined;
      expect(result.custom).to.be.undefined;
    });
  });

  describe('isDefaultConfigEnabledForFacility', () => {
    let context: ContextService;
    let logger: WinstonLogger;
    let metricConfigStore: MetricConfigStore;

    beforeEach(() => {
      context = new ContextService();
      logger = new WinstonLogger(context);
      metricConfigStore = new MetricConfigStore(context, logger);
    });

    afterEach(() => {
      sinon.restore();
    });

    it('should return true when enabled is null', () => {
      const facilityId = 'test-facility';
      const config = new DefaultMetricConfigurationEntity();
      config.enabled = null as any;

      const result = (
        metricConfigStore as any
      ).isDefaultConfigEnabledForFacility(config, facilityId);

      expect(result).to.be.true;
    });

    it('should return true when enabled is undefined', () => {
      const facilityId = 'test-facility';
      const config = new DefaultMetricConfigurationEntity();
      config.enabled = undefined;

      const result = (
        metricConfigStore as any
      ).isDefaultConfigEnabledForFacility(config, facilityId);

      expect(result).to.be.true;
    });

    it('should return true when enabled is empty object', () => {
      const facilityId = 'test-facility';
      const config = new DefaultMetricConfigurationEntity();
      config.enabled = {};

      const result = (
        metricConfigStore as any
      ).isDefaultConfigEnabledForFacility(config, facilityId);

      expect(result).to.be.true;
    });

    it('should return true when facility is not in HSTORE', () => {
      const facilityId = 'test-facility';
      const config = new DefaultMetricConfigurationEntity();
      config.enabled = {'other-facility': true};

      const result = (
        metricConfigStore as any
      ).isDefaultConfigEnabledForFacility(config, facilityId);

      expect(result).to.be.true;
    });

    it('should return true when facility value is true (boolean)', () => {
      const facilityId = 'test-facility';
      const config = new DefaultMetricConfigurationEntity();
      config.enabled = {[facilityId]: true};

      const result = (
        metricConfigStore as any
      ).isDefaultConfigEnabledForFacility(config, facilityId);

      expect(result).to.be.true;
    });

    it('should return false when facility value is false (boolean)', () => {
      const facilityId = 'test-facility';
      const config = new DefaultMetricConfigurationEntity();
      config.enabled = {[facilityId]: false};

      const result = (
        metricConfigStore as any
      ).isDefaultConfigEnabledForFacility(config, facilityId);

      expect(result).to.be.false;
    });

    it('should return true when facility value is "true" string', () => {
      const facilityId = 'test-facility';
      const config = new DefaultMetricConfigurationEntity();
      config.enabled = {[facilityId]: 'true'} as any;

      const result = (
        metricConfigStore as any
      ).isDefaultConfigEnabledForFacility(config, facilityId);

      expect(result).to.be.true;
    });

    it('should return false when facility value is "false" string', () => {
      const facilityId = 'test-facility';
      const config = new DefaultMetricConfigurationEntity();
      config.enabled = {[facilityId]: 'false'} as any;

      const result = (
        metricConfigStore as any
      ).isDefaultConfigEnabledForFacility(config, facilityId);

      expect(result).to.be.false;
    });

    it('should return true when facility value is any other string', () => {
      const facilityId = 'test-facility';
      const config = new DefaultMetricConfigurationEntity();
      config.enabled = {[facilityId]: 'enabled'} as any;

      const result = (
        metricConfigStore as any
      ).isDefaultConfigEnabledForFacility(config, facilityId);

      expect(result).to.be.true;
    });

    it('should handle multiple facilities in HSTORE', () => {
      const facilityId = 'test-facility';
      const config = new DefaultMetricConfigurationEntity();
      config.enabled = {
        facility1: 'true',
        facility2: 'false',
        facility3: 'enabled',
        [facilityId]: 'false',
      } as any;

      const result = (
        metricConfigStore as any
      ).isDefaultConfigEnabledForFacility(config, facilityId);

      expect(result).to.be.false;
    });

    it('should handle mixed boolean and string values in HSTORE', () => {
      const facilityId = 'test-facility';
      const config = new DefaultMetricConfigurationEntity();
      config.enabled = {
        facility1: true,
        facility2: 'false',
        [facilityId]: 'true',
      } as any;

      const result = (
        metricConfigStore as any
      ).isDefaultConfigEnabledForFacility(config, facilityId);

      expect(result).to.be.true;
    });
  });
});
