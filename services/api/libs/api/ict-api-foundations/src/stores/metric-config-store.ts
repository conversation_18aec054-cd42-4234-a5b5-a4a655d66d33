import {
  DefaultMetricConfigurationEntity,
  CustomMetricConfigurationEntity,
} from 'ict-api-schema';
import {SelectQueryBuilder, In} from 'typeorm';
import {DiService} from '../di/type-di.ts';
import {ContextService} from '../context/context-service.ts';
import {WinstonLogger} from '../log/winston-logger.ts';
import {MetricConfigSummaryFilters} from '../defs/metric-config-filters.ts';
import type {
  MetricConfigDetail,
  CustomMetricConfigSaveResult,
} from '../defs/metric-config-detail.ts';
import {MetricConfigFact} from '../defs/metric-config-facts.ts';

@DiService()
export class MetricConfigStore {
  public static readonly type: string = 'metric-config-store';

  constructor(
    private context: ContextService,
    private logger: WinstonLogger,
  ) {}

  get dbProvider() {
    return this.context.dbProvider;
  }

  async getMetricConfigSummaries(
    filters: MetricConfigSummaryFilters,
  ): Promise<
    (DefaultMetricConfigurationEntity | CustomMetricConfigurationEntity)[]
  > {
    this.logger.debug('Getting metric configurations from database', {filters});

    const entityManager =
      this.dbProvider.processFlowPostgres.datasource.manager;
    const defaultRepo = entityManager.getRepository(
      DefaultMetricConfigurationEntity,
    );
    const customRepo = entityManager.getRepository(
      CustomMetricConfigurationEntity,
    );

    // Build query for default configs
    const defaultQueryBuilder = defaultRepo.createQueryBuilder('defaultConfig');
    this.applyFilters(defaultQueryBuilder, filters, 'defaultConfig');

    // Build query for custom configs
    const customQueryBuilder = customRepo.createQueryBuilder('customConfig');
    this.applyFilters(customQueryBuilder, filters, 'customConfig');

    // Get results from both tables
    const [defaultConfigs, customConfigs] = await Promise.all([
      defaultQueryBuilder.getMany(),
      customQueryBuilder.getMany(),
    ]);

    // Create a set of metric names from custom configs
    const customMetricNames = new Set(
      customConfigs.map(config => config.metricConfigName),
    );

    // Filter out default configs that have a custom version
    const filteredDefaultConfigs = defaultConfigs.filter(
      config => !customMetricNames.has(config.metricConfigName),
    );

    // Combine results
    const results = [...filteredDefaultConfigs, ...customConfigs];

    // Transform HSTORE fields to booleans
    const transformedResults = results.map(config =>
      this.transformHstoreFields(config),
    );

    // Apply enabled and active filters in application layer
    const filteredResults = transformedResults.filter(config => {
      // Apply enabled filter (only for default configs)
      if (filters.enabled !== undefined && 'enabled' in config) {
        if (config.enabled !== filters.enabled) {
          return false;
        }
      }

      // Apply active filter
      if (filters.active !== undefined && 'active' in config) {
        if (config.active !== filters.active) {
          return false;
        }
      }

      return true;
    });

    this.logger.debug('Retrieved metric configurations from database', {
      count: filteredResults.length,
      defaultCount: defaultConfigs.length,
      customCount: customConfigs.length,
    });

    return filteredResults;
  }

  private applyFilters<
    T extends
      | DefaultMetricConfigurationEntity
      | CustomMetricConfigurationEntity,
  >(
    queryBuilder: SelectQueryBuilder<T>,
    filters: MetricConfigSummaryFilters,
    alias: string,
  ): void {
    if (filters.metricName) {
      queryBuilder.andWhere(`"${alias}".metric_config_name LIKE :metricName`, {
        metricName: `%${filters.metricName}%`,
      });
    }

    if (filters.metricId) {
      queryBuilder.andWhere(`"${alias}".id = :metricId`, {
        metricId: filters.metricId,
      });
    }

    if (filters.factType) {
      queryBuilder.andWhere(`"${alias}".fact_type = :factType`, {
        factType: filters.factType,
      });
    }

    if (filters.nodeName) {
      queryBuilder.andWhere(`"${alias}".node_name LIKE :nodeName`, {
        nodeName: `%${filters.nodeName}%`,
      });
    }

    if (filters.configType) {
      queryBuilder.andWhere(`"${alias}".config_type = :configType`, {
        configType: filters.configType,
      });
    }

    // Note: enabled and active filters are handled in application layer
    // to avoid HSTORE parsing issues in the database
  }

  private transformHstoreFields<
    T extends
      | DefaultMetricConfigurationEntity
      | CustomMetricConfigurationEntity,
  >(config: T, facilityId?: string): T {
    // Transform active field based on facility-specific HSTORE logic
    if ('active' in config) {
      const activeValue = config.active as unknown;

      // If null/undefined/empty, default to false for active
      if (
        activeValue === null ||
        activeValue === undefined ||
        activeValue === '{}' ||
        (typeof activeValue === 'object' &&
          activeValue !== null &&
          Object.keys(activeValue).length === 0)
      ) {
        config.active = false;
      } else if (
        typeof activeValue === 'object' &&
        activeValue !== null &&
        facilityId
      ) {
        // For HSTORE with facility ID, check facility-specific value
        const hstoreValue = activeValue as Record<string, string>;
        if (facilityId in hstoreValue) {
          // Only 'false' string disables, everything else enables
          config.active = hstoreValue[facilityId] !== 'false';
        } else {
          // If facility not in HSTORE, default to false for active
          config.active = false;
        }
      } else if (typeof activeValue === 'boolean') {
        // Direct boolean value
        config.active = activeValue;
      } else {
        // Fallback to false for active
        config.active = false;
      }
    }

    // Transform enabled field based on facility-specific HSTORE logic
    if ('enabled' in config) {
      const enabledValue = config.enabled as unknown;

      // If null/undefined/empty, default to true for enabled
      if (
        enabledValue === null ||
        enabledValue === undefined ||
        enabledValue === '{}' ||
        (typeof enabledValue === 'object' &&
          enabledValue !== null &&
          Object.keys(enabledValue).length === 0)
      ) {
        config.enabled = true;
      } else if (
        typeof enabledValue === 'object' &&
        enabledValue !== null &&
        facilityId
      ) {
        // For HSTORE with facility ID, check facility-specific value
        const hstoreValue = enabledValue as Record<string, string>;
        if (facilityId in hstoreValue) {
          // Only 'false' string disables, everything else enables
          config.enabled = hstoreValue[facilityId] !== 'false';
        } else {
          // If facility not in HSTORE, default to true for enabled
          config.enabled = true;
        }
      } else if (typeof enabledValue === 'boolean') {
        // Direct boolean value
        config.enabled = enabledValue;
      } else {
        // Fallback to true for enabled
        config.enabled = true;
      }
    }

    return config;
  }

  async putMetricConfig(
    input: MetricConfigDetail,
    facilityId: string,
  ): Promise<CustomMetricConfigSaveResult> {
    this.logger.debug('Updating or creating metric configuration', {
      metricConfigName: input.metricConfigName,
      configType: input.configType,
      facilityId,
    });

    const entityManager =
      this.dbProvider.processFlowPostgres.datasource.manager;
    const customRepo = entityManager.getRepository(
      CustomMetricConfigurationEntity,
    );

    // Check if a custom config already exists
    const existingConfig = await customRepo.findOne({
      where: {
        metricConfigName: input.metricConfigName,
        facilityId,
      },
    });

    let config: CustomMetricConfigurationEntity;
    let isNew = false;

    // Create the base config object
    const baseConfig = {
      factType: input.factType,
      sourceSystem: input.sourceSystem,
      displayName: input.displayName,
      description: input.description,
      thresholdConfig: input.thresholdConfig,
      exampleMessage: input.exampleMessage,
      configType: input.configType,
      views: input.views,
      matchConditions: input.matchConditions,
      redisParams: input.redisParams,
      label: input.label,
      parentNodes: input.parentNodes,
      active: input.active,
      enabled: input.enabled,
      graphOperation: input.graphOperation,
    };

    // Add config type specific fields
    const configWithSpecificFields = {
      ...baseConfig,
      ...this.getConfigTypeSpecificFields(input),
    };

    if (existingConfig) {
      // Update existing config
      this.logger.debug('Updating existing metric configuration', {
        id: existingConfig.id,
      });

      Object.assign(existingConfig, configWithSpecificFields);
      config = await customRepo.save(existingConfig);
    } else {
      // Create new config
      this.logger.debug('Creating new metric configuration');

      const newConfig = new CustomMetricConfigurationEntity();
      Object.assign(newConfig, {
        metricConfigName: input.metricConfigName,
        facilityId,
        ...configWithSpecificFields,
      });

      config = await customRepo.save(newConfig);
      isNew = true;
    }

    this.logger.debug('Metric configuration saved successfully', {
      id: config.id,
      isNew,
    });

    return {config, isNew};
  }

  private getConfigTypeSpecificFields(input: MetricConfigDetail) {
    switch (input.configType) {
      case 'node':
        return {
          nodeName: input.nodeName,
          metricType: input.metricType,
          timeWindow: input.timeWindow,
          aggregation: input.aggregation,
          redisOperation: input.redisOperation,
          metricUnits: input.metricUnits,
        };

      case 'inbound-edge':
        return {
          huId: input.huId,
          inboundArea: input.inboundArea,
          redisOperation: input.redisOperation,
          metricUnits: input.metricUnits,
          inboundParentNodes: input.inboundParentNodes,
        };

      case 'outbound-edge':
        return {
          outboundArea: input.outboundArea,
          huId: input.huId,
          units: input.units,
          outboundParentNodes: input.outboundParentNodes,
        };

      case 'complete-edge':
        return {
          inboundArea: input.inboundArea,
          outboundArea: input.outboundArea,
          redisOperation: input.redisOperation,
          outboundNodeLabel: input.outboundNodeLabel,
          inboundParentNodes: input.inboundParentNodes,
          outboundParentNodes: input.outboundParentNodes,
          metricUnits: input.metricUnits,
        };

      default:
        return {};
    }
  }

  /**
   * Gets a single metric configuration by name, returning both default and custom versions if they exist
   * @param metricName The name of the metric configuration to retrieve
   * @param facilityId The facility ID for custom configurations
   * @param configType Optional filter for 'default' or 'custom' configurations only
   * @returns Object containing default and/or custom configurations
   */
  async getMetricConfigDetail(
    metricName: string,
    facilityId: string,
    configType?: 'default' | 'custom',
  ): Promise<{
    default?: DefaultMetricConfigurationEntity;
    custom?: CustomMetricConfigurationEntity;
  }> {
    this.logger.debug('Getting metric configuration detail from database', {
      metricName,
      configType,
      facilityId,
    });

    const entityManager =
      this.dbProvider.processFlowPostgres.datasource.manager;
    const defaultRepo = entityManager.getRepository(
      DefaultMetricConfigurationEntity,
    );
    const customRepo = entityManager.getRepository(
      CustomMetricConfigurationEntity,
    );

    const result: {
      default?: DefaultMetricConfigurationEntity;
      custom?: CustomMetricConfigurationEntity;
    } = {};

    // Get default configuration if configType is not 'custom' (includes undefined/null and 'default')
    if (configType !== 'custom') {
      const defaultConfig = await defaultRepo.findOne({
        where: {metricConfigName: metricName},
      });

      if (defaultConfig) {
        result.default = this.transformHstoreFields(defaultConfig, facilityId);
      }
    }

    // Get custom configuration if configType is not 'default' (includes undefined/null and 'custom')
    if (configType !== 'default') {
      const customConfig = await customRepo.findOne({
        where: {
          metricConfigName: metricName,
          facilityId,
        },
      });

      if (customConfig) {
        result.custom = this.transformHstoreFields(customConfig, facilityId);
      }
    }

    this.logger.debug('Retrieved metric configuration detail from database', {
      metricName,
      hasDefault: !!result.default,
      hasCustom: !!result.custom,
    });

    return result;
  }

  /**
   * Gets multiple metric configurations by names, applying the same filtering logic as the Python service
   * Returns the appropriate config (custom or default) based on enabled status and facility settings
   * @param metricNames Array of metric configuration names to retrieve
   * @param facilityId The facility ID for custom configurations
   * @returns Map of metricName -> selected config (or null if none found)
   */
  async getMetricConfigs(
    metricNames: string[],
    facilityId: string,
  ): Promise<
    Map<
      string,
      DefaultMetricConfigurationEntity | CustomMetricConfigurationEntity | null
    >
  > {
    this.logger.debug('Getting multiple metric configurations from database', {
      metricNames,
      facilityId,
    });

    const entityManager =
      this.dbProvider.processFlowPostgres.datasource.manager;
    const defaultRepo = entityManager.getRepository(
      DefaultMetricConfigurationEntity,
    );
    const customRepo = entityManager.getRepository(
      CustomMetricConfigurationEntity,
    );

    const result = new Map<
      string,
      DefaultMetricConfigurationEntity | CustomMetricConfigurationEntity | null
    >();

    // Initialize result map with null for all metric names
    for (const metricName of metricNames) {
      result.set(metricName, null);
    }

    // Get enabled custom configs for this facility (These take precedence over defaults)
    const customConfigs = await customRepo.find({
      where: {
        metricConfigName: In(metricNames),
        facilityId,
        enabled: true, // Only enabled custom configs are considered
      },
    });

    // Get all default configs (we'll filter them later)
    const defaultConfigs = await defaultRepo.find({
      where: {metricConfigName: In(metricNames)},
    });

    // Process enabled custom configs first (they take precedence)
    // These are the configs that will be used
    for (const customConfig of customConfigs) {
      // Custom configs have boolean fields, so no transformation needed
      result.set(customConfig.metricConfigName, customConfig);
    }

    // Process default configs for metrics that don't have enabled custom configs
    for (const defaultConfig of defaultConfigs) {
      const metricName = defaultConfig.metricConfigName;

      // Only use default if no enabled custom config exists for this metric
      // This handles both cases:
      // 1. No custom config exists at all
      // 2. Custom config exists but is disabled (not in result)
      if (!result.get(metricName)) {
        // Check if default is enabled for this facility using HSTORE logic
        const isDefaultEnabled = this.isDefaultConfigEnabledForFacility(
          defaultConfig,
          facilityId,
        );

        if (isDefaultEnabled) {
          result.set(metricName, defaultConfig);
        }
      }
    }

    this.logger.debug(
      'Retrieved multiple metric configurations from database',
      {
        metricNames,
        foundConfigs: Array.from(result.values()).filter(r => r !== null)
          .length,
      },
    );

    return result;
  }

  /**
   * Checks if a default config is enabled for a specific facility
   * @param defaultConfig The default configuration
   * @param facilityId The facility ID to check
   * @returns True if the default config is enabled for this facility
   */
  private isDefaultConfigEnabledForFacility(
    defaultConfig: DefaultMetricConfigurationEntity,
    facilityId: string,
  ): boolean {
    // If enabled is null/undefined, it's enabled by default
    if (defaultConfig.enabled === null || defaultConfig.enabled === undefined) {
      return true;
    }

    // If enabled is an object (hstore), check the specific facility
    if (typeof defaultConfig.enabled === 'object') {
      // HSTORE actually stores strings, not booleans, despite the TypeScript interface
      const hstoreValue = defaultConfig.enabled as unknown as Record<
        string,
        string
      >;

      // If the facility is not in the HSTORE, it's enabled by default
      if (!(facilityId in hstoreValue)) {
        return true;
      }

      const facilityValue = hstoreValue[facilityId];

      // HSTORE stores strings, so check for 'false' string specifically
      // Any other value (including 'true', 'enabled', etc.) is considered enabled
      return facilityValue !== 'false';
    }

    // If enabled is a boolean, use that value
    return defaultConfig.enabled === true;
  }

  /**
   * Gets a single metric configuration by name, returning both default and custom versions if they exist
   * @param metricId The id of the metric configuration to retrieve
   * @param facilityId The facility ID for custom configurations
   * @param configType Optional filter for 'default' or 'custom' configurations only
   * @returns Object containing default and/or custom configurations
   */
  async getMetricConfigDetailById(
    metricId: string,
    facilityId: string,
    configType?: 'default' | 'custom',
  ): Promise<{
    default?: DefaultMetricConfigurationEntity;
    custom?: CustomMetricConfigurationEntity;
  }> {
    this.logger.debug(
      'Getting metric configuration detail by id from database',
      {
        metricId,
        configType,
        facilityId,
      },
    );

    const entityManager =
      this.dbProvider.processFlowPostgres.datasource.manager;
    const defaultRepo = entityManager.getRepository(
      DefaultMetricConfigurationEntity,
    );
    const customRepo = entityManager.getRepository(
      CustomMetricConfigurationEntity,
    );

    const result: {
      default?: DefaultMetricConfigurationEntity;
      custom?: CustomMetricConfigurationEntity;
    } = {};

    // Get default configuration if configType is not 'custom' (includes undefined/null and 'default')
    if (configType !== 'custom') {
      const defaultConfig = await defaultRepo.findOne({
        where: {id: metricId},
      });

      if (defaultConfig) {
        result.default = this.transformHstoreFields(defaultConfig);
      }
    }

    // Get custom configuration if configType is not 'default' (includes undefined/null and 'custom')
    if (configType !== 'default') {
      const customConfig = await customRepo.findOne({
        where: {
          id: metricId,
          facilityId,
        },
      });

      if (customConfig) {
        result.custom = this.transformHstoreFields(customConfig);
      }
    }

    this.logger.debug(
      'Retrieved metric configuration detail by id from database',
      {
        metricId,
        hasDefault: !!result.default,
        hasCustom: !!result.custom,
      },
    );

    return result;
  }

  /**
   * Gets metric configuration facts with statistics
   * @param facilityId The facility ID for custom configurations
   * @returns Array of fact types with their statistics
   */
  async getMetricConfigFacts(facilityId: string): Promise<MetricConfigFact[]> {
    this.logger.debug('Getting metric configuration facts from database', {
      facilityId,
    });

    const entityManager =
      this.dbProvider.processFlowPostgres.datasource.manager;

    const query = `
      WITH config_analysis AS (
          SELECT
              COALESCE(d.fact_type, c.fact_type) as "factType",
              d.metric_config_name as defaultName,
              c.metric_config_name as customName,
              c.facility_id as facilityId,
              -- Default config status
              CASE WHEN d.metric_config_name IS NOT NULL THEN 1 ELSE 0 END as isDefault,
              -- Custom-only config (no corresponding default)
              CASE WHEN d.metric_config_name IS NULL AND c.metric_config_name IS NOT NULL THEN 1 ELSE 0 END as isCustomOnly,
              -- Enabled default (when no custom override or custom disabled)
              CASE WHEN d.metric_config_name IS NOT NULL
                        AND (d.enabled IS NULL OR d.enabled->$1 IS NULL OR d.enabled->$1 != 'false')
                        AND (c.metric_config_name IS NULL OR c.enabled IS NOT TRUE)
                   THEN 1 ELSE 0 END as isEnabledDefault,
              -- Enabled custom
              CASE WHEN c.enabled = TRUE THEN 1 ELSE 0 END as isEnabledCustom,
              -- Active default (when no custom override or custom not active)
              CASE WHEN d.metric_config_name IS NOT NULL
                        AND (d.active IS NULL OR d.active->$1 IS NULL OR d.active->$1 != 'false')
                        AND (c.metric_config_name IS NULL OR c.active IS NOT TRUE)
                   THEN 1 ELSE 0 END AS isActiveDefault,
              -- Active custom
              CASE WHEN c.active = TRUE THEN 1 ELSE 0 END AS isActiveCustom
          FROM process_flow.default_metric_configurations d
          FULL OUTER JOIN process_flow.custom_metric_configurations c
              ON d.metric_config_name = c.metric_config_name
              AND c.facility_id = $1
      )
      SELECT
          "factType",
          -- Cast from bigint to integer for typescript compatibility
          (SUM(isDefault) + SUM(isCustomOnly))::integer as "totalConfigs",
          (SUM(isEnabledDefault) + SUM(isEnabledCustom))::integer as "enabledConfigs",
          (SUM(isActiveDefault) + SUM(isActiveCustom)) > 0 AS "active"
      FROM config_analysis
      GROUP BY "factType"
      ORDER BY "factType";
    `;

    const results = await entityManager.query(query, [facilityId]);

    // Results already match the MetricConfigFact interface (integers from SQL)
    const transformedResults = results as MetricConfigFact[];

    this.logger.debug(
      `Retrieved metric configuration facts for ${facilityId}`,
      {
        facilityId,
        factTypes: transformedResults.length,
      },
    );

    return transformedResults;
  }

  async deleteMetricConfig(
    id: string,
  ): Promise<CustomMetricConfigurationEntity | null> {
    this.logger.debug('Deleting metric configuration', {id});

    const entityManager =
      this.dbProvider.processFlowPostgres.datasource.manager;
    const customRepo = entityManager.getRepository(
      CustomMetricConfigurationEntity,
    );

    // Find the entity to check if it exists
    const existingConfig = await customRepo.findOne({
      where: {id},
    });

    if (!existingConfig) {
      this.logger.debug('Metric configuration not found for deletion', {id});
      return null;
    }

    // Delete the entity
    const deleteResult = await customRepo.delete(id);

    this.logger.debug('Metric configuration deleted successfully', {
      id,
      affected: deleteResult.affected,
    });

    // Return the entity that was deleted
    return existingConfig;
  }
}
