/**
 * Represents metric configuration facts with statistics for a facility
 */
export interface MetricConfigFact {
  /**
   * The type of fact this metric represents
   */
  factType: string;

  /**
   * Total number of configurations for this fact type
   */
  totalConfigs: number;

  /**
   * Number of enabled configurations for this fact type
   */
  enabledConfigs: number;

  /**
   * Whether any configuration for this fact type is active
   */
  active: boolean;
}
