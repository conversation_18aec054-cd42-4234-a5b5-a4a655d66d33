/**
 * Represents a metric configuration
 */
export interface MetricConfigSummary {
  /**
   * Unique identifier for the metric configuration
   */
  id: string;

  /**
   * Name of the metric
   */
  metricName: string;

  /**
   * Type of configuration (e.g., 'node', 'edge')
   */
  configType: string;

  /**
   * Name of the node this metric is associated with
   */
  nodeName?: string;

  /**
   * Type of fact this metric represents
   */
  factType?: string;

  /**
   * Whether the metric is enabled
   * For default configurations, this is an hstore where keys are facility IDs and values are booleans
   * For custom configurations, this is a boolean indicating if the metric is enabled for that facility
   * Can be undefined for default configurations if no facilities have enabled/disabled the metric
   * Can be null for custom configurations where enabled status is not applicable
   */
  enabled?: boolean | Record<string, boolean> | null;

  /**
   * Whether the metric is active
   * For default configurations, this is an hstore where keys are facility IDs and values are booleans
   * For custom configurations, this is a boolean indicating if the metric is active for that facility
   * Can be undefined for default configurations if no facilities have active/inactive status
   */
  active?: boolean | Record<string, boolean>;

  /**
   * Whether this is a custom configuration (true) or default configuration (false)
   */
  isCustom: boolean;

  /**
   * The facility ID this configuration applies to
   * For default configurations, this is 'default'
   * For custom configurations, this is the specific facility ID
   */
  facilityId: string;

  /**
   * The list of views that the resulting node will appear in
   */
  views: string[];
}
