import {
  ApiResponseArray,
  CacheMiddleware,
  configPostgresDatabase,
  Container,
  DatabaseTypes,
  IctError,
  ProtectedRouteMiddleware,
  startEndDateValidation,
  WinstonLogger,
} from 'ict-api-foundations';
import {
  Controller,
  Get,
  Middlewares,
  OperationId,
  Query,
  Route,
  SuccessResponse,
  Tags,
} from 'tsoa';

import {type DmsId, getAllDmsIds, isValidDmsId} from '../defs/dms-config.ts';
import {MovementByTypeData} from '../defs/movement-by-type-data.ts';
import type {
  MovementLoadUnitsData,
  MovementUnitType,
} from '../defs/movement-load-units-data.ts';
import type {
  MovementLocationData,
  MovementLocationScope,
} from '../defs/movement-location-data.ts';
import type {
  MovementTimeSeriesData,
  MovementTimeSeriesGranularity,
} from '../defs/movement-time-series-data.ts';
import {MovementsService} from '../services/movements-service.ts';

@Route('')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [{type: DatabaseTypes.BigQuery}, configPostgresDatabase()],
  }),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class MovementsController extends Controller {
  /**
   * Get movements summary with aggregated data by type, including percentages
   * @param {Date} start_date - Start date for the movements query
   * @param {Date} end_date - End date for the movements query
   * @param {DmsId} dms_id - DMS identifier to filter source groups
   * @param {'loadunit' | 'sku'} unit_type - Type of unit to aggregate by (loadunit or sku)
   * @param {unit_id} unit_id - Unit name
   * @isDateTime start_date
   * @isDateTime end_date
   */
  @SuccessResponse('200')
  @Get('/movements/summary')
  @OperationId('GetMovementsSummary')
  @Tags('movements')
  public async getMovementsSummary(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date,
    @Query('dms_id') dmsId: DmsId,
    @Query('unit_type') unit_type?: MovementUnitType,
    @Query('unit_id') unit_id?: string,
  ): Promise<MovementByTypeData[]> {
    const logger = Container.get(WinstonLogger);
    const movementsService = Container.get(MovementsService);

    logger.info('Request to get movements summary', {
      startDate,
      endDate,
      dmsId,
      unit_type,
      unit_id,
    });

    try {
      // Validate dates
      startEndDateValidation(startDate, endDate);

      // Validate dms_id
      if (!dmsId || !isValidDmsId(dmsId)) {
        const validIds = getAllDmsIds().join('", "');
        throw IctError.badRequest(
          `dms_id parameter is required and must be one of: "${validIds}"`,
        );
      }

      const movements = await movementsService.getMovementsSummary(
        startDate.toISOString(),
        endDate.toISOString(),
        dmsId,
        unit_type,
        unit_id,
      );

      logger.info('Successfully retrieved movements summary', {
        movementTypeCount: movements.length,
        startDate,
        endDate,
        dmsId,
      });

      return movements;
    } catch (error) {
      logger.error('Failed to retrieve movements summary', {
        error,
        startDate,
        endDate,
        dmsId,
      });

      if (error instanceof IctError) {
        throw error;
      }

      throw IctError.internalServerError(
        'Unable to retrieve movements summary',
        error,
      );
    }
  }

  /**
   * Get movements aggregated by location with filtering options
   * Consolidates 4 endpoints: by/source, by/destination, by/source/location, by/destination/location
   * @param {Date} start_date - Start date for the movements query
   * @param {Date} end_date - End date for the movements query
   * @param {DmsId} dms_id - DMS identifier to filter source groups
   * @param {'source_location' | 'destination_location' | 'source_type' | 'destination_type'} scope - Determines which data to return
   * @param {'loadunit' | 'sku'} unit_type - Type of unit to aggregate by (loadunit or sku)
   * @param {unit_id} unit_id - Unit name
   * @isDateTime start_date
   * @isDateTime end_date
   */
  @SuccessResponse('200')
  @Get('/movements/location')
  @OperationId('GetMovementsByLocation')
  @Tags('movements')
  public async getMovementsByLocation(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date,
    @Query('dms_id') dmsId: DmsId,
    @Query('group_by_column') group_by_column: MovementLocationScope,
    @Query('unit_type') unit_type?: MovementUnitType,
    @Query('unit_id') unit_id?: string,
  ): Promise<MovementLocationData> {
    const logger = Container.get(WinstonLogger);
    const movementsService = Container.get(MovementsService);

    logger.info('Request to get movements by location', {
      startDate,
      endDate,
      dmsId,
      group_by_column,
      unit_type,
      unit_id,
    });

    // Validate the start and end dates
    startEndDateValidation(startDate, endDate);

    // Validate dms_id parameter
    if (!dmsId || !['inventory', 'shipping'].includes(dmsId)) {
      throw IctError.badRequest(
        'dms_id parameter is required and must be either "inventory" or "shipping"',
      );
    }

    // Validate scope parameter
    if (
      !group_by_column ||
      ![
        'source_location',
        'destination_location',
        'source_type',
        'destination_type',
      ].includes(group_by_column)
    ) {
      throw IctError.badRequest(
        'scope parameter is required and must be one of: source_location, destination_location, source_type, destination_type',
      );
    }

    try {
      const movements = await movementsService.getMovementsByLocation(
        startDate.toISOString(),
        endDate.toISOString(),
        dmsId,
        group_by_column,
        unit_type,
        unit_id,
      );

      logger.info('Successfully retrieved movements by location', {
        locationDataCount: movements.locationData.length,
        dmsId,
        group_by_column,
      });

      return movements;
    } catch (error) {
      logger.error('Failed to retrieve movements by location', {
        error,
        startDate,
        endDate,
        dmsId,
        group_by_column,
      });

      if (error instanceof IctError) {
        throw error;
      }

      throw IctError.internalServerError(
        'Unable to retrieve movements by location',
        error,
      );
    }
  }

  /**
   * Get movements aggregated by time series with configurable granularity
   * Consolidates 2 endpoints: by/day and by/hour
   * @param {Date} start_date - Start date for the movements query
   * @param {Date} end_date - End date for the movements query
   * @param {DmsId} dms_id - DMS identifier to filter source groups
   * @param {'day' | 'hour'} granularity - Determines time aggregation level and response structure
   * @param {'loadunit' | 'sku'} unit_type - Type of unit to aggregate by (loadunit or sku)
   * @param {unit_id} unit_id - Unit name
   * @isDateTime start_date
   * @isDateTime end_date
   */
  @SuccessResponse('200')
  @Get('/movements/time-series')
  @OperationId('GetMovementsByTimeSeries')
  @Tags('movements')
  public async getMovementsByTimeSeries(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date,
    @Query('dms_id') dmsId: DmsId,
    @Query('group_by_column') group_by_column: MovementTimeSeriesGranularity,
    @Query('unit_type') unit_type?: MovementUnitType,
    @Query('unit_id') unit_id?: string,
  ): Promise<MovementTimeSeriesData> {
    const logger = Container.get(WinstonLogger);
    const movementsService = Container.get(MovementsService);

    logger.info('Request to get movements by time-series', {
      startDate,
      endDate,
      dmsId,
      group_by_column,
      unit_type,
      unit_id,
    });

    // Validate the start and end dates
    startEndDateValidation(startDate, endDate);

    // Validate dms_id parameter
    if (!dmsId || !['inventory', 'shipping'].includes(dmsId)) {
      throw IctError.badRequest(
        'dms_id parameter is required and must be either "inventory" or "shipping"',
      );
    }

    // Validate granularity parameter
    if (!group_by_column || !['day', 'hour'].includes(group_by_column)) {
      throw IctError.badRequest(
        'granularity parameter is required and must be either "day" or "hour"',
      );
    }

    try {
      const movements = await movementsService.getMovementsByTimeSeries(
        startDate.toISOString(),
        endDate.toISOString(),
        dmsId,
        group_by_column,
        unit_type,
        unit_id,
      );

      logger.info('Successfully retrieved movements by time-series', {
        timeSeriesDataCount: movements.timeSeriesData.length,
        dmsId,
        group_by_column,
      });

      return movements;
    } catch (error) {
      logger.error('Failed to retrieve movements by time-series', {
        error,
        startDate,
        endDate,
        dmsId,
        group_by_column,
      });

      if (error instanceof IctError) {
        throw error;
      }

      throw IctError.internalServerError(
        'Unable to retrieve movements by time-series',
        error,
      );
    }
  }

  /**
   * Get movements aggregated by load units with filtering and pagination options
   * @param {Date} start_date - Start date for the movements query
   * @param {Date} end_date - End date for the movements query
   * @param {DmsId} dms_id - DMS identifier to filter source groups
   * @param {'loadunit' | 'sku'} group_by_column - Type of unit to aggregate by (loadunit or sku)
   * @param {'loadunit' | 'sku'} unit_type - Type of unit to aggregate by (loadunit or sku)
   * @param {unit_id} unit_id - Unit type name
   * @param {number} limit - Maximum number of items per page (default: 100, max: 1000)
   * @param {number} page - Page number for pagination (default: 1)
   * @isDateTime start_date
   * @isDateTime end_date
   */
  @SuccessResponse('200')
  @Get('/movements/load-units')
  @OperationId('GetMovementsByLoadUnits')
  @Tags('movements')
  public async getMovementsByLoadUnits(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date,
    @Query('dms_id') dmsId: DmsId,
    @Query('group_by_column') group_by_column: MovementUnitType,
    @Query('unit_id') unit_id?: string,
    @Query('limit') limit?: number,
    @Query('page') page?: number,
  ): Promise<
    ApiResponseArray<
      MovementLoadUnitsData[],
      {page: number; limit: number; totalResults: number}
    >
  > {
    const logger = Container.get(WinstonLogger);
    const movementsService = Container.get(MovementsService);

    logger.info('Request to get movements by load units', {
      startDate,
      endDate,
      dmsId,
      group_by_column,
      limit,
      page,
      unit_id,
    });

    try {
      // Validate dates
      startEndDateValidation(startDate, endDate);

      // Validate dms_id
      if (!dmsId || !isValidDmsId(dmsId)) {
        const validIds = getAllDmsIds().join('", "');
        throw IctError.badRequest(
          `dms_id parameter is required and must be one of: "${validIds}"`,
        );
      }

      // Validate unit_type
      const validUnitTypes = ['loadunit', 'sku'];
      if (!group_by_column || !validUnitTypes.includes(group_by_column)) {
        throw IctError.badRequest(
          `unit_type parameter is required and must be one of: ${validUnitTypes.join(', ')}`,
        );
      }

      // Validate and set pagination parameters
      const pageNumber = Math.max(1, page || 1);
      const limitNumber = Math.min(1000, Math.max(1, limit || 100));

      const result = await movementsService.getMovementsByLoadUnits(
        startDate.toISOString(),
        endDate.toISOString(),
        dmsId,
        group_by_column,
        limitNumber,
        pageNumber,
        unit_id,
      );

      logger.info('Successfully retrieved movements by load units', {
        loadUnitsDataCount: result.data,
        totalItems: result.metadata.totalResults,
        page: pageNumber,
      });

      return result;
    } catch (error) {
      logger.error('Failed to retrieve movements by load units', {error});

      if (error instanceof IctError) {
        throw error;
      }

      throw IctError.internalServerError(
        'Unable to retrieve movements by load units',
        error,
      );
    }
  }
}
