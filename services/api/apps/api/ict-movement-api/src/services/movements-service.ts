import {ApiResponseArray} from 'ict-api-foundations';
import {Service} from 'typedi';
import {type DmsId} from '../defs/dms-config.ts';
import {MovementByTypeData} from '../defs/movement-by-type-data.ts';
import type {
  MovementLoadUnitsData,
  MovementUnitType,
} from '../defs/movement-load-units-data.ts';
import {
  MovementLocationData,
  MovementLocationScope,
} from '../defs/movement-location-data.ts';
import type {
  MovementTimeSeriesData,
  MovementTimeSeriesGranularity,
} from '../defs/movement-time-series-data.ts';
import {MovementsStore} from '../stores/movements-store.ts';

// Define pagination metadata interface to match other APIs
interface MovementLoadUnitsPaginationInfo {
  page: number;
  limit: number;
  totalResults: number;
}

@Service()
export class MovementsService {
  constructor(private movementsStore: MovementsStore) {}

  /**
   * Get movements summary with aggregated data by type, including percentages
   * @param startDate - Start date for the movements query (ISO string)
   * @param endDate - End date for the movements query (ISO string)
   * @param dmsId - DMS identifier to filter source groups
   * @param unitType - Type of unit to aggregate by ('loadunit' | 'sku')
   * @param unitId - Unit name for movements query
   * @returns Array of movement summary data with types, totals, and percentages
   */
  public async getMovementsSummary(
    startDate: string,
    endDate: string,
    dmsId: DmsId,
    unitType?: MovementUnitType,
    unitId?: string,
  ): Promise<MovementByTypeData[]> {
    return await this.movementsStore.getMovementsSummary(
      startDate,
      endDate,
      dmsId,
      unitType,
      unitId,
    );
  }

  /**
   * Get movements aggregated by location (consolidated endpoint)
   * @param startDate - Start date for the movements query (ISO string)
   * @param endDate - End date for the movements query (ISO string)
   * @param dmsId - DMS identifier to filter source groups
   * @param scope - Determines which type of data to return
   * @param unitType - Type of unit to aggregate by ('loadunit' | 'sku')
   * @param unitId - Unit name for movements query
   * @returns Movement location data with task type structure
   */
  public async getMovementsByLocation(
    startDate: string,
    endDate: string,
    dmsId: DmsId,
    scope: MovementLocationScope,
    unitType?: MovementUnitType,
    unitId?: string,
  ): Promise<MovementLocationData> {
    return await this.movementsStore.getMovementsByLocation(
      startDate,
      endDate,
      dmsId,
      scope,
      unitType,
      unitId,
    );
  }

  /**
   * Get movements aggregated by time-series with configurable granularity
   * @param startDate - Start date for the movements query (ISO string)
   * @param endDate - End date for the movements query (ISO string)
   * @param dmsId - DMS identifier to filter source groups
   * @param granularity - Time aggregation level ('day' | 'hour')
   * @param unitType - Type of unit to aggregate by ('loadunit' | 'sku')
   * @param unitId - Unit name for movements query
   * @returns Movement time-series data with task type structure
   */
  public async getMovementsByTimeSeries(
    startDate: string,
    endDate: string,
    dmsId: DmsId,
    granularity: MovementTimeSeriesGranularity,
    unitType?: MovementUnitType,
    unitId?: string,
  ): Promise<MovementTimeSeriesData> {
    return await this.movementsStore.getMovementsByTimeSeries(
      startDate,
      endDate,
      dmsId,
      granularity,
      unitType,
      unitId,
    );
  }

  /**
   * Get movements aggregated by load units with pagination
   * @param startDate - Start date for the movements query (ISO string)
   * @param endDate - End date for the movements query (ISO string)
   * @param dmsId - DMS identifier to filter source groups
   * @param unitType - Type of unit to aggregate by ('loadunit' | 'sku')
   * @param unitId - Unit name for movements query
   * @param limit - Maximum number of items per page
   * @param page - Page number for pagination
   * @returns ApiResponseArray with data and pagination metadata to match other APIs
   */
  public async getMovementsByLoadUnits(
    startDate: string,
    endDate: string,
    dmsId: DmsId,
    unitType: MovementUnitType,
    limit: number,
    page: number,
    unitId?: string,
  ): Promise<
    ApiResponseArray<MovementLoadUnitsData[], MovementLoadUnitsPaginationInfo>
  > {
    const result = await this.movementsStore.getMovementsByLoadUnits(
      startDate,
      endDate,
      dmsId,
      unitType,
      limit,
      page,
      unitId,
    );

    // Transform PaginatedResults to ApiResponseArray format to match other APIs
    return {
      data: [result.list],
      metadata: {
        page,
        limit,
        totalResults: result.totalResults,
      },
    };
  }
}
