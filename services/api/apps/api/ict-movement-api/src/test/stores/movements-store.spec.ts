import {BigQuery} from '@google-cloud/bigquery';
import {expect} from 'chai';
import {
  BigQueryDatabase,
  ContextService,
  DatabaseProvider,
  IctError,
  WinstonLogger,
} from 'ict-api-foundations';
import sinon, {SinonStub} from 'sinon';
import {type DmsId, getSourceGroupNames} from '../../defs/dms-config.ts';
import {MovementsStore} from '../../stores/movements-store.ts';

describe('MovementsStore', () => {
  let movementsStore: MovementsStore;
  let clientStub: sinon.SinonStubbedInstance<BigQuery>;
  let dbProvider: DatabaseProvider;
  let contextService: ContextService;
  let mockLogger: sinon.SinonStubbedInstance<WinstonLogger>;
  let executeMonitoredJobStub: SinonStub;

  beforeEach(() => {
    clientStub = sinon.createStubInstance(BigQuery);
    dbProvider = new DatabaseProvider();
    const bqDatabase = new BigQueryDatabase(clientStub, 'testDataset');
    dbProvider.set(bqDatabase.getType(), bqDatabase);

    contextService = new ContextService();
    sinon.stub(contextService, 'dbProvider').get(() => dbProvider);
    executeMonitoredJobStub = sinon.stub(
      dbProvider.bigQuery,
      'executeMonitoredJob',
    );

    // stub the "info" method of mockLogger
    mockLogger = sinon.createStubInstance(WinstonLogger);
    mockLogger.info.resolves();

    movementsStore = new MovementsStore(contextService, mockLogger);
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('getMovementsSummary', () => {
    it('should successfully retrieve movements summary with aggregated data and validate SQL query construction', async () => {
      const startDate = '2023-01-01T00:00:00.000Z';
      const endDate = '2023-01-02T00:00:00.000Z';
      const dmsId: DmsId = 'inventory';

      const mockQueryResult = [
        {movementType: 'All', totalMovements: 1000, percentage: 100.0},
        {movementType: 'RETRIEVAL', totalMovements: 400, percentage: 40.0},
        {movementType: 'STORAGE', totalMovements: 300, percentage: 30.0},
        {movementType: 'BYPASS', totalMovements: 200, percentage: 20.0},
        {movementType: 'IAT', totalMovements: 100, percentage: 10.0},
      ];

      executeMonitoredJobStub.resolves([mockQueryResult]);

      const result = await movementsStore.getMovementsSummary(
        startDate,
        endDate,
        dmsId,
      );

      expect(result).to.be.an('array').with.lengthOf(5);
      result.forEach((item, idx) => {
        expect(item).to.have.property(
          'name',
          mockQueryResult[idx].movementType,
        );
        expect(item.value).to.have.property(
          'totalMovements',
          mockQueryResult[idx].totalMovements,
        );
        expect(item.value).to.have.property(
          'percentage',
          mockQueryResult[idx].percentage,
        );
      });

      sinon.assert.calledOnce(executeMonitoredJobStub);

      const sqlOptions = executeMonitoredJobStub.getCall(0).args[0];
      expect(sqlOptions.params.startDate).to.equal(startDate);
      expect(sqlOptions.params.endDate).to.equal(endDate);
      expect(sqlOptions.params.sourceGroupNames).to.deep.equal(
        getSourceGroupNames(dmsId),
      );
      expect(sqlOptions.query).to.include('movement_end_timestamp_utc BETWEEN');
      expect(sqlOptions.query).to.include('movement_end_date_time_local >=');
      expect(sqlOptions.query).to.include('Source_Group_Name IN UNNEST');
      expect(sqlOptions.query).to.include('ORDER BY');
    });

    it('should handle unit-specific filtering and include unit parameters in query', async () => {
      const startDate = '2023-01-01T00:00:00.000Z';
      const endDate = '2023-01-02T00:00:00.000Z';
      const dmsId: DmsId = 'shipping';
      const unitType = 'loadunit';
      const unitId = 'LU-TEST-123';

      const mockQueryResult = [
        {movementType: 'All', totalMovements: 50, percentage: 100.0},
        {movementType: 'RETRIEVAL', totalMovements: 50, percentage: 100.0},
      ];

      executeMonitoredJobStub.resolves([mockQueryResult]);

      const result = await movementsStore.getMovementsSummary(
        startDate,
        endDate,
        dmsId,
        unitType,
        unitId,
      );

      expect(result).to.deep.equal([
        {name: 'All', value: {totalMovements: 50, percentage: 100.0}},
        {name: 'RETRIEVAL', value: {totalMovements: 50, percentage: 100.0}},
      ]);

      const sqlOptions = executeMonitoredJobStub.getCall(0).args[0];
      expect(sqlOptions.params).to.have.property('unitId', unitId);
      expect(sqlOptions.query).to.include('handling_unit_code = @unitId');
    });

    it('should throw IctError.noContent when no data is found', async () => {
      executeMonitoredJobStub.resolves([[]]);
      try {
        await movementsStore.getMovementsSummary(
          '2023-01-01',
          '2023-01-02',
          'inventory',
        );
        expect.fail('Expected IctError.noContent');
      } catch (error) {
        expect(error).to.be.instanceOf(IctError);
        expect((error as IctError).statusCode).to.equal(204);
      }
    });

    it('should handle BigQuery connection failures gracefully', async () => {
      const startDate = '2023-01-01T00:00:00.000Z';
      const endDate = '2023-01-02T00:00:00.000Z';
      const dmsId: DmsId = 'inventory';
      const connectionError = new Error('BigQuery connection timeout');

      executeMonitoredJobStub.rejects(connectionError);

      try {
        await movementsStore.getMovementsSummary(startDate, endDate, dmsId);
        expect.fail('Expected error to be thrown');
      } catch (error) {
        expect(error).to.equal(connectionError);
      }
    });

    it('should handle malformed query responses', async () => {
      executeMonitoredJobStub.resolves([null]);
      try {
        await movementsStore.getMovementsSummary(
          '2023-01-01',
          '2023-01-02',
          'inventory',
        );
        expect.fail('Expected IctError.noContent');
      } catch (error) {
        expect(error).to.be.instanceOf(IctError);
      }
    });

    it('should handle SKU-specific filtering correctly', async () => {
      const startDate = '2023-01-01T00:00:00.000Z';
      const endDate = '2023-01-02T00:00:00.000Z';
      const dmsId: DmsId = 'inventory';
      const unitType = 'sku';
      const unitId = 'SKU-ABC-789';

      const mockQueryResult = [
        {movementType: 'All', totalMovements: 25, percentage: 100.0},
        {movementType: 'STORAGE', totalMovements: 25, percentage: 100.0},
      ];

      executeMonitoredJobStub.resolves([mockQueryResult]);

      const result = await movementsStore.getMovementsSummary(
        startDate,
        endDate,
        dmsId,
        unitType,
        unitId,
      );

      expect(result).to.deep.equal([
        {name: 'All', value: {totalMovements: 25, percentage: 100.0}},
        {name: 'STORAGE', value: {totalMovements: 25, percentage: 100.0}},
      ]);

      const sqlOptions = executeMonitoredJobStub.getCall(0).args[0];
      expect(sqlOptions.params).to.have.property('unitId', unitId);
      expect(sqlOptions.query).to.include('item_sku = @unitId');
      expect(sqlOptions.query).to.not.include('handling_unit_code = @unitId');
    });
  });

  describe('getMovementsByLocation', () => {
    it('should return movements by source location', async () => {
      const startDate = '2023-01-01T00:00:00.000Z';
      const endDate = '2023-01-02T00:00:00.000Z';
      const dmsId: DmsId = 'inventory';
      const scope = 'source_location';

      const mockQueryResult = [
        {
          locationAisle: '01',
          movementType: 'RETRIEVAL',
          totalMovements: 150,
        },
        {
          locationAisle: '02',
          movementType: 'STORAGE',
          totalMovements: 100,
        },
      ];

      executeMonitoredJobStub.resolves([mockQueryResult]);

      const result = await movementsStore.getMovementsByLocation(
        startDate,
        endDate,
        dmsId,
        scope,
      );

      expect(result).to.have.property('locationData');
      expect(result.locationData).to.be.an('array');
      expect(result.locationData).to.have.lengthOf(1);
      expect(result.locationData[0]).to.have.property('RETRIEVAL');
      expect(result.locationData[0]).to.have.property('STORAGE');
      sinon.assert.calledOnce(executeMonitoredJobStub);

      const sqlOptions = executeMonitoredJobStub.getCall(0).args[0];
      expect(sqlOptions.query).to.include('Movement_Type_Code as movementType');
      expect(sqlOptions.query).to.include('COUNT(1) as totalMovements');
    });

    it('should throw error for invalid scope', async () => {
      const startDate = '2023-01-01T00:00:00.000Z';
      const endDate = '2023-01-02T00:00:00.000Z';
      const dmsId: DmsId = 'inventory';
      const scope = 'invalid_scope' as any;

      try {
        await movementsStore.getMovementsByLocation(
          startDate,
          endDate,
          dmsId,
          scope,
        );
        expect.fail('Expected IctError.badRequest to be thrown');
      } catch (error) {
        expect(error).to.be.instanceOf(IctError);
      }
    });
  });

  describe('getMovementsByTimeSeries', () => {
    it('should return movements by day granularity', async () => {
      const startDate = '2023-01-01T00:00:00.000Z';
      const endDate = '2023-01-02T00:00:00.000Z';
      const dmsId: DmsId = 'inventory';
      const granularity = 'day';

      const mockQueryResult = [
        {
          dayDate: '2023-01-01',
          movementType: 'RETRIEVAL',
          totalMovements: 200,
        },
        {
          dayDate: '2023-01-01',
          movementType: 'STORAGE',
          totalMovements: 150,
        },
      ];

      executeMonitoredJobStub.resolves([mockQueryResult]);

      const result = await movementsStore.getMovementsByTimeSeries(
        startDate,
        endDate,
        dmsId,
        granularity,
      );

      expect(result).to.have.property('timeSeriesData');
      expect(result.timeSeriesData).to.be.an('array');
      expect(result.timeSeriesData).to.have.lengthOf(1);
      expect(result.timeSeriesData[0]).to.have.property('RETRIEVAL');
      expect(result.timeSeriesData[0]).to.have.property('STORAGE');

      const sqlOptions = executeMonitoredJobStub.getCall(0).args[0];
      expect(sqlOptions.query).to.include('DATE(Date_Hour) as dayDate');
    });

    it('should return movements by hour granularity', async () => {
      const startDate = '2023-01-01T00:00:00.000Z';
      const endDate = '2023-01-01T23:59:59.000Z';
      const dmsId: DmsId = 'inventory';
      const granularity = 'hour';

      const mockQueryResult = [
        {
          hourOfDay: 8,
          retrieval: 50,
          storage: 30,
          bypass: 10,
          iat: 5,
          positioning: 3,
          shuffle: 2,
        },
      ];

      executeMonitoredJobStub.resolves([mockQueryResult]);

      const result = await movementsStore.getMovementsByTimeSeries(
        startDate,
        endDate,
        dmsId,
        granularity,
      );

      expect(result).to.have.property('timeSeriesData');
      expect(result.timeSeriesData).to.be.an('array');
      expect(result.timeSeriesData).to.have.lengthOf(1);
      // Since the hour test has different mock data structure, we check for the presence of movement types
      expect(Object.keys(result.timeSeriesData[0]).length).to.be.greaterThan(0);

      const sqlOptions = executeMonitoredJobStub.getCall(0).args[0];
      expect(sqlOptions.query).to.include(
        'EXTRACT(HOUR FROM Date_Hour) as hourOfDay',
      );
    });

    it('should throw error for invalid granularity', async () => {
      const startDate = '2023-01-01T00:00:00.000Z';
      const endDate = '2023-01-02T00:00:00.000Z';
      const dmsId: DmsId = 'inventory';
      const granularity = 'invalid' as any;

      try {
        await movementsStore.getMovementsByTimeSeries(
          startDate,
          endDate,
          dmsId,
          granularity,
        );
        expect.fail('Expected IctError.badRequest to be thrown');
      } catch (error) {
        expect(error).to.be.instanceOf(IctError);
      }
    });
  });

  describe('getMovementsByLoadUnits', () => {
    it('should return movements by load units with pagination', async () => {
      const startDate = '2023-01-01T00:00:00.000Z';
      const endDate = '2023-01-02T00:00:00.000Z';
      const dmsId: DmsId = 'inventory';
      const unitType = 'loadunit';
      const limit = 100;
      const page = 1;

      const mockCountResult = [{total_count: 150}];
      const mockDataResult = [
        {
          unitIdentifier: 'LU001',
          movementType: 'RETRIEVAL',
          totalMovements: 25,
        },
        {
          unitIdentifier: 'LU002',
          movementType: 'STORAGE',
          totalMovements: 15,
        },
      ];

      executeMonitoredJobStub.onFirstCall().resolves([mockCountResult]);
      executeMonitoredJobStub.onSecondCall().resolves([mockDataResult]);

      const result = await movementsStore.getMovementsByLoadUnits(
        startDate,
        endDate,
        dmsId,
        unitType,
        limit,
        page,
      );

      expect(result).to.have.property('list');
      expect(result).to.have.property('totalResults', 150);
      expect(result.list).to.be.an('object');
      expect(result.list).to.have.property('RETRIEVAL');
      expect(result.list).to.have.property('STORAGE');
      expect((result.list as any).RETRIEVAL).to.be.an('array');
      expect((result.list as any).STORAGE).to.be.an('array');

      sinon.assert.calledTwice(executeMonitoredJobStub);

      // Check count query
      const countSqlOptions = executeMonitoredJobStub.getCall(0).args[0];
      expect(countSqlOptions.query).to.include('COUNT(*) as total_count');

      // Check data query
      const dataSqlOptions = executeMonitoredJobStub.getCall(1).args[0];
      expect(dataSqlOptions.query).to.include(
        'Load_Unit_Code as unitIdentifier',
      );
      expect(dataSqlOptions.query).to.include(`LIMIT ${limit}`);
      expect(dataSqlOptions.query).to.include(`OFFSET ${(page - 1) * limit}`);
    });

    it('should throw error for invalid unit type', async () => {
      const startDate = '2023-01-01T00:00:00.000Z';
      const endDate = '2023-01-02T00:00:00.000Z';
      const dmsId: DmsId = 'inventory';
      const unitType = 'invalid' as any;
      const limit = 100;
      const page = 1;

      try {
        await movementsStore.getMovementsByLoadUnits(
          startDate,
          endDate,
          dmsId,
          unitType,
          limit,
          page,
        );
        expect.fail('Expected IctError.badRequest to be thrown');
      } catch (error) {
        expect(error).to.be.instanceOf(IctError);
      }
    });
  });
});
