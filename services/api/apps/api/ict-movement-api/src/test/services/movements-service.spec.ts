import {expect} from 'chai';
import sinon from 'sinon';
import {type DmsId} from '../../defs/dms-config.ts';
import {MovementByTypeData} from '../../defs/movement-by-type-data.ts';
import {MovementLoadUnitsData} from '../../defs/movement-load-units-data.ts';
import {MovementLocationData} from '../../defs/movement-location-data.ts';
import {MovementTimeSeriesData} from '../../defs/movement-time-series-data.ts';
import {MovementsService} from '../../services/movements-service.ts';
import {MovementsStore} from '../../stores/movements-store.ts';

describe('MovementsService', () => {
  // Movements Store stub, intercepts calls made by the movements service
  let movementsStoreStub: sinon.SinonStubbedInstance<MovementsStore>;

  // Movements service to use in tests
  let movementsService: MovementsService;

  beforeEach(() => {
    // Setup movements store stub
    movementsStoreStub = sinon.createStubInstance(MovementsStore);
    movementsService = new MovementsService(movementsStoreStub);
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('getMovementsSummary', () => {
    it('should successfully retrieve movements summary with aggregated data by type and validate business logic', async () => {
      const startDate = '2023-01-01T00:00:00.000Z';
      const endDate = '2023-01-02T00:00:00.000Z';
      const dmsId: DmsId = 'inventory';

      const mockQueryResponse: MovementByTypeData[] = [
        {name: 'All', value: {totalMovements: 1000, percentage: 100.0}},
        {name: 'RETRIEVAL', value: {totalMovements: 400, percentage: 40.0}},
        {name: 'STORAGE', value: {totalMovements: 300, percentage: 30.0}},
        {name: 'BYPASS', value: {totalMovements: 200, percentage: 20.0}},
        {name: 'IAT', value: {totalMovements: 100, percentage: 10.0}},
      ];

      movementsStoreStub.getMovementsSummary.resolves(mockQueryResponse);

      const result = await movementsService.getMovementsSummary(
        startDate,
        endDate,
        dmsId,
      );

      // Structure validation
      expect(result).to.be.an('array');
      expect(result).to.have.lengthOf(5);

      // Content validation
      result.forEach(item => {
        expect(item).to.have.property('name').that.is.a('string');
        expect(item.value)
          .to.have.property('totalMovements')
          .that.is.a('number')
          .greaterThan(0);
        expect(item.value)
          .to.have.property('percentage')
          .that.is.a('number')
          .within(0, 100);
      });

      // Business logic validation
      const allRecord = result.find(item => item.name === 'All');
      expect(allRecord).to.exist;
      expect(allRecord!.value.percentage).to.equal(100);
      expect(allRecord!.value.totalMovements).to.equal(1000);

      // Verify detailed data matches expected response
      expect(result).to.deep.equal(mockQueryResponse);

      sinon.assert.calledOnceWithExactly(
        movementsStoreStub.getMovementsSummary,
        startDate,
        endDate,
        dmsId,
        undefined, // unitType
        undefined, // unitId
      );
    });

    it('should handle unit-specific filtering with loadunit type', async () => {
      const startDate = '2023-01-01T00:00:00.000Z';
      const endDate = '2023-01-02T00:00:00.000Z';
      const dmsId: DmsId = 'shipping';
      const unitType = 'loadunit';
      const unitId = 'LU12345';

      const mockQueryResponse: MovementByTypeData[] = [
        {name: 'All', value: {totalMovements: 50, percentage: 100.0}},
        {name: 'RETRIEVAL', value: {totalMovements: 30, percentage: 60.0}},
        {name: 'STORAGE', value: {totalMovements: 20, percentage: 40.0}},
      ];

      movementsStoreStub.getMovementsSummary.resolves(mockQueryResponse);

      const result = await movementsService.getMovementsSummary(
        startDate,
        endDate,
        dmsId,
        unitType,
        unitId,
      );

      expect(result).to.deep.equal(mockQueryResponse);
      expect(result).to.have.lengthOf(3);

      sinon.assert.calledOnceWithExactly(
        movementsStoreStub.getMovementsSummary,
        startDate,
        endDate,
        dmsId,
        unitType,
        unitId,
      );
    });

    it('should handle SKU-specific filtering correctly', async () => {
      const startDate = '2023-01-01T00:00:00.000Z';
      const endDate = '2023-01-02T00:00:00.000Z';
      const dmsId: DmsId = 'inventory';
      const unitType = 'sku';
      const unitId = 'SKU-ABC-123';

      const mockQueryResponse: MovementByTypeData[] = [
        {name: 'All', value: {totalMovements: 25, percentage: 100.0}},
        {name: 'RETRIEVAL', value: {totalMovements: 25, percentage: 100.0}},
      ];

      movementsStoreStub.getMovementsSummary.resolves(mockQueryResponse);

      const result = await movementsService.getMovementsSummary(
        startDate,
        endDate,
        dmsId,
        unitType,
        unitId,
      );

      expect(result).to.deep.equal(mockQueryResponse);

      sinon.assert.calledOnceWithExactly(
        movementsStoreStub.getMovementsSummary,
        startDate,
        endDate,
        dmsId,
        unitType,
        unitId,
      );
    });

    it('should handle store errors gracefully and propagate them correctly', async () => {
      const startDate = '2023-01-01T00:00:00.000Z';
      const endDate = '2023-01-02T00:00:00.000Z';
      const dmsId: DmsId = 'inventory';
      const storeError = new Error('Database connection failed');

      movementsStoreStub.getMovementsSummary.rejects(storeError);

      try {
        await movementsService.getMovementsSummary(startDate, endDate, dmsId);
        expect.fail('Expected error to be thrown');
      } catch (error) {
        expect(error).to.equal(storeError);
      }

      sinon.assert.calledOnceWithExactly(
        movementsStoreStub.getMovementsSummary,
        startDate,
        endDate,
        dmsId,
        undefined,
        undefined,
      );
    });
  });

  describe('getMovementsByLocation', () => {
    it('should successfully retrieve movements aggregated by source location with proper data structure', async () => {
      const startDate = '2023-01-01T00:00:00.000Z';
      const endDate = '2023-01-02T00:00:00.000Z';
      const dmsId: DmsId = 'inventory';
      const scope = 'source_location';

      const mockQueryResponse: MovementLocationData = {
        locationData: [
          {
            RETRIEVAL: [
              {value: 150, name: '01'},
              {value: 75, name: '02'},
              {value: 50, name: '03'},
            ],
            STORAGE: [
              {value: 100, name: '01'},
              {value: 80, name: '02'},
            ],
            BYPASS: [{value: 25, name: '01'}],
          },
        ],
      };

      movementsStoreStub.getMovementsByLocation.resolves(mockQueryResponse);

      const result = await movementsService.getMovementsByLocation(
        startDate,
        endDate,
        dmsId,
        scope,
      );

      // Structure validation
      expect(result).to.have.property('locationData');
      expect(result.locationData).to.be.an('array');
      expect(result.locationData).to.have.lengthOf(1);

      // Content validation
      const locationDataEntry = result.locationData[0];
      expect(locationDataEntry).to.have.property('RETRIEVAL');
      expect(locationDataEntry).to.have.property('STORAGE');
      expect(locationDataEntry).to.have.property('BYPASS');

      // Validate data point structure
      locationDataEntry.RETRIEVAL.forEach(item => {
        expect(item)
          .to.have.property('value')
          .that.is.a('number')
          .greaterThan(0);
        expect(item).to.have.property('name').that.is.a('string').that.is.not
          .empty;
      });

      expect(result).to.deep.equal(mockQueryResponse);

      sinon.assert.calledOnceWithExactly(
        movementsStoreStub.getMovementsByLocation,
        startDate,
        endDate,
        dmsId,
        scope,
        undefined, // unitType
        undefined, // unitId
      );
    });

    it('should handle different location scope types correctly', async () => {
      const startDate = '2023-01-01T00:00:00.000Z';
      const endDate = '2023-01-02T00:00:00.000Z';
      const dmsId: DmsId = 'shipping';
      const scope = 'destination_type';

      const mockQueryResponse: MovementLocationData = {
        locationData: [
          {
            RETRIEVAL: [{value: 25, name: 'Shuttle'}],
            STORAGE: [{value: 15, name: 'Drop Station'}],
          },
        ],
      };

      movementsStoreStub.getMovementsByLocation.resolves(mockQueryResponse);

      const result = await movementsService.getMovementsByLocation(
        startDate,
        endDate,
        dmsId,
        scope,
      );

      expect(result).to.deep.equal(mockQueryResponse);

      sinon.assert.calledOnceWithExactly(
        movementsStoreStub.getMovementsByLocation,
        startDate,
        endDate,
        dmsId,
        scope,
        undefined,
        undefined,
      );
    });

    it('should handle unit filtering with location aggregation', async () => {
      const startDate = '2023-01-01T00:00:00.000Z';
      const endDate = '2023-01-02T00:00:00.000Z';
      const dmsId: DmsId = 'inventory';
      const scope = 'source_location';
      const unitType = 'sku';
      const unitId = 'SKU-XYZ-789';

      const mockQueryResponse: MovementLocationData = {
        locationData: [
          {
            RETRIEVAL: [{value: 10, name: '01'}],
          },
        ],
      };

      movementsStoreStub.getMovementsByLocation.resolves(mockQueryResponse);

      const result = await movementsService.getMovementsByLocation(
        startDate,
        endDate,
        dmsId,
        scope,
        unitType,
        unitId,
      );

      expect(result).to.deep.equal(mockQueryResponse);

      sinon.assert.calledOnceWithExactly(
        movementsStoreStub.getMovementsByLocation,
        startDate,
        endDate,
        dmsId,
        scope,
        unitType,
        unitId,
      );
    });
  });

  describe('getMovementsByTimeSeries', () => {
    it('should successfully retrieve movements aggregated by day with time series structure', async () => {
      const startDate = '2023-01-01T00:00:00.000Z';
      const endDate = '2023-01-02T00:00:00.000Z';
      const dmsId: DmsId = 'inventory';
      const granularity = 'day';

      const mockQueryResponse: MovementTimeSeriesData = {
        timeSeriesData: [
          {
            RETRIEVAL: [
              {value: 200, name: '2023-01-01'},
              {value: 180, name: '2023-01-02'},
            ],
            STORAGE: [
              {value: 150, name: '2023-01-01'},
              {value: 170, name: '2023-01-02'},
            ],
            BYPASS: [
              {value: 50, name: '2023-01-01'},
              {value: 45, name: '2023-01-02'},
            ],
          },
        ],
      };

      movementsStoreStub.getMovementsByTimeSeries.resolves(mockQueryResponse);

      const result = await movementsService.getMovementsByTimeSeries(
        startDate,
        endDate,
        dmsId,
        granularity,
      );

      // Structure validation
      expect(result).to.have.property('timeSeriesData');
      expect(result.timeSeriesData).to.be.an('array');
      expect(result.timeSeriesData).to.have.lengthOf(1);

      // Content validation
      const timeSeriesEntry = result.timeSeriesData[0];
      expect(timeSeriesEntry).to.have.property('RETRIEVAL');
      expect(timeSeriesEntry).to.have.property('STORAGE');
      expect(timeSeriesEntry).to.have.property('BYPASS');

      // Validate time series data points
      timeSeriesEntry.RETRIEVAL.forEach(item => {
        expect(item)
          .to.have.property('value')
          .that.is.a('number')
          .greaterThan(0);
        expect(item)
          .to.have.property('name')
          .that.is.a('string')
          .that.matches(/^\d{4}-\d{2}-\d{2}$/);
      });

      expect(result).to.deep.equal(mockQueryResponse);

      sinon.assert.calledOnceWithExactly(
        movementsStoreStub.getMovementsByTimeSeries,
        startDate,
        endDate,
        dmsId,
        granularity,
        undefined, // unitType
        undefined, // unitId
      );
    });

    it('should handle hourly granularity time series correctly', async () => {
      const startDate = '2023-01-01T00:00:00.000Z';
      const endDate = '2023-01-01T23:59:59.000Z';
      const dmsId: DmsId = 'shipping';
      const granularity = 'hour';

      const mockQueryResponse: MovementTimeSeriesData = {
        timeSeriesData: [
          {
            RETRIEVAL: [
              {value: 25, name: '8'},
              {value: 30, name: '9'},
              {value: 20, name: '10'},
            ],
            STORAGE: [
              {value: 15, name: '8'},
              {value: 20, name: '9'},
              {value: 18, name: '10'},
            ],
          },
        ],
      };

      movementsStoreStub.getMovementsByTimeSeries.resolves(mockQueryResponse);

      const result = await movementsService.getMovementsByTimeSeries(
        startDate,
        endDate,
        dmsId,
        granularity,
      );

      // Validate hour format in names
      const timeSeriesEntry = result.timeSeriesData[0];
      timeSeriesEntry.RETRIEVAL.forEach(item => {
        const hour = parseInt(item.name);
        expect(hour).to.be.within(0, 23);
      });

      expect(result).to.deep.equal(mockQueryResponse);

      sinon.assert.calledOnceWithExactly(
        movementsStoreStub.getMovementsByTimeSeries,
        startDate,
        endDate,
        dmsId,
        granularity,
        undefined,
        undefined,
      );
    });

    it('should handle unit filtering with time series aggregation', async () => {
      const startDate = '2023-01-01T00:00:00.000Z';
      const endDate = '2023-01-02T00:00:00.000Z';
      const dmsId: DmsId = 'inventory';
      const granularity = 'day';
      const unitType = 'loadunit';
      const unitId = 'LU-TEST-456';

      const mockQueryResponse: MovementTimeSeriesData = {
        timeSeriesData: [
          {
            RETRIEVAL: [{value: 5, name: '2023-01-01'}],
            STORAGE: [{value: 3, name: '2023-01-01'}],
          },
        ],
      };

      movementsStoreStub.getMovementsByTimeSeries.resolves(mockQueryResponse);

      const result = await movementsService.getMovementsByTimeSeries(
        startDate,
        endDate,
        dmsId,
        granularity,
        unitType,
        unitId,
      );

      expect(result).to.deep.equal(mockQueryResponse);

      sinon.assert.calledOnceWithExactly(
        movementsStoreStub.getMovementsByTimeSeries,
        startDate,
        endDate,
        dmsId,
        granularity,
        unitType,
        unitId,
      );
    });
  });

  describe('getMovementsByLoadUnits', () => {
    it('should successfully retrieve movements aggregated by load units with pagination metadata transformation', async () => {
      const startDate = '2023-01-01T00:00:00.000Z';
      const endDate = '2023-01-02T00:00:00.000Z';
      const dmsId: DmsId = 'inventory';
      const unitType = 'loadunit';
      const limit = 100;
      const page = 1;

      const mockStoreResponse = {
        list: {
          RETRIEVAL: [
            {value: 25, name: 'LU001'},
            {value: 30, name: 'LU002'},
            {value: 20, name: 'LU003'},
          ],
          STORAGE: [
            {value: 15, name: 'LU001'},
            {value: 18, name: 'LU002'},
          ],
          BYPASS: [{value: 5, name: 'LU001'}],
        } as MovementLoadUnitsData,
        totalResults: 150,
      };

      const expectedResponse = {
        data: [mockStoreResponse.list],
        metadata: {
          page,
          limit,
          totalResults: mockStoreResponse.totalResults,
        },
      };

      movementsStoreStub.getMovementsByLoadUnits.resolves(mockStoreResponse);

      const result = await movementsService.getMovementsByLoadUnits(
        startDate,
        endDate,
        dmsId,
        unitType,
        limit,
        page,
        '', // unitId
      );

      // Structure validation
      expect(result).to.have.property('data');
      expect(result).to.have.property('metadata');
      expect(result.data).to.be.an('array');
      expect(result.data).to.have.lengthOf(1);

      // Content validation
      const loadUnitsData: any = (result.data as any)[0];
      expect(loadUnitsData).to.have.property('RETRIEVAL');
      expect(loadUnitsData).to.have.property('STORAGE');
      expect(loadUnitsData).to.have.property('BYPASS');

      // Validate load unit data points
      loadUnitsData.RETRIEVAL.forEach((item: any) => {
        expect(item)
          .to.have.property('value')
          .that.is.a('number')
          .greaterThan(0);
        expect(item).to.have.property('name').that.is.a('string').that.is.not
          .empty;
      });

      // Pagination metadata validation
      expect(result.metadata).to.have.property('page').that.equals(page);
      expect(result.metadata).to.have.property('limit').that.equals(limit);
      expect(result.metadata).to.have.property('totalResults').that.equals(150);

      expect(result).to.deep.equal(expectedResponse);

      sinon.assert.calledOnceWithExactly(
        movementsStoreStub.getMovementsByLoadUnits,
        startDate,
        endDate,
        dmsId,
        unitType,
        limit,
        page,
        '', // unitId
      );
    });

    it('should handle SKU-based aggregation with proper pagination', async () => {
      const startDate = '2023-01-01T00:00:00.000Z';
      const endDate = '2023-01-02T00:00:00.000Z';
      const dmsId: DmsId = 'shipping';
      const unitType = 'sku';
      const limit = 50;
      const page = 2;
      const unitId = 'SKU-FILTER-123';

      const mockStoreResponse = {
        list: {
          RETRIEVAL: [{value: 8, name: 'SKU-ABC-001'}],
          STORAGE: [{value: 5, name: 'SKU-ABC-001'}],
        } as MovementLoadUnitsData,
        totalResults: 75,
      };

      const expectedResponse = {
        data: [mockStoreResponse.list],
        metadata: {
          page,
          limit,
          totalResults: 75,
        },
      };

      movementsStoreStub.getMovementsByLoadUnits.resolves(mockStoreResponse);

      const result = await movementsService.getMovementsByLoadUnits(
        startDate,
        endDate,
        dmsId,
        unitType,
        limit,
        page,
        unitId,
      );

      expect(result).to.deep.equal(expectedResponse);

      sinon.assert.calledOnceWithExactly(
        movementsStoreStub.getMovementsByLoadUnits,
        startDate,
        endDate,
        dmsId,
        unitType,
        limit,
        page,
        unitId,
      );
    });

    it('should transform store response format to API response format correctly', async () => {
      const startDate = '2023-01-01T00:00:00.000Z';
      const endDate = '2023-01-02T00:00:00.000Z';
      const dmsId: DmsId = 'inventory';
      const unitType = 'loadunit';
      const limit = 10;
      const page = 1;

      const mockStoreResponse = {
        list: {
          RETRIEVAL: [{value: 1, name: 'LU-SINGLE'}],
        } as MovementLoadUnitsData,
        totalResults: 1,
      };

      movementsStoreStub.getMovementsByLoadUnits.resolves(mockStoreResponse);

      const result = await movementsService.getMovementsByLoadUnits(
        startDate,
        endDate,
        dmsId,
        unitType,
        limit,
        page,
      );

      // Verify transformation from PaginatedResults to ApiResponseArray
      expect(result.data).to.be.an('array');
      expect((result.data as any)[0]).to.equal(mockStoreResponse.list);
      expect(result.metadata).to.deep.equal({
        page,
        limit,
        totalResults: mockStoreResponse.totalResults,
      });

      sinon.assert.calledOnceWithExactly(
        movementsStoreStub.getMovementsByLoadUnits,
        startDate,
        endDate,
        dmsId,
        unitType,
        limit,
        page,
        undefined,
      );
    });

    it('should handle store errors and propagate them correctly', async () => {
      const startDate = '2023-01-01T00:00:00.000Z';
      const endDate = '2023-01-02T00:00:00.000Z';
      const dmsId: DmsId = 'inventory';
      const unitType = 'loadunit';
      const limit = 100;
      const page = 1;
      const storeError = new Error('Pagination query failed');

      movementsStoreStub.getMovementsByLoadUnits.rejects(storeError);

      try {
        await movementsService.getMovementsByLoadUnits(
          startDate,
          endDate,
          dmsId,
          unitType,
          limit,
          page,
        );
        expect.fail('Expected error to be thrown');
      } catch (error) {
        expect(error).to.equal(storeError);
      }

      sinon.assert.calledOnceWithExactly(
        movementsStoreStub.getMovementsByLoadUnits,
        startDate,
        endDate,
        dmsId,
        unitType,
        limit,
        page,
        undefined,
      );
    });
  });
});
