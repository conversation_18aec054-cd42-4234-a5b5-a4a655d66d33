import {
  Container,
  ProtectedRouteMiddleware,
  WinstonLogger,
  startEndDateValidation,
  configPostgresDatabase,
  IctError,
} from 'ict-api-foundations';
import {
  Controller,
  Post,
  Put,
  Get,
  Body,
  Path,
  Middlewares,
  OperationId,
  Route,
  SuccessResponse,
  Tags,
  Example,
} from 'tsoa';
import {AlarmsService} from '../services/alarms-service.js';
import {type PostAlarmsListResponse, type Alarm} from '../defs/alarm-def.ts';
import {type PaginatedRequest} from '@ict/sdk-foundations/types';

import type {AlarmCreateDto} from '../defs/alarm-create-dto.ts';
import type {AlarmUpdateDto} from '../defs/alarm-update-dto.ts';

@Route('availability')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [configPostgresDatabase()],
  }),
])
export class AlarmsController extends Controller {
  // Example response for the standard format
  public static readonly exampleResponse: PostAlarmsListResponse = {
    data: [
      {
        id: 'c356e13f-8898-4e4f-bdce-bffcd148e605',
        splitId: '615123',
        faultId: 'b307f935',
        title: 'CA003 ANTI GRID LOCK LEVEL 1',
        description: 'CA003 ANTI GRID LOCK LEVEL 1',
        tag: 'CA003_AGL_L1',
        location: {
          area: 'DMSInboundMergeSorter',
          section: 'CC001',
          equipment: 'CA003_AGL_L1',
        },
        timing: {
          startTime: new Date('2025-07-10T02:13:40.000Z'),
          endTime: new Date('2025-07-10T02:14:07.000Z'),
          duration: '27000',
          updatedStartTime: new Date('2025-07-10T02:13:40.000Z'),
          updatedEndTime: new Date('2025-07-10T02:14:07.000Z'),
          updatedDuration: '27000',
        },
        status: 'Active',
        reason: 'KEPT',
        lastUpdatedUser: '<EMAIL>',
      },
    ],
    metadata: {
      page: 1,
      limit: 20,
      totalResults: 1,
      totalPages: 1,
    },
  };

  /**
   * Post alarms data with filters using standard pagination format
   *
   * @param {object} request - Standard pagination request with filters, sorting, grouping, and date range
   * @returns {Promise<PostAlarmsListResponse>} Paginated response with alarm data
   * @throws IctError
   */
  @Example<PostAlarmsListResponse>(AlarmsController.exampleResponse)
  @SuccessResponse('200', 'Successfully retrieved alarms')
  @Post('/alarms/list')
  @OperationId('PostAlarmsList')
  @Tags('availability')
  public async postAlarmsList(
    @Body() request: PaginatedRequest,
  ): Promise<PostAlarmsListResponse> {
    const logger = Container.get(WinstonLogger);
    const alarmsService = Container.get(AlarmsService);

    // Validate the start and end dates
    startEndDateValidation(request.start_date, request.end_date);

    logger.info('Getting alarms list', {
      request,
    });

    return await alarmsService.getAlarms(request);
  }

  /**
   * Get a single alarm by its ID
   *
   * @param {string} id - The alarm ID to retrieve
   * @returns {Promise<Alarm>} The alarm data
   * @throws IctError
   */
  @SuccessResponse('200', 'Successfully retrieved alarm')
  @Get('/alarms/{id}')
  @OperationId('GetAlarmById')
  @Tags('availability')
  public async getAlarmById(@Path() id: string): Promise<Alarm> {
    const logger = Container.get(WinstonLogger);
    const alarmsService = Container.get(AlarmsService);

    logger.info('Getting alarm by ID', {
      alarmId: id,
    });

    return await alarmsService.getAlarmById(id);
  }

  /**
   * Create a new alarm
   *
   * @param {AlarmCreateDto} request - The alarm data to create
   * @returns {Promise<void>} Whether or not the alarm was created successfully
   * @throws IctError
   */
  @SuccessResponse('201', 'Successfully created alarm')
  @Post('/alarms')
  @OperationId('PostAlarms')
  @Tags('availability')
  public async postAlarms(@Body() request: AlarmCreateDto): Promise<void> {
    if (request.startDateLocal && request.endDateLocal) {
      startEndDateValidation(request.startDateLocal, request.endDateLocal);
    }

    const logger = Container.get(WinstonLogger);
    const alarmsService = Container.get(AlarmsService);

    logger.info('Creating alarm', {
      request,
    });

    await alarmsService.createAlarm(request);
  }

  /**
   * Update an alarm
   *
   * @param {AlarmUpdateDto} request - The alarm data to update
   * @returns {Promise<void>} Whether or not the alarm was updated successfully
   * @throws IctError
   */
  @SuccessResponse('200', 'Successfully updated alarm')
  @Put('/alarms')
  @OperationId('PutAlarms')
  @Tags('availability')
  public async putAlarms(@Body() request: AlarmUpdateDto): Promise<void> {
    if (request.startDateLocal && request.endDateLocal) {
      startEndDateValidation(request.startDateLocal, request.endDateLocal);
    }

    if (!request.isIncluded) {
      // when excluding: excludeReason required, dates forbidden
      if (!request.excludeReason) {
        throw IctError.badRequest(
          'excludeReason is required when isIncluded is false',
        );
      }
      if (request.startDateLocal || request.endDateLocal) {
        throw IctError.badRequest(
          'startDateLocal and endDateLocal are not needed when isIncluded is false',
        );
      }
    } else {
      // when including: excludeReason forbidden
      if (request.excludeReason) {
        throw IctError.badRequest(
          'excludeReason should not be provided when isIncluded is true',
        );
      }
    }

    const logger = Container.get(WinstonLogger);
    const alarmsService = Container.get(AlarmsService);

    logger.info('Updating alarm', {
      request,
    });

    await alarmsService.updateAlarm(request);
  }
}
