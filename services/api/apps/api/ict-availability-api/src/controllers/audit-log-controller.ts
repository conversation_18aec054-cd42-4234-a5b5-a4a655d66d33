import {
  Container,
  ProtectedRouteMiddleware,
  WinstonLogger,
  configPostgresDatabase,
} from 'ict-api-foundations';
import {
  Controller,
  Post,
  Body,
  Middlewares,
  OperationId,
  Route,
  SuccessResponse,
  Tags,
  Example,
  Produces,
} from 'tsoa';
import {AuditLogService} from '../services/audit-log-service.js';
import {PostAuditLogsListResponse} from '../defs/audit-log-def.js';
import {
  type PaginatedRequest,
  type NonPaginatedRequest,
} from '@ict/sdk-foundations/types';
// import {NonPaginatedRequest} from '@ict/sdk-foundations/types/defs/request-filters/request';
import {Readable} from 'stream';

@Route('availability')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [configPostgresDatabase()],
  }),
])
export class AuditLogController extends Controller {
  private static readonly exampleResponse: PostAuditLogsListResponse = {
    data: [
      {
        alarmId: 'bef3e88b-8b7d-4204-a2fe-fdab70ac7021',
        alarmIdentifier: 'bef3e88b',
        operationType: 'insert',
        originalValues: {},
        newValues: {
          alarm_pk: 53003,
          alarm_id: 'bef3e88b-8b7d-4204-a2fe-fdab70ac7021',
          alarm_start_datetime_local: '2025-07-30T21:06:21',
          facility_bid: 'acehardware#jeffersonga',
        },
        diffValues: {},
        modifiedUser: 'Test Created Alarm',
        modifiedTime: '2025-07-31T21:25:11.686Z',
        modifiedComment: 'Test comments',
        processUuid: '550e8400-e29b-41d4-a716-************',
        processIdentifier: 'process_001',
      },
    ],
    metadata: {
      page: 1,
      limit: 20,
      totalResults: 1,
      totalPages: 1,
    },
  };

  /**
   * Post audit logs data with filters using standard pagination format
   *
   * @param {object} request - Standard pagination request with filters, sorting, grouping, and date range
   * @returns {Promise<PostAuditLogsListResponse>} Paginated response with audit log data
   * @throws IctError
   */
  @Example<PostAuditLogsListResponse>(AuditLogController.exampleResponse)
  @SuccessResponse('200', 'Successfully retrieved audit logs')
  @Post('/alarms/audit-log/list')
  @OperationId('PostAuditLogsList')
  @Tags('availability')
  public async postAuditLogsList(
    @Body() request: PaginatedRequest,
  ): Promise<PostAuditLogsListResponse> {
    const logger = Container.get(WinstonLogger);
    const auditLogService = Container.get(AuditLogService);

    logger.info('Getting audit logs list', {
      request,
    });

    return await auditLogService.getAuditLogs(request);
  }

  @Tags('availability')
  @SuccessResponse('200', 'Successfully exported audit logs to Excel')
  @Post('alarms/audit-log/export')
  @OperationId('ExportActivityLog')
  @Produces('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
  public async postAuditLogExport(
    @Body() request: NonPaginatedRequest,
  ): Promise<Readable> {
    const logger = Container.get(WinstonLogger);
    const auditLogService = Container.get(AuditLogService);

    logger.info('Getting audit logs list exported to Excel', {
      request,
    });

    const {stream, headers} =
      await auditLogService.getAuditLogExportToExcel(request);

    for (const [headerName, headerValue] of Object.entries(headers)) {
      if (headerName && typeof headerValue == 'string') {
        this.setHeader(headerName, headerValue as string);
      }
    }

    return stream;
  }
}
