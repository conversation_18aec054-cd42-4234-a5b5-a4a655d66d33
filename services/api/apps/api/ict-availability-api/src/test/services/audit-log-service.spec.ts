import {expect} from 'chai';
import {beforeEach, describe, it} from 'mocha';
import sinon from 'sinon';
import {Container} from 'typedi';
import {WinstonLogger, ContextService, IctError} from 'ict-api-foundations';
import {AuditLogService} from '../../services/audit-log-service.js';
import {EDPAvailabilityService} from '../../services/edp-availability-service.js';
import {ContextUtils} from '../../utils/context-utils.js';
import {PostAuditLogsListResponse} from '../../defs/audit-log-def.js';
import {type PaginatedRequest} from '@ict/sdk-foundations/types';

describe('AuditLogService', () => {
  let service: AuditLogService;
  let mockEDPService: sinon.SinonStubbedInstance<EDPAvailabilityService>;
  let mockLogger: sinon.SinonStubbedInstance<WinstonLogger>;
  let mockContext: sinon.SinonStubbedInstance<ContextService>;
  let getTenantIdStub: sinon.SinonStub;
  let getFacilityIdStub: sinon.SinonStub;

  const mockTenantBid = 'test-tenant';
  const mockFacilityBid = 'test-facility';

  const mockEDPResponse = {
    data: [
      {
        alarmId: 'bef3e88b-8b7d-4204-a2fe-fdab70ac7021',
        alarmIdentifier: 'bef3e88b',
        operationType: 'insert' as const,
        originalValues: {},
        newValues: {
          alarm_pk: 53003,
          alarm_id: 'bef3e88b-8b7d-4204-a2fe-fdab70ac7021',
          alarm_start_datetime_local: '2025-07-30T21:06:21',
          facility_bid: 'acehardware#jeffersonga',
        },
        diffValues: {},
        modifiedUser: 'Test Created Alarm',
        modifiedTime: '2025-07-31T21:25:11.686Z',
        modifiedComment: 'Test comments',
        processUuid: '550e8400-e29b-41d4-a716-************',
        processIdentifier: 'process_001',
      },
    ],
    metadata: {
      page: 1,
      totalPages: 1,
      total: 1,
      limit: 20,
    },
  };

  beforeEach(() => {
    // Create mocks
    mockEDPService = sinon.createStubInstance(EDPAvailabilityService);
    mockLogger = sinon.createStubInstance(WinstonLogger);
    mockContext = sinon.createStubInstance(ContextService);

    // Set up Container
    Container.reset();
    Container.set(EDPAvailabilityService, mockEDPService);
    Container.set(WinstonLogger, mockLogger);
    Container.set(ContextService, mockContext);

    // Stub ContextUtils methods
    getTenantIdStub = sinon.stub(ContextUtils, 'getTenantId');
    getFacilityIdStub = sinon.stub(ContextUtils, 'getFacilityId');

    getTenantIdStub.returns(mockTenantBid);
    getFacilityIdStub.returns(mockFacilityBid);

    // Create service instance
    service = new AuditLogService(mockEDPService, mockLogger, mockContext);
  });

  afterEach(() => {
    sinon.restore();
    Container.reset();
  });

  describe('constructor', () => {
    it('should create an instance', () => {
      expect(service).to.be.instanceOf(AuditLogService);
    });
  });

  describe('getAuditLogs (structured filters)', () => {
    const mockQueryParams: PaginatedRequest = {
      limit: 20,
      page: 1,
      start_date: new Date('2025-01-01'),
      end_date: new Date('2025-01-31'),
      filters: {
        operationTypes: ['insert', 'update'],
        modifiedUsers: ['<EMAIL>'],
        alarmId: 'specific-alarm-id',
      } as any,
      sortFields: [
        {
          columnName: 'modifiedTime',
          isDescending: true,
        },
      ],
      searchString: 'test search',
    };

    const mockStandardResponse: PostAuditLogsListResponse = {
      data: mockEDPResponse.data,
      metadata: {
        page: 1,
        limit: 20,
        totalResults: 1,
        totalPages: 1,
      },
    };

    it('should successfully get audit logs with structured filters', async () => {
      mockEDPService.getAlarmAuditLogs.resolves(mockEDPResponse);

      const result = await service.getAuditLogs(mockQueryParams);

      expect(result).to.deep.equal(mockStandardResponse);
      expect(
        mockEDPService.getAlarmAuditLogs.calledWith(
          mockTenantBid,
          mockFacilityBid,
          sinon.match.object,
        ),
      ).to.be.true;
      expect(
        mockLogger.info.calledWith(
          'Getting audit logs with structured filters',
          {
            tenantId: mockTenantBid,
            facilityId: mockFacilityBid,
            queryParams: mockQueryParams,
          },
        ),
      ).to.be.true;
    });

    it('should transform structured filters to direct EDP API params', async () => {
      mockEDPService.getAlarmAuditLogs.resolves(mockEDPResponse);

      await service.getAuditLogs(mockQueryParams);

      const actualParams = mockEDPService.getAlarmAuditLogs.firstCall.args[2];
      expect(actualParams).to.include({
        page: 1,
        limit: 20,
        searchTerm: 'test search',
        sortBy: 'modifiedTime',
        orderBy: 'DESC',
        operationTypes: JSON.stringify(['insert', 'update']),
        modifiedUser: '<EMAIL>',
        alarmId: 'specific-alarm-id',
      });
      expect(actualParams.startTimestamp).to.include('2025-01-01');
      expect(actualParams.endTimestamp).to.include('2025-01-31');
    });

    it('should extract tenant and facility IDs from context', async () => {
      mockEDPService.getAlarmAuditLogs.resolves(mockEDPResponse);

      await service.getAuditLogs(mockQueryParams);

      expect(getTenantIdStub.calledWith(mockContext, mockLogger)).to.be.true;
      expect(getFacilityIdStub.calledWith(mockContext, mockLogger)).to.be.true;
    });

    it('should handle errors and re-throw them', async () => {
      const error = IctError.internalServerError('Test error');
      mockEDPService.getAlarmAuditLogs.rejects(error);

      try {
        await service.getAuditLogs(mockQueryParams);
        expect.fail('Should have thrown an error');
      } catch (thrownError) {
        expect(thrownError).to.equal(error);
        expect(
          mockLogger.error.calledWith('Failed to get audit logs', {
            error,
            queryParams: mockQueryParams,
          }),
        ).to.be.true;
      }
    });
  });

  describe('getAuditLogExportToExcel', () => {
    const mockStream: any = {pipe: sinon.stub()};
    const mockHeaders = {
      'content-type':
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'content-disposition':
        'attachment; filename="AllAlarmsAuditLog_1-1-2025.xlsx"',
    } as Record<string, string>;

    beforeEach(() => {
      (mockEDPService.getAlarmAuditLogsExportStream as any).resolves({
        stream: mockStream,
        headers: mockHeaders,
      });
    });

    it('should return stream and headers from EDP service', async () => {
      const req: any = {
        start_date: new Date('2025-01-01T00:00:00.000Z'),
        end_date: new Date('2025-01-31T23:59:59.000Z'),
        filters: {modifiedUsers: ['<EMAIL>']},
        sortFields: [{columnName: 'modifiedTime', isDescending: true}],
        limit: 100000,
      };

      const result = await service.getAuditLogExportToExcel(req);
      expect(result.stream).to.equal(mockStream);
      expect(result.headers).to.equal(mockHeaders);

      // Verify transformation was applied
      const params = (mockEDPService.getAlarmAuditLogsExportStream as any)
        .firstCall.args[2];
      expect(params).to.include({
        sortBy: 'modifiedTime',
        orderBy: 'DESC',
        modifiedUser: '<EMAIL>',
      });
      expect(params.startTimestamp).to.include('2025-01-01');
      expect(params.endTimestamp).to.include('2025-01-31');
    });

    it('should include alarmId from filters if provided', async () => {
      const req: any = {filters: {alarmId: 'alarm-123'}};
      await service.getAuditLogExportToExcel(req);
      const params = (mockEDPService.getAlarmAuditLogsExportStream as any)
        .firstCall.args[2];
      expect(params.alarmId).to.equal('alarm-123');
    });

    it('should stringify operationTypes when multiple', async () => {
      const req: any = {filters: {operationTypes: ['insert', 'update']}};
      await service.getAuditLogExportToExcel(req);
      const params = (mockEDPService.getAlarmAuditLogsExportStream as any)
        .firstCall.args[2];
      expect(params.operationTypes).to.equal(
        JSON.stringify(['insert', 'update']),
      );
    });

    it('should log and rethrow on error', async () => {
      (mockEDPService.getAlarmAuditLogsExportStream as any).rejects(
        IctError.internalServerError('boom'),
      );

      try {
        await service.getAuditLogExportToExcel({} as any);
        expect.fail('should throw');
      } catch (e) {
        expect(mockLogger.error.called).to.be.true;
      }
    });
  });
});
