import {expect} from 'chai';
import {beforeEach, describe, it} from 'mocha';
import sinon from 'sinon';
import {Container} from 'typedi';
import {WinstonLogger, IctError} from 'ict-api-foundations';
import {AuditLogController} from '../../controllers/audit-log-controller.js';
import {AuditLogService} from '../../services/audit-log-service.js';
import {PostAuditLogsListResponse} from '../../defs/audit-log-def.js';

describe('AuditLogController', () => {
  let controller: AuditLogController;
  let mockAuditLogService: sinon.SinonStubbedInstance<AuditLogService>;
  let mockLogger: sinon.SinonStubbedInstance<WinstonLogger>;

  const mockAuditLogResponse: PostAuditLogsListResponse = {
    data: [
      {
        alarmId: 'bef3e88b-8b7d-4204-a2fe-fdab70ac7021',
        alarmIdentifier: 'bef3e88b',
        operationType: 'insert' as const,
        originalValues: {},
        newValues: {
          alarm_pk: 53003,
          alarm_id: 'bef3e88b-8b7d-4204-a2fe-fdab70ac7021',
          alarm_start_datetime_local: '2025-07-30T21:06:21',
          facility_bid: 'acehardware#jeffersonga',
        },
        diffValues: {},
        modifiedUser: 'Test Created Alarm',
        modifiedTime: '2025-07-31T21:25:11.686Z',
        modifiedComment: 'Test comments',
        processUuid: '550e8400-e29b-41d4-a716-************',
        processIdentifier: 'process_001',
      },
    ],
    metadata: {
      page: 1,
      limit: 20,
      totalResults: 1,
      totalPages: 1,
    },
  };

  beforeEach(() => {
    // Create mocks
    mockAuditLogService = sinon.createStubInstance(AuditLogService);
    mockLogger = sinon.createStubInstance(WinstonLogger);

    // Set up Container
    Container.reset();
    Container.set(AuditLogService, mockAuditLogService);
    Container.set(WinstonLogger, mockLogger);

    // Create controller instance
    controller = new AuditLogController();
  });

  afterEach(() => {
    sinon.restore();
    Container.reset();
  });

  describe('constructor', () => {
    it('should create an instance', () => {
      expect(controller).to.be.instanceOf(AuditLogController);
    });
  });

  describe('postAuditLogsList', () => {
    const validRequest = {
      start_date: new Date('2025-01-01T00:00:00.000Z'),
      end_date: new Date('2025-01-31T23:59:59.000Z'),
      limit: 20,
      page: 1,
      filters: {
        operationTypes: ['insert', 'update'],
        modifiedUsers: ['<EMAIL>'],
        alarmId: 'specific-alarm-id',
      },
      sortFields: [
        {
          columnName: 'modifiedTime',
          isDescending: true,
        },
      ],
      searchString: 'test search',
    };

    it('should successfully post audit logs list with structured filters', async () => {
      mockAuditLogService.getAuditLogs.resolves(mockAuditLogResponse);

      const result = await controller.postAuditLogsList(validRequest);

      expect(result).to.deep.equal(mockAuditLogResponse);
      expect(
        mockAuditLogService.getAuditLogs.calledWith(sinon.match(validRequest)),
      ).to.be.true;
      expect(
        mockLogger.info.calledWith('Getting audit logs list', {
          request: validRequest,
        }),
      ).to.be.true;
    });

    it('should handle minimal request parameters', async () => {
      const minimalRequest = {
        start_date: new Date('2025-01-01T00:00:00.000Z'),
        end_date: new Date('2025-01-31T23:59:59.000Z'),
      };

      mockAuditLogService.getAuditLogs.resolves(mockAuditLogResponse);

      const result = await controller.postAuditLogsList(minimalRequest);

      expect(result).to.deep.equal(mockAuditLogResponse);
      expect(
        mockAuditLogService.getAuditLogs.calledWith(
          sinon.match(minimalRequest),
        ),
      ).to.be.true;
    });

    it('should handle service errors', async () => {
      const error = IctError.internalServerError('Service error');
      mockAuditLogService.getAuditLogs.rejects(error);

      try {
        await controller.postAuditLogsList(validRequest);
        expect.fail('Should have thrown an error');
      } catch (thrownError) {
        expect(thrownError).to.equal(error);
      }
    });
  });

  describe('postAuditLogExport', () => {
    const mockStream: any = {pipe: sinon.stub()};
    const mockHeaders = {
      'content-type':
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'content-disposition':
        'attachment; filename="AllAlarmsAuditLog_1-1-2025.xlsx"',
    } as Record<string, string>;

    it('should set headers from service and return stream', async () => {
      (mockAuditLogService.getAuditLogExportToExcel as any).resolves({
        stream: mockStream,
        headers: mockHeaders,
      });

      // Spy on setHeader
      const setHeaderSpy = sinon.spy(controller as any, 'setHeader');

      const result = await controller.postAuditLogExport({} as any);

      expect(result).to.equal(mockStream);
      expect(
        setHeaderSpy.calledWith('content-type', mockHeaders['content-type']),
      ).to.be.true;
      expect(
        setHeaderSpy.calledWith(
          'content-disposition',
          mockHeaders['content-disposition'],
        ),
      ).to.be.true;
    });

    it('should bubble up errors from service', async () => {
      const error = IctError.internalServerError('boom');
      (mockAuditLogService.getAuditLogExportToExcel as any).rejects(error);

      try {
        await controller.postAuditLogExport({} as any);
        expect.fail('should throw');
      } catch (e) {
        expect(e).to.equal(error);
      }
    });
  });
});
