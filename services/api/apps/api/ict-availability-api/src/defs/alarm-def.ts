import {ApiResponseArray} from '@ict/sdk-foundations/types';

// Application-level alarm interface (user-facing)
export interface Alarm {
  id: string;
  splitId: string;
  faultId: string;
  title: string;
  description: string;
  tag: string;
  severity?: string;
  location: {
    area: string;
    section: string;
    equipment: string;
  };
  timing: {
    // Reference times (REF)
    startTime: Date;
    endTime?: Date;
    duration: string;
    updatedStartTime?: Date;
    updatedEndTime?: Date;
    updatedDuration?: string;
    // Split times (SPLIT)
    splitStartTime?: Date;
    splitEndTime?: Date;
    splitDuration?: string;
    // Original times (ORIG)
    origStartTime?: Date;
    origEndTime?: Date;
    origDuration?: string;
  };
  status: string;
  reason: string;
  comments?: string;
  lastUpdatedUser?: string | null;
}

// Pagination metadata for alarms
export interface AlarmPaginationInfo {
  page: number;
  limit: number;
  totalResults: number;
  totalPages: number;
}

// Standard API response type for alarm lists
export type PostAlarmsListResponse = ApiResponseArray<
  Alarm[],
  AlarmPaginationInfo
>;
