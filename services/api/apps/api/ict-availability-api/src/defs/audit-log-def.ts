import {ApiResponseArray} from '@ict/sdk-foundations/types';
import {AlarmPaginationInfo} from './alarm-def.js';

export interface AlarmAuditLogEntry {
  alarmId: string;
  alarmIdentifier: string;
  operationType: 'insert' | 'update' | 'delete';
  originalValues: Record<string, unknown>;
  newValues: Record<string, unknown>;
  diffValues: Record<string, unknown>;
  modifiedUser: string;
  modifiedTime: string; // ISO 8601
  modifiedComment: string;
  processUuid: string;
  processIdentifier: string;
}

export interface GetAuditLogsResponse {
  data: AlarmAuditLogEntry[];
  metadata: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

export interface AuditLogDirectQueryParams {
  page?: number;
  limit?: number;
  alarmId?: string;
  alarmIdentifier?: string;
  operationType?: 'insert' | 'update' | 'delete';
  operationTypes?: string;
  modifiedUser?: string;
  searchTerm?: string;
  processUuid?: string;
  processIdentifier?: string;
  startTimestamp?: string;
  endTimestamp?: string;
  alarmArea?: string;
  alarmSection?: string;
  alarmEquipment?: string;
  sortBy?:
    | 'processIdentifier'
    | 'modifiedTime'
    | 'modifiedUser'
    | 'alarmIdentifier'
    | 'operationType'
    | 'modifiedComment'
    | 'alarmId'
    | 'processUuid';
  orderBy?: 'ASC' | 'DESC';
  exportColumns?: {[key: string]: boolean};
}

// Standard API response type for audit log lists
export type PostAuditLogsListResponse = ApiResponseArray<
  AlarmAuditLogEntry[],
  AlarmPaginationInfo
>;
