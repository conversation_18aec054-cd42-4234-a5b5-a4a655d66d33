import {DiS<PERSON><PERSON>, WinstonLogger, ContextService} from 'ict-api-foundations';
import {EDPAvailabilityService} from './edp-availability-service.js';
import {
  PostAuditLogsListResponse,
  AuditLogDirectQueryParams,
} from '../defs/audit-log-def.js';
import {type PaginatedRequest} from '@ict/sdk-foundations/types';
import {ContextUtils} from '../utils/context-utils.js';
import {NonPaginatedRequest} from '@ict/sdk-foundations/types/defs/request-filters/request';
import {Readable} from 'stream';

@DiService()
export class AuditLogService {
  constructor(
    private edpAvailabilityService: EDPAvailabilityService,
    private logger: WinstonLogger,
    private context: ContextService,
  ) {}

  public async getAuditLogs(
    queryParams: PaginatedRequest,
  ): Promise<PostAuditLogsListResponse> {
    const tenantId = ContextUtils.getTenantId(this.context, this.logger);
    const facilityId = ContextUtils.getFacilityId(this.context, this.logger);

    this.logger.info('Getting audit logs with structured filters', {
      tenantId,
      facilityId,
      queryParams,
    });

    try {
      const directParams = this.transformRequestToDirectParams(queryParams);

      const response = await this.edpAvailabilityService.getAlarmAuditLogs(
        tenantId,
        facilityId,
        directParams,
      );

      return {
        data: response.data,
        metadata: {
          page: response.metadata.page,
          limit: response.metadata.limit,
          totalResults: response.metadata.total,
          totalPages: response.metadata.totalPages,
        },
      };
    } catch (error) {
      this.logger.error('Failed to get audit logs', {error, queryParams});
      throw error;
    }
  }

  public async getAuditLogExportToExcel(
    queryParams: NonPaginatedRequest,
  ): Promise<{
    stream: Readable;
    headers: Record<string, string>;
  }> {
    const tenantId = ContextUtils.getTenantId(this.context, this.logger);
    const facilityId = ContextUtils.getFacilityId(this.context, this.logger);

    this.logger.info(
      'Getting audit log exported to Excel with structured filters',
      {
        tenantId,
        facilityId,
        queryParams,
      },
    );

    try {
      const directParams = this.transformRequestToDirectParams(
        queryParams,
        false,
      );

      const {stream, headers} =
        await this.edpAvailabilityService.getAlarmAuditLogsExportStream(
          tenantId,
          facilityId,
          directParams,
        );

      return {stream, headers};
    } catch (error) {
      this.logger.error('Failed to get audit logs', {error, queryParams});
      throw error;
    }
  }

  private transformRequestToDirectParams(
    queryParams: PaginatedRequest | NonPaginatedRequest,
    pagninatedRequest: boolean = true,
  ): AuditLogDirectQueryParams {
    const params: AuditLogDirectQueryParams = {};

    if ('page' in queryParams && queryParams.page !== undefined) {
      params.page = queryParams.page;
    } else if (pagninatedRequest) {
      params.page = 1;
    }

    if (queryParams.limit !== undefined) params.limit = queryParams.limit;
    if (queryParams.searchString) params.searchTerm = queryParams.searchString;

    if (queryParams.start_date) {
      params.startTimestamp = queryParams.start_date.toISOString();
    }
    if (queryParams.end_date) {
      params.endTimestamp = queryParams.end_date.toISOString();
    }

    if (queryParams.sortFields?.length) {
      const firstSort = queryParams.sortFields[0];

      const sortMapping: Record<string, string> = {
        processIdentifier: 'processIdentifier',
        modifiedTime: 'modifiedTime',
        modifiedUser: 'modifiedUser',
        alarmIdentifier: 'alarmIdentifier',
        operationType: 'operationType',
        modifiedComment: 'modifiedComment',
        alarmId: 'alarmId',
        processUuid: 'processUuid',
      };

      const mappedSort =
        sortMapping[firstSort.columnName] || firstSort.columnName;
      params.sortBy = mappedSort as NonNullable<
        AuditLogDirectQueryParams['sortBy']
      >;
      params.orderBy = firstSort.isDescending ? 'DESC' : 'ASC';
    }

    if (queryParams.filters) {
      this.extractFiltersFromStandardFormat(queryParams.filters, params);
    }

    if (
      'exportColumns' in queryParams &&
      queryParams.exportColumns !== undefined
    ) {
      params.exportColumns = queryParams.exportColumns;
    }

    if ('id' in queryParams && queryParams.id !== undefined) {
      params.alarmId = queryParams.id;
    }

    return params;
  }

  private extractFiltersFromStandardFormat(
    filters: unknown,
    params: AuditLogDirectQueryParams,
  ): void {
    if (typeof filters === 'object' && filters !== null) {
      const filterObj = filters as unknown as Record<string, unknown>;

      if (Array.isArray(filterObj.alarmIds) && filterObj.alarmIds.length > 0) {
        params.alarmId = filterObj.alarmIds[0];
      }
      if (typeof filterObj.alarmId === 'string') {
        params.alarmId = filterObj.alarmId;
      }
      if (
        Array.isArray(filterObj.alarmIdentifiers) &&
        filterObj.alarmIdentifiers.length > 0
      ) {
        params.alarmIdentifier = filterObj.alarmIdentifiers[0];
      }
      if (typeof filterObj.alarmIdentifier === 'string') {
        params.alarmIdentifier = filterObj.alarmIdentifier;
      }

      if (
        Array.isArray(filterObj.operationTypes) &&
        filterObj.operationTypes.length > 0
      ) {
        if (filterObj.operationTypes.length === 1) {
          params.operationType = filterObj.operationTypes[0] as
            | 'insert'
            | 'update'
            | 'delete';
        } else {
          params.operationTypes = JSON.stringify(filterObj.operationTypes);
        }
      }

      if (
        Array.isArray(filterObj.modifiedUsers) &&
        filterObj.modifiedUsers.length > 0
      ) {
        params.modifiedUser = filterObj.modifiedUsers[0];
      }

      if (
        Array.isArray(filterObj.processUuids) &&
        filterObj.processUuids.length > 0
      ) {
        params.processUuid = filterObj.processUuids[0];
      }
      if (
        Array.isArray(filterObj.processIdentifiers) &&
        filterObj.processIdentifiers.length > 0
      ) {
        params.processIdentifier = filterObj.processIdentifiers[0];
      }

      if (
        Array.isArray(filterObj.alarmAreas) &&
        filterObj.alarmAreas.length > 0
      ) {
        params.alarmArea = filterObj.alarmAreas[0];
      }
      if (
        Array.isArray(filterObj.alarmSections) &&
        filterObj.alarmSections.length > 0
      ) {
        params.alarmSection = filterObj.alarmSections[0];
      }
      if (
        Array.isArray(filterObj.alarmEquipment) &&
        filterObj.alarmEquipment.length > 0
      ) {
        params.alarmEquipment = filterObj.alarmEquipment[0];
      }

      if (typeof filterObj.modifiedUser === 'string') {
        params.modifiedUser = filterObj.modifiedUser;
      }
      if (typeof filterObj.searchTerm === 'string') {
        params.searchTerm = filterObj.searchTerm;
      }
    }
  }
}
