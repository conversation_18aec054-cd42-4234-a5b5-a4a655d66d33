import type {ClearCacheResponse, Metric} from 'ict-api-foundations';
import {
  Container,
  DatabaseTypes,
  EdgeDirection,
  ProtectedRouteMiddleware,
  PostgresDatabaseOptions,
  IctError,
} from 'ict-api-foundations';
import {
  Body,
  Controller,
  Example,
  Get,
  Middlewares,
  OperationId,
  Path,
  Post,
  Put,
  Route,
  SuccessResponse,
  Tags,
  Query,
} from 'tsoa';
import type {
  ClearCacheRequest,
  Edge,
  ProcessFlowDetailsResponse,
  UpdatedArea,
  UpdatePositionPayload,
} from '../defs/inventory-process-flow-def.ts';
import {ProcessFlowResponse} from '../defs/inventory-process-flow-def.ts';
import {ProcessFlowService} from '../services/process-flow-service.ts';
import {EntityTypes} from 'ict-api-schema';

/**
 * Inventory Process Flow Controller
 */
const dbMiddleware = {
  databases: [
    {
      type: DatabaseTypes.Neo4j,
    },
    {
      type: DatabaseTypes.Postgres,
      options: {
        entityType: EntityTypes.ProcessFlow,
      } as PostgresDatabaseOptions,
    },
  ],
};
@Route('inventory')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, dbMiddleware),
])
export class InventoryProcessFlowAreaController extends Controller {
  public static readonly metricsExampleData: Metric[] = [
    {
      id: 'dematic:grand_rapids_michigan:Multishuttle:InboundTotesCount:UnitsPerHour',
      type: 'hourly',
      value: 150,
      units: '/hr',
    },
    {
      id: 'dematic:grand_rapids_michigan:Multishuttle:FaultedLocations:Count',
      type: 'value',
      panelGroup: 'workstation_process',
      value: 8000,
      units: '/hr',
    },
  ];

  public static readonly exampleData: ProcessFlowResponse = {
    areas: [
      {
        id: '1',
        label: 'Area 1',
        metrics: InventoryProcessFlowAreaController.metricsExampleData,
        position: {
          x: 500,
          y: 250,
        },
        nodeType: 'Aisle',
      },
    ],
    edges: [
      {
        id: 'link1',
        source: '1',
        target: '2',
        direction: EdgeDirection.Downstream,
        metrics: InventoryProcessFlowAreaController.metricsExampleData,
      },
    ],
    lastProcessedTime: '1739290887.78988',
  };

  public static readonly exampleDetailResponse: ProcessFlowDetailsResponse = {
    metricGroups: [
      {
        title: 'Status',
        metrics: [
          {
            panelGroup: 'Status',
            id: 'dematic:grandrapids_mi:multishuttle:inbound_totes_count:units_per_hour',
            value: 15,
            type: 'hourly',
          },
          {
            panelGroup: 'Status',
            id: 'dematic:grandrapids_mi:multishuttle:outbound_totes_count:units_per_hour',
            value: 400,
            type: 'hourly',
          },
        ],
      },
    ],
  };

  @Example<ProcessFlowResponse>(InventoryProcessFlowAreaController.exampleData)
  /**
   * @returns {Promise<ProcessFlowResponse>} contract
   * @throws IctError
   */
  @SuccessResponse('200')
  @Get('/process-flow/areas')
  @OperationId('GetInventoryProcessFlowAreas')
  @Tags('inventory')
  public async getAllAreas(): Promise<ProcessFlowResponse> {
    // Get the inventory service
    const processFlowService = Container.get(ProcessFlowService);

    // Retrieve all areas from the service and return
    const processFlowResponse = await processFlowService.getAllAreas();

    if (
      processFlowResponse.areas.length === 0 &&
      processFlowResponse.edges.length === 0 &&
      !processFlowResponse.lastProcessedTime
    ) {
      throw IctError.noContent();
    }
    return processFlowResponse;
  }

  /**
   * @returns {Promise<ProcessFlowResponse>} contract
   * @throws IctError
   */
  @SuccessResponse('200')
  @Get('/process-flow/area/{areaId}')
  @OperationId('GetInventoryProcessFlowAreaById')
  @Tags('inventory')
  public async getAreaById(
    @Path() areaId: string,
  ): Promise<ProcessFlowResponse> {
    // Get the inventory service
    const processFlowService = Container.get(ProcessFlowService);

    // Retrieve all areas from the service and return
    const processFlowResponse = await processFlowService.getAllAreas(areaId);
    if (
      processFlowResponse.areas.length === 0 &&
      processFlowResponse.edges.length === 0 &&
      !processFlowResponse.lastProcessedTime
    ) {
      throw IctError.noContent();
    }
    return processFlowResponse;
  }

  @Example(InventoryProcessFlowAreaController.exampleDetailResponse)
  /**
   * @returns {Promise<ProcessFlowDetailsResponse>} contract
   * @throws IctError
   */
  @SuccessResponse('200')
  @Get('/process-flow/details/{type}/{elementId}')
  @OperationId('GetInventoryProcessFlowGraphDetails')
  @Tags('inventory')
  public async getGraphDetails(
    @Path() type: string,
    @Path() elementId: string,
    @Query() view?: string,
  ): Promise<ProcessFlowDetailsResponse> {
    const processFlowService = Container.get(ProcessFlowService);
    return await processFlowService.getDetails(
      type,
      decodeURIComponent(elementId),
      view,
    );
  }

  /**
   * @returns {Promise<Area>} contract
   * @throws IctError
   */
  @SuccessResponse('200')
  @Put('/process-flow/areas/{areaId}')
  @OperationId('UpdateInventoryProcessFlowArea')
  @Tags('inventory')
  public async updatePosition(
    @Path() areaId: string,
    @Body() payload: UpdatePositionPayload,
    @Query() view?: string,
  ): Promise<UpdatedArea> {
    const processFlowService = Container.get(ProcessFlowService);
    return await processFlowService.updatePosition(payload, view || 'facility');
  }

  /**
   * @returns {Promise<Edge>} contract
   * @throws IctError
   */
  @SuccessResponse('200')
  @Put('/process-flow/edges/{edgeId}')
  @OperationId('UpdateInventoryProcessFlowEdge')
  @Tags('inventory')
  public async updateEdge(
    @Path() edgeId: string,
    @Body() edge: Edge,
  ): Promise<Edge> {
    const processFlowService = Container.get(ProcessFlowService);
    return await processFlowService.updateEdge(edge);
  }

  /**
   * @returns {Promise<Metric>} contract
   * @throws IctError
   */
  @SuccessResponse('200')
  @Put('/process-flow/metrics/{metricId}')
  @OperationId('UpdateInventoryProcessFlowMetric')
  @Tags('inventory')
  public async updateMetric(
    @Path() metricId: string,
    @Body() metric: Metric,
  ): Promise<Metric> {
    const processFlowService = Container.get(ProcessFlowService);
    return await processFlowService.updateMetric(metric);
  }

  /**
   * @returns {Promise<ClearCacheResponse>} contract
   * @throws IctError
   */
  @SuccessResponse('200')
  @Post('/process-flow/clear-cache/')
  @OperationId('ClearInventoryProcessFlowCache')
  @Tags('inventory')
  public async clearCache(
    @Body() clearCacheRequest: ClearCacheRequest,
  ): Promise<ClearCacheResponse> {
    const processFlowService = Container.get(ProcessFlowService);
    return await processFlowService.clearCache(clearCacheRequest);
  }
}
