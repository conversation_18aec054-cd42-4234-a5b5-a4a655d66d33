import {expect} from 'chai';
import {calculateMetricStatus} from '../../utils/metric-status-calculator.ts';
import {MetricStatus} from '@ict/sdk-foundations/types/index.ts';

describe('MetricStatusCalculator', () => {
  describe('calculateMetricStatus', () => {
    it('should return null for invalid threshold config', () => {
      const result = calculateMetricStatus(100, null);
      expect(result).to.be.null;
    });

    it('should return null for missing threshold type', () => {
      const thresholdConfig = {target_value: 100};
      const result = calculateMetricStatus(100, thresholdConfig);
      expect(result).to.be.null;
    });

    it('should return null for invalid numeric value', () => {
      const thresholdConfig = {
        threshold_type: 'numerical',
        target_value: 100,
        low_warning_range: 10,
        high_warning_range: 10,
      };
      const result = calculateMetricStatus('invalid', thresholdConfig);
      expect(result).to.be.null;
    });

    it('should return null for invalid target value', () => {
      const thresholdConfig = {
        threshold_type: 'numerical',
        target_value: 'invalid',
        low_warning_range: 10,
        high_warning_range: 10,
      };
      const result = calculateMetricStatus(100, thresholdConfig);
      expect(result).to.be.null;
    });
  });

  describe('numerical thresholds', () => {
    it('should return Ok when value is within warning range', () => {
      const thresholdConfig = {
        threshold_type: 'numerical',
        target_value: 100,
        low_warning_range: 10,
        high_warning_range: 10,
      };
      const result = calculateMetricStatus(105, thresholdConfig);
      expect(result).to.equal(MetricStatus.Ok);
    });

    it('should return Warning when value exceeds low warning range', () => {
      const thresholdConfig = {
        threshold_type: 'numerical',
        target_value: 100,
        low_warning_range: 10,
        high_warning_range: 10,
      };
      const result = calculateMetricStatus(85, thresholdConfig);
      expect(result).to.equal(MetricStatus.Warning);
    });

    it('should return Warning when value exceeds high warning range', () => {
      const thresholdConfig = {
        threshold_type: 'numerical',
        target_value: 100,
        low_warning_range: 10,
        high_warning_range: 10,
      };
      const result = calculateMetricStatus(115, thresholdConfig);
      expect(result).to.equal(MetricStatus.Warning);
    });

    it('should return Critical when value exceeds low critical range', () => {
      const thresholdConfig = {
        threshold_type: 'numerical',
        target_value: 100,
        low_warning_range: 10,
        high_warning_range: 10,
        low_critical_range: 20,
        high_critical_range: 20,
      };
      const result = calculateMetricStatus(75, thresholdConfig);
      expect(result).to.equal(MetricStatus.Critical);
    });

    it('should return Critical when value exceeds high critical range', () => {
      const thresholdConfig = {
        threshold_type: 'numerical',
        target_value: 100,
        low_warning_range: 10,
        high_warning_range: 10,
        low_critical_range: 20,
        high_critical_range: 20,
      };
      const result = calculateMetricStatus(125, thresholdConfig);
      expect(result).to.equal(MetricStatus.Critical);
    });

    it('should handle only low ranges', () => {
      const thresholdConfig = {
        threshold_type: 'numerical',
        target_value: 100,
        low_warning_range: 10,
        low_critical_range: 20,
      };
      const result = calculateMetricStatus(75, thresholdConfig);
      expect(result).to.equal(MetricStatus.Critical);
    });

    it('should handle only high ranges', () => {
      const thresholdConfig = {
        threshold_type: 'numerical',
        target_value: 100,
        high_warning_range: 10,
        high_critical_range: 20,
      };
      const result = calculateMetricStatus(125, thresholdConfig);
      expect(result).to.equal(MetricStatus.Critical);
    });
  });

  describe('percentage thresholds', () => {
    it('should return Ok when percentage difference is within warning range', () => {
      const thresholdConfig = {
        threshold_type: 'percentage',
        target_value: 100,
        low_warning_range: 15,
        high_warning_range: 15,
      };
      const result = calculateMetricStatus(110, thresholdConfig); // 10% above target
      expect(result).to.equal(MetricStatus.Ok);
    });

    it('should return Warning when percentage difference exceeds low warning range', () => {
      const thresholdConfig = {
        threshold_type: 'percentage',
        target_value: 100,
        low_warning_range: 15,
        high_warning_range: 15,
      };
      const result = calculateMetricStatus(80, thresholdConfig); // 20% below target
      expect(result).to.equal(MetricStatus.Warning);
    });

    it('should return Warning when percentage difference exceeds high warning range', () => {
      const thresholdConfig = {
        threshold_type: 'percentage',
        target_value: 100,
        low_warning_range: 15,
        high_warning_range: 15,
      };
      const result = calculateMetricStatus(120, thresholdConfig); // 20% above target
      expect(result).to.equal(MetricStatus.Warning);
    });

    it('should return Critical when percentage difference exceeds low critical range', () => {
      const thresholdConfig = {
        threshold_type: 'percentage',
        target_value: 100,
        low_warning_range: 15,
        high_warning_range: 15,
        low_critical_range: 30,
        high_critical_range: 30,
      };
      const result = calculateMetricStatus(60, thresholdConfig); // 40% below target
      expect(result).to.equal(MetricStatus.Critical);
    });

    it('should return Critical when percentage difference exceeds high critical range', () => {
      const thresholdConfig = {
        threshold_type: 'percentage',
        target_value: 100,
        low_warning_range: 15,
        high_warning_range: 15,
        low_critical_range: 30,
        high_critical_range: 30,
      };
      const result = calculateMetricStatus(140, thresholdConfig); // 40% above target
      expect(result).to.equal(MetricStatus.Critical);
    });

    it('should handle zero target value', () => {
      const thresholdConfig = {
        threshold_type: 'percentage',
        target_value: 0,
        low_warning_range: 15,
        high_warning_range: 15,
      };
      const result = calculateMetricStatus(10, thresholdConfig);
      expect(result).to.be.null;
    });

    it('should handle negative target values', () => {
      const thresholdConfig = {
        threshold_type: 'percentage',
        target_value: -100,
        low_warning_range: 15,
        high_warning_range: 15,
      };
      const result = calculateMetricStatus(-80, thresholdConfig); // 20% above target
      expect(result).to.equal(MetricStatus.Warning);
    });
  });

  describe('exact value thresholds', () => {
    it('should return Ok when value matches target exactly', () => {
      const thresholdConfig = {
        threshold_type: 'exact_value',
        target_value: 100,
      };
      const result = calculateMetricStatus(100, thresholdConfig);
      expect(result).to.equal(MetricStatus.Ok);
    });

    it('should return Critical when value does not match target', () => {
      const thresholdConfig = {
        threshold_type: 'exact_value',
        target_value: 100,
      };
      const result = calculateMetricStatus(99, thresholdConfig);
      expect(result).to.equal(MetricStatus.Critical);
    });

    it('should handle string values', () => {
      const thresholdConfig = {
        threshold_type: 'exact_value',
        target_value: '100',
      };
      const result = calculateMetricStatus('100', thresholdConfig);
      expect(result).to.equal(MetricStatus.Ok);
    });
  });

  describe('edge cases', () => {
    it('should handle undefined ranges gracefully', () => {
      const thresholdConfig = {
        threshold_type: 'numerical',
        target_value: 100,
        // No ranges defined
      };
      const result = calculateMetricStatus(1000, thresholdConfig);
      expect(result).to.equal(MetricStatus.Ok);
    });

    it('should handle string numeric values', () => {
      const thresholdConfig = {
        threshold_type: 'numerical',
        target_value: '100',
        low_warning_range: 10,
        high_warning_range: 10,
      };
      const result = calculateMetricStatus('105', thresholdConfig);
      expect(result).to.equal(MetricStatus.Ok);
    });

    it('should handle mixed string and number values', () => {
      const thresholdConfig = {
        threshold_type: 'numerical',
        target_value: 100,
        low_warning_range: '10',
        high_warning_range: 10,
      };
      const result = calculateMetricStatus('85', thresholdConfig);
      expect(result).to.equal(MetricStatus.Warning);
    });
  });
});
