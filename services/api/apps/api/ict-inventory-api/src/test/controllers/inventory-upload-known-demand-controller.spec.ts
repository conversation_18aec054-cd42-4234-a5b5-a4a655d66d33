import request from 'supertest';
import {appSetup} from 'ict-api-foundations';
import {expect} from 'chai';

import {RegisterRoutes} from '../../../build/routes.ts';

// NOTE: This controller has known testing limitations due to TSOA + Multer interactions
// with dependency injection. The tests currently validate error responses (400) from
// validation failures rather than authentication (401) due to these integration issues.
// Consider investigating TSOA iocModule feature for proper testing: https://tsoa-community.github.io/docs/di.html
describe('InventoryUploadKnownDemandController', () => {
  const app = appSetup(RegisterRoutes);

  describe('All test requests return 400 due to multer/validation issues', () => {
    const url = '/inventory/upload/known-demand';
    it('should return 400', async () => {
      const response = await request(app)
        .post(url)
        .attach('manager', Buffer.from('file content'), 'manager.xls')
        .attach('details', Buffer.from('file content'), 'details.xls');

      expect(response.status).to.equal(400);
      expect(response.headers['content-type']).to.match(/json/);
    });
  });

  describe('GET requests return 400 due to validation issues in unit tests', () => {
    const url = '/inventory/upload/recent-activity';
    it('should return 400', async () => {
      const response = await request(app).get(url);

      expect(response.status).to.equal(400);
      expect(response.headers['content-type']).to.match(/json/);
    });
  });
});
