import {MetricStatus} from '@ict/sdk-foundations/types/index.ts';

/**
 * Utility functions for calculating metric status based on threshold configurations
 */

/**
 * Calculate status based on threshold configuration
 */
export function calculateMetricStatus(
  value: number | string,
  thresholdConfig: any,
): MetricStatus | null {
  if (!thresholdConfig || !thresholdConfig.threshold_type) {
    return null;
  }

  const numValue = typeof value === 'string' ? parseFloat(value) : value;
  if (isNaN(numValue)) {
    return null;
  }

  const targetValue =
    typeof thresholdConfig.target_value === 'string'
      ? parseFloat(thresholdConfig.target_value)
      : thresholdConfig.target_value;

  if (targetValue === null || targetValue === undefined || isNaN(targetValue)) {
    return null;
  }

  const thresholdType = thresholdConfig.threshold_type;

  if (thresholdType === 'numerical') {
    return calculateNumericalStatus(numValue, targetValue, thresholdConfig);
  } else if (thresholdType === 'percentage') {
    return calculatePercentageStatus(numValue, targetValue, thresholdConfig);
  } else if (thresholdType === 'exact_value') {
    return numValue === targetValue ? MetricStatus.Ok : MetricStatus.Critical;
  }

  return null;
}

/**
 * Calculate status for numerical thresholds
 * Compares absolute difference from target value
 */
function calculateNumericalStatus(
  value: number,
  targetValue: number,
  config: any,
): MetricStatus | null {
  const diff = value - targetValue;

  // Check critical ranges first (outer boundary)
  if (
    config.low_critical_range !== undefined &&
    diff < -config.low_critical_range
  ) {
    return MetricStatus.Critical;
  }
  if (
    config.high_critical_range !== undefined &&
    diff > config.high_critical_range
  ) {
    return MetricStatus.Critical;
  }

  // Check warning ranges (middle boundary)
  if (
    config.low_warning_range !== undefined &&
    diff < -config.low_warning_range
  ) {
    return MetricStatus.Warning;
  }
  if (
    config.high_warning_range !== undefined &&
    diff > config.high_warning_range
  ) {
    return MetricStatus.Warning;
  }

  // Within acceptable range
  return MetricStatus.Ok;
}

/**
 * Calculate status for percentage thresholds
 * Compares percentage difference from target value
 */
function calculatePercentageStatus(
  value: number,
  targetValue: number,
  config: any,
): MetricStatus | null {
  if (targetValue === 0) {
    return null;
  }

  const percentDiff = ((value - targetValue) / Math.abs(targetValue)) * 100;

  // Check critical ranges first (outer boundary)
  if (
    config.low_critical_range !== undefined &&
    percentDiff < -config.low_critical_range
  ) {
    return MetricStatus.Critical;
  }
  if (
    config.high_critical_range !== undefined &&
    percentDiff > config.high_critical_range
  ) {
    return MetricStatus.Critical;
  }

  // Check warning ranges (middle boundary)
  if (
    config.low_warning_range !== undefined &&
    percentDiff < -config.low_warning_range
  ) {
    return MetricStatus.Warning;
  }
  if (
    config.high_warning_range !== undefined &&
    percentDiff > config.high_warning_range
  ) {
    return MetricStatus.Warning;
  }

  // Within acceptable range
  return MetricStatus.Ok;
}
