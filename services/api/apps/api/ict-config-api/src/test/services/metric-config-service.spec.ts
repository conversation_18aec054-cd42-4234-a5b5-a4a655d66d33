import sinon from 'sinon';
import {expect} from 'chai';
import {
  ContextService,
  WinstonLogger,
  ConfigStore,
  RedisClient,
} from 'ict-api-foundations';
import {
  DefaultMetricConfigurationEntity,
  CustomMetricConfigurationEntity,
} from 'ict-api-schema';
import {MetricConfigService} from '../../services/metric-config-service.ts';
import {
  MetricConfigStore,
  MetricConfigSummaryFilters,
  MetricConfigFact,
} from 'ict-api-foundations';
import type {MetricConfigDetail} from 'ict-api-foundations';

describe('MetricConfigService', () => {
  let service: MetricConfigService;
  let metricConfigStore: MetricConfigStore;
  let context: ContextService;
  let logger: WinstonLogger;
  let configStore: ConfigStore;
  let redisClient: sinon.SinonStubbedInstance<RedisClient>;

  beforeEach(() => {
    context = new ContextService();
    logger = new WinstonLogger(context);
    configStore = new ConfigStore(context);
    redisClient = sinon.createStubInstance(RedisClient);
    metricConfigStore = new MetricConfigStore(context, logger);
    service = new MetricConfigService(
      configStore,
      logger,
      context,
      metricConfigStore,
      redisClient,
    );

    // Mock facility maps with facilityId property
    sinon.stub(context, 'facilityMaps').get(() => [
      {
        id: 'test-facility-map',
        name: 'Test Facility',
        dataset: 'test-dataset',
        facilityId: 'test-facility-id',
        tenantId: 'test-tenant-id',
        default: true,
      },
    ]);

    // Mock store's getMetricConfigs method
    sinon.stub(metricConfigStore, 'getMetricConfigSummaries').resolves([]);
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('getMetricConfigSummaries', () => {
    it('should return both default and custom metric configurations', async () => {
      const mockDefaultConfig = new DefaultMetricConfigurationEntity();
      mockDefaultConfig.id = '1';
      mockDefaultConfig.metricConfigName = 'test-metric';
      mockDefaultConfig.configType = 'node';
      mockDefaultConfig.nodeName = 'test-node';
      mockDefaultConfig.factType = 'test-fact';
      mockDefaultConfig.enabled = {default: true};
      mockDefaultConfig.active = {default: true};
      mockDefaultConfig.views = ['facility'];

      const mockCustomConfig = new CustomMetricConfigurationEntity();
      mockCustomConfig.id = '2';
      mockCustomConfig.metricConfigName = 'test-metric';
      mockCustomConfig.configType = 'node';
      mockCustomConfig.nodeName = 'custom-node';
      mockCustomConfig.factType = 'custom-fact';
      mockCustomConfig.active = true;
      mockCustomConfig.facilityId = 'test-facility';
      mockCustomConfig.views = ['facility'];

      const mockConfigs = [mockDefaultConfig, mockCustomConfig];

      (metricConfigStore.getMetricConfigSummaries as sinon.SinonStub).resolves(
        mockConfigs,
      );
      sinon.stub(context, 'selectedFacilityId').get(() => 'test-facility-map');

      const filters: MetricConfigSummaryFilters = {
        metricName: 'test-metric',
        configType: 'node',
      };

      const result = await service.getMetricConfigSummaries(filters);

      expect(result).to.deep.equal(
        mockConfigs.map(config => ({
          id: config.id,
          metricName: config.metricConfigName,
          configType: config.configType,
          nodeName: config.nodeName,
          factType: config.factType,
          enabled:
            config instanceof DefaultMetricConfigurationEntity
              ? config.enabled
              : null,
          active:
            config instanceof CustomMetricConfigurationEntity
              ? config.active
              : config.active,
          isCustom: config instanceof CustomMetricConfigurationEntity,
          facilityId:
            config instanceof CustomMetricConfigurationEntity
              ? config.facilityId
              : 'default',
          views: config.views,
        })),
      );
    });

    it('should throw not found error when no configurations are found', async () => {
      // Mock store to return empty array
      (metricConfigStore.getMetricConfigSummaries as sinon.SinonStub).resolves(
        [],
      );

      const filters: MetricConfigSummaryFilters = {
        metricName: 'nonexistent-metric',
        configType: 'node',
      };

      await expect(
        service.getMetricConfigSummaries(filters),
      ).to.be.rejectedWith(
        'No metric configurations found matching the criteria',
      );
    });

    it('should filter out default configs that have custom versions', async () => {
      const mockDefaultConfig = new DefaultMetricConfigurationEntity();
      mockDefaultConfig.id = '1';
      mockDefaultConfig.metricConfigName = 'test-metric';
      mockDefaultConfig.configType = 'node';
      mockDefaultConfig.nodeName = 'test-node';
      mockDefaultConfig.factType = 'test-fact';
      mockDefaultConfig.enabled = {default: true};
      mockDefaultConfig.active = {default: true};
      mockDefaultConfig.views = ['facility'];

      const mockCustomConfig = new CustomMetricConfigurationEntity();
      mockCustomConfig.id = '2';
      mockCustomConfig.metricConfigName = 'test-metric';
      mockCustomConfig.configType = 'node';
      mockCustomConfig.nodeName = 'custom-node';
      mockCustomConfig.factType = 'custom-fact';
      mockCustomConfig.active = true;
      mockCustomConfig.facilityId = 'test-facility';
      mockCustomConfig.views = ['facility'];

      (metricConfigStore.getMetricConfigSummaries as sinon.SinonStub).resolves([
        mockCustomConfig,
      ]);
      sinon.stub(context, 'selectedFacilityId').get(() => 'test-facility-map');

      const filters: MetricConfigSummaryFilters = {
        metricName: 'test-metric',
        configType: 'node',
      };

      const result = await service.getMetricConfigSummaries(filters);

      expect(result).to.deep.equal([
        {
          id: mockCustomConfig.id,
          metricName: mockCustomConfig.metricConfigName,
          configType: mockCustomConfig.configType,
          nodeName: mockCustomConfig.nodeName,
          factType: mockCustomConfig.factType,
          enabled: null,
          active: mockCustomConfig.active,
          isCustom: true,
          facilityId: mockCustomConfig.facilityId,
          views: mockCustomConfig.views,
        },
      ]);
    });

    it('should filter out disabled configs', async () => {
      const mockDefaultConfig = new DefaultMetricConfigurationEntity();
      mockDefaultConfig.id = '1';
      mockDefaultConfig.metricConfigName = 'test-metric';
      mockDefaultConfig.configType = 'node';
      mockDefaultConfig.nodeName = 'test-node';
      mockDefaultConfig.factType = 'test-fact';
      mockDefaultConfig.enabled = {default: false};
      mockDefaultConfig.active = {default: true};

      const mockCustomConfig = new CustomMetricConfigurationEntity();
      mockCustomConfig.id = '2';
      mockCustomConfig.metricConfigName = 'test-metric';
      mockCustomConfig.configType = 'node';
      mockCustomConfig.nodeName = 'custom-node';
      mockCustomConfig.factType = 'custom-fact';
      mockCustomConfig.active = true;
      mockCustomConfig.facilityId = 'test-facility';

      (metricConfigStore.getMetricConfigSummaries as sinon.SinonStub).resolves(
        [],
      );
      sinon.stub(context, 'selectedFacilityId').get(() => 'test-facility-map');

      const filters: MetricConfigSummaryFilters = {
        metricName: 'test-metric',
        configType: 'node',
      };

      await expect(
        service.getMetricConfigSummaries(filters),
      ).to.be.rejectedWith(
        'No metric configurations found matching the criteria',
      );
    });

    it('should handle store errors', async () => {
      (metricConfigStore.getMetricConfigSummaries as sinon.SinonStub).rejects(
        new Error('Store error'),
      );
      sinon.stub(context, 'selectedFacilityId').get(() => 'test-facility-map');

      const filters: MetricConfigSummaryFilters = {
        metricName: 'test-metric',
        configType: 'node',
      };

      await expect(
        service.getMetricConfigSummaries(filters),
      ).to.be.rejectedWith('Store error');
    });
  });

  describe('updateOrCreateMetricConfig', () => {
    it('should create a new metric configuration when using selected facility', async () => {
      const input: MetricConfigDetail = {
        metricConfigName: 'test-metric',
        configType: 'node',
        views: ['facility'],
        matchConditions: {eventType: 'test'},
        factType: 'test-fact',
        sourceSystem: 'diq',
        displayName: 'Test Metric',
        enabled: true,
        active: true,
        isCustom: true,
        nodeName: 'test-node',
        metricType: 'testType',
        timeWindow: '60m_set',
        aggregation: 'sum',
        redisOperation: 'event_set',
        graphOperation: 'area_node',
      };

      const mockResult = {
        config: new CustomMetricConfigurationEntity(),
        isNew: true,
      };
      Object.assign(mockResult.config, {
        id: 'new-config-id',
        ...input,
      });

      sinon.stub(context, 'selectedFacilityId').get(() => 'test-facility-map');
      sinon.stub(metricConfigStore, 'putMetricConfig').resolves(mockResult);
      sinon.stub(service, 'getMetricConfigDetail').resolves({
        default: undefined,
        custom: undefined,
      });

      const result = await service.updateOrCreateMetricConfig(input);

      expect(result).to.deep.equal(mockResult);
      expect(result.isNew).to.be.true;
      expect(result.config.id).to.equal('new-config-id');
    });

    it('should throw error when no facility ID is provided', async () => {
      const input: MetricConfigDetail = {
        metricConfigName: 'test-metric',
        configType: 'node',
        views: ['facility'],
        matchConditions: {eventType: 'test'},
        factType: 'test-fact',
        sourceSystem: 'diq',
        displayName: 'Test Metric',
        enabled: true,
        active: true,
        isCustom: true,
        nodeName: 'test-node',
        metricType: 'testType',
        timeWindow: '60m_set',
        aggregation: 'sum',
        redisOperation: 'event_set',
        graphOperation: 'area_node',
      };

      sinon.stub(context, 'selectedFacilityId').get(() => undefined);
      sinon.stub(context, 'facilityMaps').get(() => []);

      await expect(
        service.updateOrCreateMetricConfig(input),
      ).to.be.rejectedWith(
        'No facility ID provided. Please ensure the ict-facility-id header is set.',
      );
    });

    it('should throw error when no facility map is configured', async () => {
      const input: MetricConfigDetail = {
        metricConfigName: 'test-metric',
        configType: 'node',
        views: ['facility'],
        matchConditions: {eventType: 'test'},
        factType: 'test-fact',
        sourceSystem: 'diq',
        displayName: 'Test Metric',
        enabled: true,
        active: true,
        isCustom: true,
        nodeName: 'test-node',
        metricType: 'testType',
        timeWindow: '60m_set',
        aggregation: 'sum',
        redisOperation: 'event_set',
        graphOperation: 'area_node',
      };

      sinon.stub(context, 'selectedFacilityId').get(() => 'test-facility-map');
      sinon.stub(context, 'facilityMaps').get(() => undefined);

      await expect(
        service.updateOrCreateMetricConfig(input),
      ).to.be.rejectedWith(
        'No facility map found for selected facility test-facility-map',
      );
    });

    it('should update an existing metric configuration', async () => {
      const input: MetricConfigDetail = {
        metricConfigName: 'existing-metric',
        configType: 'node',
        views: ['facility'],
        matchConditions: {eventType: 'test'},
        factType: 'test-fact',
        sourceSystem: 'diq',
        displayName: 'Existing Metric',
        enabled: true,
        active: true,
        isCustom: true,
        nodeName: 'test-node',
        metricType: 'testType',
        timeWindow: '60m_set',
        aggregation: 'sum',
        redisOperation: 'event_set',
        graphOperation: 'area_node',
      };

      const mockResult = {
        config: new CustomMetricConfigurationEntity(),
        isNew: false,
      };
      Object.assign(mockResult.config, {
        id: 'existing-config-id',
        ...input,
      });

      sinon.stub(context, 'selectedFacilityId').get(() => 'test-facility-map');
      sinon.stub(metricConfigStore, 'putMetricConfig').resolves(mockResult);
      sinon.stub(service, 'getMetricConfigDetail').resolves({
        default: undefined,
        custom: undefined,
      });

      const result = await service.updateOrCreateMetricConfig(input);

      expect(result).to.deep.equal(mockResult);
      expect(result.isNew).to.be.false;
      expect(result.config.id).to.equal('existing-config-id');
    });

    it('should handle store errors', async () => {
      const input: MetricConfigDetail = {
        metricConfigName: 'test-metric',
        configType: 'node',
        views: ['facility'],
        matchConditions: {eventType: 'test'},
        factType: 'test-fact',
        sourceSystem: 'diq',
        displayName: 'Test Metric',
        enabled: true,
        active: true,
        isCustom: true,
        nodeName: 'test-node',
        metricType: 'testType',
        timeWindow: '60m_set',
        aggregation: 'sum',
        redisOperation: 'event_set',
        graphOperation: 'area_node',
      };

      sinon.stub(context, 'selectedFacilityId').get(() => 'test-facility-map');
      sinon
        .stub(metricConfigStore, 'putMetricConfig')
        .rejects(new Error('Database connection failed'));
      sinon.stub(service, 'getMetricConfigDetail').resolves({
        default: undefined,
        custom: undefined,
      });

      await expect(
        service.updateOrCreateMetricConfig(input),
      ).to.be.rejectedWith('Database connection failed');
    });

    describe('key deletion functionality', () => {
      const baseInput: MetricConfigDetail = {
        metricConfigName: 'test-metric',
        configType: 'node',
        views: ['facility'],
        matchConditions: {eventType: 'test'},
        factType: 'test-fact',
        sourceSystem: 'diq',
        displayName: 'Test Metric',
        enabled: true,
        active: true,
        isCustom: true,
        nodeName: 'test-node',
        metricType: 'testType',
        timeWindow: '60m_set',
        aggregation: 'sum',
        redisOperation: 'event_set',
        graphOperation: 'area_node',
      };

      const mockResult = {
        config: new CustomMetricConfigurationEntity(),
        isNew: false,
      };

      beforeEach(() => {
        Object.assign(mockResult.config, {
          id: 'updated-config-id',
          ...baseInput,
        });
        sinon
          .stub(context, 'selectedFacilityId')
          .get(() => 'test-facility-map');
        sinon.stub(metricConfigStore, 'putMetricConfig').resolves(mockResult);
      });

      it('should delete keys when updating an enabled custom config', async () => {
        const oldCustomConfig: MetricConfigDetail = {
          metricConfigName: 'test-metric',
          configType: 'node',
          views: ['facility'],
          matchConditions: {eventType: 'test'},
          factType: 'test-fact',
          sourceSystem: 'diq',
          displayName: 'Test Metric',
          enabled: true,
          active: true,
          isCustom: true,
          nodeName: 'test-node',
          metricType: 'testType',
          timeWindow: '60m_set',
          aggregation: 'sum',
          redisOperation: 'event_set',
          graphOperation: 'area_node',
        };

        // Mock the service's getMetricConfigDetail method to return the old config
        sinon.stub(service, 'getMetricConfigDetail').resolves({
          default: undefined,
          custom: oldCustomConfig,
        });

        const deleteKeysStub = sinon
          .stub(service, 'deleteMetricConfigKeys')
          .resolves(5);

        const result = await service.updateOrCreateMetricConfig(baseInput);

        expect(deleteKeysStub.calledOnce).to.be.true;
        expect(
          deleteKeysStub.calledWith(
            oldCustomConfig,
            'test-tenant-id',
            'test-facility-id',
          ),
        ).to.be.true;
        expect(result).to.deep.equal(mockResult);
      });

      it('should delete keys when updating an enabled default config', async () => {
        const oldDefaultConfig: MetricConfigDetail = {
          metricConfigName: 'test-metric',
          configType: 'node',
          views: ['facility'],
          matchConditions: {eventType: 'test'},
          factType: 'test-fact',
          sourceSystem: 'diq',
          displayName: 'Test Metric',
          enabled: true,
          active: true,
          isCustom: false,
          nodeName: 'test-node',
          metricType: 'testType',
          timeWindow: '60m_set',
          aggregation: 'sum',
          redisOperation: 'event_set',
          graphOperation: 'area_node',
        };

        // Mock the service's getMetricConfigDetail method to return the old config
        sinon.stub(service, 'getMetricConfigDetail').resolves({
          default: oldDefaultConfig,
          custom: undefined,
        });

        const deleteKeysStub = sinon
          .stub(service, 'deleteMetricConfigKeys')
          .resolves(3);

        const result = await service.updateOrCreateMetricConfig(baseInput);

        expect(deleteKeysStub.calledOnce).to.be.true;
        expect(
          deleteKeysStub.calledWith(
            oldDefaultConfig,
            'test-tenant-id',
            'test-facility-id',
          ),
        ).to.be.true;
        expect(result).to.deep.equal(mockResult);
      });

      it('should NOT delete keys when old custom config is disabled', async () => {
        const oldCustomConfig: MetricConfigDetail = {
          metricConfigName: 'test-metric',
          configType: 'node',
          views: ['facility'],
          matchConditions: {eventType: 'test'},
          factType: 'test-fact',
          sourceSystem: 'diq',
          displayName: 'Test Metric',
          enabled: false,
          active: true,
          isCustom: true,
          nodeName: 'test-node',
          metricType: 'testType',
          timeWindow: '60m_set',
          aggregation: 'sum',
          redisOperation: 'event_set',
          graphOperation: 'area_node',
        };

        sinon.stub(service, 'getMetricConfigDetail').resolves({
          default: undefined,
          custom: oldCustomConfig,
        });

        const deleteKeysStub = sinon
          .stub(service, 'deleteMetricConfigKeys')
          .resolves(0);

        const result = await service.updateOrCreateMetricConfig(baseInput);

        expect(deleteKeysStub.called).to.be.false;
        expect(result).to.deep.equal(mockResult);
      });

      it('should NOT delete keys when old default config is disabled', async () => {
        const oldDefaultConfig: MetricConfigDetail = {
          metricConfigName: 'test-metric',
          configType: 'node',
          views: ['facility'],
          matchConditions: {eventType: 'test'},
          factType: 'test-fact',
          sourceSystem: 'diq',
          displayName: 'Test Metric',
          enabled: false,
          active: true,
          isCustom: false,
          nodeName: 'test-node',
          metricType: 'testType',
          timeWindow: '60m_set',
          aggregation: 'sum',
          redisOperation: 'event_set',
          graphOperation: 'area_node',
        };

        sinon.stub(service, 'getMetricConfigDetail').resolves({
          default: oldDefaultConfig,
          custom: undefined,
        });

        const deleteKeysStub = sinon
          .stub(service, 'deleteMetricConfigKeys')
          .resolves(0);

        const result = await service.updateOrCreateMetricConfig(baseInput);

        expect(deleteKeysStub.called).to.be.false;
        expect(result).to.deep.equal(mockResult);
      });

      it('should NOT delete keys when no old config exists', async () => {
        sinon.stub(service, 'getMetricConfigDetail').resolves({
          default: undefined,
          custom: undefined,
        });

        const deleteKeysStub = sinon
          .stub(service, 'deleteMetricConfigKeys')
          .resolves(0);

        const result = await service.updateOrCreateMetricConfig(baseInput);

        expect(deleteKeysStub.called).to.be.false;
        expect(result).to.deep.equal(mockResult);
      });

      it('should prioritize custom config over default config for deletion', async () => {
        const oldCustomConfig: MetricConfigDetail = {
          metricConfigName: 'test-metric',
          configType: 'node',
          views: ['facility'],
          matchConditions: {eventType: 'test'},
          factType: 'test-fact',
          sourceSystem: 'diq',
          displayName: 'Test Metric',
          enabled: true,
          active: true,
          isCustom: true,
          nodeName: 'test-node',
          metricType: 'testType',
          timeWindow: '60m_set',
          aggregation: 'sum',
          redisOperation: 'event_set',
          graphOperation: 'area_node',
        };

        const oldDefaultConfig: MetricConfigDetail = {
          metricConfigName: 'test-metric',
          configType: 'node',
          views: ['facility'],
          matchConditions: {eventType: 'test'},
          factType: 'test-fact',
          sourceSystem: 'diq',
          displayName: 'Test Metric',
          enabled: true,
          active: true,
          isCustom: false,
          nodeName: 'test-node',
          metricType: 'testType',
          timeWindow: '60m_set',
          aggregation: 'sum',
          redisOperation: 'event_set',
          graphOperation: 'area_node',
        };

        sinon.stub(service, 'getMetricConfigDetail').resolves({
          default: oldDefaultConfig,
          custom: oldCustomConfig,
        });

        const deleteKeysStub = sinon
          .stub(service, 'deleteMetricConfigKeys')
          .resolves(2);

        const result = await service.updateOrCreateMetricConfig(baseInput);

        // Should only call delete once for custom config, not default
        expect(deleteKeysStub.calledOnce).to.be.true;
        expect(
          deleteKeysStub.calledWith(
            sinon.match.any,
            'test-tenant-id',
            'test-facility-id',
          ),
        ).to.be.true;
        expect(result).to.deep.equal(mockResult);
      });

      it('should handle different config types for deletion', async () => {
        const edgeInput: MetricConfigDetail = {
          ...baseInput,
          configType: 'inbound-edge',
          inboundArea: 'receiving',
          huId: 'handling_unit_code',
          redisOperation: 'event_set',
          metricUnits: 'units/hr',
          inboundParentNodes: ['warehouse'],
        };

        const oldCustomConfig: MetricConfigDetail = {
          metricConfigName: 'test-metric',
          configType: 'inbound-edge',
          views: ['facility'],
          matchConditions: {eventType: 'test'},
          factType: 'test-fact',
          sourceSystem: 'diq',
          displayName: 'Test Metric',
          enabled: true,
          active: true,
          isCustom: true,
          inboundArea: 'receiving',
          huId: 'handling_unit_code',
          redisOperation: 'event_set',
          graphOperation: 'area_edge',
          metricUnits: 'units/hr',
          inboundParentNodes: ['warehouse'],
        };

        sinon.stub(service, 'getMetricConfigDetail').resolves({
          default: undefined,
          custom: oldCustomConfig,
        });

        const deleteKeysStub = sinon
          .stub(service, 'deleteMetricConfigKeys')
          .resolves(1);

        mockResult.config = new CustomMetricConfigurationEntity();
        Object.assign(mockResult.config, {
          id: 'updated-edge-config-id',
          ...edgeInput,
        });

        const result = await service.updateOrCreateMetricConfig(edgeInput);

        expect(deleteKeysStub.calledOnce).to.be.true;
        expect(
          deleteKeysStub.calledWith(
            sinon.match.any,
            'test-tenant-id',
            'test-facility-id',
          ),
        ).to.be.true;
        expect(result).to.deep.equal(mockResult);
      });

      it('should log the number of deleted keys', async () => {
        const oldCustomConfig: MetricConfigDetail = {
          metricConfigName: 'test-metric',
          configType: 'node',
          views: ['facility'],
          matchConditions: {eventType: 'test'},
          factType: 'test-fact',
          sourceSystem: 'diq',
          displayName: 'Test Metric',
          enabled: true,
          active: true,
          isCustom: true,
          nodeName: 'test-node',
          metricType: 'testType',
          timeWindow: '60m_set',
          aggregation: 'sum',
          redisOperation: 'event_set',
          graphOperation: 'area_node',
        };

        sinon.stub(service, 'getMetricConfigDetail').resolves({
          default: undefined,
          custom: oldCustomConfig,
        });

        sinon.stub(service, 'deleteMetricConfigKeys').resolves(7);

        const loggerInfoStub = sinon.stub(logger, 'info');

        await service.updateOrCreateMetricConfig(baseInput);

        expect(
          loggerInfoStub.calledWith(
            'Deleted 7 keys associated with the previous custom metric config for test-metric',
          ),
        ).to.be.true;
      });
    });
  });

  describe('getMetricConfigDetail', () => {
    beforeEach(() => {
      // Mock facility maps with facilityId
      sinon.stub(context, 'facilityMaps').get(() => [
        {
          id: 'test-facility-map',
          name: 'Test Facility',
          dataset: 'test-dataset',
          facilityId: 'test-facility-id',
          tenantId: 'test-tenant-id',
          default: true,
        },
      ]);
    });

    it('should return both default and custom configurations', async () => {
      const metricName = 'test-metric';

      sinon.stub(context, 'selectedFacilityId').get(() => 'test-facility-map');

      const mockDefaultConfig = new DefaultMetricConfigurationEntity();
      Object.assign(mockDefaultConfig, {
        id: 'default-1',
        metricConfigName: metricName,
        configType: 'node',
        factType: 'test-fact',
        enabled: {default: true},
        active: {default: true},
      });

      const mockCustomConfig = new CustomMetricConfigurationEntity();
      Object.assign(mockCustomConfig, {
        id: 'custom-1',
        metricConfigName: metricName,
        configType: 'node',
        factType: 'test-fact',
        facilityId: 'test-facility-id',
        enabled: true,
        active: true,
      });

      sinon.stub(metricConfigStore, 'getMetricConfigDetail').resolves({
        default: mockDefaultConfig,
        custom: mockCustomConfig,
      });

      const result = await service.getMetricConfigDetail(metricName);

      expect(result.default).to.exist;
      expect(result.custom).to.exist;
      expect(result.default?.metricConfigName).to.equal(metricName);
      expect(result.custom?.metricConfigName).to.equal(metricName);
    });

    it('should return only default configuration when configType is "default"', async () => {
      const metricName = 'test-metric';

      sinon.stub(context, 'selectedFacilityId').get(() => 'test-facility-map');

      const mockDefaultConfig = new DefaultMetricConfigurationEntity();
      Object.assign(mockDefaultConfig, {
        id: 'default-1',
        metricConfigName: metricName,
        configType: 'node',
        factType: 'test-fact',
        enabled: {default: true},
        active: {default: true},
      });

      sinon.stub(metricConfigStore, 'getMetricConfigDetail').resolves({
        default: mockDefaultConfig,
        custom: undefined,
      });

      const result = await service.getMetricConfigDetail(metricName, 'default');

      expect(result.default).to.exist;
      expect(result.custom).to.be.undefined;
      expect(result.default?.metricConfigName).to.equal(metricName);
    });

    it('should return only custom configuration when configType is "custom"', async () => {
      const metricName = 'test-metric';

      sinon.stub(context, 'selectedFacilityId').get(() => 'test-facility-map');

      const mockCustomConfig = new CustomMetricConfigurationEntity();
      Object.assign(mockCustomConfig, {
        id: 'custom-1',
        metricConfigName: metricName,
        configType: 'node',
        factType: 'test-fact',
        facilityId: 'test-facility-id',
        enabled: true,
        active: true,
      });

      sinon.stub(metricConfigStore, 'getMetricConfigDetail').resolves({
        default: undefined,
        custom: mockCustomConfig,
      });

      const result = await service.getMetricConfigDetail(metricName, 'custom');

      expect(result.default).to.be.undefined;
      expect(result.custom).to.exist;
      expect(result.custom?.metricConfigName).to.equal(metricName);
    });

    it('should throw error when no facility map is found', async () => {
      const metricName = 'test-metric';

      sinon
        .stub(context, 'selectedFacilityId')
        .get(() => 'nonexistent-facility');

      await expect(
        service.getMetricConfigDetail(metricName),
      ).to.be.rejectedWith(
        'No facility map found for selected facility nonexistent-facility',
      );
    });

    it('should throw error when facility map has no facilityId', async () => {
      const metricName = 'test-metric';

      sinon.stub(context, 'selectedFacilityId').get(() => 'test-facility-map');
      sinon.stub(context, 'facilityMaps').get(() => [
        {
          id: 'test-facility-map',
          name: 'Test Facility',
          dataset: 'test-dataset',
          facilityId: undefined, // No facilityId
          tenantId: 'test-tenant-id',
          default: true,
        },
      ]);

      await expect(
        service.getMetricConfigDetail(metricName),
      ).to.be.rejectedWith(
        'No facility ID provided. Please ensure the ict-facility-id header is set and a facility map is configured.',
      );
    });

    it('should handle store errors', async () => {
      const metricName = 'test-metric';

      sinon.stub(context, 'selectedFacilityId').get(() => 'test-facility-map');
      sinon
        .stub(metricConfigStore, 'getMetricConfigDetail')
        .rejects(new Error('Database connection failed'));

      await expect(
        service.getMetricConfigDetail(metricName),
      ).to.be.rejectedWith('Database connection failed');
    });
  });

  describe('getMetricConfigFacts', () => {
    it('should return metric configuration facts for the selected facility', async () => {
      const mockFacts: MetricConfigFact[] = [
        {
          factType: 'inventory',
          totalConfigs: 5,
          enabledConfigs: 3,
          active: true,
        },
        {
          factType: 'shipping',
          totalConfigs: 2,
          enabledConfigs: 1,
          active: false,
        },
      ];

      sinon.stub(context, 'selectedFacilityId').get(() => 'test-facility-map');

      const getMetricConfigFactsStub = sinon
        .stub(metricConfigStore, 'getMetricConfigFacts')
        .resolves(mockFacts);

      const result = await service.getMetricConfigFacts();

      expect(result).to.deep.equal(mockFacts);
      sinon.assert.calledOnce(getMetricConfigFactsStub);
      sinon.assert.calledWith(getMetricConfigFactsStub, 'test-facility-id');
    });

    it('should return empty array when no facts are found', async () => {
      const mockFacts: MetricConfigFact[] = [];

      sinon.stub(context, 'selectedFacilityId').get(() => 'test-facility-map');

      const getMetricConfigFactsStub = sinon
        .stub(metricConfigStore, 'getMetricConfigFacts')
        .resolves(mockFacts);

      const result = await service.getMetricConfigFacts();

      expect(result).to.be.an('array').that.is.empty;
      sinon.assert.calledOnce(getMetricConfigFactsStub);
    });

    it('should throw error when no facility ID is provided', async () => {
      sinon.stub(context, 'selectedFacilityId').get(() => undefined);
      sinon.stub(context, 'facilityMaps').get(() => []);

      await expect(service.getMetricConfigFacts()).to.be.rejectedWith(
        'No facility ID provided. Please ensure the ict-facility-id header is set.',
      );
    });

    it('should throw error when no facility map is found', async () => {
      sinon
        .stub(context, 'selectedFacilityId')
        .get(() => 'nonexistent-facility');

      await expect(service.getMetricConfigFacts()).to.be.rejectedWith(
        'No facility map found for selected facility nonexistent-facility',
      );
    });

    it('should handle database errors gracefully', async () => {
      sinon.stub(context, 'selectedFacilityId').get(() => 'test-facility-map');

      sinon
        .stub(metricConfigStore, 'getMetricConfigFacts')
        .rejects(new Error('Database connection failed'));

      await expect(service.getMetricConfigFacts()).to.be.rejectedWith(
        'Database connection failed',
      );
    });

    it('should use facility ID from facility maps when selectedFacilityId is set', async () => {
      const mockFacts: MetricConfigFact[] = [
        {
          factType: 'fault_event',
          totalConfigs: 3,
          enabledConfigs: 2,
          active: true,
        },
      ];

      // Set selectedFacilityId to match the facility map id
      sinon.stub(context, 'selectedFacilityId').get(() => 'test-facility-map');

      const getMetricConfigFactsStub = sinon
        .stub(metricConfigStore, 'getMetricConfigFacts')
        .resolves(mockFacts);

      const result = await service.getMetricConfigFacts();

      expect(result).to.deep.equal(mockFacts);
      sinon.assert.calledOnce(getMetricConfigFactsStub);
      sinon.assert.calledWith(
        getMetricConfigFactsStub,
        'test-facility-id', // Should use facilityId from facility maps
      );
    });
  });

  describe('deleteMetricConfig', () => {
    beforeEach(() => {
      // Set up context with facility information
      sinon.stub(context, 'selectedFacilityId').get(() => 'test-facility-map');
      sinon.stub(context, 'facilityMaps').get(() => [
        {
          id: 'test-facility-map',
          name: 'Test Facility',
          dataset: 'test-dataset',
          facilityId: 'test-facility-id',
          tenantId: 'test-tenant-id',
          default: true,
        },
      ]);
    });

    it('should successfully delete a metric configuration and clean up Redis keys', async () => {
      const configId = 'test-config-id';
      const mockCustomConfig = new CustomMetricConfigurationEntity();
      mockCustomConfig.id = configId;
      mockCustomConfig.metricConfigName = 'test-metric';
      mockCustomConfig.factType = 'test-fact-type';
      mockCustomConfig.configType = 'node';
      mockCustomConfig.facilityId = 'test-facility-id';

      const mockMetricConfigDetail = {
        default: undefined,
        custom: {
          metricConfigName: 'test-metric',
          factType: 'test-fact-type',
          configType: 'node' as const,
          views: ['facility'],
          matchConditions: {eventType: 'test'},
          sourceSystem: 'diq',
          displayName: 'Test Metric',
          enabled: true,
          active: true,
          isCustom: true,
          graphOperation: 'area_node' as const,
          metricType: 'testType',
          timeWindow: '60m_set' as const,
          aggregation: 'sum' as const,
          redisOperation: 'event_set' as const,
          nodeName: 'test-node',
        },
      };

      // Stub the service and store methods
      sinon
        .stub(service, 'getMetricConfigDetailById')
        .resolves(mockMetricConfigDetail);
      sinon.stub(service, 'deleteMetricConfigKeys').resolves(3);
      sinon.stub(service, 'updateLastUpdatedTimestampKey').resolves();
      sinon
        .stub(metricConfigStore, 'deleteMetricConfig')
        .resolves(mockCustomConfig);

      const result = await service.deleteMetricConfig(configId);

      expect(result).to.deep.equal(mockCustomConfig);

      // Verify service method calls
      sinon.assert.calledOnce(
        service.getMetricConfigDetailById as sinon.SinonStub,
      );
      sinon.assert.calledWith(
        service.getMetricConfigDetailById as sinon.SinonStub,
        configId,
        'custom',
      );

      // Verify service method calls
      sinon.assert.calledOnce(
        service.deleteMetricConfigKeys as sinon.SinonStub,
      );
      sinon.assert.calledWith(
        service.deleteMetricConfigKeys as sinon.SinonStub,
        mockMetricConfigDetail.custom,
        'test-tenant-id',
        'test-facility-id',
      );

      sinon.assert.calledOnce(
        service.updateLastUpdatedTimestampKey as sinon.SinonStub,
      );
      sinon.assert.calledWith(
        service.updateLastUpdatedTimestampKey as sinon.SinonStub,
        'test-fact-type',
        'test-tenant-id',
        'test-facility-id',
      );

      sinon.assert.calledOnce(
        metricConfigStore.deleteMetricConfig as sinon.SinonStub,
      );
      sinon.assert.calledWith(
        metricConfigStore.deleteMetricConfig as sinon.SinonStub,
        configId,
      );
    });

    it('should return null when no custom config is found', async () => {
      const configId = 'test-config-id';

      const mockMetricConfigDetail = {
        default: undefined,
        custom: undefined, // No custom config found
      };

      // Stub the service methods
      sinon
        .stub(service, 'getMetricConfigDetailById')
        .resolves(mockMetricConfigDetail);
      sinon.stub(service, 'deleteMetricConfigKeys').resolves(0);
      sinon.stub(service, 'updateLastUpdatedTimestampKey').resolves();
      sinon.stub(metricConfigStore, 'deleteMetricConfig').resolves(null);

      const result = await service.deleteMetricConfig(configId);

      expect(result).to.be.null;

      // Verify that NO store operations were called when no custom config exists
      sinon.assert.notCalled(
        metricConfigStore.deleteMetricConfig as sinon.SinonStub,
      );
      sinon.assert.notCalled(service.deleteMetricConfigKeys as sinon.SinonStub);
      sinon.assert.notCalled(
        service.updateLastUpdatedTimestampKey as sinon.SinonStub,
      );
    });

    it('should skip Redis operations when database delete returns null', async () => {
      const configId = 'test-config-id';

      const mockMetricConfigDetail = {
        default: undefined,
        custom: {
          metricConfigName: 'test-metric',
          factType: 'test-fact-type',
          configType: 'node' as const,
          views: ['facility'],
          matchConditions: {eventType: 'test'},
          sourceSystem: 'diq',
          displayName: 'Test Metric',
          enabled: true,
          active: true,
          isCustom: true,
          graphOperation: 'area_node' as const,
          metricType: 'testType',
          timeWindow: '60m_set' as const,
          aggregation: 'sum' as const,
          redisOperation: 'event_set' as const,
          nodeName: 'test-node',
        },
      };

      // Stub the service methods
      sinon
        .stub(service, 'getMetricConfigDetailById')
        .resolves(mockMetricConfigDetail);
      sinon.stub(metricConfigStore, 'deleteMetricConfig').resolves(null); // Database delete returns null
      sinon.stub(service, 'deleteMetricConfigKeys').resolves(3);
      sinon.stub(service, 'updateLastUpdatedTimestampKey').resolves();

      const result = await service.deleteMetricConfig(configId);

      expect(result).to.be.null;

      // Verify database delete was called but Redis operations were NOT called
      sinon.assert.calledOnce(
        metricConfigStore.deleteMetricConfig as sinon.SinonStub,
      );
      sinon.assert.notCalled(service.deleteMetricConfigKeys as sinon.SinonStub);
      sinon.assert.notCalled(
        service.updateLastUpdatedTimestampKey as sinon.SinonStub,
      );
    });

    it('should propagate errors from getMetricConfigDetailById', async () => {
      const configId = 'test-config-id';
      const error = new Error('Failed to get metric config detail');

      sinon.stub(service, 'getMetricConfigDetailById').rejects(error);
      sinon.stub(service, 'deleteMetricConfigKeys').resolves(0);
      sinon.stub(service, 'updateLastUpdatedTimestampKey').resolves();
      sinon.stub(metricConfigStore, 'deleteMetricConfig').resolves(null);

      await expect(service.deleteMetricConfig(configId)).to.be.rejectedWith(
        'Failed to get metric config detail',
      );

      // Verify that subsequent operations were not called
      sinon.assert.notCalled(service.deleteMetricConfigKeys as sinon.SinonStub);
      sinon.assert.notCalled(
        service.updateLastUpdatedTimestampKey as sinon.SinonStub,
      );
      sinon.assert.notCalled(
        metricConfigStore.deleteMetricConfig as sinon.SinonStub,
      );
    });

    it('should propagate errors from service deleteMetricConfigKeys', async () => {
      const configId = 'test-config-id';
      const error = new Error('Failed to delete Redis keys');
      const mockDeletedConfig = new CustomMetricConfigurationEntity();
      mockDeletedConfig.id = configId;

      const mockMetricConfigDetail = {
        default: undefined,
        custom: {
          metricConfigName: 'test-metric',
          factType: 'test-fact-type',
          configType: 'node' as const,
          views: ['facility'],
          matchConditions: {eventType: 'test'},
          sourceSystem: 'diq',
          displayName: 'Test Metric',
          enabled: true,
          active: true,
          isCustom: true,
          graphOperation: 'area_node' as const,
          metricType: 'testType',
          timeWindow: '60m_set' as const,
          aggregation: 'sum' as const,
          redisOperation: 'event_set' as const,
          nodeName: 'test-node',
        },
      };

      sinon
        .stub(service, 'getMetricConfigDetailById')
        .resolves(mockMetricConfigDetail);
      sinon
        .stub(metricConfigStore, 'deleteMetricConfig')
        .resolves(mockDeletedConfig); // Database delete succeeds
      sinon.stub(service, 'deleteMetricConfigKeys').rejects(error);
      sinon.stub(service, 'updateLastUpdatedTimestampKey').resolves();

      await expect(service.deleteMetricConfig(configId)).to.be.rejectedWith(
        'Failed to delete Redis keys',
      );

      // Verify that database delete was called, but timestamp update was not called
      sinon.assert.calledOnce(
        metricConfigStore.deleteMetricConfig as sinon.SinonStub,
      );
      sinon.assert.calledOnce(
        service.deleteMetricConfigKeys as sinon.SinonStub,
      );
      sinon.assert.notCalled(
        service.updateLastUpdatedTimestampKey as sinon.SinonStub,
      );
    });

    it('should propagate errors from service updateLastUpdatedTimestampKey', async () => {
      const configId = 'test-config-id';
      const error = new Error('Failed to update timestamp key');
      const mockDeletedConfig = new CustomMetricConfigurationEntity();
      mockDeletedConfig.id = configId;

      const mockMetricConfigDetail = {
        default: undefined,
        custom: {
          metricConfigName: 'test-metric',
          factType: 'test-fact-type',
          configType: 'node' as const,
          views: ['facility'],
          matchConditions: {eventType: 'test'},
          sourceSystem: 'diq',
          displayName: 'Test Metric',
          enabled: true,
          active: true,
          isCustom: true,
          graphOperation: 'area_node' as const,
          metricType: 'testType',
          timeWindow: '60m_set' as const,
          aggregation: 'sum' as const,
          redisOperation: 'event_set' as const,
          nodeName: 'test-node',
        },
      };

      sinon
        .stub(service, 'getMetricConfigDetailById')
        .resolves(mockMetricConfigDetail);
      sinon
        .stub(metricConfigStore, 'deleteMetricConfig')
        .resolves(mockDeletedConfig); // Database delete succeeds
      sinon.stub(service, 'deleteMetricConfigKeys').resolves(2);
      sinon.stub(service, 'updateLastUpdatedTimestampKey').rejects(error);

      await expect(service.deleteMetricConfig(configId)).to.be.rejectedWith(
        'Failed to update timestamp key',
      );

      // Verify that database delete and Redis key delete were called before the timestamp error
      sinon.assert.calledOnce(
        metricConfigStore.deleteMetricConfig as sinon.SinonStub,
      );
      sinon.assert.calledOnce(
        service.deleteMetricConfigKeys as sinon.SinonStub,
      );
    });

    it('should propagate errors from deleteMetricConfig store operation', async () => {
      const configId = 'test-config-id';
      const error = new Error('Failed to delete metric config from database');

      const mockMetricConfigDetail = {
        default: undefined,
        custom: {
          metricConfigName: 'test-metric',
          factType: 'test-fact-type',
          configType: 'node' as const,
          views: ['facility'],
          matchConditions: {eventType: 'test'},
          sourceSystem: 'diq',
          displayName: 'Test Metric',
          enabled: true,
          active: true,
          isCustom: true,
          graphOperation: 'area_node' as const,
          metricType: 'testType',
          timeWindow: '60m_set' as const,
          aggregation: 'sum' as const,
          redisOperation: 'event_set' as const,
          nodeName: 'test-node',
        },
      };

      sinon
        .stub(service, 'getMetricConfigDetailById')
        .resolves(mockMetricConfigDetail);
      sinon.stub(service, 'deleteMetricConfigKeys').resolves(1);
      sinon.stub(service, 'updateLastUpdatedTimestampKey').resolves();
      sinon.stub(metricConfigStore, 'deleteMetricConfig').rejects(error);

      await expect(service.deleteMetricConfig(configId)).to.be.rejectedWith(
        'Failed to delete metric config from database',
      );

      // Verify that no Redis operations were called since database delete failed first
      sinon.assert.notCalled(service.deleteMetricConfigKeys as sinon.SinonStub);
      sinon.assert.notCalled(
        service.updateLastUpdatedTimestampKey as sinon.SinonStub,
      );
    });

    it('should handle context facility resolution errors', async () => {
      const configId = 'test-config-id';

      // Create a new service instance with an empty context for this test
      const emptyContext = new ContextService();
      sinon.stub(emptyContext, 'selectedFacilityId').get(() => undefined);
      sinon.stub(emptyContext, 'facilityMaps').get(() => []);

      const testService = new MetricConfigService(
        configStore,
        logger,
        emptyContext,
        metricConfigStore,
        redisClient,
      );

      await expect(testService.deleteMetricConfig(configId)).to.be.rejectedWith(
        'No facility ID provided. Please ensure the ict-facility-id header is set.',
      );
    });

    it('should handle different factType values correctly', async () => {
      const configId = 'test-config-id';
      const factType = 'custom-fact-type';
      const mockCustomConfig = new CustomMetricConfigurationEntity();
      mockCustomConfig.id = configId;

      const mockMetricConfigDetail = {
        default: undefined,
        custom: {
          metricConfigName: 'test-metric',
          factType: factType,
          configType: 'node' as const,
          views: ['facility'],
          matchConditions: {eventType: 'test'},
          sourceSystem: 'diq',
          displayName: 'Test Metric',
          enabled: true,
          active: true,
          isCustom: true,
          graphOperation: 'area_node' as const,
          metricType: 'testType',
          timeWindow: '60m_set' as const,
          aggregation: 'sum' as const,
          redisOperation: 'event_set' as const,
          nodeName: 'test-node',
        },
      };

      sinon
        .stub(service, 'getMetricConfigDetailById')
        .resolves(mockMetricConfigDetail);
      sinon.stub(service, 'deleteMetricConfigKeys').resolves(5);
      sinon.stub(service, 'updateLastUpdatedTimestampKey').resolves();
      sinon
        .stub(metricConfigStore, 'deleteMetricConfig')
        .resolves(mockCustomConfig);

      const result = await service.deleteMetricConfig(configId);

      expect(result).to.deep.equal(mockCustomConfig);

      // Verify updateLastUpdatedTimestampKey was called with correct factType
      sinon.assert.calledWith(
        service.updateLastUpdatedTimestampKey as sinon.SinonStub,
        factType,
        'test-tenant-id',
        'test-facility-id',
      );
    });

    it('should process operations in correct sequence when database delete succeeds', async () => {
      const configId = 'test-config-id';
      const mockCustomConfig = new CustomMetricConfigurationEntity();
      mockCustomConfig.id = configId;

      const mockMetricConfigDetail = {
        default: undefined,
        custom: {
          metricConfigName: 'test-metric',
          factType: 'test-fact-type',
          configType: 'node' as const,
          views: ['facility'],
          matchConditions: {eventType: 'test'},
          sourceSystem: 'diq',
          displayName: 'Test Metric',
          enabled: true,
          active: true,
          isCustom: true,
          graphOperation: 'area_node' as const,
          metricType: 'testType',
          timeWindow: '60m_set' as const,
          aggregation: 'sum' as const,
          redisOperation: 'event_set' as const,
          nodeName: 'test-node',
        },
      };

      const getDetailStub = sinon
        .stub(service, 'getMetricConfigDetailById')
        .resolves(mockMetricConfigDetail);
      const deleteConfigStub = sinon
        .stub(metricConfigStore, 'deleteMetricConfig')
        .resolves(mockCustomConfig);
      const deleteKeysStub = sinon
        .stub(service, 'deleteMetricConfigKeys')
        .resolves(2);
      const updateTimestampStub = sinon
        .stub(service, 'updateLastUpdatedTimestampKey')
        .resolves();

      await service.deleteMetricConfig(configId);

      // Verify the order of operations
      sinon.assert.callOrder(
        getDetailStub,
        deleteConfigStub,
        deleteKeysStub,
        updateTimestampStub,
      );
    });

    it('should skip Redis operations when database delete returns null', async () => {
      const configId = 'test-config-id';

      const mockMetricConfigDetail = {
        default: undefined,
        custom: {
          metricConfigName: 'test-metric',
          factType: 'test-fact-type',
          configType: 'node' as const,
          views: ['facility'],
          matchConditions: {eventType: 'test'},
          sourceSystem: 'diq',
          displayName: 'Test Metric',
          enabled: true,
          active: true,
          isCustom: true,
          graphOperation: 'area_node' as const,
          metricType: 'testType',
          timeWindow: '60m_set' as const,
          aggregation: 'sum' as const,
          redisOperation: 'event_set' as const,
          nodeName: 'test-node',
        },
      };

      const getDetailStub = sinon
        .stub(service, 'getMetricConfigDetailById')
        .resolves(mockMetricConfigDetail);
      const deleteConfigStub = sinon
        .stub(metricConfigStore, 'deleteMetricConfig')
        .resolves(null);
      const deleteKeysStub = sinon
        .stub(service, 'deleteMetricConfigKeys')
        .resolves(2);
      const updateTimestampStub = sinon
        .stub(service, 'updateLastUpdatedTimestampKey')
        .resolves();

      const result = await service.deleteMetricConfig(configId);

      expect(result).to.be.null;

      // Verify only database operations were called, not Redis operations
      sinon.assert.calledOnce(getDetailStub);
      sinon.assert.calledOnce(deleteConfigStub);
      sinon.assert.notCalled(deleteKeysStub);
      sinon.assert.notCalled(updateTimestampStub);
    });
  });

  describe('deleteMetricConfigKeys', () => {
    afterEach(() => {
      sinon.restore();
    });

    it('should delete node metric keys and return the count', async () => {
      const nodeMetricConfig: MetricConfigDetail = {
        metricConfigName: 'test-node-metric',
        configType: 'node',
        views: ['facility'],
        matchConditions: {eventType: 'test'},
        factType: 'test-fact',
        sourceSystem: 'diq',
        displayName: 'Test Node Metric',
        enabled: true,
        active: true,
        isCustom: true,
        nodeName: 'test-node',
        metricType: 'stockTime',
        timeWindow: '60m_set',
        aggregation: 'sum',
        redisOperation: 'event_set',
        graphOperation: 'area_node',
      };

      const tenantId = 'test-tenant';
      const facilityId = 'test-facility';
      const expectedDeletedCount = 5;

      redisClient.unlinkByPattern.resolves(expectedDeletedCount);

      const result = await service.deleteMetricConfigKeys(
        nodeMetricConfig,
        tenantId,
        facilityId,
      );

      expect(result).to.equal(expectedDeletedCount);
      expect(redisClient.unlinkByPattern.calledOnce).to.be.true;

      // Verify the key pattern matches expected format
      const expectedPattern = `${tenantId}:${facilityId}:test-node:stockTime:60m_set:sum`;
      expect(redisClient.unlinkByPattern.calledWith(expectedPattern)).to.be
        .true;
    });

    it('should delete inbound-edge metric keys and return the count', async () => {
      const inboundEdgeMetricConfig: MetricConfigDetail = {
        metricConfigName: 'test-inbound-metric',
        configType: 'inbound-edge',
        views: ['facility'],
        matchConditions: {eventType: 'arrival'},
        factType: 'inbound-fact',
        sourceSystem: 'diq',
        displayName: 'Test Inbound Metric',
        enabled: true,
        active: true,
        isCustom: true,
        huId: 'handlingUnitCode',
        inboundArea: 'receiving',
        redisOperation: 'event_set',
        metricUnits: 'units/hr',
        inboundParentNodes: ['facility'],
        graphOperation: 'area_edge',
      };

      const tenantId = 'test-tenant';
      const facilityId = 'test-facility';
      const expectedDeletedCount = 3;

      redisClient.unlinkByPattern.resolves(expectedDeletedCount);

      const result = await service.deleteMetricConfigKeys(
        inboundEdgeMetricConfig,
        tenantId,
        facilityId,
      );

      expect(result).to.equal(expectedDeletedCount);
      expect(redisClient.unlinkByPattern.calledOnce).to.be.true;

      // Verify the key pattern matches expected format for inbound-edge
      const expectedPattern = `${tenantId}:${facilityId}:*:receiving:15m_set:hourly_rate`;
      expect(redisClient.unlinkByPattern.calledWith(expectedPattern)).to.be
        .true;
    });

    it('should delete outbound-edge metric keys for all views and return total count', async () => {
      const outboundEdgeMetricConfig: MetricConfigDetail = {
        metricConfigName: 'test-outbound-metric',
        configType: 'outbound-edge',
        views: ['facility', 'multishuttle'],
        matchConditions: {eventType: 'departure'},
        factType: 'outbound-fact',
        sourceSystem: 'diq',
        displayName: 'Test Outbound Metric',
        enabled: true,
        active: true,
        isCustom: true,
        outboundArea: 'shipping',
        huId: 'handlingUnitCode',
        units: 'handlingUnit',
        outboundParentNodes: ['facility'],
        graphOperation: 'area_edge',
      };

      const tenantId = 'test-tenant';
      const facilityId = 'test-facility';

      // Mock different delete counts for each pattern
      redisClient.unlinkByPattern
        .onFirstCall()
        .resolves(2)
        .onSecondCall()
        .resolves(3);

      const result = await service.deleteMetricConfigKeys(
        outboundEdgeMetricConfig,
        tenantId,
        facilityId,
      );

      expect(result).to.equal(5); // 2 + 3
      expect(redisClient.unlinkByPattern.calledTwice).to.be.true;

      // Verify both view patterns were called
      const expectedPattern1 = `${tenantId}:${facilityId}:handling_unit:*:edge:facility`;
      const expectedPattern2 = `${tenantId}:${facilityId}:handling_unit:*:edge:multishuttle`;
      expect(redisClient.unlinkByPattern.calledWith(expectedPattern1)).to.be
        .true;
      expect(redisClient.unlinkByPattern.calledWith(expectedPattern2)).to.be
        .true;
    });

    it('should delete complete-edge metric keys and return the count', async () => {
      const completeEdgeMetricConfig: MetricConfigDetail = {
        metricConfigName: 'test-complete-metric',
        configType: 'complete-edge',
        views: ['facility'],
        matchConditions: {eventType: 'complete'},
        factType: 'complete-fact',
        sourceSystem: 'diq',
        displayName: 'Test Complete Metric',
        enabled: true,
        active: true,
        isCustom: true,
        inboundArea: 'receiving',
        outboundArea: 'shipping',
        redisOperation: 'event_set',
        outboundNodeLabel: 'SortArea',
        inboundParentNodes: ['facility'],
        outboundParentNodes: ['facility'],
        metricUnits: 'units/hr',
        graphOperation: 'area_edge',
      };

      const tenantId = 'test-tenant';
      const facilityId = 'test-facility';
      const expectedDeletedCount = 4;

      redisClient.unlinkByPattern.resolves(expectedDeletedCount);

      const result = await service.deleteMetricConfigKeys(
        completeEdgeMetricConfig,
        tenantId,
        facilityId,
      );

      expect(result).to.equal(expectedDeletedCount);
      expect(redisClient.unlinkByPattern.calledOnce).to.be.true;

      // Verify the key pattern matches expected format for complete-edge
      const expectedPattern = `${tenantId}:${facilityId}:shipping:receiving:15m_set:hourly_rate`;
      expect(redisClient.unlinkByPattern.calledWith(expectedPattern)).to.be
        .true;
    });

    it('should return 0 for unknown config type', async () => {
      const unknownMetricConfig = {
        metricConfigName: 'test-unknown-metric',
        configType: 'unknown' as any,
        views: ['facility'],
        matchConditions: {eventType: 'test'},
        factType: 'test-fact',
        sourceSystem: 'diq',
        displayName: 'Test Unknown Metric',
        enabled: true,
        active: true,
        isCustom: true,
        graphOperation: 'area_node',
      } as unknown as MetricConfigDetail;

      const tenantId = 'test-tenant';
      const facilityId = 'test-facility';

      const result = await service.deleteMetricConfigKeys(
        unknownMetricConfig,
        tenantId,
        facilityId,
      );

      expect(result).to.equal(0);
      expect(redisClient.unlinkByPattern.called).to.be.false;
    });

    it('should handle redis errors for node config', async () => {
      const nodeMetricConfig: MetricConfigDetail = {
        metricConfigName: 'test-node-metric',
        configType: 'node',
        views: ['facility'],
        matchConditions: {eventType: 'test'},
        factType: 'test-fact',
        sourceSystem: 'diq',
        displayName: 'Test Node Metric',
        enabled: true,
        active: true,
        isCustom: true,
        nodeName: 'test-node',
        metricType: 'stockTime',
        timeWindow: '60m_set',
        aggregation: 'sum',
        redisOperation: 'event_set',
        graphOperation: 'area_node',
      };

      redisClient.unlinkByPattern.rejects(new Error('Redis connection failed'));

      try {
        await service.deleteMetricConfigKeys(
          nodeMetricConfig,
          'test-tenant',
          'test-facility',
        );
        expect.fail('Should have thrown an error');
      } catch (error: unknown) {
        if (!(error instanceof Error)) {
          throw error;
        }
        expect(error.message).to.equal('Redis connection failed');
      }
    });

    it('should handle redis errors for outbound-edge config and still propagate error', async () => {
      const outboundEdgeMetricConfig: MetricConfigDetail = {
        metricConfigName: 'test-outbound-metric',
        configType: 'outbound-edge',
        views: ['facility', 'multishuttle'],
        matchConditions: {eventType: 'departure'},
        factType: 'outbound-fact',
        sourceSystem: 'diq',
        displayName: 'Test Outbound Metric',
        enabled: true,
        active: true,
        isCustom: true,
        outboundArea: 'shipping',
        huId: 'handlingUnitCode',
        units: 'handlingUnit',
        outboundParentNodes: ['facility'],
        graphOperation: 'area_edge',
      };

      redisClient.unlinkByPattern.rejects(new Error('Redis timeout'));

      try {
        await service.deleteMetricConfigKeys(
          outboundEdgeMetricConfig,
          'test-tenant',
          'test-facility',
        );
        expect.fail('Should have thrown an error');
      } catch (error: unknown) {
        if (!(error instanceof Error)) {
          throw error;
        }
        expect(error.message).to.equal('Redis timeout');
      }
    });

    it('should handle node config with empty nodeName gracefully', async () => {
      const nodeMetricConfig: MetricConfigDetail = {
        metricConfigName: 'test-node-metric',
        configType: 'node',
        views: ['facility'],
        matchConditions: {eventType: 'test'},
        factType: 'test-fact',
        sourceSystem: 'diq',
        displayName: 'Test Node Metric',
        enabled: true,
        active: true,
        isCustom: true,
        nodeName: undefined as any,
        metricType: 'stockTime',
        timeWindow: '60m_set',
        aggregation: 'sum',
        redisOperation: 'event_set',
        graphOperation: 'area_node',
      };

      // Mock the Redis client to behave like the real implementation:
      // return 0 for empty patterns, just like the real unlinkByPattern method
      redisClient.unlinkByPattern.resolves(0);

      const result = await service.deleteMetricConfigKeys(
        nodeMetricConfig,
        'test-tenant',
        'test-facility',
      );

      // Should return 0 when nodeName is empty (pattern would be empty)
      expect(result).to.equal(0);
      expect(redisClient.unlinkByPattern.calledOnceWith('')).to.be.true;
    });
  });

  describe('updateLastUpdatedTimestampKey', () => {
    afterEach(() => {
      sinon.restore();
    });

    it('should set last updated timestamp key in redis', async () => {
      const factType = 'test-fact';
      const tenantId = 'test-tenant';
      const facilityId = 'test-facility';

      // Mock Date.now to return a fixed value for testing
      const mockTimestamp = 1640995200000; // 2022-01-01 00:00:00 UTC
      const clock = sinon.useFakeTimers(mockTimestamp);

      redisClient.set.resolves('OK');

      await service.updateLastUpdatedTimestampKey(
        factType,
        tenantId,
        facilityId,
      );

      const expectedKey = `${tenantId}:${facilityId}:${factType}:last_updated_timestamp`;
      const expectedValue = (mockTimestamp / 1000).toString(); // Convert to seconds

      expect(redisClient.set.calledOnceWith(expectedKey, expectedValue)).to.be
        .true;

      clock.restore();
    });

    it('should handle different fact types correctly', async () => {
      const factTypes = ['inventory', 'shipping', 'receiving'];
      const tenantId = 'test-tenant';
      const facilityId = 'test-facility';

      const mockTimestamp = 1640995200000;
      const clock = sinon.useFakeTimers(mockTimestamp);

      redisClient.set.resolves('OK');

      for (const factType of factTypes) {
        await service.updateLastUpdatedTimestampKey(
          factType,
          tenantId,
          facilityId,
        );

        const expectedKey = `${tenantId}:${facilityId}:${factType}:last_updated_timestamp`;
        const expectedValue = (mockTimestamp / 1000).toString();

        expect(redisClient.set.calledWith(expectedKey, expectedValue)).to.be
          .true;
      }

      expect(redisClient.set.callCount).to.equal(factTypes.length);

      clock.restore();
    });

    it('should handle redis set errors', async () => {
      const factType = 'test-fact';
      const tenantId = 'test-tenant';
      const facilityId = 'test-facility';

      redisClient.set.rejects(new Error('Redis connection failed'));

      try {
        await service.updateLastUpdatedTimestampKey(
          factType,
          tenantId,
          facilityId,
        );
        expect.fail('Should have thrown an error');
      } catch (error: unknown) {
        if (!(error instanceof Error)) {
          throw error;
        }
        expect(error.message).to.equal('Redis connection failed');
      }
    });

    it('should handle special characters in parameters', async () => {
      const factType = 'test-fact:with:colons';
      const tenantId = 'tenant-with-dashes';
      const facilityId = 'facility_with_underscores';

      const mockTimestamp = 1640995200000;
      const clock = sinon.useFakeTimers(mockTimestamp);

      redisClient.set.resolves('OK');

      await service.updateLastUpdatedTimestampKey(
        factType,
        tenantId,
        facilityId,
      );

      const expectedKey = `${tenantId}:${facilityId}:${factType}:last_updated_timestamp`;
      const expectedValue = (mockTimestamp / 1000).toString();

      expect(redisClient.set.calledOnceWith(expectedKey, expectedValue)).to.be
        .true;

      clock.restore();
    });

    it('should use current timestamp and convert to seconds correctly', async () => {
      const factType = 'test-fact';
      const tenantId = 'test-tenant';
      const facilityId = 'test-facility';

      // Test with different timestamps to ensure proper conversion
      const testTimestamps = [1640995200000, 1609459200000, 1672531200000];

      redisClient.set.resolves('OK');

      for (const timestamp of testTimestamps) {
        const clock = sinon.useFakeTimers(timestamp);

        await service.updateLastUpdatedTimestampKey(
          factType,
          tenantId,
          facilityId,
        );

        const expectedKey = `${tenantId}:${facilityId}:${factType}:last_updated_timestamp`;
        const expectedValue = (timestamp / 1000).toString();

        expect(redisClient.set.calledWith(expectedKey, expectedValue)).to.be
          .true;

        clock.restore();
      }
    });

    it('should return void and not return any value', async () => {
      const factType = 'test-fact';
      const tenantId = 'test-tenant';
      const facilityId = 'test-facility';

      redisClient.set.resolves('OK');

      const result = await service.updateLastUpdatedTimestampKey(
        factType,
        tenantId,
        facilityId,
      );

      expect(result).to.be.undefined;
    });
  });
});
