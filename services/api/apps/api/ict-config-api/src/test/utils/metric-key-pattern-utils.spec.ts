import {expect} from 'chai';
import sinon from 'sinon';
import type {WinstonLogger, MetricConfigDetail} from 'ict-api-foundations';
import {
  getNodeMetricKeyPattern,
  getInboundEdgeMetricKeyPattern,
  getOutboundEdgeMetricKeyPattern,
  getCompleteEdgeMetricKeyPattern,
  resolveNameFormulaToPattern,
  resolveDynamicStringToPattern,
} from '../../utils/metric-key-pattern-utils.ts';

describe('MetricKeyPatternUtils', () => {
  let mockLogger: sinon.SinonStubbedInstance<WinstonLogger>;

  beforeEach(() => {
    mockLogger = {
      warn: sinon.stub(),
    } as sinon.SinonStubbedInstance<WinstonLogger>;
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('resolveDynamicStringToPattern', () => {
    it('should replace placeholders with wildcards', () => {
      const result = resolveDynamicStringToPattern('multishuttle_{aisle_code}');
      expect(result).to.equal('multishuttle_*');
    });

    it('should replace multiple placeholders with wildcards', () => {
      const result = resolveDynamicStringToPattern('{area}_{zone}_{level}');
      expect(result).to.equal('*_*_*');
    });

    it('should return static strings unchanged', () => {
      const result = resolveDynamicStringToPattern('receiving');
      expect(result).to.equal('receiving');
    });

    it('should handle empty strings', () => {
      const result = resolveDynamicStringToPattern('');
      expect(result).to.equal('');
    });
  });

  describe('resolveNameFormulaToPattern', () => {
    it('should replace named groups with wildcards', () => {
      const nameConfig = {
        source: '{source_location_code}',
        pattern: 'MSAI(?P<aisle>\\d{2}).*',
        template: 'MSAI{aisle}',
      };

      const result = resolveNameFormulaToPattern(nameConfig);
      expect(result).to.equal('MSAI*');
    });

    it('should handle multiple named groups', () => {
      const nameConfig = {
        source: '{location_code}',
        pattern: 'MS(?P<area>\\w+)(?P<level>\\d{1}).*',
        template: 'MS{area}_{level}',
      };

      const result = resolveNameFormulaToPattern(nameConfig);
      expect(result).to.equal('MS*_*');
    });

    it('should handle template without named groups', () => {
      const nameConfig = {
        source: '{source}',
        pattern: '.*',
        template: 'static_name',
      };

      const result = resolveNameFormulaToPattern(nameConfig);
      expect(result).to.equal('static_name');
    });
  });

  describe('getNodeMetricKeyPattern', () => {
    const baseNodeConfig: MetricConfigDetail & {configType: 'node'} = {
      metricConfigName: 'test-node-metric',
      configType: 'node',
      views: ['facility'],
      matchConditions: {eventType: 'test'},
      factType: 'test-fact',
      sourceSystem: 'diq',
      displayName: 'Test Node Metric',
      enabled: true,
      active: true,
      isCustom: true,
      nodeName: 'receiving',
      metricType: 'stock_time',
      timeWindow: '15m_set',
      aggregation: 'avg',
      redisOperation: 'event_set',
      graphOperation: 'area_node',
    };

    it('should generate pattern using nodeName', () => {
      const result = getNodeMetricKeyPattern(
        baseNodeConfig,
        'tenant123',
        'facility456',
      );

      expect(result).to.equal(
        'tenant123:facility456:receiving:stock_time:15m_set:avg',
      );
    });

    it('should generate pattern using nameFormula when available', () => {
      const configWithNameFormula = {
        ...baseNodeConfig,
        nameFormula: {
          source: '{location_code}',
          pattern: 'MSAI(?P<aisle>\\d{2}).*',
          template: 'MSAI{aisle}',
        },
        nodeName: undefined,
      };

      const result = getNodeMetricKeyPattern(
        configWithNameFormula,
        'tenant123',
        'facility456',
      );

      expect(result).to.equal(
        'tenant123:facility456:MSAI*:stock_time:15m_set:avg',
      );
    });

    it('should handle dynamic metric types', () => {
      const configWithDynamicType = {
        ...baseNodeConfig,
        metricType: '{equipment_type}_utilization',
      };

      const result = getNodeMetricKeyPattern(
        configWithDynamicType,
        'tenant123',
        'facility456',
      );

      expect(result).to.equal(
        'tenant123:facility456:receiving:*_utilization:15m_set:avg',
      );
    });

    it('should return empty string and log warning when no nodeName or nameFormula', () => {
      const configWithoutName = {
        ...baseNodeConfig,
        nodeName: undefined,
        nameFormula: undefined,
      };

      const result = getNodeMetricKeyPattern(
        configWithoutName,
        'tenant123',
        'facility456',
        mockLogger,
      );

      expect(result).to.equal('');
      expect(mockLogger.warn.calledOnce).to.be.true;
      expect(
        mockLogger.warn.calledWith(
          'Node metric config has neither nameFormula nor nodeName',
        ),
      ).to.be.true;
    });
  });

  describe('getInboundEdgeMetricKeyPattern', () => {
    const baseInboundConfig: MetricConfigDetail & {configType: 'inbound-edge'} =
      {
        metricConfigName: 'test-inbound-metric',
        configType: 'inbound-edge',
        views: ['multishuttle', 'warehouse'],
        matchConditions: {eventType: 'test'},
        factType: 'test-fact',
        sourceSystem: 'diq',
        displayName: 'Test Inbound Metric',
        enabled: true,
        active: true,
        isCustom: true,
        inboundArea: 'receiving',
        huId: 'handling_unit_code',
        redisOperation: 'event_set',
        graphOperation: 'area_edge',
        metricUnits: 'units/hr',
        inboundParentNodes: ['warehouse'],
      };

    it('should generate pattern using inboundArea', () => {
      const result = getInboundEdgeMetricKeyPattern(
        baseInboundConfig,
        'tenant123',
        'facility456',
      );

      expect(result).to.equal(
        'tenant123:facility456:*:receiving:15m_set:hourly_rate',
      );
    });

    it('should generate pattern using nameFormula when available', () => {
      const configWithNameFormula = {
        ...baseInboundConfig,
        nameFormula: {
          source: '{destination_area}',
          pattern: 'AREA(?P<zone>\\d{2}).*',
          template: 'AREA{zone}',
        },
      };

      const result = getInboundEdgeMetricKeyPattern(
        configWithNameFormula,
        'tenant123',
        'facility456',
      );

      expect(result).to.equal(
        'tenant123:facility456:*:AREA*:15m_set:hourly_rate',
      );
    });
  });

  describe('getOutboundEdgeMetricKeyPattern', () => {
    const baseOutboundConfig: MetricConfigDetail & {
      configType: 'outbound-edge';
    } = {
      metricConfigName: 'test-outbound-metric',
      configType: 'outbound-edge',
      views: ['multishuttle', 'warehouse'],
      matchConditions: {eventType: 'test'},
      factType: 'test-fact',
      sourceSystem: 'diq',
      displayName: 'Test Outbound Metric',
      enabled: true,
      active: true,
      isCustom: true,
      outboundArea: 'shipping',
      huId: 'handling_unit_code',
      units: 'handlingUnit',
      outboundParentNodes: ['facility'],
    };

    it('should generate patterns for each view', () => {
      const result = getOutboundEdgeMetricKeyPattern(
        baseOutboundConfig,
        'tenant123',
        'facility456',
      );

      expect(result).to.deep.equal([
        'tenant123:facility456:handling_unit:*:edge:multishuttle',
        'tenant123:facility456:handling_unit:*:edge:warehouse',
      ]);
    });

    it('should return empty array and log warning when no views', () => {
      const configWithoutViews = {
        ...baseOutboundConfig,
        views: [],
      };

      const result = getOutboundEdgeMetricKeyPattern(
        configWithoutViews,
        'tenant123',
        'facility456',
      );

      expect(result).to.deep.equal([]);
    });
  });

  describe('getCompleteEdgeMetricKeyPattern', () => {
    const baseCompleteConfig: MetricConfigDetail & {
      configType: 'complete-edge';
    } = {
      metricConfigName: 'test-complete-metric',
      configType: 'complete-edge',
      views: ['multishuttle'],
      matchConditions: {eventType: 'test'},
      factType: 'test-fact',
      sourceSystem: 'diq',
      displayName: 'Test Complete Metric',
      enabled: true,
      active: true,
      isCustom: true,
      inboundArea: 'receiving',
      outboundArea: 'shipping',
      redisOperation: 'event_set',
      graphOperation: 'area_edge',
      outboundNodeLabel: 'Area',
      inboundParentNodes: ['warehouse'],
      outboundParentNodes: ['facility'],
      metricUnits: 'units/hr',
    };

    it('should generate pattern using inbound and outbound areas', () => {
      const result = getCompleteEdgeMetricKeyPattern(
        baseCompleteConfig,
        'tenant123',
        'facility456',
      );

      expect(result).to.equal(
        'tenant123:facility456:shipping:receiving:15m_set:hourly_rate',
      );
    });

    it('should use nameFormula for inbound area when available', () => {
      const configWithNameFormula = {
        ...baseCompleteConfig,
        nameFormula: {
          source: '{destination_area}',
          pattern: 'AREA(?P<zone>\\d{2}).*',
          template: 'AREA{zone}',
        },
      };

      const result = getCompleteEdgeMetricKeyPattern(
        configWithNameFormula,
        'tenant123',
        'facility456',
      );

      expect(result).to.equal(
        'tenant123:facility456:shipping:AREA*:15m_set:hourly_rate',
      );
    });

    it('should handle dynamic outbound areas', () => {
      const configWithDynamicOutbound = {
        ...baseCompleteConfig,
        outboundArea: '{source_area}_zone',
      };

      const result = getCompleteEdgeMetricKeyPattern(
        configWithDynamicOutbound,
        'tenant123',
        'facility456',
      );

      expect(result).to.equal(
        'tenant123:facility456:*_zone:receiving:15m_set:hourly_rate',
      );
    });
  });
});
