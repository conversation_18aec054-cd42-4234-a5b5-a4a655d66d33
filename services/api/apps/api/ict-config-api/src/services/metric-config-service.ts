import {
  DiService,
  WinstonLogger,
  ConfigStore,
  ContextService,
  IctError,
  FacilityUtils,
  RedisClient,
  MetricConfigStore,
  MetricConfigSummaryFilters,
  MetricConfigFact,
} from 'ict-api-foundations';
import type {
  MetricConfigSummary,
  MetricConfigDetail,
  CustomMetricConfigSaveResult,
  MetricConfigValueResponse,
  NodeMetricConfigDetail,
  InboundEdgeMetricConfigDetail,
  OutboundEdgeMetricConfigDetail,
  CompleteEdgeMetricConfigDetail,
} from 'ict-api-foundations';
import {
  DefaultMetricConfigurationEntity,
  CustomMetricConfigurationEntity,
} from 'ict-api-schema';
import {
  getCompleteEdgeMetricKeyPattern,
  getInboundEdgeMetricKeyPattern,
  getNodeMetricKeyPattern,
  getOutboundEdgeMetricKeyPattern,
} from '../utils/metric-key-pattern-utils.ts';

@DiService()
export class MetricConfigService {
  public static type = 'metric-config-service';

  constructor(
    private configStore: ConfigStore,
    private logger: WinstonLogger,
    private context: ContextService,
    private metricConfigStore: MetricConfigStore,
    private redisClient: RedisClient,
  ) {}

  /**
   * Retrieves metric configurations based on the provided filters.
   * @param filters The filter criteria to apply when fetching metric configurations
   * @returns A promise that resolves to an array of metric configurations
   * @throws IctError.notFound if no configurations are found
   */
  async getMetricConfigSummaries(
    filters: MetricConfigSummaryFilters,
  ): Promise<MetricConfigSummary[]> {
    this.logger.info('Getting metric configurations', {filters});

    const entities =
      await this.metricConfigStore.getMetricConfigSummaries(filters);

    this.logger.info('Retrieved metric configurations', {
      count: entities.length,
      filters,
    });

    if (entities.length === 0) {
      throw IctError.notFound(
        'No metric configurations found matching the criteria',
      );
    }

    // Map database entities to the MetricConfig interface
    return entities.map(entity => this.mapEntityToMetricConfig(entity));
  }

  /**
   * Gets a single metric configuration by name
   * @param metricName The name of the metric configuration to retrieve
   * @param configType Optional filter for 'default' or 'custom' configurations only
   * @returns A promise that resolves to the metric configuration details
   */
  async getMetricConfigDetail(
    metricName: string,
    configType?: 'default' | 'custom',
  ): Promise<MetricConfigValueResponse> {
    this.logger.info('Getting metric configuration detail', {
      metricName,
      configType,
    });

    // Get facility ID from context using shared utility
    const facilityId = FacilityUtils.resolveFacilityId(
      this.context,
      this.logger,
    );

    const result = await this.metricConfigStore.getMetricConfigDetail(
      metricName,
      facilityId,
      configType,
    );

    const response: MetricConfigValueResponse = {};

    // Map default configuration if it exists
    if (result.default) {
      response.default = this.mapEntityToMetricConfigDetail(result.default);
    }

    // Map custom configuration if it exists
    if (result.custom) {
      response.custom = this.mapEntityToMetricConfigDetail(result.custom);
    }

    this.logger.info('Retrieved metric configuration detail', {
      metricName,
      hasDefault: !!response.default,
      hasCustom: !!response.custom,
    });

    return response;
  }

  /**
   * Gets a single metric configuration by id
   * @param metricId The name of the metric configuration to retrieve
   * @param configType Optional filter for 'default' or 'custom' configurations only
   * @returns A promise that resolves to the metric configuration details
   */
  async getMetricConfigDetailById(
    metricId: string,
    configType?: 'default' | 'custom',
  ): Promise<MetricConfigValueResponse> {
    this.logger.info('Getting metric configuration detail by id', {
      metricId,
      configType,
    });

    // Get facility ID from context using shared utility
    const facilityId = FacilityUtils.resolveFacilityId(
      this.context,
      this.logger,
    );

    const result = await this.metricConfigStore.getMetricConfigDetailById(
      metricId,
      facilityId,
      configType,
    );

    const response: MetricConfigValueResponse = {};

    // Map default configuration if it exists
    if (result.default) {
      response.default = this.mapEntityToMetricConfigDetail(result.default);
    }

    // Map custom configuration if it exists
    if (result.custom) {
      response.custom = this.mapEntityToMetricConfigDetail(result.custom);
    }

    this.logger.info('Retrieved metric configuration detail by id', {
      metricId,
      hasDefault: !!response.default,
      hasCustom: !!response.custom,
    });

    return response;
  }

  /**
   * Maps a database entity to the MetricConfig interface
   * @param entity The database entity to map
   * @returns A MetricConfig object
   */
  private mapEntityToMetricConfig(
    entity: DefaultMetricConfigurationEntity | CustomMetricConfigurationEntity,
  ): MetricConfigSummary {
    const isCustom = entity instanceof CustomMetricConfigurationEntity;
    if (isCustom) {
      return {
        id: entity.id,
        metricName: entity.metricConfigName,
        configType: entity.configType,
        nodeName: entity.nodeName,
        factType: entity.factType,
        enabled: null,
        active: (entity as CustomMetricConfigurationEntity).active,
        isCustom,
        facilityId: (entity as CustomMetricConfigurationEntity).facilityId,
        views: entity.views,
      };
    }
    return {
      id: entity.id,
      metricName: entity.metricConfigName,
      configType: entity.configType,
      nodeName: entity.nodeName,
      factType: entity.factType,
      enabled: (entity as DefaultMetricConfigurationEntity).enabled,
      active: (entity as DefaultMetricConfigurationEntity).active,
      isCustom,
      facilityId: 'default',
      views: entity.views,
    };
  }

  /**
   * Maps a database entity to the MetricConfigDetail interface
   * @param entity The database entity to map
   * @returns A MetricConfigDetail object
   */
  private mapEntityToMetricConfigDetail(
    entity: DefaultMetricConfigurationEntity | CustomMetricConfigurationEntity,
  ): MetricConfigDetail {
    const isCustom = entity instanceof CustomMetricConfigurationEntity;

    const baseConfig = {
      id: entity.id,
      metricConfigName: entity.metricConfigName,
      views: entity.views || [],
      matchConditions: entity.matchConditions || {},
      configType: entity.configType,
      graphOperation: entity.graphOperation,
      redisParams: entity.redisParams,
      label: entity.label,
      parentNodes: entity.parentNodes,
      nameFormula: entity.nameFormula,
      factType: entity.factType || '',
      sourceSystem: entity.sourceSystem || '',
      displayName: entity.displayName || entity.metricConfigName,
      description: entity.description,
      thresholdConfig: entity.thresholdConfig,
      exampleMessage: entity.exampleMessage,
      enabled: entity.enabled,
      active: entity.active,
      isCustom,
    };

    // Type-specific mappings
    if (entity.configType === 'node') {
      const config: NodeMetricConfigDetail = {
        ...baseConfig,
        configType: 'node',
        graphOperation: entity.graphOperation!,
        metricType: entity.metricType || '',
        timeWindow: entity.timeWindow!,
        aggregation: entity.aggregation!,
        redisOperation: entity.redisOperation!,
        metricUnits: entity.metricUnits,
        nodeName: entity.nodeName,
      };

      return config;
    } else if (entity.configType === 'inbound-edge') {
      const config: InboundEdgeMetricConfigDetail = {
        ...baseConfig,
        configType: 'inbound-edge',
        huId: entity.huId || '',
        inboundArea: entity.inboundArea || '',
        redisOperation: entity.redisOperation!,
        graphOperation: entity.graphOperation!,
        metricUnits: entity.metricUnits,
        inboundParentNodes: entity.inboundParentNodes,
      };
      return config;
    } else if (entity.configType === 'outbound-edge') {
      const config: OutboundEdgeMetricConfigDetail = {
        ...baseConfig,
        configType: 'outbound-edge',
        outboundArea: entity.outboundArea || '',
        huId: entity.huId || '',
        units: entity.units || '',
        outboundParentNodes: entity.outboundParentNodes,
      };
      return config;
    } else {
      // complete-edge
      const config: CompleteEdgeMetricConfigDetail = {
        ...baseConfig,
        configType: 'complete-edge',
        inboundArea: entity.inboundArea || '',
        outboundArea: entity.outboundArea || '',
        redisOperation: entity.redisOperation!,
        graphOperation: entity.graphOperation!,
        outboundNodeLabel: entity.outboundNodeLabel || '',
        inboundParentNodes: entity.inboundParentNodes,
        outboundParentNodes: entity.outboundParentNodes,
        metricUnits: entity.metricUnits,
      };
      return config;
    }
  }

  /**
   * Gets statistics about metric configurations grouped by fact type
   * @returns A promise that resolves to an array of fact types with their statistics
   */
  async getMetricConfigFacts(): Promise<MetricConfigFact[]> {
    this.logger.info('Getting metric configuration facts');

    // Get facility ID from context using shared utility
    const facilityId = FacilityUtils.resolveFacilityId(
      this.context,
      this.logger,
    );

    const facts = await this.metricConfigStore.getMetricConfigFacts(facilityId);

    this.logger.info('Retrieved metric configuration facts', {
      facilityId,
      factTypesCount: facts.length,
    });

    return facts;
  }

  getType(): string {
    return MetricConfigService.type;
  }
  /**
   * Updates or creates a metric configuration.
   * @param input The metric configuration data
   * @returns A promise that resolves to the result with status information
   * @throws IctError.notFound if no facility is found in context
   */
  async updateOrCreateMetricConfig(
    input: MetricConfigDetail,
  ): Promise<CustomMetricConfigSaveResult> {
    this.logger.info('Updating or creating metric configuration', {
      metricConfigName: input.metricConfigName,
      configType: input.configType,
    });

    // Get tenant ID and facility ID from context using shared utility
    const tenantId = FacilityUtils.resolveTenantId(this.context, this.logger);
    const facilityId = FacilityUtils.resolveFacilityId(
      this.context,
      this.logger,
    );

    const oldMetricConfig = await this.getMetricConfigDetail(
      input.metricConfigName,
    );

    const result = await this.metricConfigStore.putMetricConfig(
      input,
      facilityId,
    );

    this.logger.info('Metric configuration saved successfully', {
      id: result.config.id,
      isNew: result.isNew,
      metricConfigName: input.metricConfigName,
      facilityId,
    });

    // update the last updated timestamp key in redis
    await this.updateLastUpdatedTimestampKey(
      input.factType,
      tenantId,
      facilityId,
    );

    // remove keys associated with the old metric, if applicable
    // only try and remove the keys if the config was actually used
    if (oldMetricConfig.custom && oldMetricConfig.custom.enabled) {
      const deletedKeysCount = await this.deleteMetricConfigKeys(
        oldMetricConfig.custom,
        tenantId,
        facilityId,
      );

      this.logger.info(
        `Deleted ${deletedKeysCount} keys associated with the previous custom metric config for ${input.metricConfigName}`,
      );
    } else if (oldMetricConfig.default && oldMetricConfig.default.enabled) {
      const deletedKeysCount = await this.deleteMetricConfigKeys(
        oldMetricConfig.default,
        tenantId,
        facilityId,
      );

      this.logger.info(
        `Deleted ${deletedKeysCount} keys associated with the previous default metric config for ${input.metricConfigName}`,
      );
    }

    return result;
  }

  /**
   * Deletes a metric configuration based on its id
   * Delete any keys associated with the metric config
   * Also update the last updated key in redis for the fact type
   * @param id The id of the metric configuration to delete
   * @returns A promise that resolves to the deleted metric configuration
   */
  async deleteMetricConfig(
    id: string,
  ): Promise<CustomMetricConfigurationEntity | null> {
    // Get tenant ID and facility ID from context using shared utility
    const tenantId = FacilityUtils.resolveTenantId(this.context, this.logger);
    const facilityId = FacilityUtils.resolveFacilityId(
      this.context,
      this.logger,
    );

    // Get the metric config we're deleting so we know which Redis keys to update/delete
    const metricConfig = await this.getMetricConfigDetailById(id, 'custom');
    if (metricConfig.custom) {
      // delete the metric config from the database
      const deletedConfig = await this.metricConfigStore.deleteMetricConfig(id);

      // Only perform Redis operations if the database deletion was successful
      if (deletedConfig) {
        // delete the Redis keys associated with the metric config
        await this.deleteMetricConfigKeys(
          metricConfig.custom,
          tenantId,
          facilityId,
        );

        // update the last updated timestamp key in redis
        await this.updateLastUpdatedTimestampKey(
          metricConfig.custom.factType,
          tenantId,
          facilityId,
        );
      }

      return deletedConfig;
    }

    return null;
  }

  /**
   * Save the last updated time for the fact type in redis
   * @param factType The fact type to update
   * @param tenantId The tenant ID
   * @param facilityId The facility ID
   */
  async updateLastUpdatedTimestampKey(
    factType: string,
    tenantId: string,
    facilityId: string,
  ): Promise<void> {
    // use fields from the facility map to create the key, same as graph caching
    const cacheKey = `${tenantId}:${facilityId}:${factType}:last_updated_timestamp`;
    const currentTimeSeconds = Date.now() / 1000;
    await this.redisClient.set(cacheKey, currentTimeSeconds.toString());
  }

  /**
   * Delete all metric keys associated with the given metric config
   * This is useful for when we update or delete a metric config and want to remove old keys
   * @param metricConfig
   * @returns the number of keys deleted
   */
  async deleteMetricConfigKeys(
    metricConfig: MetricConfigDetail,
    tenantId: string,
    facilityId: string,
  ): Promise<number> {
    switch (metricConfig.configType) {
      case 'node': {
        const keyPattern = getNodeMetricKeyPattern(
          metricConfig,
          tenantId,
          facilityId,
          this.logger,
        );
        this.logger.debug('Deleting node metric key pattern', {
          metricConfigName: metricConfig.metricConfigName,
          keyPattern,
        });

        // delete all keys matching the pattern
        return await this.redisClient.unlinkByPattern(keyPattern);
      }

      case 'inbound-edge': {
        const keyPattern = getInboundEdgeMetricKeyPattern(
          metricConfig,
          tenantId,
          facilityId,
        );
        this.logger.debug('Deleting inbound-edge metric key pattern', {
          metricConfigName: metricConfig.metricConfigName,
          keyPattern,
        });

        // delete all keys matching the pattern
        return await this.redisClient.unlinkByPattern(keyPattern);
      }

      case 'outbound-edge': {
        const keyPatterns = getOutboundEdgeMetricKeyPattern(
          metricConfig,
          tenantId,
          facilityId,
        );
        this.logger.debug('Deleting outbound-edge metric key patterns', {
          metricConfigName: metricConfig.metricConfigName,
          keyPatterns,
        });

        // delete all keys matching the pattern in parallel
        // keep track of the total number of keys deleted
        const deletePromises = keyPatterns.map(pattern =>
          this.redisClient.unlinkByPattern(pattern),
        );
        const deletedCounts = await Promise.all(deletePromises);
        const totalDeletedCount = deletedCounts.reduce(
          (sum, count) => sum + count,
          0,
        );
        return totalDeletedCount;
      }

      case 'complete-edge': {
        const keyPattern = getCompleteEdgeMetricKeyPattern(
          metricConfig,
          tenantId,
          facilityId,
        );
        this.logger.debug('Deleting complete-edge metric key patterns', {
          metricConfigName: metricConfig.metricConfigName,
          keyPattern,
        });

        // delete all keys matching the pattern
        return await this.redisClient.unlinkByPattern(keyPattern);
      }

      default:
        // this shouldn't ever happen
        return 0;
    }
  }
}
