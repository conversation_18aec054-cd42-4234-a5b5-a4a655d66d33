import {
  DiSer<PERSON>,
  WinstonLogger,
  ConfigStore,
  ContextService,
  IctError,
  FacilityUtils,
} from 'ict-api-foundations';
import {
  DefaultMetricConfigurationEntity,
  CustomMetricConfigurationEntity,
} from 'ict-api-schema';
import {MetricConfigSummary} from '../defs/metric-config-summary.ts';
import {MetricConfigStore} from '../stores/metric-config-store.ts';
import {MetricConfigSummaryFilters} from '../defs/metric-config-filters.ts';

import {
  MetricConfigDetail,
  CustomMetricConfigSaveResult,
  MetricConfigValueResponse,
  NodeMetricConfigDetail,
  InboundEdgeMetricConfigDetail,
  OutboundEdgeMetricConfigDetail,
  CompleteEdgeMetricConfigDetail,
} from '../defs/metric-config-detail.ts';
import {MetricConfigFact} from '../defs/metric-config-facts.ts';

@DiService()
export class MetricConfigService {
  public static type = 'metric-config-service';

  constructor(
    private configStore: ConfigStore,
    private logger: WinstonLogger,
    private context: ContextService,
    private metricConfigStore: MetricConfigStore,
  ) {}

  /**
   * Retrieves metric configurations based on the provided filters.
   * @param filters The filter criteria to apply when fetching metric configurations
   * @returns A promise that resolves to an array of metric configurations
   * @throws IctError.notFound if no configurations are found
   */
  async getMetricConfigSummaries(
    filters: MetricConfigSummaryFilters,
  ): Promise<MetricConfigSummary[]> {
    this.logger.info('Getting metric configurations', {filters});

    const entities =
      await this.metricConfigStore.getMetricConfigSummaries(filters);

    this.logger.info('Retrieved metric configurations', {
      count: entities.length,
      filters,
    });

    if (entities.length === 0) {
      throw IctError.notFound(
        'No metric configurations found matching the criteria',
      );
    }

    // Map database entities to the MetricConfig interface
    return entities.map(entity => this.mapEntityToMetricConfig(entity));
  }

  /**
   * Gets a single metric configuration by name
   * @param metricName The name of the metric configuration to retrieve
   * @param configType Optional filter for 'default' or 'custom' configurations only
   * @returns A promise that resolves to the metric configuration details
   */
  async getMetricConfigDetail(
    metricName: string,
    configType?: 'default' | 'custom',
  ): Promise<MetricConfigValueResponse> {
    this.logger.info('Getting metric configuration detail', {
      metricName,
      configType,
    });

    // Get facility ID from context using shared utility
    const facilityId = FacilityUtils.resolveFacilityId(
      this.context,
      this.logger,
    );

    const result = await this.metricConfigStore.getMetricConfigDetail(
      metricName,
      facilityId,
      configType,
    );

    const response: MetricConfigValueResponse = {};

    // Map default configuration if it exists
    if (result.default) {
      response.default = this.mapEntityToMetricConfigDetail(result.default);
    }

    // Map custom configuration if it exists
    if (result.custom) {
      response.custom = this.mapEntityToMetricConfigDetail(result.custom);
    }

    this.logger.info('Retrieved metric configuration detail', {
      metricName,
      hasDefault: !!response.default,
      hasCustom: !!response.custom,
    });

    return response;
  }

  /**
   * Maps a database entity to the MetricConfig interface
   * @param entity The database entity to map
   * @returns A MetricConfig object
   */
  private mapEntityToMetricConfig(
    entity: DefaultMetricConfigurationEntity | CustomMetricConfigurationEntity,
  ): MetricConfigSummary {
    const isCustom = entity instanceof CustomMetricConfigurationEntity;
    if (isCustom) {
      return {
        id: entity.id,
        metricName: entity.metricConfigName,
        configType: entity.configType,
        nodeName: entity.nodeName,
        factType: entity.factType,
        enabled: null,
        active: (entity as CustomMetricConfigurationEntity).active,
        isCustom,
        facilityId: (entity as CustomMetricConfigurationEntity).facilityId,
        views: entity.views,
      };
    }
    return {
      id: entity.id,
      metricName: entity.metricConfigName,
      configType: entity.configType,
      nodeName: entity.nodeName,
      factType: entity.factType,
      enabled: (entity as DefaultMetricConfigurationEntity).enabled,
      active: (entity as DefaultMetricConfigurationEntity).active,
      isCustom,
      facilityId: 'default',
      views: entity.views,
    };
  }

  /**
   * Maps a database entity to the MetricConfigDetail interface
   * @param entity The database entity to map
   * @returns A MetricConfigDetail object
   */
  private mapEntityToMetricConfigDetail(
    entity: DefaultMetricConfigurationEntity | CustomMetricConfigurationEntity,
  ): MetricConfigDetail {
    const isCustom = entity instanceof CustomMetricConfigurationEntity;

    const baseConfig = {
      metricConfigName: entity.metricConfigName,
      views: entity.views || [],
      matchConditions: entity.matchConditions || {},
      configType: entity.configType,
      graphOperation: entity.graphOperation,
      redisParams: entity.redisParams,
      label: entity.label,
      parentNodes: entity.parentNodes,
      nameFormula: entity.nameFormula,
      factType: entity.factType,
      sourceSystem: entity.sourceSystem || '',
      displayName: entity.displayName || entity.metricConfigName,
      description: entity.description,
      acceptableLowRange: entity.acceptableLowRange,
      acceptableHighRange: entity.acceptableHighRange,
      exampleMessage: entity.exampleMessage,
      enabled: entity.enabled,
      active: entity.active,
      isCustom,
    };

    // Type-specific mappings
    if (entity.configType === 'node') {
      return {
        ...baseConfig,
        configType: 'node',
        graphOperation: entity.graphOperation!,
        metricType: entity.metricType || '',
        timeWindow: entity.timeWindow!,
        aggregation: entity.aggregation!,
        redisOperation: entity.redisOperation!,
        metricUnits: entity.metricUnits,
        nodeName: entity.nodeName,
      } as NodeMetricConfigDetail;
    } else if (entity.configType === 'inbound-edge') {
      return {
        ...baseConfig,
        configType: 'inbound-edge',
        huId: entity.huId || '',
        inboundArea: entity.inboundArea || '',
        redisOperation: entity.redisOperation!,
        graphOperation: entity.graphOperation!,
        metricUnits: entity.metricUnits,
        inboundParentNodes: entity.inboundParentNodes,
      } as InboundEdgeMetricConfigDetail;
    } else if (entity.configType === 'outbound-edge') {
      return {
        ...baseConfig,
        configType: 'outbound-edge',
        outboundArea: entity.outboundArea || '',
        huId: entity.huId || '',
        units: entity.units || '',
        outboundParentNodes: entity.outboundParentNodes,
      } as OutboundEdgeMetricConfigDetail;
    } else {
      // complete-edge
      return {
        ...baseConfig,
        configType: 'complete-edge',
        inboundArea: entity.inboundArea || '',
        outboundArea: entity.outboundArea || '',
        redisOperation: entity.redisOperation!,
        graphOperation: entity.graphOperation!,
        outboundNodeLabel: entity.outboundNodeLabel,
        inboundParentNodes: entity.inboundParentNodes,
        outboundParentNodes: entity.outboundParentNodes,
        metricUnits: entity.metricUnits,
      } as CompleteEdgeMetricConfigDetail;
    }
  }

  /**
   * Gets statistics about metric configurations grouped by fact type
   * @returns A promise that resolves to an array of fact types with their statistics
   */
  async getMetricConfigFacts(): Promise<MetricConfigFact[]> {
    this.logger.info('Getting metric configuration facts');

    // Get facility ID from context using shared utility
    const facilityId = FacilityUtils.resolveFacilityId(
      this.context,
      this.logger,
    );

    const facts = await this.metricConfigStore.getMetricConfigFacts(facilityId);

    this.logger.info('Retrieved metric configuration facts', {
      facilityId,
      factTypesCount: facts.length,
    });

    return facts;
  }

  getType(): string {
    return MetricConfigService.type;
  }
  /**
   * Updates or creates a metric configuration.
   * @param input The metric configuration data
   * @returns A promise that resolves to the result with status information
   * @throws IctError.notFound if no facility is found in context
   */
  async updateOrCreateMetricConfig(
    input: MetricConfigDetail,
  ): Promise<CustomMetricConfigSaveResult> {
    this.logger.info('Updating or creating metric configuration', {
      metricConfigName: input.metricConfigName,
      configType: input.configType,
    });

    // Get tenant ID and facility ID from context using shared utility
    const tenantId = FacilityUtils.resolveTenantId(this.context, this.logger);
    const facilityId = FacilityUtils.resolveFacilityId(
      this.context,
      this.logger,
    );

    const oldMetricConfig = await this.getMetricConfigDetail(
      input.metricConfigName,
    );

    const result = await this.metricConfigStore.putMetricConfig(
      input,
      tenantId,
      facilityId,
    );

    this.logger.info('Metric configuration saved successfully', {
      id: result.config.id,
      isNew: result.isNew,
      metricConfigName: input.metricConfigName,
      facilityId,
    });

    // remove keys associated with the old metric, if applicable
    // only try and remove the keys if the config was actually used
    if (oldMetricConfig.custom && oldMetricConfig.custom.enabled) {
      const deletedKeysCount =
        await this.metricConfigStore.deleteMetricConfigKeys(
          oldMetricConfig.custom,
          tenantId,
          facilityId,
        );

      this.logger.info(
        `Deleted ${deletedKeysCount} keys associated with the previous custom metric config for ${input.metricConfigName}`,
      );
    } else if (oldMetricConfig.default && oldMetricConfig.default.enabled) {
      const deletedKeysCount =
        await this.metricConfigStore.deleteMetricConfigKeys(
          oldMetricConfig.default,
          tenantId,
          facilityId,
        );

      this.logger.info(
        `Deleted ${deletedKeysCount} keys associated with the previous default metric config for ${input.metricConfigName}`,
      );
    }

    return result;
  }
}
