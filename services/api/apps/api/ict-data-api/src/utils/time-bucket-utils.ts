import {DateTimeGranularity} from 'ict-api-foundations';
import {DataQueryProperty} from 'ict-api-schema';

export class TimeBucketUtils {
  static determineAutoTimeBucketGranularity(
    property: DataQueryProperty, // expects this to be the property with the runtime value
    queryParams: Record<string, unknown>,
  ): DateTimeGranularity {
    // fallback variable to use if we run into any issues
    let defaultGranularity: DateTimeGranularity = 'DAY';
    // First, double check that the property type is time_bucket
    if (property.type !== 'time_bucket') {
      return defaultGranularity;
    }

    // Then, check the query params for the start_date and end_date. If they don't exist, return DAY
    const startDateParam = queryParams.start_date;
    const endDateParam = queryParams.end_date;

    if (!startDateParam || !endDateParam) {
      return defaultGranularity;
    }

    // Parse the dates - they should be ISO 8601 strings
    let startDate: Date;
    let endDate: Date;

    try {
      startDate = new Date(String(startDateParam));
      endDate = new Date(String(endDateParam));

      // Validate that the dates are valid
      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
        return defaultGranularity;
      }
    } catch {
      return defaultGranularity;
    }

    // Calculate the difference between the start_date and end_date in milliseconds, then convert to seconds
    const diffSeconds = Math.floor(
      (endDate.getTime() - startDate.getTime()) / 1000,
    );

    let selectedGranularity: DateTimeGranularity = defaultGranularity;

    // If the difference is less than 2 minutes (120 seconds), return SECOND
    if (diffSeconds < 120) {
      selectedGranularity = 'SECOND';
    }
    // If the difference is less than 2 hours (7200 seconds), return MINUTE
    else if (diffSeconds < 2 * 60 * 60) {
      selectedGranularity = 'MINUTE';
    }
    // If the difference is less than 2 days (172800 seconds), return HOUR
    else if (diffSeconds < 2 * 24 * 60 * 60) {
      selectedGranularity = 'HOUR';
    }
    // If the difference is less than 32 days, return DAY
    else if (diffSeconds < 32 * 24 * 60 * 60) {
      selectedGranularity = 'DAY';
    }
    // If the difference is less than 62 days (2 months), return WEEK
    else if (diffSeconds < 62 * 24 * 60 * 60) {
      selectedGranularity = 'WEEK';
    }
    // If the difference is less than 1 year (approximately 31536000 seconds), return MONTH
    else if (diffSeconds < 365 * 24 * 60 * 60) {
      selectedGranularity = 'MONTH';
    }
    // If the difference is less than 2 years, return QUARTER
    else if (diffSeconds < 2 * 365 * 24 * 60 * 60) {
      selectedGranularity = 'QUARTER';
    }
    // If the difference is greater than 2 years, return YEAR
    else {
      selectedGranularity = 'YEAR';
    }

    return selectedGranularity;
  }
}
