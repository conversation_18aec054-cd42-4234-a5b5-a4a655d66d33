import {expect} from 'chai';
import {TimeBucketUtils} from '../../utils/time-bucket-utils.ts';

describe('determineAutoTimeBucketGranularity', () => {
  const mockTimeBucketProperty = {
    id: 'time_period',
    type: 'time_bucket',
    defaultValue: 'MONTH',
  };

  const mockNonTimeBucketProperty = {
    id: 'limit_field',
    type: 'limit',
    defaultValue: 100,
  };

  it('should return DAY for non-time_bucket property', () => {
    const queryParams = {
      start_date: '2023-01-01T00:00:00Z',
      end_date: '2023-01-07T00:00:00Z',
    };

    const result = TimeBucketUtils.determineAutoTimeBucketGranularity(
      mockNonTimeBucketProperty as any,
      queryParams,
    );

    expect(result).to.equal('DAY');
  });

  it('should return DAY when start_date is missing', () => {
    const queryParams = {
      end_date: '2023-01-07T00:00:00Z',
    };

    const result = TimeBucketUtils.determineAutoTimeBucketGranularity(
      mockTimeBucketProperty as any,
      queryParams,
    );

    expect(result).to.equal('DAY');
  });

  it('should return DAY when end_date is missing', () => {
    const queryParams = {
      start_date: '2023-01-01T00:00:00Z',
    };

    const result = TimeBucketUtils.determineAutoTimeBucketGranularity(
      mockTimeBucketProperty as any,
      queryParams,
    );

    expect(result).to.equal('DAY');
  });

  it('should return DAY when dates are invalid', () => {
    const queryParams = {
      start_date: 'invalid-date',
      end_date: '2023-01-07T00:00:00Z',
    };

    const result = TimeBucketUtils.determineAutoTimeBucketGranularity(
      mockTimeBucketProperty as any,
      queryParams,
    );

    expect(result).to.equal('DAY');
  });

  it('should return SECOND for time difference less than 2 minutes', () => {
    const startDate = '2023-01-01T00:00:00Z';
    const endDate = '2023-01-01T00:01:30Z'; // 90 seconds

    const queryParams = {
      start_date: startDate,
      end_date: endDate,
    };

    const result = TimeBucketUtils.determineAutoTimeBucketGranularity(
      mockTimeBucketProperty as any,
      queryParams,
    );

    expect(result).to.equal('SECOND');
  });

  it('should return MINUTE for time difference less than 2 hours', () => {
    const startDate = '2023-01-01T00:00:00Z';
    const endDate = '2023-01-01T01:30:00Z'; // 1.5 hours

    const queryParams = {
      start_date: startDate,
      end_date: endDate,
    };

    const result = TimeBucketUtils.determineAutoTimeBucketGranularity(
      mockTimeBucketProperty as any,
      queryParams,
    );

    expect(result).to.equal('MINUTE');
  });

  it('should return HOUR for time difference less than 2 days', () => {
    const startDate = '2023-01-01T00:00:00Z';
    const endDate = '2023-01-02T12:00:00Z'; // 1.5 days

    const queryParams = {
      start_date: startDate,
      end_date: endDate,
    };

    const result = TimeBucketUtils.determineAutoTimeBucketGranularity(
      mockTimeBucketProperty as any,
      queryParams,
    );

    expect(result).to.equal('HOUR');
  });

  it('should return DAY for time difference less than 2 weeks', () => {
    const startDate = '2023-01-01T00:00:00Z';
    const endDate = '2023-01-10T00:00:00Z'; // 9 days

    const queryParams = {
      start_date: startDate,
      end_date: endDate,
    };

    const result = TimeBucketUtils.determineAutoTimeBucketGranularity(
      mockTimeBucketProperty as any,
      queryParams,
    );

    expect(result).to.equal('DAY');
  });

  it('should return WEEK for time difference less than 2 months', () => {
    const startDate = '2023-01-01T00:00:00Z';
    const endDate = '2023-02-15T00:00:00Z'; // ~6 weeks

    const queryParams = {
      start_date: startDate,
      end_date: endDate,
    };

    const result = TimeBucketUtils.determineAutoTimeBucketGranularity(
      mockTimeBucketProperty as any,
      queryParams,
    );

    expect(result).to.equal('WEEK');
  });

  it('should return MONTH for time difference less than 1 year', () => {
    const startDate = '2023-01-01T00:00:00Z';
    const endDate = '2023-08-01T00:00:00Z'; // 7 months

    const queryParams = {
      start_date: startDate,
      end_date: endDate,
    };

    const result = TimeBucketUtils.determineAutoTimeBucketGranularity(
      mockTimeBucketProperty as any,
      queryParams,
    );

    expect(result).to.equal('MONTH');
  });

  it('should return QUARTER for time difference less than 2 years', () => {
    const startDate = '2023-01-01T00:00:00Z';
    const endDate = '2024-06-01T00:00:00Z'; // 1.5 years

    const queryParams = {
      start_date: startDate,
      end_date: endDate,
    };

    const result = TimeBucketUtils.determineAutoTimeBucketGranularity(
      mockTimeBucketProperty as any,
      queryParams,
    );

    expect(result).to.equal('QUARTER');
  });

  it('should return YEAR for time difference greater than 2 years', () => {
    const startDate = '2020-01-01T00:00:00Z';
    const endDate = '2023-01-01T00:00:00Z'; // 3 years

    const queryParams = {
      start_date: startDate,
      end_date: endDate,
    };

    const result = TimeBucketUtils.determineAutoTimeBucketGranularity(
      mockTimeBucketProperty as any,
      queryParams,
    );

    expect(result).to.equal('YEAR');
  });

  it('should handle edge case at exactly 2 minutes (120 seconds)', () => {
    const startDate = '2023-01-01T00:00:00Z';
    const endDate = '2023-01-01T00:02:00Z'; // exactly 120 seconds

    const queryParams = {
      start_date: startDate,
      end_date: endDate,
    };

    const result = TimeBucketUtils.determineAutoTimeBucketGranularity(
      mockTimeBucketProperty as any,
      queryParams,
    );

    expect(result).to.equal('MINUTE');
  });

  it('should handle date parsing errors gracefully', () => {
    const queryParams = {
      start_date: '2023-01-01T00:00:00Z',
      end_date: null, // will cause parsing error
    };

    const result = TimeBucketUtils.determineAutoTimeBucketGranularity(
      mockTimeBucketProperty as any,
      queryParams,
    );

    expect(result).to.equal('DAY');
  });
});
