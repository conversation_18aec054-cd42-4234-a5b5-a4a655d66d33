import {expect} from 'chai';
import {IctError} from 'ict-api-foundations';
import {DataQuery} from 'ict-api-schema';
import {QueryParameterUtils} from '../../utils/query-parameter-utils.js';

describe('QueryParameterUtils', () => {
  describe('validateRequiredParameters', () => {
    const mockDataQuery: DataQuery = {
      id: 'test-query',
      label: 'Test Query',
      description: 'Test description',
      parameters: {
        required: ['start_date', 'user_id'],
      },
    } as DataQuery;

    it('should pass validation when all required parameters are present', () => {
      const queryParams = {
        start_date: '2023-01-01',
        user_id: 123,
      };

      expect(() =>
        QueryParameterUtils.validateRequiredParameters(
          mockDataQuery,
          queryParams,
        ),
      ).not.to.throw();
    });

    it('should throw error when required parameters are missing', () => {
      const queryParams = {
        start_date: '2023-01-01',
      };

      expect(() =>
        QueryParameterUtils.validateRequiredParameters(
          mockDataQuery,
          queryParams,
        ),
      ).to.throw(IctError);
    });

    it('should throw error when required parameters are empty strings', () => {
      const queryParams = {
        start_date: '',
        user_id: 123,
      };

      try {
        QueryParameterUtils.validateRequiredParameters(
          mockDataQuery,
          queryParams,
        );
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.be.instanceOf(IctError);
        expect((error as IctError).errorType).to.equal(
          'parameterValidationError',
        );
        expect((error as IctError).message).to.include(
          'Empty required parameters',
        );
      }
    });

    it('should handle data queries with no required parameters', () => {
      const dataQueryNoRequired: DataQuery = {
        id: 'test-query',
        label: 'Test Query',
        description: 'Test description',
      } as DataQuery;

      expect(() =>
        QueryParameterUtils.validateRequiredParameters(dataQueryNoRequired, {}),
      ).not.to.throw();
    });

    it('should handle null and undefined values', () => {
      const queryParams = {
        start_date: null,
        user_id: undefined,
      };

      try {
        QueryParameterUtils.validateRequiredParameters(
          mockDataQuery,
          queryParams,
        );
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.be.instanceOf(IctError);
        expect((error as IctError).message).to.include(
          'Missing required parameters',
        );
      }
    });
  });

  describe('processFilterParameters', () => {
    const mockDataQuery: DataQuery = {
      id: 'test-query',
      label: 'Test Query',
      description: 'Test description',
      filters: ['status', 'priority-level'],
    } as DataQuery;

    it('should process filter parameters correctly', () => {
      const queryParams = {
        status: 'active|pending',
        priority_level: 'high|medium',
      };
      const parameters: Record<string, unknown> = {};

      QueryParameterUtils.processFilterParameters(
        mockDataQuery,
        queryParams,
        parameters,
      );

      expect(parameters.status).to.deep.equal(['active', 'pending']);
      expect(parameters.priority_level).to.deep.equal(['high', 'medium']);
    });

    it('should handle single values without pipes', () => {
      const queryParams = {
        status: 'active',
      };
      const parameters: Record<string, unknown> = {};

      QueryParameterUtils.processFilterParameters(
        mockDataQuery,
        queryParams,
        parameters,
      );

      expect(parameters.status).to.deep.equal(['active']);
    });

    it('should handle empty and undefined filter values', () => {
      const queryParams = {
        status: '',
        priority_level: undefined,
      };
      const parameters: Record<string, unknown> = {};

      QueryParameterUtils.processFilterParameters(
        mockDataQuery,
        queryParams,
        parameters,
      );

      expect(parameters.status).to.be.undefined;
      expect(parameters.priority_level).to.be.undefined;
    });

    it('should handle data queries with no filters', () => {
      const dataQueryNoFilters: DataQuery = {
        id: 'test-query',
        label: 'Test Query',
        description: 'Test description',
      } as DataQuery;

      const queryParams = {status: 'active'};
      const parameters: Record<string, unknown> = {};

      expect(() =>
        QueryParameterUtils.processFilterParameters(
          dataQueryNoFilters,
          queryParams,
          parameters,
        ),
      ).not.to.throw();

      expect(Object.keys(parameters)).to.have.length(0);
    });

    it('should sanitize filter names with hyphens', () => {
      const queryParams = {
        priority_level: 'high|low',
      };
      const parameters: Record<string, unknown> = {};

      QueryParameterUtils.processFilterParameters(
        mockDataQuery,
        queryParams,
        parameters,
      );

      expect(parameters.priority_level).to.deep.equal(['high', 'low']);
    });
  });

  describe('sanitizeParameterName', () => {
    it('should replace hyphens with underscores', () => {
      const result = QueryParameterUtils.sanitizeParameterName('user-id-value');

      expect(result).to.equal('user_id_value');
    });

    it('should handle parameter names without hyphens', () => {
      const result = QueryParameterUtils.sanitizeParameterName('user_id');

      expect(result).to.equal('user_id');
    });

    it('should handle empty strings', () => {
      const result = QueryParameterUtils.sanitizeParameterName('');

      expect(result).to.equal('');
    });
  });

  describe('addStandardParameters', () => {
    it('should add all parameters except dryRun', () => {
      const queryParams = {
        start_date: '2023-01-01',
        user_id: 123,
        dryRun: true,
        category: 'test',
      };
      const parameters: Record<string, unknown> = {};

      QueryParameterUtils.addStandardParameters(queryParams, parameters);

      expect(parameters.start_date).to.equal('2023-01-01');
      expect(parameters.user_id).to.equal(123);
      expect(parameters.category).to.equal('test');
      expect(parameters.dryRun).to.be.undefined;
    });

    it('should handle empty query parameters', () => {
      const queryParams = {};
      const parameters: Record<string, unknown> = {};

      QueryParameterUtils.addStandardParameters(queryParams, parameters);

      expect(Object.keys(parameters)).to.have.length(0);
    });
  });

  describe('extractDryRunFlag', () => {
    it('should return true for string "true"', () => {
      const queryParams = {dryRun: 'true'};

      const result = QueryParameterUtils.extractDryRunFlag(queryParams);

      expect(result).to.be.true;
    });

    it('should return true for boolean true', () => {
      const queryParams = {dryRun: true};

      const result = QueryParameterUtils.extractDryRunFlag(queryParams);

      expect(result).to.be.true;
    });

    it('should return false for string "false"', () => {
      const queryParams = {dryRun: 'false'};

      const result = QueryParameterUtils.extractDryRunFlag(queryParams);

      expect(result).to.be.false;
    });

    it('should return false for boolean false', () => {
      const queryParams = {dryRun: false};

      const result = QueryParameterUtils.extractDryRunFlag(queryParams);

      expect(result).to.be.false;
    });

    it('should return false when dryRun is not present', () => {
      const queryParams = {};

      const result = QueryParameterUtils.extractDryRunFlag(queryParams);

      expect(result).to.be.false;
    });

    it('should return false for other values', () => {
      const queryParams = {dryRun: 'maybe'};

      const result = QueryParameterUtils.extractDryRunFlag(queryParams);

      expect(result).to.be.false;
    });
  });

  describe('validateQueryProperty', () => {
    it('should pass validation for valid time_bucket property with valid granularity', () => {
      const property = {
        id: 'time_period',
        type: 'time_bucket' as const,
        defaultValue: 'MONTH' as const,
      };
      const propertyValue = 'DAY';

      expect(() =>
        QueryParameterUtils.validateQueryProperty(property, propertyValue),
      ).not.to.throw();
    });

    it('should throw error for invalid DataQueryProperty object', () => {
      const invalidProperty = {
        id: 'invalid',
        type: 'invalid_type',
      };

      try {
        QueryParameterUtils.validateQueryProperty(
          invalidProperty as any,
          'value',
        );
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.be.instanceOf(IctError);
        expect((error as IctError).errorType).to.equal(
          'parameterValidationError',
        );
        expect((error as IctError).message).to.include(
          'is not a valid DataQueryProperty',
        );
      }
    });

    it('should throw error for time_bucket with invalid granularity', () => {
      const property = {
        id: 'time_period',
        type: 'time_bucket' as const,
        defaultValue: 'MONTH' as const,
      };
      const invalidValue = 'INVALID_GRANULARITY';

      try {
        QueryParameterUtils.validateQueryProperty(property, invalidValue);
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.be.instanceOf(IctError);
        expect((error as IctError).errorType).to.equal(
          'parameterValidationError',
        );
        expect((error as IctError).message).to.include(
          'is not a valid time bucket',
        );
      }
    });

    it('should pass validation for group_by property with valid field', () => {
      const property = {
        id: 'group_field',
        type: 'group_by' as const,
        groupableFields: ['status', 'category', 'priority'],
        defaultValue: 'status',
      };
      const propertyValue = 'category';

      expect(() =>
        QueryParameterUtils.validateQueryProperty(property, propertyValue),
      ).not.to.throw();
    });

    it('should throw error for group_by property with invalid field', () => {
      const property = {
        id: 'group_field',
        type: 'group_by' as const,
        groupableFields: ['status', 'category'],
        defaultValue: 'status',
      };
      const invalidValue = 'invalid_field';

      try {
        QueryParameterUtils.validateQueryProperty(property, invalidValue);
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.be.instanceOf(IctError);
        expect((error as IctError).errorType).to.equal(
          'parameterValidationError',
        );
        expect((error as IctError).message).to.include(
          'is not a valid group by field',
        );
      }
    });

    it('should pass validation for limit property with valid number', () => {
      const property = {
        id: 'limit_field',
        type: 'limit' as const,
        defaultValue: 100,
      };
      const propertyValue = '50';

      expect(() =>
        QueryParameterUtils.validateQueryProperty(property, propertyValue),
      ).not.to.throw();
    });

    it('should throw error for limit property with invalid number', () => {
      const property = {
        id: 'limit_field',
        type: 'limit' as const,
        defaultValue: 100,
      };
      const invalidValue = 'not_a_number';

      try {
        QueryParameterUtils.validateQueryProperty(property, invalidValue);
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.be.instanceOf(IctError);
        expect((error as IctError).errorType).to.equal(
          'parameterValidationError',
        );
        expect((error as IctError).message).to.include('is not a valid limit');
      }
    });

    it('should throw error for limit property with zero or negative number', () => {
      const property = {
        id: 'limit_field',
        type: 'limit' as const,
        defaultValue: 100,
      };

      const testCases = ['0', '-5', '-1'];

      for (const testValue of testCases) {
        try {
          QueryParameterUtils.validateQueryProperty(property, testValue);
          expect.fail(`Should have thrown an error for value: ${testValue}`);
        } catch (error) {
          expect(error).to.be.instanceOf(IctError);
          expect((error as IctError).errorType).to.equal(
            'parameterValidationError',
          );
          expect((error as IctError).message).to.include(
            'is not a valid limit',
          );
        }
      }
    });

    it('should pass validation for sort property (default case)', () => {
      const property = {
        id: 'sort_field',
        type: 'sort' as const,
        defaultValue: 'ASC' as const,
      };
      const propertyValue = 'DESC';

      expect(() =>
        QueryParameterUtils.validateQueryProperty(property, propertyValue),
      ).not.to.throw();
    });

    it('should test all valid DateTimeGranularity values for time_bucket', () => {
      const property = {
        id: 'time_period',
        type: 'time_bucket' as const,
        defaultValue: 'MONTH' as const,
      };

      const validGranularities = [
        'AUTO',
        'MILLISECOND',
        'SECOND',
        'MINUTE',
        'HOUR',
        'DAY',
        'WEEK',
        'MONTH',
        'QUARTER',
        'YEAR',
      ];

      for (const granularity of validGranularities) {
        expect(() =>
          QueryParameterUtils.validateQueryProperty(property, granularity),
        ).not.to.throw(`Should not throw for granularity: ${granularity}`);
      }
    });
  });
});
