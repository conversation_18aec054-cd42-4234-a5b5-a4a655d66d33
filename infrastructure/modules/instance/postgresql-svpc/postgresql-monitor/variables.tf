variable "project_id" {
  description = "The ID of the  GCP project with the PostgreSQL instance."
  type        = string
}

variable "instance_name" {
  description = "The name of the PostgreSQL instance to create monitoring for."
  type        = string
}

variable "notification_email" {
  description = <<EOT
    The email address to send alerts to when a monitoring 
    threshold is crossed.
    EOT
  type        = string
}

# Performance Monitoring Configuration
variable "postgres_performance_monitoring_config" {
  description = "Configuration for PostgreSQL performance monitoring thresholds"
  type = object({
    cpu_threshold_percent     = number
    deadlock_threshold_count  = number
    max_connections_threshold = number
    memory_threshold_percent  = number
    slow_query_threshold_ms   = number
    transactions_per_second   = number
  })
}

variable "postgres_alerting_config" {
  description = "Configuration for PostgreSQL performance alerting"
  type = object({
    enable_connection_alerts       = bool
    enable_cpu_alerts              = bool
    enable_memory_alerts           = bool
    enable_connection_count_alerts = bool
    enable_tps_alerts              = bool
    enable_deadlock_alerts         = bool
    connectivity_alert_duration    = string
    alert_duration_threshold       = string
    alert_evaluation_period        = string
    alert_auto_close_duration      = string
  })
}


variable "postgres_instance_ip_address" {
  description = <<EOT
    The IP address of the PostgreSQL instance, required for connectivity checks
  EOT
  type        = string
  validation {
    condition     = var.postgres_instance_ip_address != null && var.postgres_instance_ip_address != ""
    error_message = "PostgreSQL instance IP address cannot be null or empty."
  }
}
variable "common_resource_labels" {
  description = <<EOT
    The common labels that should be applied to all resources that can be labeled.
  EOT

  type = map(string)
}
