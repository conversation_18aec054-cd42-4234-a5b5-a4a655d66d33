# Notification Channel (always created for potential use)
resource "google_monitoring_notification_channel" "email" {
  project      = var.project_id
  display_name = "PostgreSQL alerts - ${var.instance_name}"
  type         = "email"

  labels = {
    email_address = var.notification_email
  }

  user_labels = var.common_resource_labels
}

resource "google_monitoring_alert_policy" "cpu_utilization" {
  count        = var.postgres_alerting_config.enable_cpu_alerts ? 1 : 0
  combiner     = "OR"
  project      = var.project_id
  display_name = "PostgreSQL High CPU Usage - ${var.instance_name}"

  enabled = var.postgres_alerting_config.enable_cpu_alerts
  alert_strategy {
    auto_close = var.postgres_alerting_config.alert_auto_close_duration
  }
  conditions {
    display_name = "CPU utilization threshold exceeded"
    condition_threshold {
      filter          = "resource.type=\"cloudsql_database\" AND resource.labels.database_id=\"${var.project_id}:${var.instance_name}\" AND metric.type=\"cloudsql.googleapis.com/database/cpu/utilization\""
      comparison      = "COMPARISON_GT"
      threshold_value = var.postgres_performance_monitoring_config.cpu_threshold_percent / 100
      duration        = var.postgres_alerting_config.alert_duration_threshold

      aggregations {
        alignment_period   = var.postgres_alerting_config.alert_evaluation_period
        per_series_aligner = "ALIGN_MEAN"
      }
    }
  }

  notification_channels = [google_monitoring_notification_channel.email.id]
  user_labels           = var.common_resource_labels

}

# Memory Utilization Alert
resource "google_monitoring_alert_policy" "memory_utilization" {
  count        = var.postgres_alerting_config.enable_memory_alerts ? 1 : 0
  combiner     = "OR"
  project      = var.project_id
  display_name = "PostgreSQL High Memory Usage - ${var.instance_name}"

  enabled = var.postgres_alerting_config.enable_memory_alerts
  alert_strategy {
    auto_close = var.postgres_alerting_config.alert_auto_close_duration
  }
  conditions {
    display_name = "Memory utilization threshold exceeded"
    condition_threshold {
      filter          = "resource.type=\"cloudsql_database\" AND resource.labels.database_id=\"${var.project_id}:${var.instance_name}\" AND metric.type=\"cloudsql.googleapis.com/database/memory/utilization\""
      comparison      = "COMPARISON_GT"
      threshold_value = var.postgres_performance_monitoring_config.memory_threshold_percent / 100
      duration        = var.postgres_alerting_config.alert_duration_threshold

      aggregations {
        alignment_period   = var.postgres_alerting_config.alert_evaluation_period
        per_series_aligner = "ALIGN_MEAN"
      }
    }
  }

  notification_channels = [google_monitoring_notification_channel.email.id]
  user_labels           = var.common_resource_labels
}

resource "google_monitoring_alert_policy" "transactions_per_second" {
  count        = var.postgres_alerting_config.enable_tps_alerts ? 1 : 0
  combiner     = "OR"
  project      = var.project_id
  display_name = "PostgreSQL High TPS - ${var.instance_name}"

  enabled = var.postgres_alerting_config.enable_tps_alerts

  alert_strategy {
    auto_close = var.postgres_alerting_config.alert_auto_close_duration
  }
  conditions {
    display_name = "TPS second threshold exceeded"
    condition_threshold {
      filter          = "resource.type=\"cloudsql_database\" AND resource.labels.database_id=\"${var.project_id}:${var.instance_name}\" AND metric.type=\"cloudsql.googleapis.com/database/postgresql/transaction_count\""
      comparison      = "COMPARISON_GT"
      threshold_value = var.postgres_performance_monitoring_config.transactions_per_second
      duration        = var.postgres_alerting_config.alert_duration_threshold

      aggregations {
        alignment_period   = var.postgres_alerting_config.alert_evaluation_period
        per_series_aligner = "ALIGN_MEAN"
      }
    }
  }

  notification_channels = [google_monitoring_notification_channel.email.id]
  user_labels           = var.common_resource_labels
}

resource "google_monitoring_alert_policy" "database_connections" {
  count        = var.postgres_alerting_config.enable_connection_count_alerts ? 1 : 0
  combiner     = "OR"
  project      = var.project_id
  display_name = "PostgreSQL Connections Exceed threshold - ${var.instance_name}"

  enabled = var.postgres_alerting_config.enable_connection_count_alerts
  alert_strategy {
    auto_close = var.postgres_alerting_config.alert_auto_close_duration
  }
  conditions {
    display_name = "High number of active connections to the database"

    condition_threshold {
      filter          = "resource.type=\"cloudsql_database\" AND resource.labels.database_id=\"${var.project_id}:${var.instance_name}\" AND metric.type=\"cloudsql.googleapis.com/database/postgresql/num_backends\""
      comparison      = "COMPARISON_GT"
      threshold_value = var.postgres_performance_monitoring_config.max_connections_threshold * 0.8 # 80% of max connections
      duration        = var.postgres_alerting_config.alert_duration_threshold

      aggregations {
        alignment_period   = var.postgres_alerting_config.alert_evaluation_period
        per_series_aligner = "ALIGN_MEAN"
      }
    }
  }

  notification_channels = [google_monitoring_notification_channel.email.id]
  user_labels           = var.common_resource_labels
}


resource "google_monitoring_alert_policy" "deadlocks" {
  count        = var.postgres_alerting_config.enable_deadlock_alerts ? 1 : 0
  combiner     = "OR"
  project      = var.project_id
  display_name = "PostgreSQL Deadlocks Detected - ${var.instance_name}"

  enabled = var.postgres_alerting_config.enable_deadlock_alerts

  alert_strategy {
    auto_close = var.postgres_alerting_config.alert_auto_close_duration
  }
  conditions {
    display_name = "Deadlock count threshold exceeded"
    condition_threshold {
      filter          = "resource.type=\"cloudsql_database\" AND resource.labels.database_id=\"${var.project_id}:${var.instance_name}\" AND metric.type=\"cloudsql.googleapis.com/database/postgresql/deadlock_count\""
      comparison      = "COMPARISON_GT"
      threshold_value = var.postgres_performance_monitoring_config.deadlock_threshold_count
      duration        = "0s"

      aggregations {
        alignment_period   = var.postgres_alerting_config.alert_evaluation_period
        per_series_aligner = "ALIGN_RATE"
      }
    }
  }

  notification_channels = [google_monitoring_notification_channel.email.id]
  user_labels           = var.common_resource_labels

}



