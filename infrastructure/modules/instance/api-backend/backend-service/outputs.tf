output "backend_service_id" {
  description = "The ID of the load balancer backend service that was created for this API service."
  value       = google_compute_backend_service.main.name
}

output "basic_health_check_path" {
  description = "The endpoint for the basic health check."
  value       = var.basic_health_check_path
}

output "full_health_check_path" {
  description = "The endpoint for the full health check."
  value       = var.full_health_check_path
}

output "service_account_email" {
  description = "The email of the Service Account created for the API backend"
  value       = google_service_account.main.email
}

output "service_friendly_name" {
  description = "The friendly name of the service exposed by the API endpoint."

  value = var.service_friendly_name
}

output "service_name" {
  description = "The name of the service that was created."
  value       = var.service_name
}

output "service_full_name" {
  description = "The name of the Cloud Run service instance created for this API backend"
  value       = google_cloud_run_v2_service.main.name
}

output "url_map_path" {
  description = "The path to register for this backend service on the URL map."
  value       = var.url_map_path
}

output "enable_uptime_alert_policy" {
  description = "Whether uptime alert policies are enabled for this backend service"
  value       = var.enable_uptime_alert_policy
}
