output "backend_services" {
  description = "The list of basic backend services created."
  value = tomap({
    for api in module.backend_services :
    api.service_name => {
      backend_service_friendly_name = api.service_friendly_name
      backend_service_id            = api.backend_service_id
      backend_service_name          = api.service_name
      basic_health_check_path       = api.basic_health_check_path
      enable_uptime_alert_policy    = api.enable_uptime_alert_policy
      full_health_check_path        = api.full_health_check_path
      service_account_email         = api.service_account_email
      url_map_path                  = api.url_map_path
    }
  })
}

output "default_service_id" {
  description = <<EOT
    The ID of the backend service that should serve as the default backend for the API. This will 
    be the ID of the service named in the `default_service_name` variable.
  EOT

  value = merge(
    module.backend_services,
    module.storage_enabled_backend_services
  )[var.default_service_name].backend_service_id
}

output "storage_enabled_backend_services" {
  description = "The list of storage-enabled backend services created."
  value = tomap({
    for api in module.storage_enabled_backend_services :
    api.service_name => {
      backend_service_friendly_name = api.service_friendly_name
      backend_service_id            = api.backend_service_id
      backend_service_name          = api.service_name
      basic_health_check_path       = api.basic_health_check_path
      bucket_url                    = api.bucket_url
      enable_uptime_alert_policy    = api.enable_uptime_alert_policy
      full_health_check_path        = api.full_health_check_path
      service_account_email         = api.service_account_email
      url_map_path                  = api.url_map_path
    }
  })
}
