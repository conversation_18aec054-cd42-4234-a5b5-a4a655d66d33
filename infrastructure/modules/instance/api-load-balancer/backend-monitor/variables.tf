variable "alerting_condition_threshold_aggregation_alignment_period" {
  description = <<EOF
    The alignment period for the alerting condition threshold. This is the time period over which 
    the data is aggregated.
  EOF

  type    = string
  default = null
}

variable "alerting_condition_threshold_comparison" {
  description = <<EOF
    The comparison operator for the alerting condition threshold. This is used to determine when 
    the alert should be triggered.
  EOF

  type    = string
  default = null
}

variable "alerting_condition_threshold_duration" {
  description = <<EOF
    The duration for the alerting condition threshold. This is the time period over which the 
    condition is evaluated.
  EOF

  type    = string
  default = null
}

variable "alerting_condition_threshold_trigger_count" {
  description = <<EOF
    The count for the alerting condition threshold trigger. This is the number of times the 
    condition must be met before the alert is triggered.
  EOF

  type    = number
  default = null
}

variable "basic_health_check_path" {
  description = "The relative path of the basic health check endpoint in the container."
  type        = string
}

variable "basic_uptime_check_period" {
  description = <<EOF
    The period for the basic uptime check. This is the frequency at which the uptime check will be 
    performed.
  EOF

  type = string
}

variable "basic_uptime_check_timeout" {
  description = <<EOF
    The timeout for the basic uptime check. This is the maximum time to wait for a response from 
    the monitored resource.
  EOF

  type = string
}


variable "common_resource_labels" {
  description = <<EOT
    The common labels that should be applied to all resources that can be labeled.
  EOT

  type = map(string)
}

variable "dns_name" {
  description = <<EOF
    The DNS name (the name of the load balancer's A record resource) which will be set as the 
    monitored resource for the uptime check.
  EOF

  type = string
}

variable "enable_alerting" {
  description = <<EOF
    Whether to enable alerting for the api load balancer. This should be set to true if you want to
    receive alerts when the monitored API exceeds error or latency thresholds. 
  EOF

  type = bool
}
variable "enable_uptime_alert_policy" {
  description = <<EOF
    Whether to enable alerting for the uptime check. This should be set to true if you want to
    receive alerts when the monitored resource is down. If enabled, the alert will be wired up to
    the full uptime check.
  EOF

  type = bool
}

variable "enforce_unique_naming" {
  description = <<EOF
    Set this to true to use the provided 'environment` value to create a unique prefix for all 
    resources in the module. This is required to allow multiple instances of the module to be 
    deployed in the same GCP project. Used by Review Apps to generate the preview environments.
  EOF

  type = bool
}

variable "full_health_check_path" {
  description = "The relative path of the full health check endpoint in the container."
  type        = string
}

variable "full_uptime_check_period" {
  description = <<EOF
    The period for the full uptime check. This is the frequency at which the uptime check will be 
    performed.
  EOF

  type = string
}

variable "full_uptime_check_timeout" {
  description = <<EOF
    The timeout for the full uptime check. This is the maximum time to wait for a response from the 
    monitored resource.
  EOF

  type = string
}

variable "health_check_port" {
  description = "The port to use for the health check."
  type        = number
}

variable "instance_display_name" {
  description = <<EOT
    The human-readable name of the instance that the resources are being deployed to.
  EOT

  type = string
}

variable "instance_prefix" {
  description = <<EOT
    The name of the instance that the resources are being deployed to. This value will be 
    combined with the `naming_root` to create a unique prefix for any resources requiring a 
    globally-unique identifier. Therefore, it must be unique among Control Tower instances but 
    should be as short as possible to comply with GCP resource name length restrictions.
  EOT

  type = string
}

variable "notification_email_address" {
  description = <<EOT
    The email address to send alerts to when a monitoring 
    threshold is crossed.
    EOT
  type        = string
}

variable "project_id" {
  description = "The ID of the GCP project to deploy to."
  type        = string
}

variable "service_friendly_name" {
  description = <<EOF
    The "friendly" name for the service being monitored, for use in the display names of the uptime 
    check and alert policy. Example: "Equipment API"
  EOF

  type = string
}

variable "service_url_map_path" {
  description = <<EOF
    The path on the URL map that the service will be registered under. This is used for 
    configuring the uptime check.
  EOF

  type = string
}

variable "uptime_check_regions" {
  description = <<EOF
    The regions from which the uptime check will be performed. If not specified, the uptime check 
    will only run from the primary region.
  EOF

  type    = list(string)
  default = null
}

variable "use_ssl" {
  description = <<EOF
    Whether to use SSL for the uptime check. This should be set to true if the monitored resource 
    uses HTTPS.
  EOF

  type = bool
}

variable "validate_ssl" {
  description = <<EOF
    Whether to validate SSL for the uptime check. This should be set to true if the monitored 
    resource uses HTTPS and you want to validate the SSL certificate.
  EOF

  type = bool
}
