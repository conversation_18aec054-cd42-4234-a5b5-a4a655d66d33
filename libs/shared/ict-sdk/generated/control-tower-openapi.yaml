openapi: 3.0.3
info:
  title: Control Tower Api
  version: 1.0.0
  description: A description of endpoints that define the Control Tower api
  license:
    name: ISC
  contact:
    name: Dematic
servers:
  - url: 'http://localhost:8080/'
    description: Local development
  - url: 'https://run-ict-d-api.ict.dematic.dev'
    description: Dev development
  - url: 'https://stage.api.ict.dematic.dev'
    description: Staging
  - url: 'https://api.ict.dematic.cloud'
    description: Production
paths:
  /ai/healthcheck:
    get:
      operationId: GetAiBasicHealthCheck
      responses:
        '200':
          description: Ok
          content:
            application/json:
              schema:
                properties:
                  sha:
                    type: string
                  status:
                    $ref: '#/components/schemas/HealthCheckStatus'
                required:
                  - sha
                  - status
                type: object
      tags:
        - ai
      security: []
      parameters: []
  /ai/healthcheck/full:
    get:
      operationId: GetAiFullHealthCheck
      responses:
        '200':
          description: Ok
          content:
            application/json:
              schema:
                properties:
                  subSystems:
                    properties:
                      redis:
                        $ref: '#/components/schemas/HealthCheckStatus'
                      bigQuery:
                        $ref: '#/components/schemas/HealthCheckStatus'
                      auth0:
                        $ref: '#/components/schemas/HealthCheckStatus'
                    required:
                      - redis
                      - bigQuery
                      - auth0
                    type: object
                  sha:
                    type: string
                  status:
                    $ref: '#/components/schemas/HealthCheckStatus'
                required:
                  - subSystems
                  - sha
                  - status
                type: object
      tags:
        - ai
      security: []
      parameters: []
  /ai/enterprise-search:
    get:
      operationId: GetAiEnterpriseSearch
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EnterpriseSearchResponse'
              examples:
                Example 1:
                  value:
                    document:
                      derivedStructData:
                        extractive_answers:
                          - content: >-
                              • Any time shoe caps are broken off due to a
                              product jam condition. As required Remove the
                              divert switch to inspect.
                            pageNumber: '100'
                        link: >-
                          gs://nlp-test-documents/sorter/3FA7C4BF-C88D-4319-C8E3A1C1CE4C647F.pdf
                      id: b19cd3570e7492dea637d8694671a7a9
                      name: >-
                        projects/503659053462/locations/global/collections/default_collection/dataStores/test-search_1689888123067/branches/0/documents/b19cd3570e7492dea637d8694671a7a9
                    id: b19cd3570e7492dea637d8694671a7a9
      tags:
        - ai
      security: []
      parameters:
        - in: query
          name: searchText
          required: true
          schema:
            type: string
            minLength: 3
          example: What is a pin fault?
  /config/user-writable:
    put:
      operationId: PutUserWritable
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AppConfigSetting'
              examples:
                Example 1:
                  value:
                    id: 0e0f42b-1b58-4a5c-a785-5e6af
                    name: user-favorites
                    group: user-writable
                    dataType: json
                    value:
                      favorites:
                        - route-1
                        - route-2
                    source: user
        '201':
          description: New setting created
      description: >-
        Updates a user writable setting. This endpoint is specifically for
        user-level settings.

        Any authenticated user can update their own settings, and configurators
        can update any user's settings.
      tags:
        - config
      security: []
      parameters: []
      requestBody:
        description: >-
          Identifying information about the requested Setting resource, as well
          as requested updates.

          The setting must be in the 'user-writable' group and will always be
          updated at the user level.

          If the user is a ct_configurator, they may specify a userId to update
          another user's setting.
        required: true
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/UpdateSettingData'
                - properties:
                    userId:
                      type: string
                  type: object
              description: >-
                Identifying information about the requested Setting resource, as
                well as requested updates.

                The setting must be in the 'user-writable' group and will always
                be updated at the user level.

                If the user is a ct_configurator, they may specify a userId to
                update another user's setting.
  /config/process-flow/metric-configs:
    get:
      operationId: GetMetricConfigSummary
      responses:
        '200':
          description: Returns a list of metric configurations
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/MetricConfigSummary'
                type: array
              examples:
                Example 1:
                  value:
                    - id: '1'
                      metricName: test-metric
                      configType: node
                      nodeName: test-node
                      factType: test-fact
                      enabled: true
                      active: true
                      isCustom: false
                      facilityId: default
                      views:
                        - facility
                        - multishuttle
        '404':
          description: No metric configurations found matching the criteria
        '500':
          description: Internal server error
      description: >-
        Get metric configurations based on filter criteria.

        Without any query params, returns all metric configs that are not
        soft_deleted.
      tags:
        - config
      security: []
      parameters:
        - in: query
          name: metric_name
          required: false
          schema:
            type: string
        - in: query
          name: metric_id
          required: false
          schema:
            type: string
        - in: query
          name: fact_type
          required: false
          schema:
            type: string
        - in: query
          name: node_name
          required: false
          schema:
            type: string
        - description: Filter by active status
          in: query
          name: active
          required: false
          schema:
            type: boolean
        - description: Filter by enabled status
          in: query
          name: enabled
          required: false
          schema:
            type: boolean
        - in: query
          name: config_type
          required: false
          schema:
            type: string
  '/config/process-flow/metric-config/{metricName}':
    get:
      operationId: GetMetricConfig
      responses:
        '200':
          description: Metric configuration retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MetricConfigValueResponse'
              examples:
                Example 1:
                  value:
                    default:
                      metricConfigName: test-metric
                      configType: node
                      views:
                        - facility
                      matchConditions:
                        eventType: test-event
                      factType: test-fact
                      sourceSystem: diq
                      displayName: Test Metric
                      enabled:
                        facility-1: true
                      active:
                        facility-1: true
                      isCustom: false
                      nodeName: test-node
                      metricType: stockTime
                      timeWindow: 60m_set
                      aggregation: sum
                      redisOperation: event_set
                      graphOperation: area_node
                      metricUnits: /hr
                    custom:
                      metricConfigName: test-metric
                      configType: node
                      views:
                        - facility
                      matchConditions:
                        eventType: test-event
                      factType: test-fact
                      sourceSystem: diq
                      displayName: Test Metric Custom
                      enabled: true
                      active: true
                      isCustom: true
                      nodeName: test-node
                      metricType: stockTime
                      timeWindow: 60m_set
                      aggregation: sum
                      redisOperation: event_set
                      graphOperation: area_node
                      metricUnits: /hr
                      description: Example metric configuration for testing
        '204':
          description: No metric configuration found
        '500':
          description: Internal server error
      description: |-
        Get a single metric configuration by name.
        Returns both default and custom configurations if they exist.
        If config_type is specified, only returns that type.
      tags:
        - config
      security: []
      parameters:
        - description: The name of the metric configuration to retrieve
          in: path
          name: metricName
          required: true
          schema:
            type: string
        - description: Optional filter for 'default' or 'custom' configurations only
          in: query
          name: config_type
          required: false
          schema:
            type: string
            enum:
              - default
              - custom
  /config/process-flow/metric-config:
    put:
      operationId: PutMetricConfig
      responses:
        '200':
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomMetricConfigurationEntity'
              examples:
                Example 1:
                  value:
                    id: '1'
                    metricConfigName: test-metric
                    configType: node
                    views:
                      - facility
                      - multishuttle
                    matchConditions:
                      eventType: test-event
                    factType: test-fact
                    sourceSystem: diq
                    displayName: Test Metric
                    description: Example metric configuration for testing
                    enabled: true
                    active: true
                    facilityId: facility-1
                    nodeName: test-node
                    metricType: stockTime
                    timeWindow: 60m_set
                    aggregation: sum
                    redisOperation: event_set
                    graphOperation: area_node
                    metricUnits: /hr
                    createdAt: '2024-01-01T00:00:00.000Z'
                    updatedAt: '2024-01-01T00:00:00.000Z'
        '201':
          description: Metric configuration created successfully
        '400':
          description: Invalid metric configuration data
      description: >-
        Updates or creates a metric configuration.

        If a configuration with the same metric name and facility already
        exists, it will be updated.

        Otherwise, a new configuration will be created.

        The facility ID is determined from the ict-facility-id header.
      tags:
        - config
      security: []
      parameters: []
      requestBody:
        description: The metric configuration data
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MetricConfigDetail'
              description: The metric configuration data
  /config/process-flow/metric-configs/facts:
    get:
      operationId: GetMetricConfigFacts
      responses:
        '200':
          description: Returns metric configuration facts with statistics
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/MetricConfigFact'
                type: array
              examples:
                Example 1:
                  value:
                    - factType: multishuttle_movement
                      totalConfigs: 5
                      enabledConfigs: 3
                      active: true
                    - factType: fault_event
                      totalConfigs: 2
                      enabledConfigs: 1
                      active: false
        '500':
          description: Internal server error
      description: >-
        Get statistics about metric configurations grouped by fact type.

        Returns aggregated statistics about metric configurations grouped by
        fact type.

        The facility ID is determined from the ict-facility-id header.
      tags:
        - config
      security: []
      parameters: []
  /config/healthcheck:
    get:
      operationId: GetConfigBasicHealthCheck
      responses:
        '200':
          description: Ok
          content:
            application/json:
              schema:
                properties:
                  sha:
                    type: string
                  status:
                    $ref: '#/components/schemas/HealthCheckStatus'
                required:
                  - sha
                  - status
                type: object
      tags:
        - config
      security: []
      parameters: []
  /config/healthcheck/full:
    get:
      operationId: GetConfigFullHealthCheck
      responses:
        '200':
          description: Ok
          content:
            application/json:
              schema:
                properties:
                  subSystems:
                    properties:
                      redis:
                        $ref: '#/components/schemas/HealthCheckStatus'
                      bigQuery:
                        $ref: '#/components/schemas/HealthCheckStatus'
                      auth0:
                        $ref: '#/components/schemas/HealthCheckStatus'
                    required:
                      - redis
                      - bigQuery
                      - auth0
                    type: object
                  sha:
                    type: string
                  status:
                    $ref: '#/components/schemas/HealthCheckStatus'
                required:
                  - subSystems
                  - sha
                  - status
                type: object
      tags:
        - config
      security: []
      parameters: []
  /config/settings:
    get:
      operationId: GetConfigSettings
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AppConfigSettingArrayOrAppConfigSetting'
              examples:
                Example 1:
                  value:
                    - id: 0e0f42b-1b58-4a5c-a785-5e6af
                      name: available-packages
                      group: app-configuration
                      dataType: json
                      value:
                        test: true
                      source: tenant
      description: >-
        Retrieves all settings and their values or a single setting based on id
        and/or name
      tags:
        - config
      security: []
      parameters:
        - description: >-
            Id of the setting to retrieve. Optional, if this and settingName are
            both omitted, all settings will be returned.
          in: query
          name: settingId
          required: false
          schema:
            type: string
        - description: >-
            Name of the setting to retrieve. Optional, if this and settingName
            are both omitted, all settings will be returned.

            If both setting id and name are provided, only settings matching
            BOTH parameters will be returned.
          in: query
          name: settingName
          required: false
          schema:
            type: string
        - description: >-
            Level of setting to be returned (user, tenant, default). Optional,
            defaults to all levels.

            Only used when a setting id and/or name is provided.
          in: query
          name: settingType
          required: false
          schema:
            $ref: '#/components/schemas/AppConfigSettingSource'
        - description: >-
            If true, will fetch the default, tenant, and user setting values for
            a setting.

            If false, only the most specific stored value is returned; for
            example, if there is a tenant level value and a

            user level value applicable to the given user, then only the user
            level value is returned. Optional, defaults to false.
          in: query
          name: allStoredValues
          required: false
          schema:
            type: boolean
        - description: >-
            filter on settings that are a part of the specified group name. Only
            used when a setting id and/or name is provided.
          in: query
          name: group
          required: false
          schema:
            type: string
    put:
      operationId: PutConfigSettings
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AppConfigSetting'
              examples:
                Example 1:
                  value:
                    id: 0e0f42b-1b58-4a5c-a785-5e6af
                    name: available-packages
                    group: app-configuration
                    dataType: json
                    value:
                      test: true
                    source: tenant
        '201':
          description: New setting created
      description: >-
        Updates the specified setting with the properties in settingInfo. 
        Creates a new setting if

        the setting isn't found, and the required information is provided.
      tags:
        - config
      security:
        - all-roles:
            - ct_configurator
      parameters: []
      requestBody:
        description: >-
          Identifying information about the requested Setting resource, as well
          as requested updates.<br><br>

          <b>id</b> - the setting Id.  ID is required in order to update an
          existing setting, but not to add a new one<br>

          <b>name</b> - the name of the setting.  Required.<br>

          <b>dataType</b> - one of: 'json', 'string', 'boolean', or 'number'<br>

          <b>group</b> - the Setting group.  This value is updated if it is
          different.<br>

          <b>value</b> - the value to update the setting with. Can be any
          value<br>

          <b>levelToUpdate</b> - one of: 'default', 'tenant', 'site', or 'user'.
          This determines the appropriate value to change.<br>

          <b>description</b> - optional string description of the setting.<br>
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateSettingData'
              description: >-
                Identifying information about the requested Setting resource, as
                well as requested updates.<br><br>

                <b>id</b> - the setting Id.  ID is required in order to update
                an existing setting, but not to add a new one<br>

                <b>name</b> - the name of the setting.  Required.<br>

                <b>dataType</b> - one of: 'json', 'string', 'boolean', or
                'number'<br>

                <b>group</b> - the Setting group.  This value is updated if it
                is different.<br>

                <b>value</b> - the value to update the setting with. Can be any
                value<br>

                <b>levelToUpdate</b> - one of: 'default', 'tenant', 'site', or
                'user'. This determines the appropriate value to change.<br>

                <b>description</b> - optional string description of the
                setting.<br>
  /config/insert-default-config:
    post:
      operationId: PostConfigDefaultConfig
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DefaultConfigSeedingResult'
              examples:
                Example 1:
                  value:
                    successful:
                      - id: 0e0f42b-1b58-4a5c-a785-5e6af
                        name: available-packages
                        group: app-configuration
                        dataType: json
                        value:
                          test: true
                        source: tenant
                    unsuccessful:
                      - id: 0e0f42b-1b58-4a5c-a785-5e6af
                        name: available-packages
                        group: app-configuration
                        dataType: json
                        value:
                          test: true
                        source: tenant
                    existing:
                      - id: 0e0f42b-1b58-4a5c-a785-5e6af
                        name: available-packages
                        group: app-configuration
                        dataType: json
                        value:
                          test: true
                        source: tenant
      description: Inserts the default settings to seed a new tenant's database
      tags:
        - config
      security: []
      parameters: []
  '/config/settings/{settingId}':
    delete:
      operationId: DeleteConfigSetting
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeletedRecordsResult'
              examples:
                Example 1:
                  value:
                    recordsDeleted: 3
                    message: 'Deleted tenant, user, and default setting.'
      tags:
        - config
      security:
        - all-roles:
            - ct_configurator
      parameters:
        - description: setting ID to delete
          in: path
          name: settingId
          required: true
          schema:
            type: string
        - description: >-
            specific record to delete.  If blank, all stored setting values
            associated with the setting ID are deleted
          in: query
          name: levelToDelete
          required: false
          schema:
            $ref: '#/components/schemas/AppConfigSettingSource'
        - in: query
          name: force
          required: false
          schema:
            default: false
            type: boolean
  /config/settings/schema:
    get:
      operationId: getSettingSchema
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema: {}
              examples:
                Example 1:
                  value:
                    $schema: 'http://json-schema.org/draft-07/schema#'
                    title: Generated schema for Root
                    type: object
                    properties:
                      suffixes:
                        type: array
                        items:
                          type: string
                    required:
                      - suffixes
      tags:
        - config
      security:
        - any-roles:
            - ct_configurator
            - ct_engineers
      parameters:
        - in: query
          name: settingId
          required: false
          schema:
            type: string
        - in: query
          name: settingName
          required: false
          schema:
            type: string
  /config/setting-logs:
    get:
      operationId: GetConfigSettingLogs
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AppConfigSettingLogsData'
              examples:
                Example 1:
                  value:
                    data:
                      - changedBy: testUser
                        timestamp: '2024-08-14T12:26:29.001Z'
                        source: user
                        newValue: 'false'
                      - changedBy: testUser
                        timestamp: '2024-08-14T12:26:28.261Z'
                        source: user
                        newValue: '33'
                      - changedBy: testUser
                        timestamp: '2024-08-14T12:26:27.557Z'
                        source: tenant
                        newValue: testStringValue
                      - changedBy: testUser
                        timestamp: '2024-08-14T12:26:26.933Z'
                        source: tenant
                        newValue: '{json data}'
      description: Retrieves logs for a setting
      tags:
        - config
      security: []
      parameters:
        - description: Id of the setting being searched for
          in: query
          name: setting_id
          required: true
          schema:
            type: string
        - description: |-
            maximum number of logs that may be returned
            (if 0, all logs will be returned)
          in: query
          name: limit
          required: false
          schema:
            default: 0
            format: double
            type: number
  /config/facility-config:
    get:
      operationId: GetConfigFacilityConfig
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FacilityConfigSettings'
              examples:
                Example 1:
                  value:
                    - id: 0e0f42b-1b58-4a5c-a785-5e6af
                      name: available-packages
                      group: app-configuration
                      dataType: json
                      value:
                        test: true
                      source: tenant
      tags:
        - config
      security: []
      parameters: []
  /config/curated-data:
    get:
      operationId: GetConfigCuratedData
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema: {}
      description: Handle a request for the top 100 rows of a table.
      tags:
        - config
      deprecated: true
      security:
        - all-roles:
            - ct_engineers
      parameters:
        - description: Name of the table to get data for.
          in: query
          name: table
          required: true
          schema:
            type: string
  /config/v2/curated-data:
    get:
      operationId: GetConfigCuratedDataV2
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/CuratedTableRow'
                type: array
      description: Handle a request for the top 100 rows of a table.
      tags:
        - config
      security:
        - all-roles:
            - ct_engineers
      parameters:
        - description: Name of the table to get data for.
          in: query
          name: table
          required: true
          schema:
            type: string
  /config/curated-tables/list:
    get:
      operationId: GetConfigCuratedTablesList
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                items:
                  type: string
                type: array
              examples:
                Example 1:
                  value:
                    - dim_table1
                    - fct_table2
                    - fct_table3
      description: Endpoint for getting a list of tables available in the curated dataset.
      tags:
        - config
      security:
        - all-roles:
            - ct_engineers
      parameters: []
  /config/app-config:
    get:
      operationId: GetConfigAppConfig
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AppConfig'
              examples:
                Example 1:
                  value:
                    api:
                      baseUrl: 'https://run-ict-d-api.ict.dematic.dev/'
                    site:
                      timezone: America/New_York
      tags:
        - config
      security: []
      parameters:
        - in: query
          name: config
          required: false
          schema:
            type: string
          example: my-special-config
  /data/healthcheck:
    get:
      operationId: GetDataBasicHealthCheck
      responses:
        '200':
          description: Ok
          content:
            application/json:
              schema:
                properties:
                  sha:
                    type: string
                  status:
                    $ref: '#/components/schemas/HealthCheckStatus'
                required:
                  - sha
                  - status
                type: object
      tags:
        - data
      security: []
      parameters: []
  /data/healthcheck/full:
    get:
      operationId: GetDataFullHealthCheck
      responses:
        '200':
          description: Ok
          content:
            application/json:
              schema:
                properties:
                  subSystems:
                    properties:
                      redis:
                        $ref: '#/components/schemas/HealthCheckStatus'
                      bigQuery:
                        $ref: '#/components/schemas/HealthCheckStatus'
                      auth0:
                        $ref: '#/components/schemas/HealthCheckStatus'
                    required:
                      - redis
                      - bigQuery
                      - auth0
                    type: object
                  sha:
                    type: string
                  status:
                    $ref: '#/components/schemas/HealthCheckStatus'
                required:
                  - subSystems
                  - sha
                  - status
                type: object
      tags:
        - data
      security: []
      parameters: []
  /data/query:
    post:
      operationId: ExecuteDataQuery
      responses:
        '200':
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DataQueryResult'
      tags:
        - data
      security:
        - all-roles:
            - ct_configurator
      parameters:
        - in: query
          name: start_date
          required: false
          schema:
            type: string
        - in: query
          name: end_date
          required: false
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DataQuery'
  '/data/{dataQueryId}/value':
    get:
      operationId: GetDataQueryValue
      responses:
        '200':
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DataQueryResult'
      tags:
        - data
      security:
        - all-roles:
            - ct_configurator
      parameters:
        - in: path
          name: dataQueryId
          required: true
          schema:
            type: string
        - in: query
          name: start_date
          required: false
          schema:
            type: string
        - in: query
          name: end_date
          required: false
          schema:
            type: string
  /data-explorer/search:
    get:
      operationId: GetDataExplorerSearch
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DataExplorerResult'
              examples:
                Example 1:
                  value:
                    answer: answer
                    id: id
                    prompt: prompt
                    queryResults: []
                    timestamp: '2024-01-01T00:00:00.000Z'
                    isRecommended: not_provided
                    eChartCode: null
      tags:
        - data-explorer
      security: []
      parameters:
        - in: query
          name: searchText
          required: true
          schema:
            type: string
  /data-explorer/results:
    get:
      operationId: GetDataExplorerResults
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DataExplorerResults'
              examples:
                Example 1:
                  value:
                    data:
                      - answer: answer
                        id: 2ef6813b-250b-4e0d-8622-cb7fe8ad88c8
                        prompt: prompt
                        queryResults: []
                        timestamp: '2024-01-01T00:00:00.000Z'
                        isRecommended: not_provided
                        eChartCode: null
      tags:
        - data-explorer
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: limit
          required: false
          schema:
            default: 10
            format: int32
            type: integer
        - in: query
          name: bookmark
          required: false
          schema:
            type: boolean
  '/data-explorer/results/{resultId}':
    get:
      operationId: GetDataExplorerResult
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DataExplorerResult'
              examples:
                Example 1:
                  value:
                    answer: answer
                    id: 2ef6813b-250b-4e0d-8622-cb7fe8ad88c8
                    prompt: prompt
                    queryResults: []
                    timestamp: '2024-01-01T00:00:00.000Z'
                    isRecommended: not_provided
                    eChartCode: null
      tags:
        - data-explorer
      security: []
      parameters:
        - in: path
          name: resultId
          required: true
          schema:
            type: string
            pattern: >-
              ^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$
    delete:
      operationId: DeleteDataExplorerResult
      responses:
        '200':
          description: Result had been removed from the database
        '404':
          description: >-
            Result does not exist or user does not have permission to specified
            result
      tags:
        - data-explorer
      security: []
      parameters:
        - in: path
          name: resultId
          required: true
          schema:
            type: string
    put:
      operationId: PutDataExplorerResult
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DataExplorerResult'
              examples:
                Example 1:
                  value:
                    answer: answer
                    id: 2ef6813b-250b-4e0d-8622-cb7fe8ad88c8
                    prompt: prompt
                    queryResults: []
                    timestamp: '2024-01-01T00:00:00.000Z'
                    isRecommended: not_provided
                    eChartCode: null
        '404':
          description: >-
            Result does not exist or user does not have permission to specified
            result
      tags:
        - data-explorer
      security: []
      parameters:
        - in: path
          name: resultId
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DataExplorerResult'
  /data-explorer/recommendations:
    get:
      operationId: GetDataExplorerRecommendations
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DataExplorerRecommendations'
              examples:
                Example 1:
                  value:
                    - prompt: What's a good recommendation?
      tags:
        - data-explorer
      security: []
      parameters: []
  /data-explorer/gold-questions:
    get:
      operationId: GetDematicChatGoldQuestions
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                properties:
                  goldQuestions:
                    items:
                      $ref: '#/components/schemas/DataExplorerRecommendationsData'
                    type: array
                required:
                  - goldQuestions
                type: object
              examples:
                Example 1:
                  value:
                    - prompt: What's a good gold question?
      tags:
        - data-explorer
      security: []
      parameters: []
  /data-explorer/healthcheck:
    get:
      operationId: GetDataExplorerBasicHealthCheck
      responses:
        '200':
          description: Ok
          content:
            application/json:
              schema:
                properties:
                  sha:
                    type: string
                  status:
                    $ref: '#/components/schemas/HealthCheckStatus'
                required:
                  - sha
                  - status
                type: object
      tags:
        - data-explorer
      security: []
      parameters: []
  /data-explorer/healthcheck/full:
    get:
      operationId: GetDataExplorerFullHealthCheck
      responses:
        '200':
          description: Ok
          content:
            application/json:
              schema:
                properties:
                  subSystems:
                    properties:
                      redis:
                        $ref: '#/components/schemas/HealthCheckStatus'
                      bigQuery:
                        $ref: '#/components/schemas/HealthCheckStatus'
                      auth0:
                        $ref: '#/components/schemas/HealthCheckStatus'
                    required:
                      - redis
                      - bigQuery
                      - auth0
                    type: object
                  sha:
                    type: string
                  status:
                    $ref: '#/components/schemas/HealthCheckStatus'
                required:
                  - subSystems
                  - sha
                  - status
                type: object
      tags:
        - data-explorer
      security: []
      parameters: []
  /data-explorer/aiml-healthcheck:
    get:
      operationId: GetDataExplorerAimlHealthCheck
      responses:
        '200':
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AimlHealthCheckResponse'
      tags:
        - data-explorer
        - aiml
      security: []
      parameters: []
  /data-explorer/agentsearch:
    get:
      operationId: GetDataExplorerAgentSearch
      responses:
        '200':
          description: Successfully retrieved example data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DataExplorerAgentSearchResponse'
              examples:
                Example 1:
                  value:
                    sessionId: example-session-id-agent-123
                    status: COMPLETED
                    messages:
                      - Agent search completed successfully.
      description: Retrieves an example data explorer result.
      tags:
        - data-explorer
      security: []
      parameters:
        - in: query
          name: searchText
          required: true
          schema:
            type: string
        - in: query
          name: sessionId
          required: false
          schema:
            type: string
  /diagnostics/infrastructure/list:
    get:
      operationId: GetDiagnosticsInfrastructureList
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DiagnosticsInfrastructureContract'
              examples:
                Example 1:
                  value:
                    - name: test name
                      lastUpdate: '2020-01-01T00:00:00.000Z'
                      status: Offline
                      cpuPercentage: 10
                      memoryUsagePercentage: 20
                      diskUsagePercentage: 30
                      labels:
                        - test label 1
                        - test label 2
      tags:
        - diagnostics
      security: []
      parameters: []
  /diagnostics/healthcheck:
    get:
      operationId: GetDiagnosticsBasicHealthCheck
      responses:
        '200':
          description: Ok
          content:
            application/json:
              schema:
                properties:
                  sha:
                    type: string
                  status:
                    $ref: '#/components/schemas/HealthCheckStatus'
                required:
                  - sha
                  - status
                type: object
      tags:
        - diagnostics
      security: []
      parameters: []
  /diagnostics/healthcheck/full:
    get:
      operationId: GetDiagnosticsFullHealthCheck
      responses:
        '200':
          description: Ok
          content:
            application/json:
              schema:
                properties:
                  subSystems:
                    properties:
                      redis:
                        $ref: '#/components/schemas/HealthCheckStatus'
                      bigQuery:
                        $ref: '#/components/schemas/HealthCheckStatus'
                      auth0:
                        $ref: '#/components/schemas/HealthCheckStatus'
                    required:
                      - redis
                      - bigQuery
                      - auth0
                    type: object
                  sha:
                    type: string
                  status:
                    $ref: '#/components/schemas/HealthCheckStatus'
                required:
                  - subSystems
                  - sha
                  - status
                type: object
      tags:
        - diagnostics
      security: []
      parameters: []
  /inventory/wms/stock/distribution/under/percentage/series:
    get:
      operationId: GetInventoryWmsStockDistributionUnderPercentageSeries
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InventoryStockDistributionUnderData'
              examples:
                Example 1:
                  value:
                    underInventory:
                      - name: '2022-01-01T08:00:00.000Z'
                        value: 92
                        isValidPercentage: true
                      - name: '2022-01-01T09:00:00.000Z'
                        value: 85
                        isValidPercentage: true
                      - name: '2022-01-01T10:00:00.000Z'
                        value: 89
                        isValidPercentage: true
                      - name: '2022-01-01T11:00:00.000Z'
                        value: 95
                        isValidPercentage: true
                      - name: '2022-01-01T12:00:00.000Z'
                        value: 94
                        isValidPercentage: true
                      - name: '2022-01-01T13:00:00.000Z'
                        value: 92
                        isValidPercentage: true
                      - name: '2022-01-01T14:00:00.000Z'
                        value: 90
                        isValidPercentage: true
      tags:
        - inventory
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
  /inventory/wms/stock/distribution/over/percentage/series:
    get:
      operationId: GetInventoryWmsStockDistributionOverPercentageSeries
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InventoryStockDistributionOverData'
              examples:
                Example 1:
                  value:
                    overInventory:
                      - name: '2022-01-01T08:00:00.000Z'
                        value: 92
                        isValidPercentage: true
                      - name: '2022-01-01T09:00:00.000Z'
                        value: 85
                        isValidPercentage: true
                      - name: '2022-01-01T10:00:00.000Z'
                        value: 89
                        isValidPercentage: true
                      - name: '2022-01-01T11:00:00.000Z'
                        value: 95
                        isValidPercentage: true
                      - name: '2022-01-01T12:00:00.000Z'
                        value: 94
                        isValidPercentage: true
                      - name: '2022-01-01T13:00:00.000Z'
                        value: 92
                        isValidPercentage: true
                      - name: '2022-01-01T14:00:00.000Z'
                        value: 90
                        isValidPercentage: true
      tags:
        - inventory
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
  /inventory/wms/stock/distribution/no/percentage/series:
    get:
      operationId: GetInventoryWmsStockDistributionNoPercentageSeries
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InventoryStockDistributionNoData'
              examples:
                Example 1:
                  value:
                    noInventory:
                      - name: '2022-01-01T08:00:00.000Z'
                        value: 92
                        isValidPercentage: true
                      - name: '2022-01-01T09:00:00.000Z'
                        value: 85
                        isValidPercentage: true
                      - name: '2022-01-01T10:00:00.000Z'
                        value: 89
                        isValidPercentage: true
                      - name: '2022-01-01T11:00:00.000Z'
                        value: 95
                        isValidPercentage: true
                      - name: '2022-01-01T12:00:00.000Z'
                        value: 94
                        isValidPercentage: true
                      - name: '2022-01-01T13:00:00.000Z'
                        value: 92
                        isValidPercentage: true
                      - name: '2022-01-01T14:00:00.000Z'
                        value: 90
                        isValidPercentage: true
      tags:
        - inventory
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
  /inventory/wms/stock/distribution/at/percentage/series:
    get:
      operationId: GetInventoryWmsStockDistributionAtPercentageSeries
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InventoryStockDistributionAtData'
              examples:
                Example 1:
                  value:
                    atInventory:
                      - name: '2020-01-01T00:00:00.000Z'
                        value: 20
                        isValidPercentage: true
                      - name: '2020-01-02T00:00:00.000Z'
                        value: 17
                        isValidPercentage: true
                      - name: '2020-01-03T00:00:00.000Z'
                        value: 24
                        isValidPercentage: true
                      - name: '2020-01-04T00:00:00.000Z'
                        value: 42
                        isValidPercentage: true
      tags:
        - inventory
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
  /inventory/wms/skus/list:
    post:
      operationId: PostInventoryWmsSkusList
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PostWmsInventorySkusListResponse'
              examples:
                Example 1:
                  value:
                    data:
                      - sku: Dematic#1000000
                        description: Test item
                        daysOnHand: 1.3
                        status: under
                        averageDailyQuantity: 2.3
                        averageDailyOrders: 50.45
                        targetMultiplicity: 1.5
                        velocityClassification: '10'
                        quantityAvailable: 5
                        quantityAllocated: 18
                        totalQuantity: 23
                        locations: 3
                        skuPositions: 1
                        maxContainers: 2
                        contOverage: 1
                        latestCycleCountTimestamp: '2024-08-01T09:32:42.000Z'
                        latestActivityDateTimestamp: '2024-08-01T09:32:42.000Z'
                      - sku: Dematic#1000001
                        description: Test item 2
                        daysOnHand: 1.3
                        status: under
                        averageDailyQuantity: 2.3
                        averageDailyOrders: 50.45
                        targetMultiplicity: 1.5
                        velocityClassification: '20'
                        quantityAvailable: 5
                        quantityAllocated: 18
                        totalQuantity: 23
                        locations: 1
                        skuPositions: 1
                        maxContainers: 3
                        contOverage: -2
                        latestCycleCountTimestamp: '2024-08-01T09:32:42.000Z'
                        latestActivityDateTimestamp: '2024-08-01T09:32:42.000Z'
                      - sku: Dematic#1000002
                        description: Test item 3
                        daysOnHand: 1.3
                        status: under
                        averageDailyQuantity: 2.3
                        averageDailyOrders: 50.45
                        targetMultiplicity: 1.5
                        velocityClassification: '1'
                        quantityAvailable: 5
                        quantityAllocated: 18
                        totalQuantity: 23
                        locations: 2
                        skuPositions: 1
                        maxContainers: 2
                        contOverage: 0
                        latestCycleCountTimestamp: '2024-08-01T09:32:42.000Z'
                        latestActivityDateTimestamp: '2024-08-01T09:32:42.000Z'
                    metadata:
                      limit: 10
                      page: 1
                      totalResults: 3
      tags:
        - inventory
      security: []
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PaginatedRequestNoDates'
  /inventory/wms/skus/list/export:
    post:
      operationId: PostInventoryWmsSkusListExport
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: string
                format: byte
      tags:
        - inventory
      security: []
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/PaginatedRequestNoDates'
                - properties:
                    columns:
                      $ref: '#/components/schemas/IncludedColumns'
                  required:
                    - columns
                  type: object
  /inventory/upload/known-demand:
    post:
      operationId: PostInventoryUploadKnownDemand
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                properties:
                  orderLinesProcessed:
                    type: number
                    format: double
                  ordersProcessed:
                    type: number
                    format: double
                required:
                  - orderLinesProcessed
                  - ordersProcessed
                type: object
      description: >-
        Receives two xls files and parses them into multiline JSON to forward to
        the EDP PubSub Topic
      tags:
        - inventory
      security: []
      parameters: []
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                manager:
                  type: string
                  format: binary
                details:
                  type: string
                  format: binary
              required:
                - manager
                - details
  /inventory/upload/recent-activity:
    get:
      operationId: GetInventoryUploadRecentActivity
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/InventoryUploadRecentActivity'
                type: array
              examples:
                Example 1:
                  value:
                    - date: '2025-02-18T13:48:59.000Z'
                      knownOrderCount: 2454
                      knownOrderLineCount: 31490
                    - date: '2025-02-18T11:33:59.000Z'
                      knownOrderCount: 2454
                      knownOrderLineCount: 31490
                    - date: '2025-02-17T15:41:59.000Z'
                      knownOrderCount: 2268
                      knownOrderLineCount: 30987
                    - date: '2025-02-17T12:23:00.000Z'
                      knownOrderCount: 2268
                      knownOrderLineCount: 30987
                    - date: '2025-02-17T07:13:00.000Z'
                      knownOrderCount: 2268
                      knownOrderLineCount: 30987
                    - date: '2025-02-16T13:41:00.000Z'
                      knownOrderCount: 2936
                      knownOrderLineCount: 33736
                    - date: '2025-02-16T12:34:59.000Z'
                      knownOrderCount: 2936
                      knownOrderLineCount: 33736
                    - date: '2025-02-15T14:42:00.000Z'
                      knownOrderCount: 1730
                      knownOrderLineCount: 16854
                    - date: '2025-02-15T12:12:59.000Z'
                      knownOrderCount: 1730
                      knownOrderLineCount: 16854
                    - date: '2025-02-14T11:27:00.000Z'
                      knownOrderCount: 2197
                      knownOrderLineCount: 23452
      tags:
        - inventory
      security: []
      parameters: []
  /inventory/storage/utilization:
    get:
      operationId: GetInventoryStorageUtilization
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InventoryStorageUtilization'
              examples:
                Example 1:
                  value:
                    utilizationPercentage: 50
      tags:
        - inventory
      security: []
      parameters:
        - in: query
          name: start_date
          required: false
          schema:
            format: date-time
            type: string
          deprecated: true
        - in: query
          name: end_date
          required: false
          schema:
            format: date-time
            type: string
          deprecated: true
  '/inventory/stock/distribution/{distributionType}/percentage/series':
    get:
      operationId: GetInventoryStockDistributionPercentageSeries
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InventoryDistributionStatusResponse'
              examples:
                Example 1:
                  value:
                    noInventory:
                      - name: '2020-01-01T00:00:00.000Z'
                        value: 20
                        isValidPercentage: true
                      - name: '2020-01-02T00:00:00.000Z'
                        value: 17
                        isValidPercentage: true
                      - name: '2020-01-03T00:00:00.000Z'
                        value: 24
                        isValidPercentage: true
                      - name: '2020-01-04T00:00:00.000Z'
                        value: 42
                        isValidPercentage: true
      tags:
        - inventory
      security: []
      parameters:
        - in: path
          name: distributionType
          required: true
          schema:
            $ref: '#/components/schemas/DistributionType'
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
  /inventory/replenishment/task-type-series:
    get:
      operationId: GetInventoryReplenishmentTaskTypeSeries
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TaskTypeData'
              examples:
                Example 1:
                  value:
                    demand:
                      - name: '2025-01-01T00:00:00.000Z'
                        value: 15
                      - name: '2025-01-02T00:00:00.000Z'
                        value: 12
                      - name: '2025-01-03T00:00:00.000Z'
                        value: 18
                      - name: '2025-01-04T00:00:00.000Z'
                        value: 10
                      - name: '2025-01-05T00:00:00.000Z'
                        value: 22
                      - name: '2025-01-06T00:00:00.000Z'
                        value: 8
                      - name: '2025-01-07T00:00:00.000Z'
                        value: 14
                    topOff:
                      - name: '2025-01-01T00:00:00.000Z'
                        value: 8
                      - name: '2025-01-02T00:00:00.000Z'
                        value: 6
                      - name: '2025-01-03T00:00:00.000Z'
                        value: 10
                      - name: '2025-01-04T00:00:00.000Z'
                        value: 7
                      - name: '2025-01-05T00:00:00.000Z'
                        value: 12
                      - name: '2025-01-06T00:00:00.000Z'
                        value: 5
                      - name: '2025-01-07T00:00:00.000Z'
                        value: 9
                    relocation:
                      - name: '2025-01-01T00:00:00.000Z'
                        value: 5
                      - name: '2025-01-02T00:00:00.000Z'
                        value: 3
                      - name: '2025-01-03T00:00:00.000Z'
                        value: 8
                      - name: '2025-01-04T00:00:00.000Z'
                        value: 4
                      - name: '2025-01-05T00:00:00.000Z'
                        value: 7
                      - name: '2025-01-06T00:00:00.000Z'
                        value: 2
                      - name: '2025-01-07T00:00:00.000Z'
                        value: 6
      tags:
        - inventory
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
  /inventory/replenishment/details:
    get:
      operationId: GetInventoryReplenishmentDetails
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InventoryReplenishmentDetails'
              examples:
                Example 1:
                  value:
                    dailyReplenishments:
                      - name: '2025-01-01T00:00:00.000Z'
                        value: 3
                      - name: '2025-01-02T00:00:00.000Z'
                        value: 9
                      - name: '2025-01-03T00:00:00.000Z'
                        value: 14
                      - name: '2025-01-04T00:00:00.000Z'
                        value: 33
                      - name: '2025-01-05T00:00:00.000Z'
                        value: 12
                      - name: '2025-01-06T00:00:00.000Z'
                        value: 40
                      - name: '2025-01-07T00:00:00.000Z'
                        value: 15
                    dailyPendingOrders:
                      - name: '2025-01-01T00:00:00.000Z'
                        value: 25
                      - name: '2025-01-02T00:00:00.000Z'
                        value: 19
                      - name: '2025-01-03T00:00:00.000Z'
                        value: 23
                      - name: '2025-01-04T00:00:00.000Z'
                        value: 10
                      - name: '2025-01-05T00:00:00.000Z'
                        value: 8
                      - name: '2025-01-06T00:00:00.000Z'
                        value: 32
                      - name: '2025-01-07T00:00:00.000Z'
                        value: 20
                    dailyCycleTimes:
                      - name: '2025-01-01T00:00:00.000Z'
                        value: 14
                      - name: '2025-01-02T00:00:00.000Z'
                        value: 22
                      - name: '2025-01-03T00:00:00.000Z'
                        value: 34
                      - name: '2025-01-04T00:00:00.000Z'
                        value: 13
                      - name: '2025-01-05T00:00:00.000Z'
                        value: 9
                      - name: '2025-01-06T00:00:00.000Z'
                        value: 4
                      - name: '2025-01-07T00:00:00.000Z'
                        value: 19
                    shiftData:
                      - firstShift:
                          - name: '2025-01-01T00:00:00.000Z'
                            value: 12
                          - name: '2025-01-02T00:00:00.000Z'
                            value: 42
                          - name: '2025-01-03T00:00:00.000Z'
                            value: 66
                          - name: '2025-01-04T00:00:00.000Z'
                            value: 42
                          - name: '2025-01-05T00:00:00.000Z'
                            value: 37
                          - name: '2025-01-06T00:00:00.000Z'
                            value: 22
                          - name: '2025-01-07T00:00:00.000Z'
                            value: 18
                        secondShift:
                          - name: '2025-01-01T00:00:00.000Z'
                            value: 2
                          - name: '2025-01-02T00:00:00.000Z'
                            value: 23
                          - name: '2025-01-03T00:00:00.000Z'
                            value: 12
                          - name: '2025-01-04T00:00:00.000Z'
                            value: 9
                          - name: '2025-01-05T00:00:00.000Z'
                            value: 12
                          - name: '2025-01-06T00:00:00.000Z'
                            value: 34
                          - name: '2025-01-07T00:00:00.000Z'
                            value: 22
                        thirdShift:
                          - name: '2025-01-01T00:00:00.000Z'
                            value: 5
                          - name: '2025-01-02T00:00:00.000Z'
                            value: 13
                          - name: '2025-01-03T00:00:00.000Z'
                            value: 23
                          - name: '2025-01-04T00:00:00.000Z'
                            value: 6
                          - name: '2025-01-05T00:00:00.000Z'
                            value: 11
                          - name: '2025-01-06T00:00:00.000Z'
                            value: 4
                          - name: '2025-01-07T00:00:00.000Z'
                            value: 33
                    shiftTimes:
                      first_endTime: 929
                      second_endTime: 1439
                      third_endTime: '06:59'
                      first_startTime: '07:00'
                      second_startTime: 930
                      third_startTime: '00:00'
      tags:
        - inventory
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
  /inventory/process-flow/areas:
    get:
      operationId: GetInventoryProcessFlowAreas
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProcessFlowResponse'
              examples:
                Example 1:
                  value:
                    areas:
                      - id: '1'
                        label: Area 1
                        metrics:
                          - id: >-
                              dematic:grand_rapids_michigan:Multishuttle:InboundTotesCount:UnitsPerHour
                            type: hourly
                            value: 150
                            units: /hr
                          - id: >-
                              dematic:grand_rapids_michigan:Multishuttle:FaultedLocations:Count
                            type: value
                            panelGroup: workstation_process
                            value: 8000
                            units: /hr
                        position:
                          x: 500
                          'y': 250
                        nodeType: Aisle
                    edges:
                      - id: link1
                        source: '1'
                        target: '2'
                        direction: downstream
                        metrics:
                          - id: >-
                              dematic:grand_rapids_michigan:Multishuttle:InboundTotesCount:UnitsPerHour
                            type: hourly
                            value: 150
                            units: /hr
                          - id: >-
                              dematic:grand_rapids_michigan:Multishuttle:FaultedLocations:Count
                            type: value
                            panelGroup: workstation_process
                            value: 8000
                            units: /hr
                    lastProcessedTime: '1739290887.78988'
      tags:
        - inventory
      security: []
      parameters: []
  '/inventory/process-flow/area/{areaId}':
    get:
      operationId: GetInventoryProcessFlowAreaById
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProcessFlowResponse'
      tags:
        - inventory
      security: []
      parameters:
        - in: path
          name: areaId
          required: true
          schema:
            type: string
  '/inventory/process-flow/details/{type}/{elementId}':
    get:
      operationId: GetInventoryProcessFlowGraphDetails
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProcessFlowDetailsResponse'
              examples:
                Example 1:
                  value:
                    metricGroups:
                      - title: Status
                        metrics:
                          - panelGroup: Status
                            id: >-
                              dematic:grandrapids_mi:multishuttle:inbound_totes_count:units_per_hour
                            value: 15
                            type: hourly
                          - panelGroup: Status
                            id: >-
                              dematic:grandrapids_mi:multishuttle:outbound_totes_count:units_per_hour
                            value: 400
                            type: hourly
      tags:
        - inventory
      security: []
      parameters:
        - in: path
          name: type
          required: true
          schema:
            type: string
        - in: path
          name: elementId
          required: true
          schema:
            type: string
        - in: query
          name: view
          required: false
          schema:
            type: string
  '/inventory/process-flow/areas/{areaId}':
    put:
      operationId: UpdateInventoryProcessFlowArea
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdatedArea'
      tags:
        - inventory
      security: []
      parameters:
        - in: path
          name: areaId
          required: true
          schema:
            type: string
        - in: query
          name: view
          required: false
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdatePositionPayload'
  '/inventory/process-flow/edges/{edgeId}':
    put:
      operationId: UpdateInventoryProcessFlowEdge
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Edge'
      tags:
        - inventory
      security: []
      parameters:
        - in: path
          name: edgeId
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Edge'
  '/inventory/process-flow/metrics/{metricId}':
    put:
      operationId: UpdateInventoryProcessFlowMetric
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Metric'
      tags:
        - inventory
      security: []
      parameters:
        - in: path
          name: metricId
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Metric'
  /inventory/process-flow/clear-cache:
    post:
      operationId: ClearInventoryProcessFlowCache
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ClearCacheResponse'
      tags:
        - inventory
      security: []
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ClearCacheRequest'
  /inventory/performance/series:
    get:
      operationId: GetInventoryPerformanceSeries
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InventoryPerformanceSeriesContract'
              examples:
                Example 1:
                  value:
                    - name: Inventory Accuracy
                      series:
                        - name: '2023-10-01T00:00:00.000Z'
                          value: 50
                        - name: '2023-11-01T00:00:00.000Z'
                          value: 65
                    - name: Order Fulfillment
                      series:
                        - name: '2023-10-01T00:00:00.000Z'
                          value: 54
                        - name: '2023-11-01T00:00:00.000Z'
                          value: 23
                    - name: Storage Utilization
                      series:
                        - name: '2023-10-01T00:00:00.000Z'
                          value: 70
                        - name: '2023-11-01T00:00:00.000Z'
                          value: 87
                    - name: Stock Out
                      series:
                        - name: '2023-10-01T00:00:00.000Z'
                          value: 10
                        - name: '2023-11-01T00:00:00.000Z'
                          value: 9
      tags:
        - inventory
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: department_filter
          required: true
          schema:
            type: string
  /inventory/sku/high-impact/list:
    post:
      operationId: PostInventorySkuHighImpactList
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InventoryHighImpactSKUContract'
              examples:
                Example 1:
                  value:
                    data:
                      - sku: '123456'
                        accuracy: 0.9
                        storageArea: A
                        quantity: 100
                        cubeUtilization: 0.5
                        daysOnHand: 10
                    metadata:
                      page: 1
                      limit: 10
                      totalResults: 1
      tags:
        - inventory
      security: []
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PaginatedRequest'
  /inventory/healthcheck:
    get:
      operationId: GetInventoryBasicHealthCheck
      responses:
        '200':
          description: Ok
          content:
            application/json:
              schema:
                properties:
                  sha:
                    type: string
                  status:
                    $ref: '#/components/schemas/HealthCheckStatus'
                required:
                  - sha
                  - status
                type: object
      tags:
        - inventory
      security: []
      parameters: []
  /inventory/healthcheck/full:
    get:
      operationId: GetInventoryFullHealthCheck
      responses:
        '200':
          description: Ok
          content:
            application/json:
              schema:
                properties:
                  subSystems:
                    properties:
                      redis:
                        $ref: '#/components/schemas/HealthCheckStatus'
                      bigQuery:
                        $ref: '#/components/schemas/HealthCheckStatus'
                      auth0:
                        $ref: '#/components/schemas/HealthCheckStatus'
                    required:
                      - redis
                      - bigQuery
                      - auth0
                    type: object
                  sha:
                    type: string
                  status:
                    $ref: '#/components/schemas/HealthCheckStatus'
                required:
                  - subSystems
                  - sha
                  - status
                type: object
      tags:
        - inventory
      security: []
      parameters: []
  /inventory/handling-units/trayed:
    get:
      operationId: GetInventoryHandlingUnitsTrayed
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InventoryHandlingUnitsTrayedContract'
              examples:
                Example 1:
                  value:
                    metadata: {}
      tags:
        - inventory
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
  /inventory/forecast/data-analysis-timestamp:
    get:
      operationId: GetDataAnalysisTimestampData
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InventoryForecastDataAnalysisTimestamp'
              examples:
                Example 1:
                  value:
                    dataUpdateTimestamp: '2024-08-05T13:00:00.000Z'
                    analysisPerformedTimestamp: '2024-08-05T13:15:00.000Z'
      tags:
        - inventory
      security: []
      parameters: []
  /inventory/forecast/list:
    post:
      operationId: PostInventoryForecastList
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InventoryForecastListingData'
              examples:
                Example 1:
                  value:
                    data:
                      - sku: Item 1
                        current:
                          reserveStorage: 100
                          forwardPick: 200
                        projected:
                          pendingReplenishment: 200
                          pendingPicks: 50
                          allocatedOrders: 100
                          projectedForwardPick: 460
                        forecast:
                          averageReplenishment: 200
                          averageDemand: 100
                          demandTomorrow: 200
                          knownDemand: 37
                          forwardPickTomorrow: -122
                          twoDayDemand: 100
                          twoDayForwardPick: 200
                    metadata:
                      page: 1
                      limit: 50
                      totalResults: 1
      tags:
        - inventory
      security: []
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PaginatedRequestNoDates'
  /inventory/forecast/list/export:
    post:
      operationId: PostInventoryForecastListExport
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: string
                format: byte
      description: Creates an excel export for inventory forecast data.
      tags:
        - inventory
      security: []
      parameters: []
      requestBody:
        description: 'Request body with filters, sorting, and columns parameter.'
        required: true
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/PaginatedRequestNoDates'
                - properties:
                    columns:
                      $ref: '#/components/schemas/IncludedColumns'
                  required:
                    - columns
                  type: object
              description: 'Request body with filters, sorting, and columns parameter.'
  '/inventory/forecast/{skuId}/locations':
    get:
      operationId: GetInventoryForecastSkuLocations
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InventoryForecastSkuLocationAreas'
              examples:
                Example 1:
                  value:
                    data:
                      - area: Reserve Storage
                        details:
                          - locationId: RESERVE LOC 1
                            locationType: Bulk
                            containerId: CONT ABC
                            containerCount: 1
                            quantity: 40
                            uom: Eaches
                            containerDimensions: '1'
                            conditionCode: UNRESTRICTED
                            zone: ZONE 2
                            lastActivityDate: '2024-05-08T09:59:58.000Z'
                      - area: Forward Pick
                        details:
                          - locationId: FORWARD PICK 2
                            locationType: Fast
                            containerId: null
                            containerCount: 2
                            quantity: 600
                            uom: Eaches
                            skuSize: NS
                            containerDimensions: 24" - 49"
                            conditionCode: UNRESTRICTED
                            zone: ASRS
                            lastActivityDate: '2024-04-25T16:50:55.000Z'
      tags:
        - inventory
      security: []
      parameters:
        - in: path
          name: skuId
          required: true
          schema:
            type: string
  '/inventory/forecast/{skuId}/orders':
    get:
      operationId: GetInventoryForecastSkuOrders
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InventoryForecastSkuOrders'
              examples:
                Example 1:
                  value:
                    skuId: SKU123
                    openOrderCount: 2
                    data:
                      - skuId: SKU123
                        orderId: ORDER 1
                        priority: '1'
                        allocationDate: '2024-06-28 13:45:57 UTC'
                        shipDate: '2024-06-28T00:00:00.000Z'
                        orderLines: 12
                        allocatedQty: 2
                      - skuId: SKU123
                        orderId: ORDER 2
                        priority: '1'
                        allocationDate: '2024-06-28 05:12:22 UTC'
                        shipDate: '2024-06-28T00:00:00.000Z'
                        orderLines: 5
                        allocatedQty: 10
      tags:
        - inventory
      security: []
      parameters:
        - in: path
          name: skuId
          required: true
          schema:
            type: string
  '/inventory/forecast/{skuId}':
    get:
      operationId: GetInventorySkuForecast
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InventorySkuForecastDetails'
              examples:
                Example 1:
                  value:
                    confidence: 10
                    knownDemand:
                      percentage: 45
                      quantity: 85
                    shortTermDaily: 180
                    shortTermDailyDays: 88
                    longTermDaily: 178
                    longTermDailyDays: 360
                    nonZeroDemand: 60
                    zeroDemandIntermittency: 3
                    predictedDemandSeries:
                      - name: '2024-10-08T12:05:06.615Z'
                        value: 18
                      - name: '2024-10-11T16:10:26.829Z'
                        value: 0
                      - name: '2024-10-14T03:16:45.028Z'
                        value: 30
                    actualDemandSeries:
                      - name: '2024-10-08T12:05:06.615Z'
                        value: 12
                      - name: '2024-10-11T16:10:26.829Z'
                        value: 0
                      - name: '2024-10-14T03:16:45.028Z'
                        value: 26
                    confidenceLowSeries:
                      - name: '2024-10-08T12:05:06.615Z'
                        value: 0
                      - name: '2024-10-11T16:10:26.829Z'
                        value: 8
                      - name: '2024-10-14T03:16:45.028Z'
                        value: 0
                    confidenceHighSeries:
                      - name: '2024-10-08T12:05:06.615Z'
                        value: 27
                      - name: '2024-10-11T16:10:26.829Z'
                        value: 6
                      - name: '2024-10-14T03:16:45.028Z'
                        value: 1
      tags:
        - inventory
      security: []
      parameters:
        - in: path
          name: skuId
          required: true
          schema:
            type: string
  /inventory/filter:
    get:
      operationId: GetInventoryFilter
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InventoryAreaFilterDefinition'
              examples:
                Example 1:
                  value:
                    areaFilterTable: []
      tags:
        - inventory
      security: []
      parameters: []
  /inventory/facility/skus/list:
    post:
      operationId: PostInventoryFacilitySkusList
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PostFacilityInventorySkusListResponse'
              examples:
                Example 1:
                  value:
                    data:
                      - sku: Dematic#1000000
                        description: Test item
                        daysOnHand: 1.3
                        status: under
                        averageDailyQuantity: 2.3
                        averageDailyOrders: 50.45
                        quantityAvailable: 5
                        quantityAllocated: 18
                        totalQuantity: 23
                        locations: 3
                        skuPositions: 1
                        latestActivityDateTimestamp: '2024-08-01T09:32:42.000Z'
                      - sku: Dematic#1000001
                        description: Test item 2
                        daysOnHand: 1.3
                        status: under
                        averageDailyQuantity: 2.3
                        averageDailyOrders: 50.45
                        quantityAvailable: 5
                        quantityAllocated: 18
                        totalQuantity: 23
                        locations: 1
                        skuPositions: 1
                        latestActivityDateTimestamp: '2024-08-01T09:32:42.000Z'
                      - sku: Dematic#1000002
                        description: Test item 3
                        daysOnHand: 1.3
                        status: under
                        averageDailyQuantity: 2.3
                        averageDailyOrders: 50.45
                        quantityAvailable: 5
                        quantityAllocated: 18
                        totalQuantity: 23
                        locations: 2
                        skuPositions: 1
                        latestActivityDateTimestamp: '2024-08-01T09:32:42.000Z'
                    metadata:
                      limit: 10
                      page: 1
                      totalResults: 3
      tags:
        - inventory
      security: []
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PaginatedRequestNoDates'
  /inventory/facility/skus/list/export:
    post:
      operationId: PostInventoryFacilitySkusListExport
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: string
                format: byte
      tags:
        - inventory
      security: []
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/PaginatedRequestNoDates'
                - properties:
                    columns:
                      $ref: '#/components/schemas/IncludedColumns'
                  required:
                    - columns
                  type: object
  /inventory/containers/list:
    post:
      operationId: PostInventoryContainersList
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PostInventoryContainersListResponse'
              examples:
                Example 1:
                  value:
                    data:
                      - container_id: AN0884
                        location_id: LOC987
                        zone: Zone A
                        sku: TRL_4536
                        quantity: 150
                        last_activity_date: '2024-11-03 09:15:10 AM'
                        last_cycle_count: '2024-10-30 11:45:20 AM'
                        data_updated: '2024-11-05 08:00:00 AM'
                        free_cycle_count: null
                    metadata:
                      limit: 10
                      page: 1
                      totalResults: 2
      description: Post inventory container data with filters
      tags:
        - inventory
      security: []
      parameters: []
      requestBody:
        description: '- Request body with filters, sorting, and pagination'
        required: true
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/PaginatedRequestNoDates'
                - properties:
                    byPassConfigSetting:
                      type: boolean
                  type: object
              description: '- Request body with filters, sorting, and pagination'
  /inventory/containers/list/export:
    post:
      operationId: PostInventoryContainersListExport
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: string
                format: byte
      tags:
        - inventory
      security: []
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/PaginatedRequestNoDates'
                - properties:
                    columns:
                      $ref: '#/components/schemas/IncludedColumns'
                    byPassConfigSetting:
                      type: boolean
                  required:
                    - columns
                  type: object
  /inventory/wms/containers/list:
    post:
      operationId: PostInventoryWMSContainersList
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PostInventoryContainersListResponse'
              examples:
                Example 1:
                  value:
                    data:
                      - container_id: AN0884
                        location_id: LOC987
                        zone: Zone A
                        sku: TRL_4536
                        quantity: 150
                        last_activity_date: '2024-11-03 09:15:10 AM'
                        last_cycle_count: '2024-10-30 11:45:20 AM'
                        data_updated: '2024-11-05 08:00:00 AM'
                        free_cycle_count: null
                    metadata:
                      limit: 10
                      page: 1
                      totalResults: 2
      description: Post inventory container data with filters and using WMS inventory data
      tags:
        - inventory
      security: []
      parameters: []
      requestBody:
        description: '- Request body with filters, sorting, and pagination'
        required: true
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/PaginatedRequestNoDates'
                - properties:
                    byPassConfigSetting:
                      type: boolean
                  type: object
              description: '- Request body with filters, sorting, and pagination'
  /inventory/wms/containers/list/export:
    post:
      operationId: PostInventoryWMSContainersListExport
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: string
                format: byte
      tags:
        - inventory
      security: []
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/PaginatedRequestNoDates'
                - properties:
                    columns:
                      $ref: '#/components/schemas/IncludedColumns'
                    byPassConfigSetting:
                      type: boolean
                  required:
                    - columns
                  type: object
  '/inventory/container-events/list/{containerId}':
    post:
      operationId: PostInventoryContainerEventsList
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PostInventoryContainerEventsListResponse'
              examples:
                Example 1:
                  value:
                    data:
                      - timestamp: '2024-09-26 12:54 PM'
                        event: FREECYCLECOUNT
                        workstationCode: M11-GTP-03
                        destinationContainer: '000000'
                        operator: EUD202284
                        sku: TRL1_STACK
                        quantity: 0
                    metadata:
                      limit: 10
                      page: 1
                      totalResults: 1
      description: Post inventory container events data with filters and sorting.
      tags:
        - inventory
      security: []
      parameters:
        - description: Container identifier.
          in: path
          name: containerId
          required: true
          schema:
            type: string
      requestBody:
        description: 'Request body with filters, sorting, pagination, and months parameter.'
        required: true
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/PaginatedRequestNoDates'
                - properties:
                    months:
                      type: number
                      format: double
                  type: object
              description: >-
                Request body with filters, sorting, pagination, and months
                parameter.
  '/inventory/container-events/list/export/{containerId}':
    post:
      operationId: PostInventoryContainerEventsListExport
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: string
                format: byte
      description: Creates an excel export for inventory container events.
      tags:
        - inventory
      security: []
      parameters:
        - description: Container identifier.
          in: path
          name: containerId
          required: true
          schema:
            type: string
      requestBody:
        description: >-
          Request body with filters, sorting, pagination, columns, and months
          parameter.
        required: true
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/PaginatedRequestNoDates'
                - properties:
                    columns:
                      $ref: '#/components/schemas/IncludedColumns'
                    months:
                      type: number
                      format: double
                  required:
                    - columns
                  type: object
              description: >-
                Request body with filters, sorting, pagination, columns, and
                months parameter.
  /inventory/bin-locations:
    get:
      operationId: GetInventoryBinLocations
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InventoryBinLocationsResponse'
              examples:
                Example 1:
                  value:
                    data:
                      - binLocation: MS011113020111
                        locationSide: Right
                        status: Occupied
                        containerType: Inventory Tote
                        skus:
                          - sku: sku1
                            quantity: 5
                          - sku: sku2
                            quantity: 10
      description: Endpoint for retrieving bin locations for a given DMS aisle and level.
      tags:
        - inventory
      security: []
      parameters:
        - description: The double digit aisle code for the bin locations to retrieve.
          in: query
          name: aisle
          required: true
          schema:
            type: string
        - description: The double digit level code for the bin locations to retrieve.
          in: query
          name: level
          required: true
          schema:
            type: string
  /inventory/advices/outstanding:
    get:
      operationId: GetInventoryAdvicesOutstanding
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AdvicesOutstandingData'
              examples:
                Example 1:
                  value:
                    outstandingAdvices: 2
      tags:
        - inventory
      security: []
      parameters: []
  /inventory/advices/list:
    get:
      operationId: GetInventoryAdvicesList
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AdvicesList'
              examples:
                Example 1:
                  value:
                    adviceList: []
      tags:
        - inventory
      security: []
      parameters: []
  /inventory/advices/in-progress:
    get:
      operationId: GetInventoryAdvicesInProgress
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InventoryAdvicesInProgressData'
              examples:
                Example 1:
                  value:
                    inProgressAdvices: 2
      tags:
        - inventory
      security: []
      parameters: []
  /inventory/advices/finished:
    get:
      operationId: GetInventoryAdvicesFinished
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AdvicesFinishedData'
              examples:
                Example 1:
                  value:
                    finishedAdvices: 2
      tags:
        - inventory
      security: []
      parameters: []
  '/inventory/advices/{adviceId}/details':
    get:
      operationId: GetInventoryAdvicesDetails
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AdviceDetailsData'
              examples:
                Example 1:
                  value:
                    supplierId: '001'
                    itemsReceived:
                      - handlingUnit: HU-00001
                        handlingUnitType: PALLET
                        adviceLine: ADV-00003#1
                        sku: SKU-00001
                        quantity: 10
                        packagingLevel: CASE
      tags:
        - inventory
      security: []
      parameters:
        - in: path
          name: adviceId
          required: true
          schema:
            type: string
  /inventory/advices/cycle-time:
    get:
      operationId: GetInventoryAdvicesCycleTime
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AdvicesCycleTimeData'
              examples:
                Example 1:
                  value:
                    cycleTime: 2
      tags:
        - inventory
      security: []
      parameters: []
  /inventory/accuracy:
    get:
      operationId: GetInventoryAccuracy
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: >-
                  #/components/schemas/ApiResponse_InventoryAccuracy.InventoryAccuracyConfig_
              examples:
                Example 1:
                  value:
                    accuracy: 90
                    status: ok
                    areaFilter: All
                    metadata:
                      seg1: 80
                      seg2: 87
                      seg3: 90
      tags:
        - inventory
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: area_filter
          required: false
          schema:
            default: All
            type: string
  '/inventory/container-events/{containerId}':
    get:
      operationId: GetInventoryContainerEventsDetails
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InventoryContainerEventsKpiContract'
              examples:
                Example 1:
                  value:
                    events:
                      - containerId: AN0884
                        locationId: LOC987
                        zone: Zone A
                        sku: TRL_4536
                        quantity: 150
                        lastActivityDate: '2024-11-03 09:15:10 AM'
                        lastCycleCount: '2024-10-30 11:45:20 AM'
                        dataUpdated: '2024-11-05 08:00:00 AM'
                        cycleCountEventsToday: 3
                        averageDailyCycleCount: 15
                        pickEventsToday: 18
                        averageDailyPickEvents: 29
      description: Get container KPI metrics for a specific container ID.
      tags:
        - inventory
      security: []
      parameters:
        - description: '- ID of the container for which KPI metrics are retrieved.'
          in: path
          name: containerId
          required: true
          schema:
            type: string
  '/inventory/wms/container-events/{containerId}':
    get:
      operationId: GetInventoryWMSContainerEventsDetails
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InventoryContainerEventsKpiContract'
              examples:
                Example 1:
                  value:
                    events:
                      - containerId: AN0884
                        locationId: LOC987
                        zone: Zone A
                        sku: TRL_4536
                        quantity: 150
                        lastActivityDate: '2024-11-03 09:15:10 AM'
                        lastCycleCount: '2024-10-30 11:45:20 AM'
                        dataUpdated: '2024-11-05 08:00:00 AM'
                        cycleCountEventsToday: 3
                        averageDailyCycleCount: 15
                        pickEventsToday: 18
                        averageDailyPickEvents: 29
      description: Get container KPI metrics for a specific container ID using WMS tables.
      tags:
        - inventory
      security: []
      parameters:
        - description: '- ID of the container for which KPI metrics are retrieved.'
          in: path
          name: containerId
          required: true
          schema:
            type: string
  '/equipment/workstation/{workstationId}/starved-blocked-time/series':
    get:
      operationId: GetEquipmentWorkstationStarvedBlockedTimeSeries
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: >-
                  #/components/schemas/EquipmentWorkstationStarvedBlockedTimeSeriesApiResponse
              examples:
                Example 1:
                  value:
                    starvedTime:
                      - name: '2022-01-01T08:00:00.000Z'
                        value: 16
                      - name: '2022-01-01T09:00:00.000Z'
                        value: 17
                    blockedTime:
                      - name: '2022-01-01T08:00:00.000Z'
                        value: 11
                      - name: '2022-01-01T09:00:00.000Z'
                        value: 12
                    metadata:
                      yMin: 0
                      yMax: 19
                      xAxisValueFormatType: time
                      yAxisValueFormatType: percentage
                      starvedTime:
                        type: line
                        color: '#DD5F94'
                        icon: emptyDiamond
                      blockedTime:
                        type: line
                        color: '#AC862C'
                        icon: emptySquare
      tags:
        - equipment
      security: []
      parameters:
        - in: path
          name: workstationId
          required: true
          schema:
            type: string
  '/equipment/workstation/{workstationId}/operator/activity':
    get:
      operationId: GetEquipmentWorkstationOperatorActivity
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                items:
                  $ref: >-
                    #/components/schemas/EquipmentWorkstationOperatorActivityData
                type: array
              examples:
                Example 1:
                  value:
                    - operatorId: TestOp1
                      startTime: '2020-01-01T00:00:00.000Z'
                      endTime: '2020-01-01T01:00:00.000Z'
                      totalTimeDuration: 3600
                      idleTime: 60
                      starvedTime: 70
                      blockedTime: 80
                      lineRatePerHour: 75
                      weightedLineRatePerHour: 90
                    - operatorId: TestOp2
                      startTime: '2020-01-01T01:00:00.000Z'
                      endTime: '2020-01-01T03:00:00.000Z'
                      totalTimeDuration: 7200
                      idleTime: 120
                      starvedTime: 140
                      blockedTime: 160
                      lineRatePerHour: 80
                      weightedLineRatePerHour: 100
      tags:
        - equipment
      security: []
      parameters:
        - in: path
          name: workstationId
          required: true
          schema:
            type: string
  '/equipment/workstation/{workstationId}/line-rates/series':
    get:
      operationId: GetEquipmentWorkstationLineRatesSeries
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EquipmentWorkstationLineRatesSeriesData'
              examples:
                Example 1:
                  value:
                    lineRates:
                      - name: '2023-01-01T00:00:00.000Z'
                        value: 100
                      - name: '2023-01-01T01:00:00.000Z'
                        value: 120
                      - name: '2023-01-01T02:00:00.000Z'
                        value: 15
                      - name: '2023-01-01T03:00:00.000Z'
                        value: 70
      tags:
        - equipment
      security: []
      parameters:
        - in: path
          name: workstationId
          required: true
          schema:
            type: string
  '/equipment/workstation/{workstationId}/movements/detail':
    get:
      operationId: GetEquipmentWorkstationMovementsDetail
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EquipmentWorkstationAisleMovementData'
              examples:
                Example 1:
                  value:
                    retrieval: 17548
                    storage: 10517
                    positioning: 5688
                    iat: 5282
                    shuffle: 1435
                    bypass: 341
      tags:
        - equipment
      security: []
      parameters:
        - in: path
          name: workstationId
          required: true
          schema:
            type: string
  '/equipment/workstation/{workstationId}/aisle/active-faults':
    get:
      operationId: GetEquipmentWorkstationAisleActiveFaults
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/EquipmentWorkstationActiveFaultsData'
                type: array
              examples:
                Example 1:
                  value:
                    - description: Maintenance Gate Level 1
                      startTime: '2023-11-23T14:30:00.000Z'
                      duration: 720
                      faultId: F1234
      tags:
        - equipment
      security: []
      parameters:
        - in: path
          name: workstationId
          required: true
          schema:
            type: string
  '/equipment/summary/workstations/{workstationId}':
    get:
      operationId: GetEquipmentSummaryWorkstations
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: >-
                  #/components/schemas/ApiResponse_EquipmentSummaryWorkstationDefinition.EquipmentSummaryWorkstationConfigDefinition_
              examples:
                Example 1:
                  value:
                    status: ok
                    operatorId: 1534
                    iatDonorTotes: 0.2
                    linesPerHour: 110
                    activeTime: PT8H30M
                    idleTime: PT1H15M
                    starvedTime: PT15M
                    metadata:
                      metrics:
                        - id: operatorId
                          label: Operator ID
                          type: number
                          format: integer
                          display: true
                        - id: iatDonorTotes
                          label: IAT Donor Totes
                          type: number
                          format: percent
                          display: true
                        - id: linesPerHour
                          label: Lines Per Hour
                          type: string
                          format: duration
                          display: true
                        - id: activeTime
                          label: Active Time
                          type: string
                          format: duration
                          display: true
                        - id: idleTime
                          label: Idle Time
                          type: string
                          format: duration
                          display: true
                        - id: starvedTime
                          label: Starved Time
                          type: string
                          format: duration
                          display: true
      tags:
        - equipment
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
        - in: path
          name: workstationId
          required: true
          schema:
            type: string
  /equipment/summary/areas:
    get:
      operationId: GetEquipmentSummaryAreas
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EquipmentSummaryAreaContract'
              examples:
                Example 1:
                  value:
                    areas:
                      - id: Area1
                        downTimeMinutes: 8
                        movementsPerFault: 52.5
                        name: Area1
                        qualityPercentage: 99.5
                        outboundRatePerHour: 500
                        status: ok
                        alerts:
                          - status: critical
                            identifier: ''
                          - status: caution
                            identifier: Workstation 5
                          - status: warning
                            identifier: Workstation 2
                          - status: ok
                            identifier: Workstation 1
                      - id: Area2
                        downTimeMinutes: 2
                        movementsPerFault: 55
                        name: Area2
                        qualityPercentage: 89
                        outboundRatePerHour: 100
                        status: ok
                        alerts:
                          - status: critical
                            identifier: ''
                          - status: caution
                            identifier: Workstation 5
                          - status: warning
                            identifier: Workstation 2
                          - status: ok
                            identifier: Workstation 1
      tags:
        - equipment
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
  '/equipment/summary/aisles/{aisleId}':
    get:
      operationId: GetEquipmentSummaryAisle
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: >-
                  #/components/schemas/ApiResponse_EquipmentSummaryAisleDefinition.EquipmentSummaryAisleConfigDefinition_
              examples:
                Example 1:
                  value:
                    status: ok
                    totalMovements: 40811
                    storageUtilization: 0.98
                    storedTotesPerHour: 120
                    inventoryTotes: 1800
                    retrievedTotesPerHour: 300
                    orderTotes: 2000
                    metadata:
                      metrics:
                        - id: totalMovements
                          label: Total Movements
                          type: number
                          format: integer
                          display: true
                        - id: storageUtilization
                          label: Storage Utilization
                          type: number
                          format: percent
                          display: true
                        - id: storedTotesPerHour
                          label: Stored Totes Per Hour
                          type: number
                          format: integer
                          display: true
                        - id: inventoryTotes
                          label: Inventory Totes
                          type: number
                          format: integer
                          display: true
                        - id: retrievedTotesPerHour
                          label: Retrieved Totes Per Hour
                          type: number
                          format: integer
                          display: true
                        - id: orderTotes
                          label: Order Totes
                          type: number
                          format: integer
                          display: true
      tags:
        - equipment
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
        - in: path
          name: aisleId
          required: true
          schema:
            type: string
  /equipment/events/list:
    post:
      operationId: PostEquipmentEventsList
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: >-
                  #/components/schemas/ApiResponseArray_RecentEventQueryResponse-Array.RecentEventQueryConfig_
              examples:
                Example 1:
                  value:
                    data:
                      - tenant: Dematic Software
                        facility: Site 1
                        description: Description
                        eventType: event_type
                        equipmentId: '123'
                        totalTime: 123
                        faultCode: E123
                    metadata:
                      page: 1
                      limit: 100
                      totalResults: 25
      tags:
        - equipment
      security: []
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              properties:
                end_date:
                  type: string
                  format: date-time
                start_date:
                  type: string
                  format: date-time
                limit:
                  type: number
                  format: double
                page:
                  type: number
                  format: double
                sortFields:
                  items:
                    $ref: '#/components/schemas/SortField'
                  type: array
                filters:
                  $ref: '#/components/schemas/BaseFilterType'
              required:
                - end_date
                - start_date
              type: object
  /equipment/outbound-rate:
    get:
      operationId: GetEquipmentOutboundRate
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EquipmentOutboundRateApiResponse'
              examples:
                Example 1:
                  value:
                    outboundDivertsHour: 600
                    status: ok
                    metadata:
                      max: 1000
                      rangeColor: royalblue
      tags:
        - equipment
      security: []
      parameters: []
  /equipment/healthcheck:
    get:
      operationId: GetEquipmentBasicHealthCheck
      responses:
        '200':
          description: Ok
          content:
            application/json:
              schema:
                properties:
                  sha:
                    type: string
                  status:
                    $ref: '#/components/schemas/HealthCheckStatus'
                required:
                  - sha
                  - status
                type: object
      tags:
        - equipment
      security: []
      parameters: []
  /equipment/healthcheck/full:
    get:
      operationId: GetEquipmentFullHealthCheck
      responses:
        '200':
          description: Ok
          content:
            application/json:
              schema:
                properties:
                  subSystems:
                    properties:
                      redis:
                        $ref: '#/components/schemas/HealthCheckStatus'
                      bigQuery:
                        $ref: '#/components/schemas/HealthCheckStatus'
                      auth0:
                        $ref: '#/components/schemas/HealthCheckStatus'
                    required:
                      - redis
                      - bigQuery
                      - auth0
                    type: object
                  sha:
                    type: string
                  status:
                    $ref: '#/components/schemas/HealthCheckStatus'
                required:
                  - subSystems
                  - sha
                  - status
                type: object
      tags:
        - equipment
      security: []
      parameters: []
  /equipment/faults/status/list:
    post:
      operationId: PostEquipmentFaultsStatusList
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                properties:
                  availableStatuses:
                    items:
                      type: string
                    type: array
                required:
                  - availableStatuses
                type: object
              examples:
                Example 1:
                  value:
                    availableStatuses:
                      - Status 1
                      - Status 2
                      - Status 3
      tags:
        - equipment
      security: []
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FaultsStatusListRequest'
  /equipment/faults/series:
    get:
      operationId: GetEquipmentFaultsSeries
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EquipmentFaultsSeriesContractData'
              examples:
                Example 1:
                  value:
                    faultsOrderData:
                      - name: '2023-04-10T10:00:00.000Z'
                        value: 2
                      - name: '2023-04-10T11:00:00.000Z'
                        value: 5
                    orderData:
                      - name: '2023-04-10T10:00:00.000Z'
                        value: 224
                      - name: '2023-04-10T11:00:00.000Z'
                        value: 106
      tags:
        - equipment
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
  /equipment/faults/events:
    get:
      operationId: GetEquipmentFaultsEvents
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FaultEventContract'
              examples:
                Example 1:
                  value:
                    - time: '2024-01-04T18:40:29.371Z'
                      area: 03dc76c5-e348-43ca-9b2a-9f3dfbe2079a MS072145160111
                      description: ' '
                    - time: '2024-01-04T18:33:40.098Z'
                      area: 03dc76c5-e348-43ca-9b2a-9f3dfbe2079a MS052065060111
                      description: ' '
                    - time: '2024-01-04T18:31:31.433Z'
                      area: f746a38e-8e7f-4863-8ddd-5df5e192c760 CCTA01TRNS
                      description: ' '
                    - time: '2024-01-04T18:29:04.873Z'
                      area: f746a38e-8e7f-4863-8ddd-5df5e192c760 CCTA01TRNS
                      description: ' '
      tags:
        - equipment
      security: []
      parameters: []
  /equipment/faults/movements/series:
    post:
      operationId: PostEquipmentFaultsMovementsSeries
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FaultsMovementsSeriesData'
              examples:
                Example 1:
                  value:
                    movementCounts:
                      - name: '01'
                        value: 50
                      - name: '02'
                        value: 42
                      - name: '03'
                        value: 66
                    movementsPerFault:
                      - name: '01'
                        value: 10
                      - name: '02'
                        value: 20
                      - name: '03'
                        value: 30
      tags:
        - equipment
      security: []
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FaultsMovementsSeriesRequest'
  /equipment/faults/list:
    post:
      operationId: PostEquipmentFaultsList
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PostEquipmentFaultsListResponse'
              examples:
                Example 1:
                  value:
                    data:
                      - timestamp: '2024-08-19T12:00:00.000Z'
                        durationMinutes: 1.2
                        status: 430 - Lift Drive Faulted
                        aisle: '01'
                        level: '02'
                        device: MSAI01ER02LO00
                        deviceType: Lift
                      - timestamp: '2024-08-19T12:00:00.000Z'
                        durationMinutes: 1.2
                        status: Offline
                        aisle: '02'
                        level: '01'
                        device: MSAI02ER01LO00
                        deviceType: Lift
                      - timestamp: '2024-08-19T12:00:00.000Z'
                        durationMinutes: 1.2
                        status: Offline
                        aisle: '01'
                        level: '03'
                        device: MSAI01LV03SH10
                        deviceType: Shuttle
                    metadata:
                      limit: 10
                      page: 1
                      totalResults: 3
      tags:
        - equipment
      security: []
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PaginatedRequest'
  /equipment/faults/level/list:
    post:
      operationId: PostEquipmentFaultsLevelList
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AvailableLevels'
              examples:
                Example 1:
                  value:
                    availableLevels:
                      - Level 1
                      - Level 2
                      - Level 3
      tags:
        - equipment
      security: []
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FaultsLevelListRequest'
  /equipment/faults/grouped/count/series:
    post:
      operationId: getFaultGroupedByCounts
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FaultCountGroupedSeriesData'
              examples:
                Example 1:
                  value:
                    faultCounts:
                      - name: '01'
                        value: 50
                      - name: '02'
                        value: 42
                      - name: '03'
                        value: 66
      tags:
        - equipment
      security: []
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FaultCountGroupedByRequest'
  /equipment/faults/device-id/list:
    post:
      operationId: PostEquipmentFaultsDeviceIdsList
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                items:
                  type: string
                type: array
              examples:
                Example 1:
                  value:
                    - equipment1
                    - equipment2
                    - equipment3
      tags:
        - equipment
      security: []
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EquipmentFaultsDeviceIdRequest'
  /equipment/faults/device-type/list:
    post:
      operationId: PostEquipmentFaultsDeviceTypeList
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                properties:
                  availableDeviceTypes:
                    items:
                      type: string
                    type: array
                required:
                  - availableDeviceTypes
                type: object
              examples:
                Example 1:
                  value:
                    availableDeviceTypes:
                      - Type 1
                      - Type 2
                      - Type 3
      tags:
        - equipment
      security: []
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FaultsDeviceTypeListRequest'
  /equipment/faults:
    get:
      operationId: GetEquipmentFaults
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: >-
                  #/components/schemas/ApiResponse_EquipmentFaultsDefinition.EquipmentFaultsConfigDefinition_
              examples:
                Example 1:
                  value:
                    faults: 50
                    status: caution
                    metadata:
                      highRange: 5
                      max: 50
      tags:
        - equipment
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
  /equipment/faults/average/duration/status/series:
    post:
      operationId: getFaultAvgDurationByStatus
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FaultAvgDurationSeriesData'
              examples:
                Example 1:
                  value:
                    avgDuration:
                      - name: Reason 1
                        value: 33.4
                      - name: Reason 2
                        value: 233.7
                      - name: Reason 3
                        value: 199.2
      description: >-
        Endpoint to get the average duration of faults by status for a given
        date range.
      tags:
        - equipment
      security: []
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FaultAvgDurationByStatusRequest'
  /equipment/faults/area:
    get:
      operationId: GetEquipmentFaultsArea
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: >-
                  #/components/schemas/ApiResponse_FaultsAreasResponse.FaultsAreasConfig_
              examples:
                Example 1:
                  value:
                    areas:
                      - id: f746a38e-8e7f-4863-8ddd-5df5e192c760
                        name: Picking
                        alertStatus:
                          status: ok
                          identifier: ''
                        operators:
                          activeOperators: 1
                          lowRecOperators: 0
                          highRecOperators: 2
                        faults:
                          totalFaults: 35
                          maxFaultsAllowed: 5
                          downtimeMinutes: 104.96945
                          status: caution
                      - id: 03dc76c5-e348-43ca-9b2a-9f3dfbe2079a
                        name: Packing
                        alertStatus:
                          status: ok
                          identifier: ''
                        operators:
                          activeOperators: 1
                          lowRecOperators: 0
                          highRecOperators: 2
                        faults:
                          totalFaults: 4
                          maxFaultsAllowed: 5
                          downtimeMinutes: 0.0020666666666666667
                          status: caution
                    metadata:
                      min: 0
                      max: 5
      tags:
        - equipment
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
  /equipment/faults/aisle/list:
    post:
      operationId: PostEquipmentFaultsAisleList
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AvailableAisles'
              examples:
                Example 1:
                  value:
                    availableAisles:
                      - Aisle 1
                      - Aisle 2
                      - Aisle 3
      tags:
        - equipment
      security: []
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FaultsAisleListRequest'
  /equipment/faults/active/list:
    get:
      operationId: GetEquipmentFaultsActiveList
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EquipmentActiveFaults'
              examples:
                Example 1:
                  value:
                    activeFaultsTable:
                      - area: Shipping
                        deviceReason: Code 123456
                      - area: Shipping
                        deviceReason: Code 343241324
                      - area: Packing
                        deviceReason: Code 765475674
      tags:
        - equipment
      security: []
      parameters: []
  /operators/healthcheck:
    get:
      operationId: GetOperatorsBasicHealthCheck
      responses:
        '200':
          description: Ok
          content:
            application/json:
              schema:
                properties:
                  sha:
                    type: string
                  status:
                    $ref: '#/components/schemas/HealthCheckStatus'
                required:
                  - sha
                  - status
                type: object
      tags:
        - operators
      security: []
      parameters: []
  /operators/healthcheck/full:
    get:
      operationId: GetOperatorsFullHealthCheck
      responses:
        '200':
          description: Ok
          content:
            application/json:
              schema:
                properties:
                  subSystems:
                    properties:
                      redis:
                        $ref: '#/components/schemas/HealthCheckStatus'
                      bigQuery:
                        $ref: '#/components/schemas/HealthCheckStatus'
                      auth0:
                        $ref: '#/components/schemas/HealthCheckStatus'
                    required:
                      - redis
                      - bigQuery
                      - auth0
                    type: object
                  sha:
                    type: string
                  status:
                    $ref: '#/components/schemas/HealthCheckStatus'
                required:
                  - subSystems
                  - sha
                  - status
                type: object
      tags:
        - operators
      security: []
      parameters: []
  /operators/areas:
    put:
      operationId: PutOperatorsAreas
      responses:
        '200':
          description: ''
      tags:
        - operators
      security: []
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RequestBody'
  /operators/active/series:
    get:
      operationId: GetOperatorsActiveSeries
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OperatorsChartSeriesApiResponse'
              examples:
                Example 1:
                  value:
                    operators:
                      - name: '2021-11-01T00:00:00.000Z'
                        value: 5
                      - name: '2021-11-01T01:00:00.000Z'
                        value: 10
                      - name: '2021-11-01T02:00:00.000Z'
                        value: 11
                      - name: '2021-11-01T03:00:00.000Z'
                        value: 10
                    metadata:
                      yMin: 0
                      yMax: 12
                      yHighBand: 8
                      yLowBand: 6
      tags:
        - operators
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
  /operators/active:
    get:
      operationId: GetOperatorsActive
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: >-
                  #/components/schemas/ApiResponse_OperatorCountData.OperatorCountsConfig_
              examples:
                Example 1:
                  value:
                    operatorCount: 1
                    alertStatus:
                      status: critical
                    metadata:
                      startDateTime: '2023-01-01T00:00:00.000Z'
                      endDateTime: '2023-01-02T00:00:00.000Z'
                      lowRecOperators: 150
                      highRecOperators: 250
                      maxOperators: 300
      tags:
        - operators
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
  /operators/active/areas:
    get:
      operationId: GetOperatorsActiveAreas
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/OperatorFacilityArea'
                type: array
              examples:
                Example 1:
                  value:
                    - id: '1'
                      name: test area
                      alertStatus:
                        status: ok
                      operators:
                        activeOperators: 2
                        lowRecOperators: 1
                        highRecOperators: 4
                        maxOperators: 5
                        pickRate: 20
                        queuedOrders: 8
      tags:
        - operators
      security: []
      parameters: []
  /orders/shipped:
    get:
      operationId: GetPickOrdersShipped
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse_OrderShippedData.___'
              examples:
                Example 1:
                  value:
                    total: 1000
                    current: 225
                    past: 300
                    change: -25
                    metadata: {}
      tags:
        - orders
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
  /orders/pick/cycle-count:
    get:
      operationId: GetOrdersPickCycleCount
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrdersPickCycleCountData'
              examples:
                Example 1:
                  value:
                    cycleCount: 208
      tags:
        - orders
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
  /orders/facility/shipped:
    get:
      operationId: GetOrdersFacilityShipped
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse_OrdersShipped.___'
              examples:
                Example 1:
                  value:
                    total: 1000
                    shipped: 225
                    metadata: {}
      description: >-
        Queries the wms_customer_order table to calculate the number of orders
        that have been shipped out of

        the total number of orders open between start_date and end_date
      tags:
        - orders
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
  /orders/facility/progress:
    get:
      operationId: GetOrdersFacilityProgress
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: >-
                  #/components/schemas/ApiResponse_OrderProgress.OrderProgressConfig_
              examples:
                Example 1:
                  value:
                    orderProgressPercentage: 35
      tags:
        - orders
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
  /orders/facility/completion:
    get:
      operationId: GetOrdersFacilityEstimatedCompletion
      responses:
        '200':
          description: contract
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerOrderEstimatedCompletionData'
              examples:
                Example 1:
                  value:
                    estimatedCompletionMinutes: 242
      tags:
        - orders
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
  /orders/customer/shipped:
    get:
      operationId: GetOrdersCustomerShipped
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse_OrdersShipped.___'
              examples:
                Example 1:
                  value:
                    total: 1000
                    shipped: 225
                    metadata: {}
      description: >-
        Queries the wms_customer_order table to calculate the number of orders
        that have been shipped out of

        the total number of orders open between start_date and end_date
      tags:
        - orders
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
  /orders/throughput:
    get:
      operationId: GetOrdersThroughput
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: >-
                  #/components/schemas/ApiResponse_OrderThroughputData.OrderThroughputConfig_
              examples:
                Example 1:
                  value:
                    throughputRateLinesPerHour: 100
                    metadata:
                      lowRange: 600
                      max: 1600
      tags:
        - orders
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: area
          required: false
          schema:
            $ref: '#/components/schemas/PickOrderArea'
  /orders/throughput/series:
    get:
      operationId: GetOrdersThroughputSeries
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrderThroughputChartApiResponse'
              examples:
                Example 1:
                  value:
                    throughput:
                      - name: '01'
                        value: 20
                      - name: '02'
                        value: 30
                    metadata: {}
      tags:
        - orders
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
  /orders/remaining:
    get:
      operationId: GetOrdersRemaining
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UnitsRemainingData'
              examples:
                Example 1:
                  value:
                    unitsRemaining: 1337
      tags:
        - orders
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
  /orders/customer/fulfillment:
    get:
      operationId: GetCustomerOrdersFulfillment
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectedOrderFulfillment'
              examples:
                Example 1:
                  value:
                    projectedOrderFulfillmentPercentage: 67.5
      tags:
        - orders
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
  /orders/pick/progress/series:
    get:
      operationId: GetOrdersProgressSeries
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrderProgressSeriesData'
              examples:
                Example 1:
                  value:
                    progress:
                      - name: '2024-04-01T00:00:00.000Z'
                        value: 82.3
                      - name: '2024-04-01T01:00:00.000Z'
                        value: 82.1
                      - name: '2024-04-01T02:00:00.000Z'
                        value: 81.5
                      - name: '2024-04-01T03:00:00.000Z'
                        value: 83.7
      description: >-
        Gets a series of order progress percentage, averaging by each hour
        incrementing.
      tags:
        - orders
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
  /orders/pick/line/throughput/series:
    get:
      operationId: GetOrdersPickLineThroughputSeries
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrderPickLineThroughputSeriesData'
              examples:
                Example 1:
                  value:
                    throughput:
                      - name: '01'
                        value: 20
                      - name: '02'
                        value: 30
      tags:
        - orders
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
  /orders/lineprogress:
    get:
      operationId: GetOrdersLineProgress
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: >-
                  #/components/schemas/ApiResponse_OrderLineProgress.OrderLineProgressConfig_
              examples:
                Example 1:
                  value:
                    lineProgressPercent: 98.37804793582056
                    metadata:
                      lowRange: 60
                      highRange: 75
                      max: 100
      tags:
        - orders
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
  /orders/performance/fulfillment:
    get:
      operationId: GetOrdersPerformanceFulfillment
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrderPerformanceFulfillmentData'
              examples:
                Example 1:
                  value:
                    orderFulfillment: 34
      tags:
        - orders
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: department_filter
          required: true
          schema:
            type: string
  /orders/line/throughput:
    get:
      operationId: GetOrdersLineThroughput
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrderLineThroughputData'
              examples:
                Example 1:
                  value:
                    totalCompletedOrderLines: 100
      tags:
        - orders
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
  /orders/lineprogress/series:
    get:
      operationId: GetOrdersLineProgressSeries
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrderLineProgressChartApiResponse'
              examples:
                Example 1:
                  value:
                    progress:
                      - name: '04'
                        value: 782
                      - name: '05'
                        value: 1535
                    trend:
                      - name: '05'
                        value: 1535
                      - name: '06'
                        value: 2302
                      - name: '07'
                        value: 3069
                    metadata:
                      yMin: 0
                      yMax: 3375
      tags:
        - orders
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
  /orders/healthcheck:
    get:
      operationId: GetOrdersBasicHealthCheck
      responses:
        '200':
          description: Ok
          content:
            application/json:
              schema:
                properties:
                  sha:
                    type: string
                  status:
                    $ref: '#/components/schemas/HealthCheckStatus'
                required:
                  - sha
                  - status
                type: object
      tags:
        - orders
      security: []
      parameters: []
  /orders/healthcheck/full:
    get:
      operationId: GetOrdersFullHealthCheck
      responses:
        '200':
          description: Ok
          content:
            application/json:
              schema:
                properties:
                  subSystems:
                    properties:
                      redis:
                        $ref: '#/components/schemas/HealthCheckStatus'
                      bigQuery:
                        $ref: '#/components/schemas/HealthCheckStatus'
                      auth0:
                        $ref: '#/components/schemas/HealthCheckStatus'
                    required:
                      - redis
                      - bigQuery
                      - auth0
                    type: object
                  sha:
                    type: string
                  status:
                    $ref: '#/components/schemas/HealthCheckStatus'
                required:
                  - subSystems
                  - sha
                  - status
                type: object
      tags:
        - orders
      security: []
      parameters: []
  /orders/fulfillment-outstanding:
    get:
      operationId: GetOrdersFulfillmentOutstanding
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse_OrdersOutstandingData.___'
              examples:
                Example 1:
                  value:
                    incompletedTotal: 28
                    metadata: {}
      tags:
        - orders
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
  /orders/facility/outstanding:
    get:
      operationId: GetOrdersFacilityOutstanding
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse_OrdersOutstandingData.___'
              examples:
                Example 1:
                  value:
                    incompletedTotal: 28
                    metadata: {}
      tags:
        - orders
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
  /orders/pick/cycletime:
    get:
      operationId: GetOrdersPickCycleTime
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: >-
                  #/components/schemas/ApiResponse_OrderCycleTimeData.OrderCycleTimeConfig_
              examples:
                Example 1:
                  value:
                    orderCycleTimeMinutes: 208
                    status: caution
                    metadata:
                      max: 200
                      highRange: 109
      tags:
        - orders
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
  /orders/cycletime/series:
    get:
      operationId: GetOrdersCycleTimeSeries
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrderCycleTimeChartApiResponse'
              examples:
                Example 1:
                  value:
                    orderCycleTimeChart:
                      - name: '00'
                        value: 23
                      - name: '01'
                        value: 34
                    metadata: {}
      tags:
        - orders
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
  /orders/progress:
    get:
      operationId: GetOrdersProgress
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: >-
                  #/components/schemas/ApiResponse_OrderProgress.OrderProgressConfig_
              examples:
                Example 1:
                  value:
                    orderProgressPercentage: 35
                    metadata:
                      seg1: 40
                      seg2: 50
                      seg3: 60
      tags:
        - orders
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
  /orders/customer/line/throughput:
    get:
      operationId: GetOrdersCustomerLineThroughput
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrderCustomerLineThroughputData'
              examples:
                Example 1:
                  value:
                    throughputRateOrderLinesPerHour: 100
      tags:
        - orders
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: area
          required: false
          schema:
            $ref: '#/components/schemas/WMSCustomerOrderArea'
  /orders/throughput/areas:
    get:
      operationId: GetOrdersThroughputAreas
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                properties:
                  areas:
                    items:
                      $ref: '#/components/schemas/ThroughputFacilityArea'
                    type: array
                required:
                  - areas
                type: object
              examples:
                Example 1:
                  value:
                    areas:
                      - id: testArea4
                        name: Shipping
                        operators:
                          activeOperators: 30
                          lowRecOperators: 25
                          highRecOperators: 31
                        alertStatus:
                          status: critical
                        throughput:
                          throughputRate: 2082
                          maxThroughputCapacity: 1100
                          minThroughputTarget: 800
                          maxThroughputTarget: 1000
                      - id: testArea3
                        name: Packing
                        operators:
                          activeOperators: 28
                          lowRecOperators: 19
                          highRecOperators: 35
                        alertStatus:
                          status: ok
                        throughput:
                          throughputRate: 1907
                          maxThroughputCapacity: 1100
                          minThroughputTarget: 800
                          maxThroughputTarget: 1000
                      - id: testArea1
                        name: Picking
                        operators:
                          activeOperators: 14
                          lowRecOperators: 19
                          highRecOperators: 37
                        alertStatus:
                          status: ok
                        throughput:
                          throughputRate: 1778
                          maxThroughputCapacity: 1100
                          minThroughputTarget: 800
                          maxThroughputTarget: 1000
                      - id: testArea2
                        name: Put Wall
                        operators:
                          activeOperators: 54
                          lowRecOperators: 32
                          highRecOperators: 34
                        alertStatus:
                          status: caution
                        throughput:
                          throughputRate: 954
                          maxThroughputCapacity: 1100
                          minThroughputTarget: 800
                          maxThroughputTarget: 1000
      tags:
        - orders
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: area
          required: false
          schema:
            type: string
  /orders/lineprogress/areas:
    get:
      operationId: GetOrdersLineProgressAreas
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrderLineProgressAreasData'
              examples:
                Example 1:
                  value:
                    areas:
                      - id: testArea1
                        name: Picking
                        alertStatus:
                          status: ok
                        operators:
                          activeOperators: 13
                          lowRecOperators: 19
                          highRecOperators: 37
                        orderLines:
                          orderLineProgress: 99.5
                          orderLinesCompleted: 426
                          totalOrderLines: 428
                      - id: testArea2
                        name: Put Wall
                        alertStatus:
                          status: caution
                        operators:
                          activeOperators: 55
                          lowRecOperators: 32
                          highRecOperators: 34
                        orderLines:
                          orderLineProgress: 100
                          orderLinesCompleted: 221
                          totalOrderLines: 221
                      - id: testArea3
                        name: Packing
                        alertStatus:
                          status: ok
                        operators:
                          activeOperators: 28
                          lowRecOperators: 19
                          highRecOperators: 35
                        orderLines:
                          orderLineProgress: 100
                          orderLinesCompleted: 764
                          totalOrderLines: 764
                      - id: testArea4
                        name: Shipping
                        alertStatus:
                          status: critical
                        operators:
                          activeOperators: 30
                          lowRecOperators: 25
                          highRecOperators: 31
                        orderLines:
                          orderLineProgress: 99.5
                          orderLinesCompleted: 386
                          totalOrderLines: 388
      tags:
        - orders
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
  /orders/cycletime/areas:
    get:
      operationId: GetOrdersCycleTime
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: >-
                  #/components/schemas/ApiResponse__areas-FacilityArea-Array_.CycleTimeAreasConfig_
              examples:
                Example 1:
                  value:
                    areas:
                      - id: testArea3
                        name: picking
                        operators:
                          activeOperators: 3
                          lowRecOperators: 14
                          highRecOperators: 20
                        cycleTime:
                          averageCycleTimeSeconds: 20
                          fastestCycleTimeSeconds: 10
                          slowestCycleTimeSeconds: 40
                          completionTime: '2021-11-28T06:34:35.551Z'
                          targetCycleTimeSeconds: 2000
                          slowestRecCycleTimeSeconds: 130000
                          status: ok
                        alertStatus:
                          status: ok
                      - id: testArea1
                        name: shipping
                        operators:
                          activeOperators: 3
                          lowRecOperators: 14
                          highRecOperators: 20
                        cycleTime:
                          averageCycleTimeSeconds: 30
                          fastestCycleTimeSeconds: 10
                          slowestCycleTimeSeconds: 40
                          completionTime: '2021-11-28T06:35:07.278Z'
                          targetCycleTimeSeconds: 2000
                          slowestRecCycleTimeSeconds: 130000
                          status: ok
                        alertStatus:
                          status: ok
                    metadata:
                      targetTime: 'T2:00:00'
      tags:
        - orders
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
  /orders/facility/throughput:
    get:
      operationId: GetOrdersFacilityThroughput
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerOrderThroughputData'
              examples:
                Example 1:
                  value:
                    throughputRateOrdersPerHour: 100
      description: >-
        Calculates the number of orders per hour that were shipped in the given
        time frame.

        If an area param is used, then the matching status is used in the
        calculation instead of 'shipped'.

        If the end_date is in the future, then the current datetime is used
        instead.
      tags:
        - orders
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
  /orders/facility/fulfillment:
    get:
      operationId: GetFacilityOrdersFulfillment
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectedOrderFulfillment'
              examples:
                Example 1:
                  value:
                    projectedOrderFulfillmentPercentage: 67.5
      tags:
        - orders
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
  /orders/facility/line/throughput/series:
    get:
      operationId: GetOrdersFacilityLineThroughputSeries
      responses:
        '200':
          description: contract
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FacilityOrderLineThroughputSeriesResponse'
              examples:
                Example 1:
                  value:
                    throughput:
                      - name: '01'
                        value: 20
                      - name: '02'
                        value: 30
      tags:
        - orders
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
  /orders/facility/line/progress/series:
    get:
      operationId: GetOrderLinesFacilityProgressSeries
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrderCustomerLineProgressSeriesData'
              examples:
                Example 1:
                  value:
                    progress:
                      - name: '2024-04-01T00:00:00.000Z'
                        value: 82.3
                      - name: '2024-04-01T01:00:00.000Z'
                        value: 82.1
                      - name: '2024-04-01T02:00:00.000Z'
                        value: 81.5
                      - name: '2024-04-01T03:00:00.000Z'
                        value: 83.7
      description: >-
        Gets a series of order progress percentage, averaging by each hour
        incrementing.
      tags:
        - orders
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
  /orders/facility/line/progress:
    get:
      operationId: GetOrdersFacilityLineProgress
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrderLineProgress'
              examples:
                Example 1:
                  value:
                    lineProgressPercent: 98.37804793582056
      tags:
        - orders
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
  /orders/facility/cycletime:
    get:
      operationId: GetOrdersFacilityCycleTime
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrderCycleTime'
              examples:
                Example 1:
                  value:
                    orderCycleTimeMinutes: 208
      tags:
        - orders
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
  /orders/completion:
    get:
      operationId: GetOrdersCompletion
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: >-
                  #/components/schemas/ApiResponse_EstimatedOrderCompletionTimes.EstimatedOrderCompletionConfig_
              examples:
                Example 1:
                  value:
                    completionTime: '2023-07-30T20:38:05.000Z'
                    metadata:
                      targetTime: 960
      tags:
        - orders
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: false
          schema:
            format: date-time
            type: string
  /orders/customer/throughput:
    get:
      operationId: GetOrdersCustomerThroughput
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerOrderThroughputData'
              examples:
                Example 1:
                  value:
                    throughputRateOrdersPerHour: 100
      description: >-
        Calculates the number of orders per hour that were shipped in the given
        time frame.

        If an area param is used, then the matching status is used in the
        calculation instead of 'shipped'.

        If the end_date is in the future, then the current datetime is used
        instead.
      tags:
        - orders
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: area
          required: false
          schema:
            $ref: '#/components/schemas/WMSCustomerOrderArea'
  /orders/customer/throughput/series:
    get:
      operationId: GetOrdersCustomerThroughputSeries
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                properties:
                  throughput:
                    items:
                      $ref: '#/components/schemas/ChartSeriesData'
                    type: array
                required:
                  - throughput
                type: object
              examples:
                Example 1:
                  value:
                    throughput:
                      - name: '01'
                        value: 20
                      - name: '02'
                        value: 30
      tags:
        - orders
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
  /orders/customer/progress/series:
    get:
      operationId: GetOrdersCustomerProgressSeries
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrderProgressSeriesData'
              examples:
                Example 1:
                  value:
                    progress:
                      - name: '2024-04-01T00:00:00.000Z'
                        value: 82.3
                      - name: '2024-04-01T01:00:00.000Z'
                        value: 82.1
                      - name: '2024-04-01T02:00:00.000Z'
                        value: 81.5
                      - name: '2024-04-01T03:00:00.000Z'
                        value: 83.7
      description: >-
        Gets a series of order progress percentage, averaging by each hour
        incrementing.
      tags:
        - orders
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: area
          required: false
          schema:
            $ref: '#/components/schemas/WMSCustomerOrderArea'
  /orders/customer/progress:
    get:
      operationId: GetOrdersCustomerProgress
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerOrderProgress'
              examples:
                Example 1:
                  value:
                    orderProgressPercentage: 35
      tags:
        - orders
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
  /orders/customer/line/throughput/series:
    get:
      operationId: GetOrdersCustomerLineThroughputSeries
      responses:
        '200':
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerOrderLineThroughputSeriesData'
              examples:
                Example 1:
                  value:
                    throughput:
                      - name: '2024-04-22T07:00:00.000Z'
                        value: 24
                      - name: '2024-04-22T07:15:00.000Z'
                        value: 62
                      - name: '2024-04-22T07:30:00.000Z'
                        value: 57
      tags:
        - orders
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
  /orders/customer/line/progress/series:
    get:
      operationId: GetOrderLinesCustomerProgressSeries
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrderCustomerLineProgressSeriesData'
              examples:
                Example 1:
                  value:
                    progress:
                      - name: '2024-04-01T00:00:00.000Z'
                        value: 82.3
                      - name: '2024-04-01T01:00:00.000Z'
                        value: 82.1
                      - name: '2024-04-01T02:00:00.000Z'
                        value: 81.5
                      - name: '2024-04-01T03:00:00.000Z'
                        value: 83.7
      description: >-
        Gets a series of order progress percentage, averaging by each hour
        incrementing.
      tags:
        - orders
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: area
          required: false
          schema:
            $ref: '#/components/schemas/WMSCustomerOrderArea'
  /orders/customer/line/progress:
    get:
      operationId: GetOrdersCustomerLineProgress
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrderLineProgress'
              examples:
                Example 1:
                  value:
                    lineProgressPercent: 98.37804793582056
      tags:
        - orders
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: area
          required: false
          schema:
            $ref: '#/components/schemas/WMSCustomerOrderArea'
  /orders/customer/completion:
    get:
      operationId: GetOrdersCustomerEstimatedCompletion
      responses:
        '200':
          description: contract
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerOrderEstimatedCompletionData'
              examples:
                Example 1:
                  value:
                    estimatedCompletionMinutes: 242
      tags:
        - orders
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
  /orders/customer/cycletime:
    get:
      operationId: GetOrdersCustomerCycleTime
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrderCycleTime'
              examples:
                Example 1:
                  value:
                    orderCycleTimeMinutes: 208
      tags:
        - orders
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: area
          required: false
          schema:
            $ref: '#/components/schemas/WMSCustomerOrderArea'
  /workstation/workstations:
    get:
      operationId: GetWorkstations
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkstationStringArray'
              examples:
                Example 1:
                  value:
                    - Workstation 1
                    - Workstation 2
      tags:
        - workstation
      security: []
      parameters:
        - in: query
          name: start_date
          required: false
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: false
          schema:
            format: date-time
            type: string
  /workstation/series:
    get:
      operationId: GetWorkstationSeriesData
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkstationSeriesData'
              examples:
                Example 1:
                  value:
                    data:
                      - id: Station 1
                        data:
                          - name: Item 1
                            value: 2
                          - name: Item 2
                            value: 39
                          - name: Item 3
                            value: 84
                          - name: Item 4
                            value: 30
                          - name: Item 5
                            value: 23
                          - name: Item 6
                            value: 46
                          - name: Item 7
                            value: 48
                          - name: Item 8
                            value: 69
                          - name: Item 9
                            value: 37
                          - name: Item 10
                            value: 83
      tags:
        - workstation
      security: []
      parameters:
        - in: query
          name: chart
          required: true
          schema:
            $ref: '#/components/schemas/WorkstationSeriesCharts'
  /workstation/orders/status:
    get:
      operationId: GetWorkstationOrdersStatus
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkstationOrdersStatusResponse'
      tags:
        - workstation
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
  /workstation/orders/list:
    post:
      operationId: PostWorkstationOrdersList
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkstationOrdersListData'
      tags:
        - workstation
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PaginatedRequestNoDates'
  /workstation/orders/list/export:
    post:
      operationId: PostWorkstationOrdersListExport
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: string
                format: byte
      tags:
        - workstation
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/PaginatedRequestNoDates'
                - properties:
                    columns:
                      properties: {}
                      additionalProperties:
                        type: boolean
                      type: object
                  required:
                    - columns
                  type: object
  '/workstation/orders/{orderId}/picks/list':
    post:
      operationId: PostWorkstationOrdersDetailsPicksList
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkstationOrdersDetailsPicksListData'
      tags:
        - workstation
      security: []
      parameters:
        - in: path
          name: orderId
          required: true
          schema:
            type: string
        - in: query
          name: zone
          required: false
          schema:
            default: ASRS
            type: string
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PaginatedRequestNoDates'
  '/workstation/orders/{orderId}/picks/list/export':
    post:
      operationId: PostWorkstationOrdersDetailsPicksListExport
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: string
                format: byte
      tags:
        - workstation
      security: []
      parameters:
        - in: path
          name: orderId
          required: true
          schema:
            type: string
        - in: query
          name: zone
          required: false
          schema:
            default: ASRS
            type: string
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/PaginatedRequestNoDates'
                - properties:
                    columns:
                      properties: {}
                      additionalProperties:
                        type: boolean
                      type: object
                  required:
                    - columns
                  type: object
  /workstation/metrics/summary:
    get:
      operationId: GetWorkstationSummary
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkstationStats'
              examples:
                Example 1:
                  value:
                    totalWorkstations: 20
                    status:
                      - count: 1
                        type: Active
                      - count: 2
                        type: Inactive
                    workstationMode:
                      - count: 1
                        type: Picking
                      - count: 2
                        type: Other
      tags:
        - workstation
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
  /workstation/metrics/operators:
    get:
      operationId: GetWorkstationOperatorSummary
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkstationOperatorStats'
              examples:
                Example 1:
                  value:
                    totalOperators: 10
                    actualLinesPerHour: 10
                    targetLinesPerHour: 20
      description: |-

        returns {Promise<WorkstationOperatorStats>} contract
      tags:
        - workstation
      security: []
      parameters: []
  /workstation/metrics/performance:
    get:
      operationId: GetWorkstationPerformanceSummary
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkstationPerformanceStats'
              examples:
                Example 1:
                  value:
                    totalActiveTime: 250
                    totalStarvationTime: 13
      description: |-

        returns {Promise<WorkstationPerformanceStats>} contract
      tags:
        - workstation
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
  /workstation/metrics/health:
    get:
      operationId: GetWorkstationHealthSummary
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkstationHealthStats'
              examples:
                Example 1:
                  value:
                    totalDowntime: 9
      description: |-

        returns {Promise<WorkstationHealthStats>} contract
      tags:
        - workstation
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
  /workstation/healthcheck:
    get:
      operationId: GetWorkstationBasicHealthCheck
      responses:
        '200':
          description: Ok
          content:
            application/json:
              schema:
                properties:
                  sha:
                    type: string
                  status:
                    $ref: '#/components/schemas/HealthCheckStatus'
                required:
                  - sha
                  - status
                type: object
      tags:
        - workstation
      security: []
      parameters: []
  /workstation/healthcheck/full:
    get:
      operationId: GetWorkstationFullHealthCheck
      responses:
        '200':
          description: Ok
          content:
            application/json:
              schema:
                properties:
                  subSystems:
                    properties:
                      redis:
                        $ref: '#/components/schemas/HealthCheckStatus'
                      bigQuery:
                        $ref: '#/components/schemas/HealthCheckStatus'
                      auth0:
                        $ref: '#/components/schemas/HealthCheckStatus'
                    required:
                      - redis
                      - bigQuery
                      - auth0
                    type: object
                  sha:
                    type: string
                  status:
                    $ref: '#/components/schemas/HealthCheckStatus'
                required:
                  - subSystems
                  - sha
                  - status
                type: object
      tags:
        - workstation
      security: []
      parameters: []
  /workstation/daily-performance/list:
    post:
      operationId: PostWorkstationDailyPerformanceList
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkstationDailyPerformanceList'
              examples:
                Example 1:
                  value:
                    - date: '2024-05-01T00:00:00.000Z'
                      totalLoggedInHours: 128.1
                      idlePercentage: 7.9
                      starvedPercentage: 10.3
                      starvedHours: 13.2
                      donorContainers: 7880
                      gtpContainers: 4700
                      linesPicked: 10397
                      qtyPerLine: 3.5
                      pickLineQty: 36555
                      linesPerHour: 81.1
                      avgLinesPickedPerHr1stShiftPercentage: 92
                      avgLinesPickedPerHr2ndShiftPercentage: 70
                      retrievalFromDMS: 11012
                      storageToDMS: 11725
                      retrievalFromASRS: 1101
                      storageToASRS: 486
      tags:
        - workstation
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/WorkstationDailyPerformanceListRequestBody'
  /workstation/daily-performance/list/export:
    post:
      operationId: PostWorkstationDailyPerformanceListExport
      responses:
        '200':
          description: Ok
          content:
            application/json:
              schema:
                type: string
                format: byte
      tags:
        - workstation
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              allOf:
                - $ref: >-
                    #/components/schemas/WorkstationDailyPerformanceListRequestBody
                - properties:
                    filename:
                      type: string
                    columns:
                      properties: {}
                      additionalProperties:
                        type: boolean
                      type: object
                  required:
                    - filename
                    - columns
                  type: object
  /workstation/list:
    post:
      operationId: GetWorkstationList
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkstationList'
              examples:
                Example 1:
                  value:
                    workstationList:
                      - null
                      - null
                      - null
                      - null
                      - null
      tags:
        - workstation
      security: []
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PaginatedRequestNoDates'
  /workstation/list/export:
    post:
      operationId: PostWorkstationListExport
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: string
                format: byte
      tags:
        - workstation
      security: []
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/PaginatedRequestNoDates'
                - properties:
                    columns:
                      properties: {}
                      additionalProperties:
                        type: boolean
                      type: object
                  required:
                    - columns
                  type: object
  /workstation/containers/list:
    post:
      operationId: PostWorkstationContainersList
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkstationContainersListData'
      tags:
        - workstation
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: size
          required: true
          schema:
            type: string
        - in: query
          name: style
          required: true
          schema:
            type: string
        - in: query
          name: zone
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PaginatedRequestNoDates'
  '/simulation/job/{jobId}/{taskIndex}/{fileName}/{fileType}/output':
    get:
      operationId: GetSimulationOutputByJobId
      responses:
        '200':
          description: Ok
          content:
            application/json:
              schema:
                anyOf:
                  - type: string
                    format: byte
                  - items: {}
                    type: array
      tags:
        - simulation
      security: []
      parameters:
        - in: path
          name: jobId
          required: true
          schema:
            type: string
        - in: path
          name: taskIndex
          required: true
          schema:
            format: double
            type: number
        - in: path
          name: fileName
          required: true
          schema:
            type: string
        - in: path
          name: fileType
          required: true
          schema:
            type: string
  /simulation/jobs/list:
    get:
      operationId: GetSimulationJobs
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponseArray_SimulationJob-Array.___'
      tags:
        - simulation
      security: []
      parameters: []
  /simulation/healthcheck:
    get:
      operationId: GetSimulationBasicHealthCheck
      responses:
        '200':
          description: Ok
          content:
            application/json:
              schema:
                properties:
                  sha:
                    type: string
                  status:
                    $ref: '#/components/schemas/HealthCheckStatus'
                required:
                  - sha
                  - status
                type: object
      tags:
        - simulation
      security: []
      parameters: []
  /simulation/healthcheck/full:
    get:
      operationId: GetSimulationFullHealthCheck
      responses:
        '200':
          description: Ok
          content:
            application/json:
              schema:
                properties:
                  subSystems:
                    properties:
                      auth0:
                        $ref: '#/components/schemas/HealthCheckStatus'
                    required:
                      - auth0
                    type: object
                  sha:
                    type: string
                  status:
                    $ref: '#/components/schemas/HealthCheckStatus'
                required:
                  - subSystems
                  - sha
                  - status
                type: object
      tags:
        - simulation
      security: []
      parameters: []
  /management/auth/trusted:
    get:
      operationId: GetTrustedTicket
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthTicket'
      tags:
        - tableau-management
      security: []
      parameters: []
  /admin/healthcheck:
    get:
      operationId: GetAdminBasicHealthCheck
      responses:
        '200':
          description: Ok
          content:
            application/json:
              schema:
                properties:
                  sha:
                    type: string
                  status:
                    $ref: '#/components/schemas/HealthCheckStatus'
                required:
                  - sha
                  - status
                type: object
      tags:
        - admin
      security: []
      parameters: []
  /admin/healthcheck/full:
    get:
      operationId: GetAdminFullHealthCheck
      responses:
        '200':
          description: Ok
          content:
            application/json:
              schema:
                properties:
                  subSystems:
                    properties:
                      redis:
                        $ref: '#/components/schemas/HealthCheckStatus'
                      bigQuery:
                        $ref: '#/components/schemas/HealthCheckStatus'
                      auth0:
                        $ref: '#/components/schemas/HealthCheckStatus'
                    required:
                      - redis
                      - bigQuery
                      - auth0
                    type: object
                  sha:
                    type: string
                  status:
                    $ref: '#/components/schemas/HealthCheckStatus'
                required:
                  - subSystems
                  - sha
                  - status
                type: object
      tags:
        - admin
      security: []
      parameters: []
  /admin/auth/resend-verification:
    post:
      operationId: ResendEmailVerification
      responses:
        '200':
          description: Email verification resent successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResendEmailVerificationResponse'
      tags:
        - admin
      security: []
      parameters: []
  /admin/auth/roles:
    get:
      operationId: GetAssignableRoles
      responses:
        '200':
          description: Roles retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetRolesResponse'
      tags:
        - admin
      security: []
      parameters: []
  /admin/auth/users/list:
    post:
      operationId: GetUsersList
      responses:
        '200':
          description: Users retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UsersListData'
      tags:
        - admin
      security: []
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PaginatedRequestNoDates'
  '/admin/auth/users/{id}':
    get:
      operationId: GetUser
      responses:
        '200':
          description: User retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserInfo'
      tags:
        - admin
      security: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
  '/admin/auth/users/{userId}/roles':
    post:
      operationId: AssignUserRoles
      responses:
        '200':
          description: Roles assigned successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AssignRoleResponse'
      tags:
        - admin
      security: []
      parameters:
        - in: path
          name: userId
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AssignRoleRequest'
  '/admin/auth/users/{userId}/roles/{roleName}':
    delete:
      operationId: RemoveUserRole
      responses:
        '200':
          description: Role removed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RemoveRoleResponse'
      tags:
        - admin
      security: []
      parameters:
        - in: path
          name: userId
          required: true
          schema:
            type: string
        - in: path
          name: roleName
          required: true
          schema:
            type: string
  /availability/sections/list:
    get:
      operationId: GetAvailableSections
      responses:
        '200':
          description: Successfully retrieved available sections
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAvailableSectionsResponse'
      description: Get available sections with filters using offset-based pagination
      tags:
        - availability
      security: []
      parameters:
        - description: '- Page number for pagination (1-based)'
          in: query
          name: page
          required: false
          schema:
            format: double
            type: number
        - description: '- Maximum number of records to return'
          in: query
          name: limit
          required: false
          schema:
            format: double
            type: number
        - description: '- Optional search term to filter sections'
          in: query
          name: search_string
          required: false
          schema:
            type: string
        - description: '- Optional equipment ID to filter sections by equipment'
          in: query
          name: equipment_id
          required: false
          schema:
            format: double
            type: number
  /availability/healthcheck:
    get:
      operationId: GetAdminBasicHealthCheck1
      responses:
        '200':
          description: Ok
          content:
            application/json:
              schema:
                properties:
                  sha:
                    type: string
                  status:
                    $ref: '#/components/schemas/HealthCheckStatus'
                required:
                  - sha
                  - status
                type: object
      tags:
        - availability
      security: []
      parameters: []
  /availability/healthcheck/full:
    get:
      operationId: GetAvailabilityFullHealthCheck
      responses:
        '200':
          description: Ok
          content:
            application/json:
              schema:
                properties:
                  subSystems:
                    properties:
                      auth0:
                        $ref: '#/components/schemas/HealthCheckStatus'
                    required:
                      - auth0
                    type: object
                  sha:
                    type: string
                  status:
                    $ref: '#/components/schemas/HealthCheckStatus'
                required:
                  - subSystems
                  - sha
                  - status
                type: object
      tags:
        - availability
      security: []
      parameters: []
  /availability/equipment/list:
    get:
      operationId: GetAvailableEquipment
      responses:
        '200':
          description: Successfully retrieved available equipment
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAvailableEquipmentResponse'
      description: Get available equipment with filters using offset-based pagination
      tags:
        - availability
      security: []
      parameters:
        - description: '- Page number for pagination (1-based)'
          in: query
          name: page
          required: false
          schema:
            format: double
            type: number
        - description: '- Maximum number of records to return'
          in: query
          name: limit
          required: false
          schema:
            format: double
            type: number
        - description: '- Optional search term to filter equipment'
          in: query
          name: search_string
          required: false
          schema:
            type: string
        - description: '- Optional section ID to filter equipment by section'
          in: query
          name: section_id
          required: false
          schema:
            format: double
            type: number
  /availability/alarms/audit-log/list:
    post:
      operationId: PostAuditLogsList
      responses:
        '200':
          description: Successfully retrieved audit logs
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PostAuditLogsListResponse'
              examples:
                Example 1:
                  value:
                    data:
                      - alarmId: bef3e88b-8b7d-4204-a2fe-fdab70ac7021
                        alarmIdentifier: bef3e88b
                        operationType: insert
                        originalValues: {}
                        newValues:
                          alarm_pk: 53003
                          alarm_id: bef3e88b-8b7d-4204-a2fe-fdab70ac7021
                          alarm_start_datetime_local: '2025-07-30T21:06:21.000Z'
                          facility_bid: acehardware#jeffersonga
                        diffValues: {}
                        modifiedUser: Test Created Alarm
                        modifiedTime: '2025-07-31T21:25:11.686Z'
                        modifiedComment: Test comments
                        processUuid: 550e8400-e29b-41d4-a716-************
                        processIdentifier: process_001
                    metadata:
                      page: 1
                      limit: 20
                      totalResults: 1
                      totalPages: 1
      description: Post audit logs data with filters using standard pagination format
      tags:
        - availability
      security: []
      parameters: []
      requestBody:
        description: >-
          - Standard pagination request with filters, sorting, grouping, and
          date range
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PaginatedRequest'
              description: >-
                - Standard pagination request with filters, sorting, grouping,
                and date range
  /availability/alarms/audit-log/export:
    post:
      operationId: ExportActivityLog
      responses:
        '200':
          description: Successfully exported audit logs to Excel
          content:
            application/json:
              schema:
                type: string
                format: byte
      tags:
        - availability
      security: []
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/NonPaginatedRequest'
  /availability/alarms/list:
    post:
      operationId: PostAlarmsList
      responses:
        '200':
          description: Successfully retrieved alarms
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PostAlarmsListResponse'
              examples:
                Example 1:
                  value:
                    data:
                      - id: c356e13f-8898-4e4f-bdce-bffcd148e605
                        splitId: '615123'
                        faultId: b307f935
                        title: CA003 ANTI GRID LOCK LEVEL 1
                        description: CA003 ANTI GRID LOCK LEVEL 1
                        tag: CA003_AGL_L1
                        location:
                          area: DMSInboundMergeSorter
                          section: CC001
                          equipment: CA003_AGL_L1
                        timing:
                          startTime: '2025-07-10T02:13:40.000Z'
                          endTime: '2025-07-10T02:14:07.000Z'
                          duration: '27000'
                          updatedStartTime: '2025-07-10T02:13:40.000Z'
                          updatedEndTime: '2025-07-10T02:14:07.000Z'
                          updatedDuration: '27000'
                        status: Active
                        reason: KEPT
                        lastUpdatedUser: <EMAIL>
                    metadata:
                      page: 1
                      limit: 20
                      totalResults: 1
                      totalPages: 1
      description: Post alarms data with filters using standard pagination format
      tags:
        - availability
      security: []
      parameters: []
      requestBody:
        description: >-
          - Standard pagination request with filters, sorting, grouping, and
          date range
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PaginatedRequest'
              description: >-
                - Standard pagination request with filters, sorting, grouping,
                and date range
  '/availability/alarms/{id}':
    get:
      operationId: GetAlarmById
      responses:
        '200':
          description: Successfully retrieved alarm
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Alarm'
      description: Get a single alarm by its ID
      tags:
        - availability
      security: []
      parameters:
        - description: '- The alarm ID to retrieve'
          in: path
          name: id
          required: true
          schema:
            type: string
  /availability/alarms:
    post:
      operationId: PostAlarms
      responses:
        '201':
          description: Successfully created alarm
      description: Create a new alarm
      tags:
        - availability
      security: []
      parameters: []
      requestBody:
        description: '- The alarm data to create'
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AlarmCreateDto'
              description: '- The alarm data to create'
    put:
      operationId: PutAlarms
      responses:
        '200':
          description: Successfully updated alarm
      description: Update an alarm
      tags:
        - availability
      security: []
      parameters: []
      requestBody:
        description: '- The alarm data to update'
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AlarmUpdateDto'
              description: '- The alarm data to update'
  /movements/healthcheck:
    get:
      operationId: GetMovementsBasicHealthCheck
      responses:
        '200':
          description: Ok
          content:
            application/json:
              schema:
                properties:
                  sha:
                    type: string
                  status:
                    $ref: '#/components/schemas/HealthCheckStatus'
                required:
                  - sha
                  - status
                type: object
      tags:
        - movements
      security: []
      parameters: []
  /movements/healthcheck/full:
    get:
      operationId: GetMovementsFullHealthCheck
      responses:
        '200':
          description: Ok
          content:
            application/json:
              schema:
                properties:
                  subSystems:
                    properties:
                      auth0:
                        $ref: '#/components/schemas/HealthCheckStatus'
                    required:
                      - auth0
                    type: object
                  sha:
                    type: string
                  status:
                    $ref: '#/components/schemas/HealthCheckStatus'
                required:
                  - subSystems
                  - sha
                  - status
                type: object
      tags:
        - movements
      security: []
      parameters: []
  /movements/summary:
    get:
      operationId: GetMovementsSummary
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/MovementByTypeData'
                type: array
      description: >-
        Get movements summary with aggregated data by type, including
        percentages
      tags:
        - movements
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: dms_id
          required: true
          schema:
            $ref: '#/components/schemas/DmsId'
        - description: '- Type of unit to aggregate by (loadunit or sku)'
          in: query
          name: unit_type
          required: false
          schema:
            $ref: '#/components/schemas/MovementUnitType'
        - description: '- Unit name'
          in: query
          name: unit_id
          required: false
          schema:
            type: string
  /movements/location:
    get:
      operationId: GetMovementsByLocation
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MovementLocationData'
      description: >-
        Get movements aggregated by location with filtering options

        Consolidates 4 endpoints: by/source, by/destination, by/source/location,
        by/destination/location
      tags:
        - movements
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: dms_id
          required: true
          schema:
            $ref: '#/components/schemas/DmsId'
        - in: query
          name: group_by_column
          required: true
          schema:
            $ref: '#/components/schemas/MovementLocationScope'
        - description: '- Type of unit to aggregate by (loadunit or sku)'
          in: query
          name: unit_type
          required: false
          schema:
            $ref: '#/components/schemas/MovementUnitType'
        - description: '- Unit name'
          in: query
          name: unit_id
          required: false
          schema:
            type: string
  /movements/time-series:
    get:
      operationId: GetMovementsByTimeSeries
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MovementTimeSeriesData'
      description: |-
        Get movements aggregated by time series with configurable granularity
        Consolidates 2 endpoints: by/day and by/hour
      tags:
        - movements
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: dms_id
          required: true
          schema:
            $ref: '#/components/schemas/DmsId'
        - in: query
          name: group_by_column
          required: true
          schema:
            $ref: '#/components/schemas/MovementTimeSeriesGranularity'
        - description: '- Type of unit to aggregate by (loadunit or sku)'
          in: query
          name: unit_type
          required: false
          schema:
            $ref: '#/components/schemas/MovementUnitType'
        - description: '- Unit name'
          in: query
          name: unit_id
          required: false
          schema:
            type: string
  /movements/load-units:
    get:
      operationId: GetMovementsByLoadUnits
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: >-
                  #/components/schemas/ApiResponseArray_MovementLoadUnitsData-Array._page-number--limit-number--totalResults-number__
      description: >-
        Get movements aggregated by load units with filtering and pagination
        options
      tags:
        - movements
      security: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: end_date
          required: true
          schema:
            format: date-time
            type: string
        - in: query
          name: dms_id
          required: true
          schema:
            $ref: '#/components/schemas/DmsId'
        - description: '- Type of unit to aggregate by (loadunit or sku)'
          in: query
          name: group_by_column
          required: true
          schema:
            $ref: '#/components/schemas/MovementUnitType'
        - description: '- Unit type name'
          in: query
          name: unit_id
          required: false
          schema:
            type: string
        - description: '- Maximum number of items per page (default: 100, max: 1000)'
          in: query
          name: limit
          required: false
          schema:
            format: double
            type: number
        - description: '- Page number for pagination (default: 1)'
          in: query
          name: page
          required: false
          schema:
            format: double
            type: number
components:
  schemas:
    HealthCheckStatus:
      enum:
        - ONLINE
        - OFFLINE
        - NOT_IN_USE
        - UNHEALTHY
        - UNKOWN
      type: string
    ExtractiveAnswer:
      properties:
        content:
          type: string
        pageNumber:
          type: string
      required:
        - content
        - pageNumber
      type: object
      additionalProperties: false
    DerivedStructData:
      properties:
        extractive_answers:
          items:
            $ref: '#/components/schemas/ExtractiveAnswer'
          type: array
        link:
          type: string
      required:
        - extractive_answers
        - link
      type: object
      additionalProperties: false
    EnterpriseSearchDocument:
      properties:
        derivedStructData:
          $ref: '#/components/schemas/DerivedStructData'
        id:
          type: string
        name:
          type: string
      required:
        - derivedStructData
        - id
        - name
      type: object
      additionalProperties: false
    EnterpriseSearchResponse:
      properties:
        document:
          $ref: '#/components/schemas/EnterpriseSearchDocument'
        id:
          type: string
      required:
        - document
        - id
      type: object
      additionalProperties: false
    AppConfigSettingName:
      anyOf:
        - allOf:
            - type: string
            - properties: {}
              type: object
        - type: string
          enum:
            - facility-maps
            - menu
            - selected-facility-id
            - site-time-zone
            - shift-start-end-times
            - user-favorites
            - file-upload-dashboard
            - inbound-overview-dashboard
            - inventory-overview-dashboard
            - outbound-overview-dashboard
            - picking-buffer-area-details-dashboard
            - workstation-overview-dashboard
            - inventory-replenishment-dashboard
            - fault-tracking-manual-entry-button
            - fault-tracking-edit-timings-button
            - fault-tracking-calculation-status-button
            - ict-nav-help-content
            - ict-facility-process-flow-admin-controls
            - ict-facility-process-flow-config-management
            - ict-facility-process-flow-detail-panel
            - ict-facility-process-flow-detail-panel-layout
            - ict-facility-process-flow-edge-animation-effect
            - ict-facility-process-flow-edge-label-alerts
            - ict-facility-process-flow-edge-label-status-indicators
            - ict-facility-process-flow-edge-labels
            - ict-facility-process-flow-graph-config
            - ict-facility-process-flow-node-alerts
            - ict-facility-process-flow-node-drilldown-button
            - ict-facility-process-flow-node-metrics
            - ict-facility-process-flow-polling
            - ict-facility-process-flow-polling-interval
            - data-explorer-debug-data
            - ict-data-integration-configuration
            - inventory-high-impact-sku-percentage
            - inventory-sku-list-column-order
            - inventory-storage-utilization-aisle-filter-list
            - inventory-storage-utilization-area-filter-list
            - inventory-work-area-code-filter-list
            - inventory-zone-mapping
            - cycle-time-state-range
            - order-complete-event-codes
            - excluded-workstation-list
            - workstation-destination-locations-suffixes
            - workstation-order-delayed-time
            - data-explorer-default-recommendations
            - tableau-top-level-project-name
    AppConfigSettingGroupName:
      anyOf:
        - allOf:
            - type: string
            - properties: {}
              type: object
        - type: string
          enum:
            - system
            - feature-flags
            - custom-dashboards
            - inventory
            - orders
            - workstations
            - data-explorer
            - tableau
    AppConfigSettingDataType:
      type: string
      enum:
        - json
        - string
        - number
        - boolean
    AppConfigSettingSource:
      enum:
        - default
        - tenant
        - facility
        - user
      type: string
    AppConfigSetting:
      description: >-
        DTO for a setting with its value set to the most relevant value for the
        user.
      properties:
        id:
          type: string
        name:
          anyOf:
            - $ref: '#/components/schemas/AppConfigSettingName'
            - type: string
        group:
          anyOf:
            - $ref: '#/components/schemas/AppConfigSettingGroupName'
            - type: string
          nullable: true
        description:
          type: string
          nullable: true
        dataType:
          $ref: '#/components/schemas/AppConfigSettingDataType'
        source:
          $ref: '#/components/schemas/AppConfigSettingSource'
        value: {}
        tags:
          properties: {}
          additionalProperties:
            type: string
          type: object
          nullable: true
        parentSetting:
          type: string
          nullable: true
      required:
        - id
        - name
        - dataType
      type: object
      additionalProperties: false
    UpdateSettingData:
      properties:
        id:
          type: string
        name:
          type: string
        group:
          type: string
          nullable: true
        dataType:
          $ref: '#/components/schemas/AppConfigSettingDataType'
        value: {}
        levelToUpdate:
          $ref: '#/components/schemas/AppConfigSettingSource'
        description:
          type: string
          nullable: true
      required:
        - name
        - dataType
        - value
        - levelToUpdate
      type: object
      additionalProperties: false
    Record_string.boolean_:
      properties: {}
      additionalProperties:
        type: boolean
      type: object
      description: Construct a type with a set of properties K of type T
    MetricConfigSummary:
      description: Represents a metric configuration
      properties:
        id:
          type: string
          description: Unique identifier for the metric configuration
        metricName:
          type: string
          description: Name of the metric
        configType:
          type: string
          description: 'Type of configuration (e.g., ''node'', ''edge'')'
        nodeName:
          type: string
          description: Name of the node this metric is associated with
        factType:
          type: string
          description: Type of fact this metric represents
        enabled:
          anyOf:
            - type: boolean
            - $ref: '#/components/schemas/Record_string.boolean_'
          nullable: true
          description: >-
            Whether the metric is enabled

            For default configurations, this is an hstore where keys are
            facility IDs and values are booleans

            For custom configurations, this is a boolean indicating if the
            metric is enabled for that facility

            Can be undefined for default configurations if no facilities have
            enabled/disabled the metric

            Can be null for custom configurations where enabled status is not
            applicable
        active:
          anyOf:
            - type: boolean
            - $ref: '#/components/schemas/Record_string.boolean_'
          description: >-
            Whether the metric is active

            For default configurations, this is an hstore where keys are
            facility IDs and values are booleans

            For custom configurations, this is a boolean indicating if the
            metric is active for that facility

            Can be undefined for default configurations if no facilities have
            active/inactive status
        isCustom:
          type: boolean
          description: >-
            Whether this is a custom configuration (true) or default
            configuration (false)
        facilityId:
          type: string
          description: |-
            The facility ID this configuration applies to
            For default configurations, this is 'default'
            For custom configurations, this is the specific facility ID
        views:
          items:
            type: string
          type: array
          description: The list of views that the resulting node will appear in
      required:
        - id
        - metricName
        - configType
        - isCustom
        - facilityId
        - views
      type: object
      additionalProperties: false
    Record_string.string_:
      properties: {}
      type: object
      description: Construct a type with a set of properties K of type T
    Record_string.string-or-Array_Record_string.string___:
      properties: {}
      additionalProperties:
        anyOf:
          - type: string
          - items:
              $ref: '#/components/schemas/Record_string.string_'
            type: array
      type: object
      description: Construct a type with a set of properties K of type T
    MatchConditions:
      $ref: >-
        #/components/schemas/Record_string.string-or-Array_Record_string.string___
    ConfigType:
      type: string
      enum:
        - node
        - inbound-edge
        - outbound-edge
        - complete-edge
    GraphOperation:
      type: string
      enum:
        - area_node
        - area_edge
        - shuttle_node
    Record_string.string-or-number_:
      properties: {}
      additionalProperties:
        anyOf:
          - type: string
          - type: number
            format: double
      type: object
      description: Construct a type with a set of properties K of type T
    RedisParams:
      $ref: '#/components/schemas/Record_string.string-or-number_'
    NodeLabel:
      type: string
      enum:
        - Aisle
        - Area
        - Lift
        - Station
        - StationLocation
        - Level
        - Metric
        - Shuttle
    NameConfig:
      description: >-
        Defines the structure for dynamically generating metric names based on
        event data for inbound or outbound edges
      properties:
        source:
          type: string
          description: >-
            A format string representing the source field from the event data.

            Example: `"{source_location_code}"` (must match a key in event
            data).
        pattern:
          type: string
          description: >-
            A regular expression pattern for extracting dynamic values from
            `source`.

            Example: `"MSAI(?P<aisle>\\d{2}).*"` extracts an `aisle` number from
            a multishuttle location.
        template:
          type: string
          description: >-
            A format string that defines how to generate the final metric name
            using extracted values.

            Example: `"MSAI{aisle}"` produces `"MSAI01"`, `"MSAI02"`, etc.
      required:
        - source
        - pattern
        - template
      type: object
      additionalProperties: false
    BaseMetricConfigDetail:
      description: Base interface for all metric configurations
      properties:
        metricConfigName:
          type: string
          description: |-
            Identifier for the metric config
            Example: multishuttleAisleStorageLocationDistributionAvailable
        views:
          items:
            type: string
          type: array
          description: |-
            The hierarchy graph view at which this metric applies
            Each view pertains to a unique graph node.
            Example: ["facility", "multishuttle"]
        matchConditions:
          $ref: '#/components/schemas/MatchConditions'
          description: Conditions an event must meet for this metric to be processed
        configType:
          $ref: '#/components/schemas/ConfigType'
          description: The type of metric being processed (node or edge)
        graphOperation:
          allOf:
            - $ref: '#/components/schemas/GraphOperation'
          nullable: true
          description: The type of graph database operation
        redisParams:
          allOf:
            - $ref: '#/components/schemas/RedisParams'
          nullable: true
          description: |-
            Optional parameters required for metric processing
            Example: {"rowHash": "{rowHash}"}
        label:
          allOf:
            - $ref: '#/components/schemas/NodeLabel'
          nullable: true
          description: The label for the node. Defaults to "Area"
        parentNodes:
          items:
            type: string
          type: array
          nullable: true
          description: Defines the parent node hierarchy for this metric
        nameFormula:
          allOf:
            - $ref: '#/components/schemas/NameConfig'
          nullable: true
          description: >-
            Defines how to dynamically generate metric names based on source
            fields.

            Note: Either this or node_name should be provided, but not both.
        factType:
          type: string
          description: The type of fact this metric represents
        sourceSystem:
          type: string
          description: |-
            The source system of the facts
            Example: "diq"
        displayName:
          type: string
          description: Display name for the UI
        description:
          type: string
          nullable: true
          description: Description of the metric configuration
        acceptableLowRange:
          type: number
          format: double
          nullable: true
          description: |-
            Acceptable low range
            Used to indicate if the actual metric value is acceptable
        acceptableHighRange:
          type: number
          format: double
          nullable: true
          description: |-
            Acceptable high range
            Used to indicate if the actual metric value is acceptable
        exampleMessage:
          nullable: true
          description: Example message for testing
        enabled:
          anyOf:
            - type: boolean
            - $ref: '#/components/schemas/Record_string.boolean_'
          nullable: true
          description: >-
            Whether the metric is enabled

            For default configurations, this is an hstore where keys are
            facility IDs and values are booleans

            For custom configurations, this is a boolean indicating if the
            metric is enabled for that facility

            Can be undefined for default configurations if no facilities have
            enabled/disabled the metric

            Can be null for custom configurations where enabled status is not
            applicable
        active:
          anyOf:
            - type: boolean
            - $ref: '#/components/schemas/Record_string.boolean_'
          description: >-
            Whether the metric is active

            For default configurations, this is an hstore where keys are
            facility IDs and values are booleans

            For custom configurations, this is a boolean indicating if the
            metric is active for that facility

            Can be undefined for default configurations if no facilities have
            active/inactive status
        isCustom:
          type: boolean
          description: >-
            Whether this is a custom configuration (true) or default
            configuration (false)
      required:
        - metricConfigName
        - views
        - matchConditions
        - configType
        - factType
        - sourceSystem
        - displayName
        - isCustom
      type: object
      additionalProperties: false
    TimeWindow:
      type: string
      enum:
        - 15m_set
        - 30m_set
        - 60m_set
        - value
    Aggregation:
      type: string
      enum:
        - avg
        - count
        - destination_position_ratio
        - hourly_rate
        - ratio
        - static
        - sum
        - sum_item_values
    RedisOperation:
      type: string
      enum:
        - boolean_toggle
        - complex_event_set
        - cycle_time_start
        - cycle_time_stop
        - distinct_item_count
        - distinct_item_subtract
        - event_set
        - storage_location_distribution_available
        - storage_location_distribution_occupied
        - storage_utilization
        - store_static_value
        - total_storage_locations_occupied
    NodeMetricConfigDetail:
      allOf:
        - $ref: '#/components/schemas/BaseMetricConfigDetail'
        - properties:
            nodeName:
              type: string
              nullable: true
              description: >-
                Static node name (alternative to nameFormula)

                Note: Either this or nameFormula should be provided, but not
                both.

                If provided, nameFormula should not be used.
            metricUnits:
              type: string
              nullable: true
              description: |-
                Optional units for the metric
                Example: "/hr"
            redisOperation:
              $ref: '#/components/schemas/RedisOperation'
              description: |-
                The Redis method to be performed
                Example: "eventSet"
            aggregation:
              $ref: '#/components/schemas/Aggregation'
              description: The aggregation method for the metric
            timeWindow:
              $ref: '#/components/schemas/TimeWindow'
              description: The time window for the metric
            metricType:
              type: string
              description: |-
                The type of metric being measured
                Example: "stockTime"
            graphOperation:
              $ref: '#/components/schemas/GraphOperation'
              description: |-
                The type of operation performed on the graph database
                Example: "area_node"
            configType:
              type: string
              enum:
                - node
              nullable: false
          required:
            - redisOperation
            - aggregation
            - timeWindow
            - metricType
            - graphOperation
            - configType
          type: object
      description: Node Metric Configuration
    InboundEdgeMetricConfigDetail:
      allOf:
        - $ref: '#/components/schemas/BaseMetricConfigDetail'
        - properties:
            inboundParentNodes:
              items:
                type: string
              type: array
              nullable: true
              description: The parent nodes for the inbound area
            metricUnits:
              type: string
              nullable: true
              description: |-
                Optional units for the metric
                Default: "units/hr"
            graphOperation:
              $ref: '#/components/schemas/GraphOperation'
              description: |-
                The type of operation performed on the graph database
                Example: "area_node"
            redisOperation:
              $ref: '#/components/schemas/RedisOperation'
              description: |-
                The Redis operation performed upon arrival
                Example: "eventSet"
            inboundArea:
              type: string
              description: |-
                The name of the area where the handling unit is arriving
                Example: "multishuttle"
            huId:
              type: string
              description: |-
                The key in event data that identifies the handling unit
                Example: "handlingUnitCode"
            configType:
              type: string
              enum:
                - inbound-edge
              nullable: false
          required:
            - graphOperation
            - redisOperation
            - inboundArea
            - huId
            - configType
          type: object
      description: |-
        Inbound Edge Metric Configuration
        Tracks movement INTO an area
    OutboundEdgeMetricConfigDetail:
      allOf:
        - $ref: '#/components/schemas/BaseMetricConfigDetail'
        - properties:
            outboundParentNodes:
              items:
                type: string
              type: array
              nullable: true
              description: The parent nodes for the outbound area
            units:
              type: string
              description: |-
                The type of units being counted
                Example: "handlingUnit"
            huId:
              type: string
              description: |-
                The key in event data that identifies the handling unit
                Example: "handlingUnitCode"
            outboundArea:
              type: string
              description: |-
                The name of the area the handling unit is leaving
                Example: "multishuttle"
            configType:
              type: string
              enum:
                - outbound-edge
              nullable: false
          required:
            - units
            - huId
            - outboundArea
            - configType
          type: object
      description: |-
        Outbound Edge Metric Configuration
        Tracks movement OUT OF an area
    CompleteEdgeMetricConfigDetail:
      description: |-
        Complete Edge Metric Configuration
        Tracks movement BETWEEN two areas
      properties:
        metricConfigName:
          type: string
          description: |-
            Identifier for the metric config
            Example: multishuttleAisleStorageLocationDistributionAvailable
        views:
          items:
            type: string
          type: array
          description: |-
            The hierarchy graph view at which this metric applies
            Each view pertains to a unique graph node.
            Example: ["facility", "multishuttle"]
        matchConditions:
          $ref: '#/components/schemas/MatchConditions'
          description: Conditions an event must meet for this metric to be processed
        configType:
          type: string
          enum:
            - complete-edge
          nullable: false
          description: The type of metric being processed (node or edge)
        graphOperation:
          $ref: '#/components/schemas/GraphOperation'
          description: |-
            The type of operation performed on the graph database
            Example: "area_node"
        redisParams:
          allOf:
            - $ref: '#/components/schemas/RedisParams'
          nullable: true
          description: |-
            Optional parameters required for metric processing
            Example: {"rowHash": "{rowHash}"}
        label:
          allOf:
            - $ref: '#/components/schemas/NodeLabel'
          nullable: true
          description: The label for the node. Defaults to "Area"
        parentNodes:
          items:
            type: string
          type: array
          nullable: true
          description: Defines the parent node hierarchy for this metric
        nameFormula:
          allOf:
            - $ref: '#/components/schemas/NameConfig'
          nullable: true
          description: >-
            Defines how to dynamically generate metric names based on source
            fields.

            Note: Either this or node_name should be provided, but not both.
        factType:
          type: string
          description: The type of fact this metric represents
        sourceSystem:
          type: string
          description: |-
            The source system of the facts
            Example: "diq"
        displayName:
          type: string
          description: Display name for the UI
        description:
          type: string
          nullable: true
          description: Description of the metric configuration
        acceptableLowRange:
          type: number
          format: double
          nullable: true
          description: |-
            Acceptable low range
            Used to indicate if the actual metric value is acceptable
        acceptableHighRange:
          type: number
          format: double
          nullable: true
          description: |-
            Acceptable high range
            Used to indicate if the actual metric value is acceptable
        exampleMessage:
          nullable: true
          description: Example message for testing
        enabled:
          anyOf:
            - type: boolean
            - $ref: '#/components/schemas/Record_string.boolean_'
          nullable: true
          description: >-
            Whether the metric is enabled

            For default configurations, this is an hstore where keys are
            facility IDs and values are booleans

            For custom configurations, this is a boolean indicating if the
            metric is enabled for that facility

            Can be undefined for default configurations if no facilities have
            enabled/disabled the metric

            Can be null for custom configurations where enabled status is not
            applicable
        active:
          anyOf:
            - type: boolean
            - $ref: '#/components/schemas/Record_string.boolean_'
          description: >-
            Whether the metric is active

            For default configurations, this is an hstore where keys are
            facility IDs and values are booleans

            For custom configurations, this is a boolean indicating if the
            metric is active for that facility

            Can be undefined for default configurations if no facilities have
            active/inactive status
        isCustom:
          type: boolean
          description: >-
            Whether this is a custom configuration (true) or default
            configuration (false)
        inboundArea:
          type: string
          description: |-
            The name of the area where the handling unit is arriving
            Example: "multishuttle"
        outboundArea:
          type: string
          description: |-
            The name of the area the handling unit is leaving
            Example: "multishuttle"
        redisOperation:
          $ref: '#/components/schemas/RedisOperation'
          description: |-
            The Redis operation performed
            Example: "eventSet"
        outboundNodeLabel:
          type: string
          description: |-
            The label for the outbound node
            Default: "Area"
        inboundParentNodes:
          items:
            type: string
          type: array
          nullable: true
          description: The parent nodes for the inbound area
        outboundParentNodes:
          items:
            type: string
          type: array
          nullable: true
          description: The parent nodes for the outbound area
        metricUnits:
          type: string
          nullable: true
          description: |-
            Optional units for the metric
            Default: "units/hr"
      required:
        - metricConfigName
        - views
        - matchConditions
        - configType
        - factType
        - sourceSystem
        - displayName
        - isCustom
        - inboundArea
        - outboundArea
        - redisOperation
        - graphOperation
        - outboundNodeLabel
      type: object
      additionalProperties: false
    MetricConfigDetail:
      anyOf:
        - $ref: '#/components/schemas/NodeMetricConfigDetail'
        - $ref: '#/components/schemas/InboundEdgeMetricConfigDetail'
        - $ref: '#/components/schemas/OutboundEdgeMetricConfigDetail'
        - $ref: '#/components/schemas/CompleteEdgeMetricConfigDetail'
      description: Union type for all metric configurations (API request/response format)
    MetricConfigValueResponse:
      description: |-
        Response structure for GET metric config detail endpoint
        Returns a single metric configuration with precedence rules applied
      properties:
        default:
          $ref: '#/components/schemas/MetricConfigDetail'
          description: 'Default metric configuration, if available'
        custom:
          $ref: '#/components/schemas/MetricConfigDetail'
          description: 'Custom metric configuration, if available'
      type: object
      additionalProperties: false
    CustomMetricConfigurationEntity:
      description: >-
        Entity for custom metric configurations.

        Extends the base metric configuration to add facility-specific
        overrides.

        The composite unique index on (metric_config_name, facility_id) ensures
        each facility

        can only have one custom configuration per metric configuration.
      properties:
        id:
          type: string
          description: 'Primary key id of the entity, set to be generated to a UUID.'
        description:
          type: string
          nullable: true
          description: Optional description of the entity.
          default: null
        createdAt:
          type: string
          format: date-time
          description: Date and time the entity was created.
        updatedAt:
          type: string
          format: date-time
          description: Date and time the entity was last updated.
        metricConfigName:
          type: string
          description: >-
            The unique identifier for this metric configuration.

            This is used to reference the configuration in custom configurations
            and facility settings.

            Example: shuttle_inventory_totes_shuffle_movements
        factType:
          type: string
          description: |-
            The type of fact this metric processes
            e.g. multishuttle_movement
        sourceSystem:
          type: string
          description: |-
            The source system of the facts
            e.g. diq
        displayName:
          type: string
          description: Display name for the UI
        configType:
          $ref: '#/components/schemas/ConfigType'
          description: Type of configuration
        acceptableLowRange:
          type: number
          format: double
          description: |-
            Acceptable low range
            Used to indicate if the actual metric value is acceptable
        acceptableHighRange:
          type: number
          format: double
          description: |-
            Acceptable high range
            Used to indicate if the actual metric value is acceptable
        exampleMessage:
          description: Example message for testing
        views:
          items:
            type: string
          type: array
          description: |-
            The hierarchy graph view at which this metric applies
            Example: `["facility", "multishuttle"]`
        matchConditions:
          $ref: '#/components/schemas/MatchConditions'
          description: >-
            Defines the conditions an event must meet for this metric to be
            processed
        redisParams:
          $ref: '#/components/schemas/RedisParams'
          description: >-
            Optional parameters required for metric processing

            Example: `{"row_hash": "{row_hash}"}` dynamically fills `row_hash`
            with event data
        nameFormula:
          $ref: '#/components/schemas/NameConfig'
          description: >-
            Defines how to dynamically generate metric names based on source
            fields
        label:
          $ref: '#/components/schemas/NodeLabel'
          description: The label for the node. Defaults to "Area"
        parentNodes:
          items:
            type: string
          type: array
          description: |-
            Defines the parent node hierarchy for this metric
            Can be:
            - undefined: No parent nodes
            - string: Single parent node name
            - string[]: Array of parent node names in hierarchical order
            Example: `"multishuttle"` or `["multishuttle", "aisle_code"]`
        nodeName:
          type: string
        metricType:
          type: string
        timeWindow:
          $ref: '#/components/schemas/TimeWindow'
        aggregation:
          $ref: '#/components/schemas/Aggregation'
        graphOperation:
          $ref: '#/components/schemas/GraphOperation'
        redisOperation:
          $ref: '#/components/schemas/RedisOperation'
        metricUnits:
          type: string
        huId:
          type: string
        inboundArea:
          type: string
        outboundArea:
          type: string
        units:
          type: string
        outboundNodeLabel:
          $ref: '#/components/schemas/NodeLabel'
        inboundParentNodes:
          items:
            type: string
          type: array
        outboundParentNodes:
          items:
            type: string
          type: array
        facilityId:
          type: string
          description: |-
            The facility ID this configuration applies to.
            Part of the composite unique index with metric_config_name to ensure
            each facility can only have one custom configuration per metric.
        active:
          type: boolean
          description: |-
            Whether this metric is active for this facility.
            A metric is active if an incoming fact was matched on it.
        enabled:
          type: boolean
          description: |-
            Whether this metric is enabled for this facility.
            A metric is enabled on the facility level if a user has enabled it.
        defaultConfig:
          $ref: '#/components/schemas/Awaited_DefaultMetricConfigurationEntity_'
          description: >-
            The default configuration this custom configuration extends
            (optional).

            This creates a many-to-one relationship with the default
            configuration.

            Custom configurations can exist independently without a default
            configuration.

            When the default configuration is deleted, this custom configuration
            will be deleted as well.
      required:
        - id
        - createdAt
        - updatedAt
        - metricConfigName
        - configType
        - views
        - matchConditions
        - facilityId
        - active
        - enabled
      type: object
      additionalProperties: false
    Awaited_CustomMetricConfigurationEntity_:
      $ref: '#/components/schemas/CustomMetricConfigurationEntity'
      description: >-
        Recursively unwraps the "awaited type" of a type. Non-promise
        "thenables" should resolve to `never`. This emulates the behavior of
        `await`.
    DefaultMetricConfigurationEntity:
      description: >-
        Entity for default metric configurations.

        This is the base configuration that defines how a metric should be
        processed.

        It has a unique metric_config_name and can be referenced by both custom
        configurations

        and facility settings.


        The enabled and active fields are h-stores where:

        - Keys are facility IDs

        - Values are booleans indicating if the metric is enabled/active for
        that facility
      properties:
        id:
          type: string
          description: 'Primary key id of the entity, set to be generated to a UUID.'
        description:
          type: string
          nullable: true
          description: Optional description of the entity.
          default: null
        createdAt:
          type: string
          format: date-time
          description: Date and time the entity was created.
        updatedAt:
          type: string
          format: date-time
          description: Date and time the entity was last updated.
        metricConfigName:
          type: string
          description: >-
            The unique identifier for this metric configuration.

            This is used to reference the configuration in custom configurations
            and facility settings.

            Example: shuttle_inventory_totes_shuffle_movements
        factType:
          type: string
          description: |-
            The type of fact this metric processes
            e.g. multishuttle_movement
        sourceSystem:
          type: string
          description: |-
            The source system of the facts
            e.g. diq
        displayName:
          type: string
          description: Display name for the UI
        configType:
          $ref: '#/components/schemas/ConfigType'
          description: Type of configuration
        acceptableLowRange:
          type: number
          format: double
          description: |-
            Acceptable low range
            Used to indicate if the actual metric value is acceptable
        acceptableHighRange:
          type: number
          format: double
          description: |-
            Acceptable high range
            Used to indicate if the actual metric value is acceptable
        exampleMessage:
          description: Example message for testing
        views:
          items:
            type: string
          type: array
          description: |-
            The hierarchy graph view at which this metric applies
            Example: `["facility", "multishuttle"]`
        matchConditions:
          $ref: '#/components/schemas/MatchConditions'
          description: >-
            Defines the conditions an event must meet for this metric to be
            processed
        redisParams:
          $ref: '#/components/schemas/RedisParams'
          description: >-
            Optional parameters required for metric processing

            Example: `{"row_hash": "{row_hash}"}` dynamically fills `row_hash`
            with event data
        nameFormula:
          $ref: '#/components/schemas/NameConfig'
          description: >-
            Defines how to dynamically generate metric names based on source
            fields
        label:
          $ref: '#/components/schemas/NodeLabel'
          description: The label for the node. Defaults to "Area"
        parentNodes:
          items:
            type: string
          type: array
          description: |-
            Defines the parent node hierarchy for this metric
            Can be:
            - undefined: No parent nodes
            - string: Single parent node name
            - string[]: Array of parent node names in hierarchical order
            Example: `"multishuttle"` or `["multishuttle", "aisle_code"]`
        nodeName:
          type: string
        metricType:
          type: string
        timeWindow:
          $ref: '#/components/schemas/TimeWindow'
        aggregation:
          $ref: '#/components/schemas/Aggregation'
        graphOperation:
          $ref: '#/components/schemas/GraphOperation'
        redisOperation:
          $ref: '#/components/schemas/RedisOperation'
        metricUnits:
          type: string
        huId:
          type: string
        inboundArea:
          type: string
        outboundArea:
          type: string
        units:
          type: string
        outboundNodeLabel:
          $ref: '#/components/schemas/NodeLabel'
        inboundParentNodes:
          items:
            type: string
          type: array
        outboundParentNodes:
          items:
            type: string
          type: array
        enabled:
          $ref: '#/components/schemas/Record_string.boolean_'
          description: |-
            Whether this metric is enabled for each facility.
            Keys are facility IDs, values are booleans.
            A metric can only be disabled by a configurator via the API.
        active:
          $ref: '#/components/schemas/Record_string.boolean_'
          description: |-
            Whether this metric is active for each facility.
            Keys are facility IDs, values are booleans.
            A metric is active if an incoming fact was matched on it.
        customConfigs:
          items:
            $ref: '#/components/schemas/Awaited_CustomMetricConfigurationEntity_'
          type: array
          description: >-
            Custom configurations that extend this default configuration
            (optional).

            Each custom configuration is specific to a facility.

            Custom configurations can exist independently without a default
            configuration.
      required:
        - id
        - createdAt
        - updatedAt
        - metricConfigName
        - configType
        - views
        - matchConditions
      type: object
      additionalProperties: false
    Awaited_DefaultMetricConfigurationEntity_:
      $ref: '#/components/schemas/DefaultMetricConfigurationEntity'
      description: >-
        Recursively unwraps the "awaited type" of a type. Non-promise
        "thenables" should resolve to `never`. This emulates the behavior of
        `await`.
    MetricConfigFact:
      description: Represents metric configuration facts with statistics for a facility
      properties:
        factType:
          type: string
          description: The type of fact this metric represents
        totalConfigs:
          type: number
          format: double
          description: Total number of configurations for this fact type
        enabledConfigs:
          type: number
          format: double
          description: Number of enabled configurations for this fact type
        active:
          type: boolean
          description: Whether any configuration for this fact type is active
      required:
        - factType
        - totalConfigs
        - enabledConfigs
        - active
      type: object
      additionalProperties: false
    AppConfigSettingArrayOrAppConfigSetting:
      anyOf:
        - items:
            $ref: '#/components/schemas/AppConfigSetting'
          type: array
        - $ref: '#/components/schemas/AppConfigSetting'
    DefaultConfigSeedingResult:
      properties:
        successful:
          items:
            $ref: '#/components/schemas/AppConfigSetting'
          type: array
        unsuccessful:
          items:
            $ref: '#/components/schemas/AppConfigSetting'
          type: array
        existing:
          items:
            $ref: '#/components/schemas/AppConfigSetting'
          type: array
      required:
        - successful
        - unsuccessful
        - existing
      type: object
      additionalProperties: false
    DeletedRecordsResult:
      properties:
        recordsDeleted:
          type: number
          format: double
        message:
          type: string
      required:
        - recordsDeleted
      type: object
      additionalProperties: false
    AppConfigSettingLog:
      description: Relevant informaiton about a setting from the setting log
      properties:
        changedBy:
          type: string
        timestamp:
          type: string
          format: date-time
        source:
          type: string
        newValue:
          type: string
      required:
        - changedBy
        - timestamp
        - source
      type: object
      additionalProperties: false
    AppConfigSettingLogsData:
      properties:
        data:
          items:
            $ref: '#/components/schemas/AppConfigSettingLog'
          type: array
      required:
        - data
      type: object
      additionalProperties: false
    FacilityConfigSettings:
      items:
        $ref: '#/components/schemas/AppConfigSetting'
      type: array
      description: >-
        DTO for a the facility config settings with its value set to the most
        relevant value for the user.
    CuratedTableColumnValueRenderType:
      enum:
        - number
        - string
        - boolean
        - 'null'
      type: string
    CuratedTableColumn:
      properties:
        name:
          type: string
        renderType:
          $ref: '#/components/schemas/CuratedTableColumnValueRenderType'
        value: {}
      required:
        - name
        - renderType
        - value
      type: object
      additionalProperties: false
    CuratedTableRow:
      properties:
        columns:
          items:
            $ref: '#/components/schemas/CuratedTableColumn'
          type: array
      required:
        - columns
      type: object
      additionalProperties: false
    AppConfigApiEndpointConfig:
      properties:
        operationId:
          type: string
        baseUrl:
          type: string
      required:
        - operationId
        - baseUrl
      type: object
      additionalProperties: false
    AppConfigApi:
      properties:
        baseUrl:
          type: string
        overrides:
          items:
            $ref: '#/components/schemas/AppConfigApiEndpointConfig'
          type: array
      required:
        - baseUrl
      type: object
      additionalProperties: false
    AppConfigDataUrls:
      properties:
        tableauProxy:
          type: string
        tableauMovementsAndFaultsDashboard:
          type: string
        tableauMovementsAndFaultsDetailsDashboard:
          type: string
        tableauMovementsDashboard:
          type: string
        tableauHistoricalMovementsAndFaultsDashboard:
          type: string
        tableauShuttleHardwareAnalysis:
          type: string
        mockApi:
          type: string
        insightsSiteName:
          type: string
      required:
        - tableauProxy
        - tableauMovementsAndFaultsDashboard
        - tableauMovementsAndFaultsDetailsDashboard
        - tableauMovementsDashboard
        - tableauHistoricalMovementsAndFaultsDashboard
        - tableauShuttleHardwareAnalysis
        - mockApi
        - insightsSiteName
      type: object
      additionalProperties: false
    LogLevel:
      enum:
        - 20
        - 30
        - 40
        - 50
        - 60
        - 100
        - 1000
      type: number
    AppConfigSettings:
      properties:
        logLevel:
          $ref: '#/components/schemas/LogLevel'
      required:
        - logLevel
      type: object
      additionalProperties: false
    AppConfigSite:
      properties:
        timezone:
          type: string
      required:
        - timezone
      type: object
      additionalProperties: false
    AppConfigPackage:
      description: Interface used to set the menu group index (order) for the menu.
      properties:
        name:
          type: string
          description: >-
            The name of the package, which is used to dynamically load the
            package as it must match the folder name.
        enabled:
          type: boolean
          description: lag to set if the package is enabled.
        requiredRoles:
          items:
            type: string
          type: array
          description: an array of roles that match auth0 roles
      required:
        - name
        - enabled
      type: object
      additionalProperties: false
    AppConfig:
      properties:
        api:
          $ref: '#/components/schemas/AppConfigApi'
        dataUrls:
          $ref: '#/components/schemas/AppConfigDataUrls'
        settings:
          $ref: '#/components/schemas/AppConfigSettings'
        site:
          $ref: '#/components/schemas/AppConfigSite'
        packages:
          items:
            $ref: '#/components/schemas/AppConfigPackage'
          type: array
        featureFlags:
          items:
            $ref: '#/components/schemas/AppConfigSetting'
          type: array
      required:
        - api
        - site
      type: object
      additionalProperties: false
    DataQueryResultType:
      type: string
      enum:
        - singleValue
        - categorySeries
        - timeSeries
        - tabular
        - filter
    DataQueryField:
      properties:
        name:
          type: string
        type:
          type: string
          enum:
            - string
            - number
            - timestamp
            - boolean
        label:
          type: string
        unit:
          type: string
      required:
        - name
        - type
      type: object
      additionalProperties: false
    Record_string.unknown_:
      properties: {}
      additionalProperties: {}
      type: object
      description: Construct a type with a set of properties K of type T
    DataQueryValidation:
      properties:
        isValid:
          type: boolean
        reason:
          type: string
      required:
        - isValid
      type: object
      additionalProperties: false
    DataQueryResult:
      properties:
        type:
          $ref: '#/components/schemas/DataQueryResultType'
        fields:
          items:
            $ref: '#/components/schemas/DataQueryField'
          type: array
        rows:
          items:
            $ref: '#/components/schemas/Record_string.unknown_'
          type: array
        validation:
          $ref: '#/components/schemas/DataQueryValidation'
        metadata:
          $ref: '#/components/schemas/Record_string.unknown_'
        queryRequest:
          type: string
      required:
        - type
        - fields
        - rows
        - validation
        - metadata
      type: object
      additionalProperties: false
    DataQueryType:
      enum:
        - singleValue
        - categorySeries
        - timeSeries
        - tabular
        - filter
      type: string
    DataQuerySource:
      properties:
        table:
          type: string
        type:
          type: string
        id:
          type: string
      required:
        - table
        - type
        - id
      type: object
    DataQueryParameters:
      properties:
        required:
          items:
            type: string
          type: array
      required:
        - required
      type: object
    QueryPropertyType:
      type: string
      enum:
        - sort
        - group_by
        - time_bucket
        - limit
    BaseQueryProperty:
      properties:
        type:
          $ref: '#/components/schemas/QueryPropertyType'
        id:
          type: string
      required:
        - type
        - id
      type: object
    DateTimeGranularity:
      type: string
      enum:
        - AUTO
        - SECOND
        - MINUTE
        - HOUR
        - DAY
        - WEEK
        - MONTH
        - QUARTER
        - YEAR
    TimeBucket:
      properties:
        defaultValue:
          $ref: '#/components/schemas/DateTimeGranularity'
        type:
          type: string
          enum:
            - time_bucket
          nullable: false
      required:
        - defaultValue
        - type
      type: object
    SortOrder:
      type: string
      enum:
        - ASC
        - DESC
    Sort:
      properties:
        defaultValue:
          $ref: '#/components/schemas/SortOrder'
        type:
          type: string
          enum:
            - sort
          nullable: false
      required:
        - defaultValue
        - type
      type: object
    Limit:
      properties:
        defaultValue:
          type: number
          format: double
        type:
          type: string
          enum:
            - limit
          nullable: false
      required:
        - defaultValue
        - type
      type: object
    GroupBy:
      properties:
        defaultValue:
          type: string
        groupableFields:
          items:
            type: string
          type: array
        type:
          type: string
          enum:
            - group_by
          nullable: false
      required:
        - defaultValue
        - groupableFields
        - type
      type: object
    DataQueryProperty:
      allOf:
        - $ref: '#/components/schemas/BaseQueryProperty'
        - anyOf:
            - $ref: '#/components/schemas/TimeBucket'
            - $ref: '#/components/schemas/Sort'
            - $ref: '#/components/schemas/Limit'
            - $ref: '#/components/schemas/GroupBy'
      description: >-
        This will be a list of properties that are SQL keywords. They need to
        use handlebars

        because they are keywords that can't be injected with parameters. 
        Current list is:
         - Time period aggregation:
        Example: {
          id: "time_period_aggregation",
          type: "time_bucket",
          defaultValue: "MONTH",
        }
    DataQuery:
      properties:
        queryProperties:
          items:
            $ref: '#/components/schemas/DataQueryProperty'
          type: array
        query:
          type: string
        parameters:
          $ref: '#/components/schemas/DataQueryParameters'
        config:
          items:
            type: string
          type: array
        dataSources:
          items:
            $ref: '#/components/schemas/DataQuerySource'
          type: array
        metadata:
          properties:
            isCategoryFilter:
              type: boolean
            category:
              type: string
            unit:
              type: string
          required:
            - category
          type: object
        isDraft:
          type: boolean
        filters:
          items:
            type: string
          type: array
        description:
          type: string
        label:
          type: string
        type:
          $ref: '#/components/schemas/DataQueryType'
        id:
          type: string
      required:
        - query
        - parameters
        - dataSources
        - metadata
        - isDraft
        - filters
        - description
        - label
        - type
        - id
      type: object
    DataQueryQueryParams:
      properties:
        start_date:
          type: string
        end_date:
          type: string
      type: object
      additionalProperties:
        anyOf:
          - type: string
          - type: boolean
    Recommended:
      enum:
        - is_recommended
        - not_recommended
        - not_provided
      type: string
    AIMLResponse:
      properties:
        Error:
          type: string
        KnownDB:
          type: string
        ResponseCode:
          type: number
          format: double
        echart_code:
          nullable: true
        generated_sql_query:
          type: string
        question:
          type: string
        sql_query_output:
          type: string
        summary_response:
          type: string
      required:
        - ResponseCode
      type: object
      additionalProperties: false
    DataExplorerResult:
      properties:
        debugData:
          $ref: '#/components/schemas/AIMLResponse'
        eChartCode:
          nullable: true
        timestamp:
          type: string
          format: date-time
        queryResults:
          items: {}
          type: array
        isRecommended:
          $ref: '#/components/schemas/Recommended'
        isBookmarked:
          type: boolean
        answer:
          type: string
        prompt:
          type: string
        id:
          type: string
      required:
        - timestamp
        - isRecommended
        - prompt
        - id
      type: object
    DataExplorerResults:
      properties:
        data:
          items:
            $ref: '#/components/schemas/DataExplorerResult'
          type: array
      required:
        - data
      type: object
    DataExplorerRecommendationsData:
      properties:
        prompt:
          type: string
      required:
        - prompt
      type: object
      additionalProperties: false
    DataExplorerRecommendations:
      properties:
        recommendations:
          items:
            $ref: '#/components/schemas/DataExplorerRecommendationsData'
          type: array
      required:
        - recommendations
      type: object
      additionalProperties: false
    AIMLCheck:
      properties:
        ok:
          type: boolean
        detail:
          type: string
      required:
        - ok
        - detail
      type: object
      additionalProperties: false
    AimlHealthCheckResponse:
      properties:
        status:
          type: string
        checks:
          properties:
            rag_engine:
              $ref: '#/components/schemas/AIMLCheck'
            bigquery:
              $ref: '#/components/schemas/AIMLCheck'
            postgres:
              $ref: '#/components/schemas/AIMLCheck'
          additionalProperties:
            $ref: '#/components/schemas/AIMLCheck'
          required:
            - rag_engine
            - bigquery
            - postgres
          type: object
        total_ms:
          type: number
          format: double
        version:
          type: string
      required:
        - status
        - checks
        - total_ms
        - version
      type: object
      additionalProperties: false
    DataExplorerAgentSearchResponse:
      properties:
        sessionId:
          type: string
        status:
          type: string
        messages:
          items:
            type: string
          type: array
      required:
        - sessionId
        - status
        - messages
      type: object
      additionalProperties: false
    DiagnosticsInfrastructureObjectStatus:
      description: DTO that describes the Diagnostics Infrastructure Object Status.
      enum:
        - Online
        - Offline
      type: string
    DiagnosticsInfrastructureObject:
      description: DTO that describes the Diagnostics Infrastructure Object.
      properties:
        name:
          type: string
        lastUpdate:
          type: string
        status:
          anyOf:
            - type: string
            - $ref: '#/components/schemas/DiagnosticsInfrastructureObjectStatus'
        cpuPercentage:
          type: number
          format: double
        memoryUsagePercentage:
          type: number
          format: double
        diskUsagePercentage:
          type: number
          format: double
        labels:
          items:
            type: string
          type: array
      required:
        - name
        - lastUpdate
        - status
        - cpuPercentage
        - memoryUsagePercentage
        - diskUsagePercentage
        - labels
      type: object
      additionalProperties: false
    DiagnosticsInfrastructureContract:
      items:
        $ref: '#/components/schemas/DiagnosticsInfrastructureObject'
      type: array
    ChartSeriesPercentageData:
      description: Chart series data with percentage validation
      properties:
        name:
          type: string
        value:
          type: number
          format: double
        isValidPercentage:
          type: boolean
      required:
        - name
        - value
        - isValidPercentage
      type: object
      additionalProperties: false
    InventoryStockDistributionUnderData:
      properties:
        underInventory:
          items:
            $ref: '#/components/schemas/ChartSeriesPercentageData'
          type: array
      required:
        - underInventory
      type: object
    InventoryStockDistributionOverData:
      properties:
        overInventory:
          items:
            $ref: '#/components/schemas/ChartSeriesPercentageData'
          type: array
      required:
        - overInventory
      type: object
    InventoryStockDistributionNoData:
      properties:
        noInventory:
          items:
            $ref: '#/components/schemas/ChartSeriesPercentageData'
          type: array
      required:
        - noInventory
      type: object
    InventoryStockDistributionAtData:
      properties:
        atInventory:
          items:
            $ref: '#/components/schemas/ChartSeriesPercentageData'
          type: array
      required:
        - atInventory
      type: object
    WmsInventorySku:
      properties:
        sku:
          type: string
        description:
          type: string
        daysOnHand:
          type: number
          format: double
        status:
          type: string
        averageDailyQuantity:
          type: number
          format: double
        averageDailyOrders:
          type: number
          format: double
        targetMultiplicity:
          type: number
          format: double
        velocityClassification:
          type: string
        quantityAvailable:
          type: number
          format: double
        quantityAllocated:
          type: number
          format: double
        totalQuantity:
          type: number
          format: double
        locations:
          type: number
          format: double
        skuPositions:
          type: number
          format: double
        maxContainers:
          type: number
          format: double
        contOverage:
          type: number
          format: double
        latestInventorySnapshotTimestamp:
          type: string
        latestCycleCountTimestamp:
          type: string
        latestActivityDateTimestamp:
          type: string
      required:
        - sku
        - description
        - daysOnHand
        - status
        - averageDailyQuantity
        - averageDailyOrders
        - targetMultiplicity
        - velocityClassification
        - quantityAvailable
        - quantityAllocated
        - totalQuantity
        - locations
        - skuPositions
        - maxContainers
        - contOverage
        - latestCycleCountTimestamp
        - latestActivityDateTimestamp
      type: object
      additionalProperties: false
    InventorySkuPaginationInfo:
      properties:
        page:
          type: number
          format: double
        limit:
          type: number
          format: double
        totalResults:
          type: number
          format: double
      required:
        - page
        - limit
      type: object
      additionalProperties: false
    ApiResponseArray_WmsInventorySku-Array.InventorySkuPaginationInfo_:
      properties:
        metadata:
          $ref: '#/components/schemas/InventorySkuPaginationInfo'
        data:
          items:
            $ref: '#/components/schemas/WmsInventorySku'
          type: array
      required:
        - metadata
        - data
      type: object
    PostWmsInventorySkusListResponse:
      $ref: >-
        #/components/schemas/ApiResponseArray_WmsInventorySku-Array.InventorySkuPaginationInfo_
    SortField:
      description: Defines a column to add to a ORDER BY clause in a SQL statement.
      properties:
        columnName:
          type: string
        isDescending:
          type: boolean
      required:
        - columnName
        - isDescending
      type: object
      additionalProperties: false
    PaginatedRequestNoDates:
      properties:
        filters: {}
        sortFields:
          items:
            $ref: '#/components/schemas/SortField'
          type: array
        page:
          type: integer
          format: int32
          minimum: 0
        limit:
          type: integer
          format: int32
          minimum: 1
          maximum: 1000
        searchString:
          type: string
      type: object
      additionalProperties: false
    IncludedColumns:
      properties: {}
      additionalProperties:
        type: boolean
      type: object
      description: DTO for objects related to the excel export.
    InventoryUploadRecentActivity:
      description: >-
        DTO that defines the relevant details of the inventory upload history
        object.
      properties:
        date:
          type: string
        knownOrderCount:
          type: number
          format: double
        knownOrderLineCount:
          type: number
          format: double
      required:
        - date
        - knownOrderCount
        - knownOrderLineCount
      type: object
      additionalProperties: false
    InventoryStorageUtilization:
      description: >-
        DTO that defines the relevant details the inventory storage utilization
        tile.
      properties:
        utilizationPercentage:
          type: number
          format: double
      required:
        - utilizationPercentage
      type: object
      additionalProperties: false
    InventoryDistributionStatusResponse:
      anyOf:
        - $ref: '#/components/schemas/InventoryStockDistributionNoData'
        - $ref: '#/components/schemas/InventoryStockDistributionAtData'
        - $ref: '#/components/schemas/InventoryStockDistributionOverData'
        - $ref: '#/components/schemas/InventoryStockDistributionUnderData'
    DistributionType:
      type: string
      enum:
        - at
        - under
        - over
        - 'no'
    ChartSeriesData:
      description: Base chart series data
      properties:
        name:
          type: string
        value:
          type: number
          format: double
      required:
        - name
        - value
      type: object
      additionalProperties: false
    TaskTypeData:
      properties:
        demand:
          items:
            $ref: '#/components/schemas/ChartSeriesData'
          type: array
        topOff:
          items:
            $ref: '#/components/schemas/ChartSeriesData'
          type: array
        relocation:
          items:
            $ref: '#/components/schemas/ChartSeriesData'
          type: array
      type: object
      additionalProperties: false
    ShiftData:
      description: >-
        DTO that defines the relevant details of the inventory replenishment
        details.
      properties:
        firstShift:
          items:
            $ref: '#/components/schemas/ChartSeriesData'
          type: array
        secondShift:
          items:
            $ref: '#/components/schemas/ChartSeriesData'
          type: array
        thirdShift:
          items:
            $ref: '#/components/schemas/ChartSeriesData'
          type: array
      required:
        - firstShift
      type: object
      additionalProperties: false
    InventoryReplenishmentDetails:
      properties:
        dailyReplenishments:
          items:
            $ref: '#/components/schemas/ChartSeriesData'
          type: array
        dailyPendingOrders:
          items:
            $ref: '#/components/schemas/ChartSeriesData'
          type: array
        dailyCycleTimes:
          items:
            $ref: '#/components/schemas/ChartSeriesData'
          type: array
        shiftData:
          items:
            $ref: '#/components/schemas/ShiftData'
          type: array
        shiftTimes:
          properties: {}
          type: object
      required:
        - dailyReplenishments
        - dailyPendingOrders
        - dailyCycleTimes
        - shiftData
        - shiftTimes
      type: object
      additionalProperties: false
    NodeAlert:
      properties:
        color:
          type: string
        value:
          type: string
      required:
        - color
        - value
      type: object
      additionalProperties: false
    Metric:
      properties:
        id:
          type: string
        type:
          type: string
        value:
          anyOf:
            - type: number
              format: double
            - type: string
          nullable: true
        panelGroup:
          type: string
        units:
          type: string
        description:
          type: string
      required:
        - id
        - type
        - value
      type: object
      additionalProperties: false
    AreaPosition:
      properties:
        x:
          type: number
          format: double
        'y':
          type: number
          format: double
      required:
        - x
        - 'y'
      type: object
      additionalProperties: false
    Shuttle:
      properties:
        id:
          type: string
        name:
          type: string
        metrics:
          items:
            $ref: '#/components/schemas/Metric'
          type: array
      required:
        - id
        - name
        - metrics
      type: object
      additionalProperties: false
    AisleLevel:
      properties:
        id:
          type: string
        label:
          type: string
        shuttles:
          items:
            $ref: '#/components/schemas/Shuttle'
          type: array
      required:
        - id
        - label
        - shuttles
      type: object
      additionalProperties: false
    Area:
      properties:
        id:
          type: string
        alerts:
          items:
            $ref: '#/components/schemas/NodeAlert'
          type: array
        label:
          type: string
        metrics:
          items:
            $ref: '#/components/schemas/Metric'
          type: array
        position:
          $ref: '#/components/schemas/AreaPosition'
        nodeType:
          type: string
          enum:
            - Area
            - Aisle
            - Lift
        aisleLevels:
          items:
            $ref: '#/components/schemas/AisleLevel'
          type: array
        view:
          type: string
      required:
        - id
        - label
        - metrics
        - position
        - nodeType
      type: object
      additionalProperties: false
    EdgeDirection:
      enum:
        - upstream
        - downstream
      type: string
    Edge:
      properties:
        id:
          type: string
        source:
          type: string
        target:
          type: string
        direction:
          $ref: '#/components/schemas/EdgeDirection'
        metrics:
          items:
            $ref: '#/components/schemas/Metric'
          type: array
      required:
        - id
        - source
        - target
        - direction
        - metrics
      type: object
      additionalProperties: false
    ProcessFlowResponse:
      properties:
        areas:
          items:
            $ref: '#/components/schemas/Area'
          type: array
        edges:
          items:
            $ref: '#/components/schemas/Edge'
          type: array
        lastProcessedTime:
          type: string
      required:
        - areas
        - edges
        - lastProcessedTime
      type: object
      additionalProperties: false
    MetricGroup:
      properties:
        title:
          type: string
        metrics:
          items:
            $ref: '#/components/schemas/Metric'
          type: array
      required:
        - title
        - metrics
      type: object
      additionalProperties: false
    ProcessFlowDetailsResponse:
      properties:
        metricGroups:
          items:
            $ref: '#/components/schemas/MetricGroup'
          type: array
      required:
        - metricGroups
      type: object
      additionalProperties: false
    UpdatedArea:
      properties:
        identity:
          type: number
          format: double
        labels:
          items:
            type: string
          type: array
        properties:
          properties: {}
          additionalProperties:
            anyOf:
              - type: string
              - items:
                  type: string
                type: array
              - type: number
                format: double
          type: object
        elementId:
          type: string
      required:
        - identity
        - labels
        - properties
        - elementId
      type: object
      additionalProperties: false
    UpdatePositionPayload:
      properties:
        id:
          type: string
        position:
          properties:
            'y':
              type: number
              format: double
            x:
              type: number
              format: double
          required:
            - 'y'
            - x
          type: object
      required:
        - id
        - position
      type: object
      additionalProperties: false
    ClearCacheResponse:
      properties:
        success:
          type: boolean
      required:
        - success
      type: object
      additionalProperties: false
    ClearCacheRequest:
      properties:
        scope:
          type: string
          enum:
            - all
            - metrics
            - graph
            - graph_and_neo4j
      required:
        - scope
      type: object
      additionalProperties: false
    InventoryPerformanceSeriesData:
      properties:
        name:
          type: string
        series:
          items:
            $ref: '#/components/schemas/ChartSeriesData'
          type: array
      required:
        - name
        - series
      type: object
      additionalProperties: false
    InventoryPerformanceSeriesContract:
      items:
        $ref: '#/components/schemas/InventoryPerformanceSeriesData'
      type: array
    InventoryHighImpactSKU:
      properties:
        sku:
          type: string
        accuracy:
          type: number
          format: double
        storageArea:
          type: string
        quantity:
          type: number
          format: double
        cubeUtilization:
          type: number
          format: double
        daysOnHand:
          type: number
          format: double
      required:
        - sku
        - accuracy
        - storageArea
        - quantity
        - cubeUtilization
        - daysOnHand
      type: object
      additionalProperties: false
    InventoryHighImpactSkuPaginationInfo:
      properties:
        page:
          type: number
          format: double
        limit:
          type: number
          format: double
        totalResults:
          type: number
          format: double
      required:
        - page
        - limit
      type: object
      additionalProperties: false
    ApiResponseArray_InventoryHighImpactSKU-Array.InventoryHighImpactSkuPaginationInfo_:
      properties:
        metadata:
          $ref: '#/components/schemas/InventoryHighImpactSkuPaginationInfo'
        data:
          items:
            $ref: '#/components/schemas/InventoryHighImpactSKU'
          type: array
      required:
        - metadata
        - data
      type: object
    InventoryHighImpactSKUContract:
      $ref: >-
        #/components/schemas/ApiResponseArray_InventoryHighImpactSKU-Array.InventoryHighImpactSkuPaginationInfo_
    Pick_NonPaginatedRequest.Exclude_keyofNonPaginatedRequest.exportColumns__:
      properties:
        start_date:
          type: string
          format: date-time
        end_date:
          type: string
          format: date-time
        filters: {}
        sortFields:
          items:
            $ref: '#/components/schemas/SortField'
          type: array
        searchString:
          type: string
        limit:
          type: integer
          format: int32
          minimum: 1
          maximum: 1000
        id:
          type: string
      required:
        - start_date
        - end_date
      type: object
      description: 'From T, pick a set of properties whose keys are in the union K'
    PaginatedRequest:
      properties:
        start_date:
          type: string
          format: date-time
        end_date:
          type: string
          format: date-time
        filters: {}
        sortFields:
          items:
            $ref: '#/components/schemas/SortField'
          type: array
        searchString:
          type: string
        limit:
          type: integer
          format: int32
          minimum: 1
          maximum: 1000
        id:
          type: string
        page:
          type: integer
          format: int32
          minimum: 0
      required:
        - start_date
        - end_date
      type: object
      additionalProperties: false
    InventoryHandlingUnitsTrayedData:
      description: Data to hold in a contract for Handling Units Trayed.
      properties:
        handlingUnitsTrayed:
          type: number
          format: double
      required:
        - handlingUnitsTrayed
      type: object
      additionalProperties: false
    InventoryHandlingUnitsTrayedConfig:
      description: Configuration to hold in a contract for Handling Units Trayed.
      properties:
        timePeriodInHours:
          type: number
          format: double
      required:
        - timePeriodInHours
      type: object
      additionalProperties: false
    InventoryHandlingUnitsTrayedContract:
      description: Contract for Handling Units Trayed.
      properties:
        metadata:
          $ref: '#/components/schemas/InventoryHandlingUnitsTrayedConfig'
        data:
          $ref: '#/components/schemas/InventoryHandlingUnitsTrayedData'
      required:
        - metadata
        - data
      type: object
      additionalProperties: false
    InventoryForecastDataAnalysisTimestamp:
      description: DTO that defines the relevant details of the inventory forecast objects.
      properties:
        dataUpdateTimestamp:
          type: string
        analysisPerformedTimestamp:
          type: string
      required:
        - analysisPerformedTimestamp
      type: object
      additionalProperties: false
    CurrentInventory:
      properties:
        forwardPick:
          type: number
          format: double
        reserveStorage:
          type: number
          format: double
      required:
        - forwardPick
        - reserveStorage
      type: object
    ProjectedInventory:
      properties:
        projectedForwardPick:
          type: number
          format: double
        allocatedOrders:
          type: number
          format: double
        pendingPicks:
          type: number
          format: double
        pendingReplenishment:
          type: number
          format: double
      required:
        - projectedForwardPick
        - allocatedOrders
        - pendingPicks
        - pendingReplenishment
      type: object
    ForecastedInventory:
      properties:
        twoDayForwardPick:
          type: number
          format: double
          nullable: true
        twoDayDemand:
          type: number
          format: double
          nullable: true
        forwardPickTomorrow:
          type: number
          format: double
          nullable: true
        knownDemand:
          type: number
          format: double
          nullable: true
        demandTomorrow:
          type: number
          format: double
          nullable: true
        averageDemand:
          type: number
          format: double
        averageReplenishment:
          type: number
          format: double
      required:
        - twoDayForwardPick
        - twoDayDemand
        - forwardPickTomorrow
        - knownDemand
        - demandTomorrow
        - averageDemand
        - averageReplenishment
      type: object
    InventoryForecastListing:
      properties:
        forecast:
          $ref: '#/components/schemas/ForecastedInventory'
        projected:
          $ref: '#/components/schemas/ProjectedInventory'
        current:
          $ref: '#/components/schemas/CurrentInventory'
        sku:
          type: string
      required:
        - forecast
        - projected
        - current
        - sku
      type: object
    InventoryForecastListingConfig:
      properties:
        page:
          type: number
          format: double
        limit:
          type: number
          format: double
        totalResults:
          type: number
          format: double
      required:
        - page
        - limit
      type: object
      additionalProperties: false
    ApiResponseArray_InventoryForecastListing-Array.InventoryForecastListingConfig_:
      properties:
        metadata:
          $ref: '#/components/schemas/InventoryForecastListingConfig'
        data:
          items:
            $ref: '#/components/schemas/InventoryForecastListing'
          type: array
      required:
        - metadata
        - data
      type: object
    InventoryForecastListingData:
      $ref: >-
        #/components/schemas/ApiResponseArray_InventoryForecastListing-Array.InventoryForecastListingConfig_
    InventoryForecastSkuLocationDetails:
      properties:
        lastActivityDate:
          type: string
        zone:
          type: string
        conditionCode:
          type: string
        containerDimensions:
          type: string
        skuSize:
          type: string
        uom:
          type: string
        containerQuantity:
          type: number
          format: double
        quantity:
          type: number
          format: double
        containerCount:
          type: number
          format: double
        containerId:
          type: string
          nullable: true
        locationType:
          type: string
        locationId:
          type: string
      required:
        - lastActivityDate
        - zone
        - conditionCode
        - quantity
        - containerCount
        - containerId
        - locationType
        - locationId
      type: object
    InventoryForecastSkuLocationByArea:
      properties:
        details:
          items:
            $ref: '#/components/schemas/InventoryForecastSkuLocationDetails'
          type: array
        area:
          type: string
      required:
        - details
        - area
      type: object
    InventoryForecastSkuLocationAreas:
      properties:
        data:
          items:
            $ref: '#/components/schemas/InventoryForecastSkuLocationByArea'
          type: array
      required:
        - data
      type: object
      additionalProperties: false
    InventoryForecastSkuOrderDetails:
      properties:
        allocatedQty:
          type: number
          format: double
        orderLines:
          type: number
          format: double
        shipDate:
          type: string
        allocationDate:
          type: string
        priority:
          type: string
        orderId:
          type: string
        skuId:
          type: string
      required:
        - allocatedQty
        - orderLines
        - shipDate
        - allocationDate
        - priority
        - orderId
        - skuId
      type: object
    InventoryForecastSkuOrders:
      properties:
        skuId:
          type: string
        openOrderCount:
          type: number
          format: double
        data:
          items:
            $ref: '#/components/schemas/InventoryForecastSkuOrderDetails'
          type: array
      required:
        - skuId
        - openOrderCount
        - data
      type: object
      additionalProperties: false
    InventoryKnownDemand:
      properties:
        percentage:
          type: number
          format: double
        quantity:
          type: number
          format: double
      required:
        - percentage
        - quantity
      type: object
    InventorySkuForecastDetails:
      properties:
        confidence:
          type: number
          format: double
        knownDemand:
          $ref: '#/components/schemas/InventoryKnownDemand'
        shortTermDaily:
          type: number
          format: double
        shortTermDailyDays:
          type: number
          format: double
        longTermDaily:
          type: number
          format: double
        longTermDailyDays:
          type: number
          format: double
        nonZeroDemand:
          type: number
          format: double
        zeroDemandIntermittency:
          type: number
          format: double
        predictedDemandSeries:
          items:
            $ref: '#/components/schemas/ChartSeriesData'
          type: array
        actualDemandSeries:
          items:
            $ref: '#/components/schemas/ChartSeriesData'
          type: array
        confidenceLowSeries:
          items:
            $ref: '#/components/schemas/ChartSeriesData'
          type: array
        confidenceHighSeries:
          items:
            $ref: '#/components/schemas/ChartSeriesData'
          type: array
      required:
        - confidence
        - knownDemand
        - shortTermDaily
        - shortTermDailyDays
        - longTermDaily
        - longTermDailyDays
        - nonZeroDemand
        - zeroDemandIntermittency
        - predictedDemandSeries
        - actualDemandSeries
        - confidenceLowSeries
        - confidenceHighSeries
      type: object
      additionalProperties: false
    InventoryAreaFilterDefinition:
      description: DTO that defines the relevant details the inventory area dropdown.
      properties:
        areaFilterTable:
          items:
            type: string
          type: array
      required:
        - areaFilterTable
      type: object
      additionalProperties: false
    FacilityInventorySku:
      properties:
        sku:
          type: string
        description:
          type: string
        daysOnHand:
          type: number
          format: double
        status:
          type: string
        averageDailyQuantity:
          type: number
          format: double
        averageDailyOrders:
          type: number
          format: double
        quantityAvailable:
          type: number
          format: double
        quantityAllocated:
          type: number
          format: double
        totalQuantity:
          type: number
          format: double
        locations:
          type: number
          format: double
        skuPositions:
          type: number
          format: double
        latestInventorySnapshotTimestamp:
          type: string
        latestActivityDateTimestamp:
          type: string
      required:
        - sku
        - description
        - daysOnHand
        - status
        - averageDailyQuantity
        - averageDailyOrders
        - quantityAvailable
        - quantityAllocated
        - totalQuantity
        - locations
        - skuPositions
        - latestActivityDateTimestamp
      type: object
      additionalProperties: false
    ApiResponseArray_FacilityInventorySku-Array.InventorySkuPaginationInfo_:
      properties:
        metadata:
          $ref: '#/components/schemas/InventorySkuPaginationInfo'
        data:
          items:
            $ref: '#/components/schemas/FacilityInventorySku'
          type: array
      required:
        - metadata
        - data
      type: object
    PostFacilityInventorySkusListResponse:
      $ref: >-
        #/components/schemas/ApiResponseArray_FacilityInventorySku-Array.InventorySkuPaginationInfo_
    FlattenedInventoryContainerData:
      properties:
        free_cycle_count:
          type: string
          nullable: true
        data_updated:
          type: string
          nullable: true
        last_cycle_count:
          type: string
          nullable: true
        last_activity_date:
          type: string
          nullable: true
        quantity:
          type: number
          format: double
        sku:
          type: string
        zone:
          type: string
        location_id:
          type: string
        container_id:
          type: string
      required:
        - free_cycle_count
        - data_updated
        - last_cycle_count
        - last_activity_date
        - quantity
        - sku
        - zone
        - location_id
        - container_id
      type: object
    InventoryContainerPaginationInfo:
      properties:
        page:
          type: number
          format: double
        limit:
          type: number
          format: double
        totalResults:
          type: number
          format: double
      required:
        - page
        - limit
      type: object
      additionalProperties: false
    ApiResponseArray_FlattenedInventoryContainerData-Array.InventoryContainerPaginationInfo_:
      properties:
        metadata:
          $ref: '#/components/schemas/InventoryContainerPaginationInfo'
        data:
          items:
            $ref: '#/components/schemas/FlattenedInventoryContainerData'
          type: array
      required:
        - metadata
        - data
      type: object
    PostInventoryContainersListResponse:
      $ref: >-
        #/components/schemas/ApiResponseArray_FlattenedInventoryContainerData-Array.InventoryContainerPaginationInfo_
    FlattenedInventoryContainerEventsDataItem:
      properties:
        timestamp:
          type: string
        event:
          type: string
        destinationContainer:
          type: string
        operator:
          type: string
        quantity:
          type: number
          format: double
        workstationCode:
          type: string
        sku:
          type: string
      required:
        - timestamp
        - event
        - destinationContainer
        - operator
        - quantity
        - workstationCode
        - sku
      type: object
      additionalProperties: false
    InventoryContainerEventsPaginationInfo:
      properties:
        page:
          type: number
          format: double
        limit:
          type: number
          format: double
        totalResults:
          type: number
          format: double
      required:
        - page
        - limit
      type: object
      additionalProperties: false
    ApiResponseArray_FlattenedInventoryContainerEventsDataItem-Array.InventoryContainerEventsPaginationInfo_:
      properties:
        metadata:
          $ref: '#/components/schemas/InventoryContainerEventsPaginationInfo'
        data:
          items:
            $ref: '#/components/schemas/FlattenedInventoryContainerEventsDataItem'
          type: array
      required:
        - metadata
        - data
      type: object
    PostInventoryContainerEventsListResponse:
      $ref: >-
        #/components/schemas/ApiResponseArray_FlattenedInventoryContainerEventsDataItem-Array.InventoryContainerEventsPaginationInfo_
    InventoryBinLocation:
      description: DTO that defines the relevant details of the bin location objects
      properties:
        binLocation:
          type: string
        locationSide:
          type: string
          enum:
            - Left
            - Right
        status:
          type: string
          enum:
            - Occupied
            - Empty
        containerType:
          type: string
        skus:
          items:
            properties:
              quantity:
                type: number
                format: double
              sku:
                type: string
            required:
              - quantity
              - sku
            type: object
          type: array
      required:
        - binLocation
        - locationSide
        - status
        - skus
      type: object
      additionalProperties: false
    InventoryBinLocationsResponse:
      properties:
        data:
          items:
            $ref: '#/components/schemas/InventoryBinLocation'
          type: array
      required:
        - data
      type: object
      additionalProperties: false
    AdvicesOutstandingData:
      properties:
        outstandingAdvices:
          type: number
          format: double
      required:
        - outstandingAdvices
      type: object
    AdvicesListData:
      properties:
        adviceId:
          type: string
        adviceStatus:
          type: string
        ownerId:
          type: string
        adviceType:
          type: string
        supplierId:
          type: string
        createdTime:
          type: string
        startTime:
          type: string
          nullable: true
        finishedTime:
          type: string
          nullable: true
        adviceLines:
          type: number
          format: double
        deliveredLines:
          type: number
          format: double
        overdeliveredLines:
          type: number
          format: double
        underdeliveredLines:
          type: number
          format: double
      required:
        - adviceId
        - adviceStatus
        - ownerId
        - adviceType
        - supplierId
        - createdTime
        - startTime
        - finishedTime
        - adviceLines
        - deliveredLines
        - overdeliveredLines
        - underdeliveredLines
      type: object
      additionalProperties: false
    AdvicesList:
      properties:
        adviceList:
          items:
            $ref: '#/components/schemas/AdvicesListData'
          type: array
      required:
        - adviceList
      type: object
    InventoryAdvicesInProgressData:
      properties:
        inProgressAdvices:
          type: number
          format: double
      required:
        - inProgressAdvices
      type: object
      additionalProperties: false
    AdvicesFinishedData:
      properties:
        finishedAdvices:
          type: number
          format: double
      required:
        - finishedAdvices
      type: object
    AdviceDetailsData:
      properties:
        itemsReceived:
          items:
            properties:
              packagingLevel:
                type: string
              quantity:
                type: number
                format: double
              sku:
                type: string
              adviceLine:
                type: string
              handlingUnitType:
                type: string
              handlingUnit:
                type: string
            required:
              - packagingLevel
              - quantity
              - sku
              - adviceLine
              - handlingUnitType
              - handlingUnit
            type: object
          type: array
        supplierId:
          type: string
      required:
        - itemsReceived
        - supplierId
      type: object
    AdvicesCycleTimeData:
      properties:
        cycleTime:
          type: number
          format: double
      required:
        - cycleTime
      type: object
    InventoryAccuracy:
      description: DTO that defines the relevant details of the inventory accuracy object.
      properties:
        accuracy:
          type: number
          format: double
        status:
          type: string
        areaFilter:
          type: string
      required:
        - accuracy
        - status
        - areaFilter
      type: object
      additionalProperties: false
    InventoryAccuracyConfig:
      properties:
        seg1:
          type: number
          format: double
        seg2:
          type: number
          format: double
        seg3:
          type: number
          format: double
      required:
        - seg1
        - seg2
        - seg3
      type: object
      additionalProperties: false
    ApiResponse_InventoryAccuracy.InventoryAccuracyConfig_:
      allOf:
        - $ref: '#/components/schemas/InventoryAccuracy'
        - properties:
            metadata:
              $ref: '#/components/schemas/InventoryAccuracyConfig'
          required:
            - metadata
          type: object
    InventoryContainerEventsKpiData:
      properties:
        averageDailyPickEvents:
          type: number
          format: double
        pickEventsToday:
          type: number
          format: double
        averageDailyCycleCount:
          type: number
          format: double
        cycleCountEventsToday:
          type: number
          format: double
        dataUpdated:
          type: string
          nullable: true
        lastCycleCount:
          type: string
          nullable: true
        lastActivityDate:
          type: string
          nullable: true
        quantity:
          type: number
          format: double
        sku:
          type: string
        zone:
          type: string
        locationId:
          type: string
        containerId:
          type: string
      required:
        - averageDailyPickEvents
        - pickEventsToday
        - averageDailyCycleCount
        - cycleCountEventsToday
        - dataUpdated
        - lastCycleCount
        - lastActivityDate
        - quantity
        - sku
        - zone
        - locationId
        - containerId
      type: object
    InventoryContainerEventsKpiContract:
      properties:
        events:
          items:
            $ref: '#/components/schemas/InventoryContainerEventsKpiData'
          type: array
      required:
        - events
      type: object
      additionalProperties: false
    EquipmentWorkstationStarvedBlockedTimeSeriesData:
      properties:
        blockedTime:
          items:
            $ref: '#/components/schemas/ChartSeriesData'
          type: array
        starvedTime:
          items:
            $ref: '#/components/schemas/ChartSeriesData'
          type: array
      required:
        - blockedTime
        - starvedTime
      type: object
    BaseChartConfig:
      properties:
        yMin:
          type: number
          format: double
          description: Min value for the y-axis
        yMax:
          type: number
          format: double
          description: Max value for the y-axis
        yHighBand:
          type: number
          format: double
          description: Value for the high range
        yLowBand:
          type: number
          format: double
          description: Value for the low range
        xAxisValueFormatType:
          type: string
          description: What the X-axis value should be formatted for display in the chart
        yAxisValueFormatType:
          type: string
          description: What the Y-axis value should be formatted for display in the chart
      type: object
      additionalProperties: false
    SeriesConfig:
      description: Config for a series in chart
      properties:
        yMin:
          type: number
          format: double
          description: Min value for the y-axis
        yMax:
          type: number
          format: double
          description: Max value for the y-axis
        yHighBand:
          type: number
          format: double
          description: Value for the high range
        yLowBand:
          type: number
          format: double
          description: Value for the low range
        xAxisValueFormatType:
          type: string
          description: What the X-axis value should be formatted for display in the chart
        yAxisValueFormatType:
          type: string
          description: What the Y-axis value should be formatted for display in the chart
        type:
          type: string
        color:
          type: string
        icon:
          type: string
      type: object
      additionalProperties: false
    EquipmentWorkstationStarvedBlockedTimeSeriesConfig:
      allOf:
        - $ref: '#/components/schemas/BaseChartConfig'
        - properties:
            blockedTime:
              $ref: '#/components/schemas/SeriesConfig'
            starvedTime:
              $ref: '#/components/schemas/SeriesConfig'
          required:
            - blockedTime
            - starvedTime
          type: object
    ApiResponse_EquipmentWorkstationStarvedBlockedTimeSeriesData.EquipmentWorkstationStarvedBlockedTimeSeriesConfig_:
      allOf:
        - $ref: >-
            #/components/schemas/EquipmentWorkstationStarvedBlockedTimeSeriesData
        - properties:
            metadata:
              $ref: >-
                #/components/schemas/EquipmentWorkstationStarvedBlockedTimeSeriesConfig
          required:
            - metadata
          type: object
    EquipmentWorkstationStarvedBlockedTimeSeriesApiResponse:
      $ref: >-
        #/components/schemas/ApiResponse_EquipmentWorkstationStarvedBlockedTimeSeriesData.EquipmentWorkstationStarvedBlockedTimeSeriesConfig_
    EquipmentWorkstationOperatorActivityData:
      description: Expected return data object format for the response
      properties:
        operatorId:
          type: string
          description: Id of the operator
        startTime:
          type: string
          description: Time the operator started working at the workstation
        endTime:
          type: string
          nullable: true
          description: >-
            Time the operator stopped working at the workstation, null if
            operator is still active
        totalTimeDuration:
          type: number
          format: double
          description: >-
            How long the operator was/currently has been at the workstation, in
            seconds
        idleTime:
          type: number
          format: double
          description: 'Time the workstation was idle, in seconds'
        starvedTime:
          type: number
          format: double
          description: 'Time the workstation was starved for picks, in seconds'
        blockedTime:
          type: number
          format: double
          description: 'Time the workstation was blocked for picks, in seconds'
        lineRatePerHour:
          type: number
          format: double
          description: The workstations per hour line rates
        weightedLineRatePerHour:
          type: number
          format: double
          description: The workstations per hour weighted line rates
      required:
        - operatorId
        - startTime
        - endTime
        - totalTimeDuration
        - idleTime
        - starvedTime
        - blockedTime
        - lineRatePerHour
        - weightedLineRatePerHour
      type: object
      additionalProperties: false
    EquipmentWorkstationLineRatesSeriesData:
      properties:
        lineRates:
          items:
            $ref: '#/components/schemas/ChartSeriesData'
          type: array
      required:
        - lineRates
      type: object
    EquipmentWorkstationAisleMovementData:
      properties:
        retrieval:
          type: number
          format: double
        storage:
          type: number
          format: double
        positioning:
          type: number
          format: double
        iat:
          type: number
          format: double
        shuffle:
          type: number
          format: double
        bypass:
          type: number
          format: double
      required:
        - retrieval
        - storage
        - positioning
        - iat
        - shuffle
        - bypass
      type: object
      additionalProperties: false
    EquipmentWorkstationActiveFaultsData:
      description: >-
        Definiton of the data for the data contract for workstation active
        faults
      properties:
        description:
          type: string
          description: Description of the fault
        startTime:
          type: string
          description: Start time of the fault
        duration:
          type: number
          format: double
          description: 'How long the fault has been active, in seconds'
        faultId:
          type: string
          description: Fault ID
      required:
        - description
        - startTime
        - duration
        - faultId
      type: object
      additionalProperties: false
    HealthStatus:
      description: DTO of the necessary data to display a health status.
      enum:
        - ok
        - caution
        - critical
        - warning
      type: string
    EquipmentSummaryWorkstationDefinition:
      properties:
        status:
          $ref: '#/components/schemas/HealthStatus'
        operatorId:
          type: number
          format: double
        iatDonorTotes:
          type: number
          format: double
        linesPerHour:
          type: number
          format: double
        activeTime:
          type: string
        idleTime:
          type: string
        starvedTime:
          type: string
      required:
        - status
        - operatorId
        - iatDonorTotes
        - linesPerHour
        - activeTime
        - idleTime
        - starvedTime
      type: object
      additionalProperties: false
    WorkstationMetricConfiguration:
      properties:
        id:
          type: string
        label:
          type: string
        type:
          type: string
        format:
          type: string
        display:
          type: boolean
      required:
        - id
        - label
        - type
        - format
        - display
      type: object
      additionalProperties: false
    EquipmentSummaryWorkstationConfigDefinition:
      properties:
        metrics:
          items:
            $ref: '#/components/schemas/WorkstationMetricConfiguration'
          type: array
      required:
        - metrics
      type: object
      additionalProperties: false
    ApiResponse_EquipmentSummaryWorkstationDefinition.EquipmentSummaryWorkstationConfigDefinition_:
      allOf:
        - $ref: '#/components/schemas/EquipmentSummaryWorkstationDefinition'
        - properties:
            metadata:
              $ref: '#/components/schemas/EquipmentSummaryWorkstationConfigDefinition'
          required:
            - metadata
          type: object
    HealthStatusValue:
      type: string
      enum:
        - ok
        - caution
        - critical
        - warning
      nullable: false
    AlertStatus:
      description: DTO of the necessary data to display an Alert Status.
      properties:
        status:
          $ref: '#/components/schemas/HealthStatusValue'
          description: Status of area.
        identifier:
          type: string
          description: >-
            Identifies the equipment, area, or location related to this alert

            TODO: this field is currently optional, but once it is implemented
            for all endpoints it should be mandatory
      required:
        - status
      type: object
      additionalProperties: false
    AreaOperators:
      description: DTO of the necessary base data to display a Facility Area.
      properties:
        activeOperators:
          type: number
          format: double
          description: Number of operators actively working in area.
        lowRecOperators:
          type: number
          format: double
          description: Lowest number of recommended operators for the area.
        highRecOperators:
          type: number
          format: double
          description: Highest number of recommended operators for the area.
        maxOperators:
          type: number
          format: double
          description: Max number of recommended operators for the area.
        alertStatus:
          $ref: '#/components/schemas/AlertStatus'
          description: Alert status object.
        queuedOrders:
          type: number
          format: double
          description: Number of orders in queue.
        pickRate:
          type: number
          format: double
          description: Orders picked per operator per hour.
        operatorStatus:
          $ref: '#/components/schemas/HealthStatus'
      required:
        - activeOperators
        - lowRecOperators
        - highRecOperators
      type: object
      additionalProperties: false
    EquipmentSummaryAreaDefinition:
      description: DTO that describes the equipment summary area data.
      properties:
        id:
          type: string
          description: Id of area.
        name:
          type: string
          description: Name of the area.
        operators:
          $ref: '#/components/schemas/AreaOperators'
          description: Operator area data.
        alertStatus:
          $ref: '#/components/schemas/AlertStatus'
        status:
          $ref: '#/components/schemas/HealthStatus'
        movementsPerFault:
          type: number
          format: double
        outboundRatePerHour:
          type: number
          format: double
        downTimeMinutes:
          type: number
          format: double
        qualityPercentage:
          type: number
          format: double
        alerts:
          items:
            $ref: '#/components/schemas/AlertStatus'
          type: array
      required:
        - id
        - name
        - status
        - movementsPerFault
        - outboundRatePerHour
        - downTimeMinutes
        - qualityPercentage
      type: object
      additionalProperties: false
    EquipmentSummaryAreaContract:
      description: DTO that describes the areas summary data.
      properties:
        areas:
          items:
            $ref: '#/components/schemas/EquipmentSummaryAreaDefinition'
          type: array
      required:
        - areas
      type: object
      additionalProperties: false
    EquipmentSummaryAisleDefinition:
      properties:
        status:
          $ref: '#/components/schemas/HealthStatus'
        totalMovements:
          type: number
          format: double
        storageUtilization:
          type: number
          format: double
        storedTotesPerHour:
          type: number
          format: double
        inventoryTotes:
          type: number
          format: double
        retrievedTotesPerHour:
          type: number
          format: double
        orderTotes:
          type: number
          format: double
      required:
        - status
        - totalMovements
        - storageUtilization
        - storedTotesPerHour
        - inventoryTotes
        - retrievedTotesPerHour
        - orderTotes
      type: object
      additionalProperties: false
    AisleMetricConfiguration:
      properties:
        id:
          type: string
        label:
          type: string
        type:
          type: string
        format:
          type: string
        display:
          type: boolean
      required:
        - id
        - label
        - type
        - format
        - display
      type: object
      additionalProperties: false
    EquipmentSummaryAisleConfigDefinition:
      properties:
        metrics:
          items:
            $ref: '#/components/schemas/AisleMetricConfiguration'
          type: array
      required:
        - metrics
      type: object
      additionalProperties: false
    ApiResponse_EquipmentSummaryAisleDefinition.EquipmentSummaryAisleConfigDefinition_:
      allOf:
        - $ref: '#/components/schemas/EquipmentSummaryAisleDefinition'
        - properties:
            metadata:
              $ref: '#/components/schemas/EquipmentSummaryAisleConfigDefinition'
          required:
            - metadata
          type: object
    BigQueryTimestamp:
      description: >-
        Timestamp class for BigQuery.


        The recommended input here is a `Date` or `PreciseDate` class.

        If passing as a `string`, it should be Timestamp literals:
        https://cloud.google.com/bigquery/docs/reference/standard-sql/lexical#timestamp_literals.

        When passing a `number` input, it should be epoch seconds in float
        representation.
      properties:
        value:
          type: string
      required:
        - value
      type: object
      additionalProperties: false
    RecentEventQueryResponse:
      properties:
        time:
          $ref: '#/components/schemas/BigQueryTimestamp'
        tenant:
          type: string
        facility:
          type: string
        description:
          type: string
        eventType:
          type: string
        equipmentId:
          type: string
        eventStartTime:
          $ref: '#/components/schemas/BigQueryTimestamp'
        totalTime:
          type: number
          format: double
        faultCode:
          type: string
      required:
        - time
        - tenant
        - facility
        - description
        - eventType
        - equipmentId
        - eventStartTime
        - totalTime
        - faultCode
      type: object
      additionalProperties: false
    RecentEventQueryConfig:
      properties:
        page:
          type: number
          format: double
        limit:
          type: number
          format: double
        totalResults:
          type: number
          format: double
      required:
        - page
        - limit
        - totalResults
      type: object
      additionalProperties: false
    ApiResponseArray_RecentEventQueryResponse-Array.RecentEventQueryConfig_:
      properties:
        metadata:
          $ref: '#/components/schemas/RecentEventQueryConfig'
        data:
          items:
            $ref: '#/components/schemas/RecentEventQueryResponse'
          type: array
      required:
        - metadata
        - data
      type: object
    BaseFilterType:
      properties:
        type:
          type: string
          enum:
            - multiple
            - single
      required:
        - type
      type: object
      additionalProperties: false
    EquipmentOutboundRate:
      properties:
        outboundDivertsHour:
          type: number
          format: double
        status:
          type: string
      required:
        - outboundDivertsHour
        - status
      type: object
      additionalProperties: false
    EquipmentOutboundRateConfig:
      properties:
        max:
          type: number
          format: double
        rangeColor:
          type: string
      required:
        - max
        - rangeColor
      type: object
      additionalProperties: false
    ApiResponse_EquipmentOutboundRate.EquipmentOutboundRateConfig_:
      allOf:
        - $ref: '#/components/schemas/EquipmentOutboundRate'
        - properties:
            metadata:
              $ref: '#/components/schemas/EquipmentOutboundRateConfig'
          required:
            - metadata
          type: object
    EquipmentOutboundRateApiResponse:
      $ref: >-
        #/components/schemas/ApiResponse_EquipmentOutboundRate.EquipmentOutboundRateConfig_
      description: Data contract for the response from the api
    FaultsStatusListRequest:
      properties:
        filters: {}
        end_date:
          type: string
          format: date-time
        start_date:
          type: string
          format: date-time
      type: object
    EquipmentFaultsSeriesData:
      description: Describes the equipment faults series data returned by the service.
      properties:
        name:
          type: string
          description: 'Hour of the day that the throughput is measured. Eg: "16".'
        value:
          type: number
          format: double
          description: Number of faults for that hour.
      required:
        - name
        - value
      type: object
      additionalProperties: false
    EquipmentThroughputSeriesData:
      description: Describes the throughput rates series data returned by the service.
      properties:
        name:
          type: string
          description: 'Hour of the day that the throughput is measured. Eg: "16".'
        value:
          type: number
          format: double
          description: Throughput for that hour.
      required:
        - name
        - value
      type: object
      additionalProperties: false
    EquipmentFaultsSeriesContractData:
      description: Describes the contract for the data returned by the api.
      properties:
        faultsOrderData:
          items:
            $ref: '#/components/schemas/EquipmentFaultsSeriesData'
          type: array
        orderData:
          items:
            $ref: '#/components/schemas/EquipmentThroughputSeriesData'
          type: array
      required:
        - faultsOrderData
        - orderData
      type: object
      additionalProperties: false
    FaultEvent:
      description: DTO for a Fault Event
      properties:
        time:
          type: string
          description: 'Time the event happened. UTC, ISO 8601.'
        area:
          type: string
          description: Area that the fault occured in
        description:
          type: string
          description: Desciption of the fault event
      required:
        - time
        - area
        - description
      type: object
      additionalProperties: false
    FaultEventContract:
      items:
        $ref: '#/components/schemas/FaultEvent'
      type: array
    FaultsMovementsSeriesData:
      properties:
        movementCounts:
          items:
            $ref: '#/components/schemas/ChartSeriesData'
          type: array
        movementsPerFault:
          items:
            $ref: '#/components/schemas/ChartSeriesData'
          type: array
      required:
        - movementCounts
        - movementsPerFault
      type: object
      additionalProperties: false
    FaultsMovementsSeriesRequest:
      properties:
        start_date:
          type: string
          format: date-time
        end_date:
          type: string
          format: date-time
        filters: {}
      required:
        - start_date
        - end_date
      type: object
      additionalProperties: false
    EquipmentFaultsListItem:
      properties:
        timestamp:
          type: string
        durationMinutes:
          type: number
          format: double
        status:
          type: string
        aisle:
          type: string
        level:
          type: string
        device:
          type: string
        deviceType:
          type: string
      required:
        - timestamp
        - durationMinutes
        - status
        - aisle
        - level
        - device
        - deviceType
      type: object
      additionalProperties: false
    EquipmentFaultsPaginationInfo:
      properties:
        page:
          type: number
          format: double
        limit:
          type: number
          format: double
        totalResults:
          type: number
          format: double
      required:
        - page
        - limit
      type: object
      additionalProperties: false
    ApiResponseArray_EquipmentFaultsListItem-Array.EquipmentFaultsPaginationInfo_:
      properties:
        metadata:
          $ref: '#/components/schemas/EquipmentFaultsPaginationInfo'
        data:
          items:
            $ref: '#/components/schemas/EquipmentFaultsListItem'
          type: array
      required:
        - metadata
        - data
      type: object
    PostEquipmentFaultsListResponse:
      $ref: >-
        #/components/schemas/ApiResponseArray_EquipmentFaultsListItem-Array.EquipmentFaultsPaginationInfo_
    AvailableLevels:
      properties:
        availableLevels:
          items:
            type: string
          type: array
      required:
        - availableLevels
      type: object
    FaultsLevelListRequest:
      properties:
        filters: {}
        end_date:
          type: string
          format: date-time
        start_date:
          type: string
          format: date-time
      type: object
    FaultCountGroupedSeriesData:
      properties:
        faultCounts:
          items:
            $ref: '#/components/schemas/ChartSeriesData'
          type: array
      required:
        - faultCounts
      type: object
      additionalProperties: false
    FaultCountGroupedByRequest:
      properties:
        filters: {}
        isAscendingOrder:
          type: boolean
        groupByColumn:
          type: string
          enum:
            - aisle
            - level
            - device_functional_type
            - device_code
            - reason_name
        endDate:
          type: string
          format: date-time
        startDate:
          type: string
          format: date-time
      required:
        - groupByColumn
        - endDate
        - startDate
      type: object
    EquipmentFaultsDeviceIdRequest:
      properties:
        filters: {}
        end_date:
          type: string
          format: date-time
        start_date:
          type: string
          format: date-time
      type: object
    FaultsDeviceTypeListRequest:
      properties:
        start_date:
          type: string
          format: date-time
        end_date:
          type: string
          format: date-time
        filters: {}
      type: object
      additionalProperties: false
    EquipmentFaultsDefinition:
      description: DTO that describes the equipment faults data.
      properties:
        faults:
          type: number
          format: double
        status:
          type: string
      required:
        - faults
        - status
      type: object
      additionalProperties: false
    EquipmentFaultsConfigDefinition:
      description: DTO that describes the equipment faults config data.
      properties:
        lowRange:
          type: number
          format: double
        highRange:
          type: number
          format: double
        max:
          type: number
          format: double
      required:
        - max
      type: object
      additionalProperties: false
    ApiResponse_EquipmentFaultsDefinition.EquipmentFaultsConfigDefinition_:
      allOf:
        - $ref: '#/components/schemas/EquipmentFaultsDefinition'
        - properties:
            metadata:
              $ref: '#/components/schemas/EquipmentFaultsConfigDefinition'
          required:
            - metadata
          type: object
    FaultAvgDurationSeriesData:
      properties:
        avgDuration:
          items:
            $ref: '#/components/schemas/ChartSeriesData'
          type: array
      required:
        - avgDuration
      type: object
      additionalProperties: false
    FaultAvgDurationByStatusRequest:
      properties:
        filters: {}
        isAscendingOrder:
          type: boolean
        endDate:
          type: string
          format: date-time
        startDate:
          type: string
          format: date-time
      required:
        - endDate
        - startDate
      type: object
    FaultsArea:
      properties:
        totalFaults:
          type: number
          format: double
        maxFaultsAllowed:
          type: number
          format: double
        downtimeMinutes:
          type: number
          format: double
        status:
          type: string
      required:
        - totalFaults
        - maxFaultsAllowed
        - downtimeMinutes
        - status
      type: object
      additionalProperties: false
    FaultsFacilityArea:
      description: DTO that describes the fault areas data.
      properties:
        id:
          type: string
          description: Id of area.
        name:
          type: string
          description: Name of the area.
        operators:
          $ref: '#/components/schemas/AreaOperators'
          description: Operator area data.
        alertStatus:
          $ref: '#/components/schemas/AlertStatus'
        faults:
          $ref: '#/components/schemas/FaultsArea'
      required:
        - id
        - name
        - faults
      type: object
      additionalProperties: false
    FaultsAreasResponse:
      description: Type that describes the return structure of the data contract response.
      properties:
        areas:
          items:
            $ref: '#/components/schemas/FaultsFacilityArea'
          type: array
      required:
        - areas
      type: object
      additionalProperties: false
    FaultsAreasConfig:
      description: DTO that describes the fault areas config data.
      properties:
        min:
          type: number
          format: double
        max:
          type: number
          format: double
      required:
        - min
        - max
      type: object
      additionalProperties: false
    ApiResponse_FaultsAreasResponse.FaultsAreasConfig_:
      allOf:
        - $ref: '#/components/schemas/FaultsAreasResponse'
        - properties:
            metadata:
              $ref: '#/components/schemas/FaultsAreasConfig'
          required:
            - metadata
          type: object
    AvailableAisles:
      properties:
        availableAisles:
          items:
            type: string
          type: array
      required:
        - availableAisles
      type: object
    FaultsAisleListRequest:
      properties:
        filters: {}
        end_date:
          type: string
          format: date-time
        start_date:
          type: string
          format: date-time
      type: object
    EquipmentActiveFaultsDefinition:
      properties:
        area:
          type: string
        duration:
          type: number
          format: double
        deviceReason:
          type: string
      required:
        - area
        - duration
        - deviceReason
      type: object
      additionalProperties: false
    EquipmentActiveFaults:
      description: >-
        DTO that defines the relevant details of the inventory recent
        activities.
      properties:
        activeFaultsTable:
          items:
            $ref: '#/components/schemas/EquipmentActiveFaultsDefinition'
          type: array
      required:
        - activeFaultsTable
      type: object
      additionalProperties: false
    FromArea:
      properties:
        area_id:
          type: string
        number_to_move:
          type: number
          format: double
      required:
        - area_id
        - number_to_move
      type: object
      additionalProperties: false
    RequestBody:
      properties:
        from_areas:
          items:
            $ref: '#/components/schemas/FromArea'
          type: array
        to_area_id:
          type: string
      required:
        - from_areas
        - to_area_id
      type: object
      additionalProperties: false
    OperatorsChartSeriesData:
      properties:
        operators:
          items:
            $ref: '#/components/schemas/ChartSeriesData'
          type: array
      required:
        - operators
      type: object
    ApiResponse_OperatorsChartSeriesData.BaseChartConfig_:
      allOf:
        - $ref: '#/components/schemas/OperatorsChartSeriesData'
        - properties:
            metadata:
              $ref: '#/components/schemas/BaseChartConfig'
          required:
            - metadata
          type: object
    OperatorsChartSeriesApiResponse:
      $ref: >-
        #/components/schemas/ApiResponse_OperatorsChartSeriesData.BaseChartConfig_
    OperatorCountData:
      description: >-
        DTO of the necessary data to display total number of operators at a
        given time.
      properties:
        operatorCount:
          type: number
          format: double
        alertStatus:
          $ref: '#/components/schemas/AlertStatus'
      required:
        - operatorCount
        - alertStatus
      type: object
      additionalProperties: false
    OperatorCountsConfig:
      properties:
        startDateTime:
          type: string
        endDateTime:
          type: string
        lowRecOperators:
          type: number
          format: double
        highRecOperators:
          type: number
          format: double
        maxOperators:
          type: number
          format: double
      required:
        - startDateTime
        - endDateTime
        - lowRecOperators
        - highRecOperators
        - maxOperators
      type: object
      additionalProperties: false
    ApiResponse_OperatorCountData.OperatorCountsConfig_:
      allOf:
        - $ref: '#/components/schemas/OperatorCountData'
        - properties:
            metadata:
              $ref: '#/components/schemas/OperatorCountsConfig'
          required:
            - metadata
          type: object
    Pick_AlertStatus.Exclude_keyofAlertStatus.status-or-message__:
      properties:
        identifier:
          type: string
          description: >-
            Identifies the equipment, area, or location related to this alert

            TODO: this field is currently optional, but once it is implemented
            for all endpoints it should be mandatory
      type: object
      description: 'From T, pick a set of properties whose keys are in the union K'
    OperatorAlertStatus:
      properties:
        identifier:
          type: string
          description: >-
            Identifies the equipment, area, or location related to this alert

            TODO: this field is currently optional, but once it is implemented
            for all endpoints it should be mandatory
        status:
          $ref: '#/components/schemas/HealthStatusValue'
        message:
          type: string
      required:
        - status
      type: object
      additionalProperties: false
    Pick_FacilityArea.Exclude_keyofFacilityArea.alertStatus__:
      properties:
        id:
          type: string
          description: Id of area.
        name:
          type: string
          description: Name of the area.
        operators:
          $ref: '#/components/schemas/AreaOperators'
          description: Operator area data.
      required:
        - id
        - name
      type: object
      description: 'From T, pick a set of properties whose keys are in the union K'
    OperatorFacilityArea:
      properties:
        id:
          type: string
          description: Id of area.
        name:
          type: string
          description: Name of the area.
        operators:
          $ref: '#/components/schemas/AreaOperators'
          description: Operator area data.
        alertStatus:
          $ref: '#/components/schemas/OperatorAlertStatus'
      required:
        - id
        - name
        - alertStatus
      type: object
      additionalProperties: false
    OrderShippedData:
      properties:
        total:
          type: number
          format: double
        current:
          type: number
          format: double
        past:
          type: number
          format: double
        change:
          type: number
          format: double
      required:
        - total
        - current
        - past
        - change
      type: object
      additionalProperties: false
    ApiResponse_OrderShippedData.___:
      allOf:
        - $ref: '#/components/schemas/OrderShippedData'
        - properties:
            metadata:
              properties: {}
              type: object
          required:
            - metadata
          type: object
    OrdersPickCycleCountData:
      properties:
        cycleCount:
          type: number
          format: double
      required:
        - cycleCount
      type: object
      additionalProperties: false
    OrdersShipped:
      description: >-
        DTO that defines the relevant details for the current number of orders
        shipped.
      properties:
        total:
          type: number
          format: double
        shipped:
          type: number
          format: double
      required:
        - total
        - shipped
      type: object
      additionalProperties: false
    ApiResponse_OrdersShipped.___:
      allOf:
        - $ref: '#/components/schemas/OrdersShipped'
        - properties:
            metadata:
              properties: {}
              type: object
          required:
            - metadata
          type: object
    OrderProgress:
      description: >-
        DTO that defines the relevant details of the projected order fulfillment
        percentage.
      properties:
        orderProgressPercentage:
          type: number
          format: double
      required:
        - orderProgressPercentage
      type: object
      additionalProperties: false
    OrderProgressConfig:
      properties:
        seg1:
          type: number
          format: double
        seg2:
          type: number
          format: double
        seg3:
          type: number
          format: double
      required:
        - seg1
        - seg2
        - seg3
      type: object
      additionalProperties: false
    ApiResponse_OrderProgress.OrderProgressConfig_:
      allOf:
        - $ref: '#/components/schemas/OrderProgress'
        - properties:
            metadata:
              $ref: '#/components/schemas/OrderProgressConfig'
          required:
            - metadata
          type: object
    CustomerOrderEstimatedCompletion:
      type: number
      format: double
    CustomerOrderEstimatedCompletionData:
      properties:
        estimatedCompletionMinutes:
          $ref: '#/components/schemas/CustomerOrderEstimatedCompletion'
      required:
        - estimatedCompletionMinutes
      type: object
      additionalProperties: false
    OrderThroughputData:
      description: DTO that describes the order throughput data.
      properties:
        throughputRateLinesPerHour:
          type: number
          format: double
      required:
        - throughputRateLinesPerHour
      type: object
      additionalProperties: false
    OrderThroughputConfig:
      description: DTO that describes the order config data.
      properties:
        lowRange:
          type: number
          format: double
        max:
          type: number
          format: double
      required:
        - lowRange
        - max
      type: object
      additionalProperties: false
    ApiResponse_OrderThroughputData.OrderThroughputConfig_:
      allOf:
        - $ref: '#/components/schemas/OrderThroughputData'
        - properties:
            metadata:
              $ref: '#/components/schemas/OrderThroughputConfig'
          required:
            - metadata
          type: object
    PickOrderArea:
      enum:
        - shipping
        - packing
        - picking
      type: string
    OrderThroughputChartSeriesResponseData:
      properties:
        throughput:
          items:
            $ref: '#/components/schemas/ChartSeriesData'
          type: array
      required:
        - throughput
      type: object
    ApiResponse_OrderThroughputChartSeriesResponseData.BaseChartConfig_:
      allOf:
        - $ref: '#/components/schemas/OrderThroughputChartSeriesResponseData'
        - properties:
            metadata:
              $ref: '#/components/schemas/BaseChartConfig'
          required:
            - metadata
          type: object
    OrderThroughputChartApiResponse:
      $ref: >-
        #/components/schemas/ApiResponse_OrderThroughputChartSeriesResponseData.BaseChartConfig_
      description: Describes the contract for the data returned by the api.
    UnitsRemainingData:
      properties:
        unitsRemaining:
          type: number
          format: double
      required:
        - unitsRemaining
      type: object
      additionalProperties: false
    ProjectedOrderFulfillment:
      description: >-
        DTO that defines the relevant details of the projected order fulfillment
        percentage.
      properties:
        projectedOrderFulfillmentPercentage:
          type: number
          format: double
      required:
        - projectedOrderFulfillmentPercentage
      type: object
      additionalProperties: false
    OrderProgressSeriesData:
      properties:
        progress:
          items:
            $ref: '#/components/schemas/ChartSeriesData'
          type: array
      required:
        - progress
      type: object
    OrderPickLineThroughputSeriesData:
      properties:
        throughput:
          items:
            $ref: '#/components/schemas/ChartSeriesData'
          type: array
      required:
        - throughput
      type: object
    OrderLineProgress:
      properties:
        lineProgressPercent:
          type: number
          format: double
      required:
        - lineProgressPercent
      type: object
      additionalProperties: false
    OrderLineProgressConfig:
      properties:
        lowRange:
          type: number
          format: double
        highRange:
          type: number
          format: double
        max:
          type: number
          format: double
      required:
        - lowRange
        - highRange
        - max
      type: object
      additionalProperties: false
    ApiResponse_OrderLineProgress.OrderLineProgressConfig_:
      allOf:
        - $ref: '#/components/schemas/OrderLineProgress'
        - properties:
            metadata:
              $ref: '#/components/schemas/OrderLineProgressConfig'
          required:
            - metadata
          type: object
    OrderPerformanceFulfillmentData:
      properties:
        orderFulfillment:
          type: number
          format: double
      required:
        - orderFulfillment
      type: object
      additionalProperties: false
    OrderLineThroughputData:
      description: DTO that describes the order line throughput data.
      properties:
        totalCompletedOrderLines:
          type: number
          format: double
      required:
        - totalCompletedOrderLines
      type: object
      additionalProperties: false
    OrderLineProgressSeriesData:
      properties:
        trend:
          items:
            $ref: '#/components/schemas/ChartSeriesData'
          type: array
        progress:
          items:
            $ref: '#/components/schemas/ChartSeriesData'
          type: array
      required:
        - trend
        - progress
      type: object
    ApiResponse_OrderLineProgressSeriesData.BaseChartConfig_:
      allOf:
        - $ref: '#/components/schemas/OrderLineProgressSeriesData'
        - properties:
            metadata:
              $ref: '#/components/schemas/BaseChartConfig'
          required:
            - metadata
          type: object
    OrderLineProgressChartApiResponse:
      $ref: >-
        #/components/schemas/ApiResponse_OrderLineProgressSeriesData.BaseChartConfig_
    OrdersOutstandingData:
      description: DTO that defines the relevant details of the orders outstanding.
      properties:
        incompletedTotal:
          type: number
          format: double
      required:
        - incompletedTotal
      type: object
      additionalProperties: false
    ApiResponse_OrdersOutstandingData.___:
      allOf:
        - $ref: '#/components/schemas/OrdersOutstandingData'
        - properties:
            metadata:
              properties: {}
              type: object
          required:
            - metadata
          type: object
    OrderCycleTimeData:
      properties:
        orderCycleTimeMinutes:
          type: number
          format: double
        status:
          type: string
      required:
        - orderCycleTimeMinutes
        - status
      type: object
      additionalProperties: false
    OrderCycleTimeConfig:
      properties:
        max:
          type: number
          format: double
        highRange:
          type: number
          format: double
      required:
        - max
        - highRange
      type: object
      additionalProperties: false
    ApiResponse_OrderCycleTimeData.OrderCycleTimeConfig_:
      allOf:
        - $ref: '#/components/schemas/OrderCycleTimeData'
        - properties:
            metadata:
              $ref: '#/components/schemas/OrderCycleTimeConfig'
          required:
            - metadata
          type: object
    OrderCycleTimeChartData:
      properties:
        orderCycleTimeChart:
          items:
            $ref: '#/components/schemas/ChartSeriesData'
          type: array
      required:
        - orderCycleTimeChart
      type: object
    ApiResponse_OrderCycleTimeChartData.BaseChartConfig_:
      allOf:
        - $ref: '#/components/schemas/OrderCycleTimeChartData'
        - properties:
            metadata:
              $ref: '#/components/schemas/BaseChartConfig'
          required:
            - metadata
          type: object
    OrderCycleTimeChartApiResponse:
      $ref: >-
        #/components/schemas/ApiResponse_OrderCycleTimeChartData.BaseChartConfig_
    OrderCustomerLineThroughputData:
      description: DTO that describes the order line throughput data.
      properties:
        throughputRateOrderLinesPerHour:
          type: number
          format: double
      required:
        - throughputRateOrderLinesPerHour
      type: object
      additionalProperties: false
    WMSCustomerOrderArea:
      enum:
        - shipping
        - picking
      type: string
    ThroughputAreaData:
      description: DTO that describes the order throughput data by area.
      properties:
        throughputRate:
          type: number
          format: double
        maxThroughputCapacity:
          type: number
          format: double
        minThroughputTarget:
          type: number
          format: double
        maxThroughputTarget:
          type: number
          format: double
        status:
          $ref: '#/components/schemas/HealthStatus'
      required:
        - throughputRate
      type: object
      additionalProperties: false
    ThroughputFacilityArea:
      properties:
        id:
          type: string
          description: Id of area.
        name:
          type: string
          description: Name of the area.
        operators:
          $ref: '#/components/schemas/AreaOperators'
          description: Operator area data.
        alertStatus:
          $ref: '#/components/schemas/AlertStatus'
        throughput:
          $ref: '#/components/schemas/ThroughputAreaData'
          description: Order throughput area data
      required:
        - id
        - name
        - throughput
      type: object
      additionalProperties: false
    OrderAreasLineProgress:
      description: >-
        DTO that defines the relevant details for the current orderline progress
        per area.
      properties:
        orderLineProgress:
          type: number
          format: double
        orderLinesCompleted:
          type: number
          format: double
        totalOrderLines:
          type: number
          format: double
      required:
        - orderLineProgress
        - orderLinesCompleted
        - totalOrderLines
      type: object
      additionalProperties: false
    LineProgressFacilityArea:
      properties:
        id:
          type: string
          description: Id of area.
        name:
          type: string
          description: Name of the area.
        operators:
          $ref: '#/components/schemas/AreaOperators'
          description: Operator area data.
        alertStatus:
          $ref: '#/components/schemas/AlertStatus'
        orderLines:
          $ref: '#/components/schemas/OrderAreasLineProgress'
          description: Orderline progress area data
      required:
        - id
        - name
        - orderLines
      type: object
      additionalProperties: false
    OrderLineProgressAreasData:
      properties:
        areas:
          items:
            $ref: '#/components/schemas/LineProgressFacilityArea'
          type: array
      required:
        - areas
      type: object
      additionalProperties: false
    FacilityArea:
      description: DTO of the necessary base data to display a Facility Area.
      properties:
        id:
          type: string
          description: Id of area.
        name:
          type: string
          description: Name of the area.
        operators:
          $ref: '#/components/schemas/AreaOperators'
          description: Operator area data.
        alertStatus:
          $ref: '#/components/schemas/AlertStatus'
      required:
        - id
        - name
      type: object
      additionalProperties: false
    CycleTimeAreasConfig:
      properties:
        targetTime:
          type: string
      required:
        - targetTime
      type: object
      additionalProperties: false
    ApiResponse__areas-FacilityArea-Array_.CycleTimeAreasConfig_:
      allOf:
        - properties:
            areas:
              items:
                $ref: '#/components/schemas/FacilityArea'
              type: array
          required:
            - areas
          type: object
        - properties:
            metadata:
              $ref: '#/components/schemas/CycleTimeAreasConfig'
          required:
            - metadata
          type: object
    CustomerOrderThroughputData:
      description: DTO that describes the order throughput data.
      properties:
        throughputRateOrdersPerHour:
          type: number
          format: double
      required:
        - throughputRateOrdersPerHour
      type: object
      additionalProperties: false
    FacilityOrderLineThroughputSeriesResponse:
      properties:
        throughput:
          items:
            $ref: '#/components/schemas/ChartSeriesData'
          type: array
      required:
        - throughput
      type: object
      additionalProperties: false
    OrderCustomerLineProgressSeriesData:
      properties:
        progress:
          items:
            $ref: '#/components/schemas/ChartSeriesData'
          type: array
      required:
        - progress
      type: object
    OrderCycleTime:
      properties:
        orderCycleTimeMinutes:
          type: number
          format: double
      required:
        - orderCycleTimeMinutes
      type: object
      additionalProperties: false
    EstimatedOrderCompletionTimes:
      description: Definition of the necessary data to display the Estimated Completion
      properties:
        completionTime:
          type: string
      required:
        - completionTime
      type: object
      additionalProperties: false
    EstimatedOrderCompletionConfig:
      properties:
        targetTime:
          type: string
      required:
        - targetTime
      type: object
      additionalProperties: false
    ApiResponse_EstimatedOrderCompletionTimes.EstimatedOrderCompletionConfig_:
      allOf:
        - $ref: '#/components/schemas/EstimatedOrderCompletionTimes'
        - properties:
            metadata:
              $ref: '#/components/schemas/EstimatedOrderCompletionConfig'
          required:
            - metadata
          type: object
    CustomerOrderProgress:
      properties:
        orderProgressPercentage:
          type: number
          format: double
      required:
        - orderProgressPercentage
      type: object
      additionalProperties: false
    CustomerOrderLineThroughputSeriesData:
      properties:
        throughput:
          items:
            $ref: '#/components/schemas/ChartSeriesData'
          type: array
      required:
        - throughput
      type: object
    WorkstationStringArray:
      items:
        type: string
      type: array
    WorkstationData:
      properties:
        data:
          items:
            $ref: '#/components/schemas/ChartSeriesData'
          type: array
        id:
          type: string
      required:
        - data
        - id
      type: object
    WorkstationSeriesData:
      properties:
        data:
          items:
            $ref: '#/components/schemas/WorkstationData'
          type: array
      required:
        - data
      type: object
      additionalProperties: false
    WorkstationSeriesCharts:
      enum:
        - LinesPerHour
        - QuantityPerHour
        - SourceContainersPerHour
        - DestinationContainersPerHour
        - WeightedLinesPerHour
        - WeightedQuantityPerHour
        - WeightedSourceContainersPerHour
        - WeightedDestinationContainersPerHour
      type: string
    WorkstationOrdersStatusResponse:
      properties:
        activeOrders:
          type: number
          format: double
        delayedOrders:
          type: number
          format: double
      required:
        - activeOrders
        - delayedOrders
      type: object
      additionalProperties: false
    WorkstationOrdersListItem:
      properties:
        arrivalTime:
          type: string
        totalPicks:
          type: number
          format: double
          nullable: true
        completedPicks:
          type: number
          format: double
          nullable: true
        container:
          type: string
        orderId:
          type: string
        pickTask:
          type: string
        orderStatus:
          type: string
        dwellTime:
          type: number
          format: double
          description: Minutes
        position:
          type: string
        station:
          type: string
      required:
        - arrivalTime
        - totalPicks
        - completedPicks
        - container
        - orderId
        - pickTask
        - orderStatus
        - dwellTime
        - position
        - station
      type: object
    ApiResponseArray_WorkstationOrdersListItem-Array._page-number--limit-number--totalResults-number__:
      properties:
        metadata:
          properties:
            totalResults:
              type: number
              format: double
            limit:
              type: number
              format: double
            page:
              type: number
              format: double
          required:
            - totalResults
            - limit
            - page
          type: object
        data:
          items:
            $ref: '#/components/schemas/WorkstationOrdersListItem'
          type: array
      required:
        - metadata
        - data
      type: object
    WorkstationOrdersListData:
      $ref: >-
        #/components/schemas/ApiResponseArray_WorkstationOrdersListItem-Array._page-number--limit-number--totalResults-number__
    WorkstationOrdersDetailsPicksListData:
      properties:
        metadata:
          properties:
            totalResults:
              type: number
              format: double
            limit:
              type: number
              format: double
            page:
              type: number
              format: double
            orderId:
              type: string
          required:
            - totalResults
            - limit
            - page
            - orderId
          type: object
        data:
          properties:
            tableData:
              items:
                properties:
                  containers:
                    type: number
                    format: double
                  qtyPicked:
                    type: number
                    format: double
                  qtyOrdered:
                    type: number
                    format: double
                  size:
                    type: string
                  style:
                    type: string
                  orderLine:
                    type: string
                  status:
                    type: string
                  container:
                    type: string
                required:
                  - containers
                  - qtyPicked
                  - qtyOrdered
                  - size
                  - style
                  - orderLine
                  - status
                  - container
                type: object
              type: array
            deliveryNumber:
              type: string
          required:
            - tableData
            - deliveryNumber
          type: object
      required:
        - metadata
        - data
      type: object
    WorkstationStatusStats:
      properties:
        count:
          type: number
          format: double
        type:
          type: string
      required:
        - count
        - type
      type: object
    WorkstationModeStats:
      properties:
        count:
          type: number
          format: double
        type:
          type: string
      required:
        - count
        - type
      type: object
    WorkstationStats:
      properties:
        workstationMode:
          items:
            $ref: '#/components/schemas/WorkstationModeStats'
          type: array
        status:
          items:
            $ref: '#/components/schemas/WorkstationStatusStats'
          type: array
        totalWorkstations:
          type: number
          format: double
      required:
        - workstationMode
        - status
        - totalWorkstations
      type: object
    WorkstationOperatorStats:
      properties:
        targetLinesPerHour:
          type: number
          format: double
        actualLinesPerHour:
          type: number
          format: double
        totalOperators:
          type: number
          format: double
      required:
        - targetLinesPerHour
        - actualLinesPerHour
        - totalOperators
      type: object
    WorkstationPerformanceStats:
      properties:
        totalStarvationTime:
          type: number
          format: double
        totalActiveTime:
          type: number
          format: double
      required:
        - totalStarvationTime
        - totalActiveTime
      type: object
    WorkstationHealthStats:
      properties:
        totalDowntime:
          type: number
          format: double
      required:
        - totalDowntime
      type: object
    WorkstationDailyPerformanceData:
      properties:
        date:
          type: string
        totalLoggedInHours:
          type: number
          format: double
        idlePercentage:
          type: number
          format: double
        starvedPercentage:
          type: number
          format: double
        starvedHours:
          type: number
          format: double
        donorContainers:
          type: number
          format: double
        gtpContainers:
          type: number
          format: double
        linesPicked:
          type: number
          format: double
        qtyPerLine:
          type: number
          format: double
        pickLineQty:
          type: number
          format: double
        linesPerHour:
          type: number
          format: double
        avgLinesPickedPerHr1stShiftPercentage:
          type: number
          format: double
        avgLinesPickedPerHr2ndShiftPercentage:
          type: number
          format: double
        retrievalFromDMS:
          type: number
          format: double
        storageToDMS:
          type: number
          format: double
        retrievalFromASRS:
          type: number
          format: double
        storageToASRS:
          type: number
          format: double
      required:
        - date
      type: object
      additionalProperties: false
    WorkstationDailyPerformanceList:
      items:
        $ref: '#/components/schemas/WorkstationDailyPerformanceData'
      type: array
    WorkstationDailyPerformanceListRequestBody:
      allOf:
        - $ref: '#/components/schemas/PaginatedRequestNoDates'
        - properties:
            workstations:
              items:
                type: string
              type: array
          type: object
    WorkstationStatus:
      type: string
      enum:
        - Available
        - Closed
        - Paused
        - Unknown
    WorkstationMode:
      type: string
      enum:
        - Consolidation
        - Counting
        - Picking
        - Unknown
    WorkstationWorkflowStatus:
      type: string
      enum:
        - Disabled
        - Enabled
        - Unknown
    Workstation:
      properties:
        orderTotesPerHour:
          type: number
          format: double
        donorTotesPerHour:
          type: number
          format: double
        weightedLinesPerHour:
          type: number
          format: double
        linesPerHour:
          type: number
          format: double
        weightedQuantityPerHour:
          type: number
          format: double
        quantityPerHour:
          type: number
          format: double
        blockedTimeMins:
          type: number
          format: double
        idleTimeMins:
          type: number
          format: double
        starvedTimeMins:
          type: number
          format: double
        activeTimeSecs:
          type: number
          format: double
        operatorId:
          type: string
        workflowStatus:
          $ref: '#/components/schemas/WorkstationWorkflowStatus'
        workMode:
          $ref: '#/components/schemas/WorkstationMode'
        status:
          $ref: '#/components/schemas/WorkstationStatus'
        workstation:
          type: string
      required:
        - orderTotesPerHour
        - donorTotesPerHour
        - weightedLinesPerHour
        - linesPerHour
        - weightedQuantityPerHour
        - quantityPerHour
        - blockedTimeMins
        - idleTimeMins
        - starvedTimeMins
        - activeTimeSecs
        - operatorId
        - workflowStatus
        - workMode
        - status
        - workstation
      type: object
    WorkstationList:
      properties:
        workstationList:
          items:
            $ref: '#/components/schemas/Workstation'
          type: array
      required:
        - workstationList
      type: object
    WorkstationContainersListItem:
      properties:
        container:
          type: string
        status:
          type: string
        transport:
          type: string
        quantity:
          type: number
          format: double
        lastLocation:
          type: string
        eventTime:
          type: string
      required:
        - container
        - status
        - transport
        - quantity
        - lastLocation
        - eventTime
      type: object
      additionalProperties: false
    ApiResponseArray_WorkstationContainersListItem-Array._page-number--limit-number--totalResults-number__:
      properties:
        metadata:
          properties:
            totalResults:
              type: number
              format: double
            limit:
              type: number
              format: double
            page:
              type: number
              format: double
          required:
            - totalResults
            - limit
            - page
          type: object
        data:
          items:
            $ref: '#/components/schemas/WorkstationContainersListItem'
          type: array
      required:
        - metadata
        - data
      type: object
    WorkstationContainersListData:
      $ref: >-
        #/components/schemas/ApiResponseArray_WorkstationContainersListItem-Array._page-number--limit-number--totalResults-number__
    ComputeResources:
      properties:
        maxDuration:
          type: string
          nullable: true
        preemptible:
          type: boolean
        instanceType:
          type: string
          nullable: true
        memoryPerTaskMb:
          type: number
          format: double
          nullable: true
        cpuPerTask:
          type: number
          format: double
          nullable: true
      required:
        - maxDuration
        - preemptible
        - instanceType
        - memoryPerTaskMb
        - cpuPerTask
      type: object
      additionalProperties: false
    ExtraArgs:
      properties:
        simulationTime:
          type: string
        variant:
          type: string
        runWithDocker:
          type: boolean
        solution:
          type: string
        type:
          type: string
        version:
          type: string
      required:
        - variant
        - runWithDocker
        - solution
        - type
        - version
      type: object
      additionalProperties: false
    Event:
      properties:
        eventType:
          type: string
        state:
          type: string
        timestamp:
          type: string
        taskIndex:
          type: number
          format: double
          nullable: true
        jobId:
          type: string
        randomSeeds:
          items:
            type: number
            format: double
          type: array
        tags:
          items:
            type: string
          type: array
        computeResources:
          $ref: '#/components/schemas/ComputeResources'
        extraArgs:
          allOf:
            - $ref: '#/components/schemas/ExtraArgs'
          nullable: true
        taskCount:
          type: number
          format: double
        userId:
          type: string
        uploadId:
          type: string
        runEnv:
          type: string
          nullable: true
        runnerBackend:
          type: string
        unifiedRunnerVersion:
          type: number
          enum:
            - null
          nullable: true
        taskExitCode:
          type: number
          format: double
      required:
        - eventType
        - state
        - timestamp
        - taskIndex
        - jobId
      type: object
      additionalProperties: false
    Task:
      properties:
        taskIndex:
          type: number
          format: double
        lastUpdate:
          type: string
        state:
          type: string
        timeTaken:
          type: number
          format: double
          nullable: true
        visState:
          type: string
        exitCode:
          type: number
          format: double
      required:
        - taskIndex
        - lastUpdate
        - state
        - timeTaken
        - visState
        - exitCode
      type: object
      additionalProperties: false
    Tasks:
      properties: {}
      type: object
      additionalProperties:
        $ref: '#/components/schemas/Task'
    Attributes:
      properties:
        randomSeeds:
          items:
            type: number
            format: double
          type: array
        uploadId:
          type: string
        runnerBackend:
          type: string
      required:
        - randomSeeds
        - uploadId
        - runnerBackend
      type: object
      additionalProperties: false
    SimulationJob:
      properties:
        jobId:
          type: string
        events:
          items:
            $ref: '#/components/schemas/Event'
          type: array
        tags:
          items:
            type: string
          type: array
        lastUpdate:
          type: string
        tasks:
          $ref: '#/components/schemas/Tasks'
        createTime:
          type: string
        userId:
          type: string
        taskCount:
          type: number
          format: double
        state:
          type: string
        outputReady:
          type: boolean
        attributes:
          $ref: '#/components/schemas/Attributes'
        timeTaken:
          type: number
          format: double
          nullable: true
        uploadId:
          type: string
      required:
        - jobId
        - events
        - tags
        - lastUpdate
        - tasks
        - createTime
        - userId
        - taskCount
        - state
        - outputReady
        - attributes
        - timeTaken
        - uploadId
      type: object
      additionalProperties: false
    ApiResponseArray_SimulationJob-Array.___:
      properties:
        metadata:
          properties: {}
          type: object
        data:
          items:
            $ref: '#/components/schemas/SimulationJob'
          type: array
      required:
        - metadata
        - data
      type: object
    AuthTicket:
      properties:
        userId:
          type: string
        ticket:
          type: string
      required:
        - userId
        - ticket
      type: object
      additionalProperties: false
    ResendEmailVerificationResponse:
      properties:
        success:
          type: boolean
        message:
          type: string
        isSocialAuth:
          type: boolean
      required:
        - success
        - message
      type: object
      additionalProperties: false
    RoleType:
      enum:
        - internal
        - facility
        - admin
      type: string
    Role:
      properties:
        name:
          type: string
        displayName:
          type: string
        description:
          type: string
        roleType:
          $ref: '#/components/schemas/RoleType'
        assignable:
          type: boolean
      required:
        - name
        - displayName
        - roleType
      type: object
      additionalProperties: false
    GetRolesResponse:
      items:
        $ref: '#/components/schemas/Role'
      type: array
    UserInfo:
      properties:
        id:
          type: string
        email:
          type: string
        name:
          type: string
        emailVerified:
          type: boolean
        createdAt:
          type: string
        lastLogin:
          type: string
        loginCount:
          type: number
          format: double
        roles:
          items:
            $ref: '#/components/schemas/Role'
          type: array
          description: >-
            Roles are not returned in the list view, but are returned in the
            GetUser response
        isSocialAuth:
          type: boolean
      required:
        - id
        - email
        - emailVerified
        - createdAt
        - loginCount
        - isSocialAuth
      type: object
      additionalProperties: false
    ApiResponseArray_UserInfo-Array._page-number--limit-number--totalResults-number__:
      properties:
        metadata:
          properties:
            totalResults:
              type: number
              format: double
            limit:
              type: number
              format: double
            page:
              type: number
              format: double
          required:
            - totalResults
            - limit
            - page
          type: object
        data:
          items:
            $ref: '#/components/schemas/UserInfo'
          type: array
      required:
        - metadata
        - data
      type: object
    UsersListData:
      $ref: >-
        #/components/schemas/ApiResponseArray_UserInfo-Array._page-number--limit-number--totalResults-number__
    AssignRoleResponse:
      properties:
        success:
          type: boolean
        message:
          type: string
        assignedRoles:
          items:
            $ref: '#/components/schemas/Role'
          type: array
      required:
        - success
        - message
        - assignedRoles
      type: object
      additionalProperties: false
    AssignRoleRequest:
      properties:
        roleNames:
          items:
            type: string
          type: array
      required:
        - roleNames
      type: object
      additionalProperties: false
    RemoveRoleResponse:
      properties:
        success:
          type: boolean
        message:
          type: string
      required:
        - success
        - message
      type: object
      additionalProperties: false
    SectionsPaginationInfo:
      properties:
        page:
          type: number
          format: double
        limit:
          type: number
          format: double
        totalResults:
          type: number
          format: double
        totalPages:
          type: number
          format: double
      required:
        - page
        - limit
        - totalResults
        - totalPages
      type: object
      additionalProperties: false
    ApiResponseArray__name-string--pk-number_-Array.SectionsPaginationInfo_:
      properties:
        metadata:
          $ref: '#/components/schemas/SectionsPaginationInfo'
        data:
          items:
            properties:
              pk:
                type: number
                format: double
              name:
                type: string
            required:
              - pk
              - name
            type: object
          type: array
      required:
        - metadata
        - data
      type: object
    GetAvailableSectionsResponse:
      $ref: >-
        #/components/schemas/ApiResponseArray__name-string--pk-number_-Array.SectionsPaginationInfo_
    EquipmentPaginationInfo:
      properties:
        page:
          type: number
          format: double
        limit:
          type: number
          format: double
        totalResults:
          type: number
          format: double
        totalPages:
          type: number
          format: double
      required:
        - page
        - limit
        - totalResults
        - totalPages
      type: object
      additionalProperties: false
    ApiResponseArray__name-string--pk-number_-Array.EquipmentPaginationInfo_:
      properties:
        metadata:
          $ref: '#/components/schemas/EquipmentPaginationInfo'
        data:
          items:
            properties:
              pk:
                type: number
                format: double
              name:
                type: string
            required:
              - pk
              - name
            type: object
          type: array
      required:
        - metadata
        - data
      type: object
    GetAvailableEquipmentResponse:
      $ref: >-
        #/components/schemas/ApiResponseArray__name-string--pk-number_-Array.EquipmentPaginationInfo_
    AlarmAuditLogEntry:
      properties:
        alarmId:
          type: string
        alarmIdentifier:
          type: string
        operationType:
          type: string
          enum:
            - insert
            - update
            - delete
        originalValues:
          $ref: '#/components/schemas/Record_string.unknown_'
        newValues:
          $ref: '#/components/schemas/Record_string.unknown_'
        diffValues:
          $ref: '#/components/schemas/Record_string.unknown_'
        modifiedUser:
          type: string
        modifiedTime:
          type: string
        modifiedComment:
          type: string
        processUuid:
          type: string
        processIdentifier:
          type: string
      required:
        - alarmId
        - alarmIdentifier
        - operationType
        - originalValues
        - newValues
        - diffValues
        - modifiedUser
        - modifiedTime
        - modifiedComment
        - processUuid
        - processIdentifier
      type: object
      additionalProperties: false
    AlarmPaginationInfo:
      properties:
        page:
          type: number
          format: double
        limit:
          type: number
          format: double
        totalResults:
          type: number
          format: double
        totalPages:
          type: number
          format: double
      required:
        - page
        - limit
        - totalResults
        - totalPages
      type: object
      additionalProperties: false
    ApiResponseArray_AlarmAuditLogEntry-Array.AlarmPaginationInfo_:
      properties:
        metadata:
          $ref: '#/components/schemas/AlarmPaginationInfo'
        data:
          items:
            $ref: '#/components/schemas/AlarmAuditLogEntry'
          type: array
      required:
        - metadata
        - data
      type: object
    PostAuditLogsListResponse:
      $ref: >-
        #/components/schemas/ApiResponseArray_AlarmAuditLogEntry-Array.AlarmPaginationInfo_
    NonPaginatedRequest:
      properties:
        start_date:
          type: string
          format: date-time
        end_date:
          type: string
          format: date-time
        filters: {}
        sortFields:
          items:
            $ref: '#/components/schemas/SortField'
          type: array
        searchString:
          type: string
        exportColumns:
          properties: {}
          additionalProperties:
            type: boolean
          type: object
        limit:
          type: integer
          format: int32
          minimum: 1
          maximum: 1000
        id:
          type: string
      required:
        - start_date
        - end_date
      type: object
      additionalProperties: false
    Alarm:
      properties:
        id:
          type: string
        splitId:
          type: string
        faultId:
          type: string
        title:
          type: string
        description:
          type: string
        tag:
          type: string
        severity:
          type: string
        location:
          properties:
            equipment:
              type: string
            section:
              type: string
            area:
              type: string
          required:
            - equipment
            - section
            - area
          type: object
        timing:
          properties:
            origDuration:
              type: string
            origEndTime:
              type: string
              format: date-time
            origStartTime:
              type: string
              format: date-time
            splitDuration:
              type: string
            splitEndTime:
              type: string
              format: date-time
            splitStartTime:
              type: string
              format: date-time
            updatedDuration:
              type: string
            updatedEndTime:
              type: string
              format: date-time
            updatedStartTime:
              type: string
              format: date-time
            duration:
              type: string
            endTime:
              type: string
              format: date-time
            startTime:
              type: string
              format: date-time
          required:
            - duration
            - startTime
          type: object
        status:
          type: string
        reason:
          type: string
        comments:
          type: string
        lastUpdatedUser:
          type: string
          nullable: true
      required:
        - id
        - splitId
        - faultId
        - title
        - description
        - tag
        - location
        - timing
        - status
        - reason
      type: object
      additionalProperties: false
    ApiResponseArray_Alarm-Array.AlarmPaginationInfo_:
      properties:
        metadata:
          $ref: '#/components/schemas/AlarmPaginationInfo'
        data:
          items:
            $ref: '#/components/schemas/Alarm'
          type: array
      required:
        - metadata
        - data
      type: object
    PostAlarmsListResponse:
      $ref: '#/components/schemas/ApiResponseArray_Alarm-Array.AlarmPaginationInfo_'
    AlarmCreateDto:
      properties:
        startDateLocal:
          type: string
          format: date-time
        endDateLocal:
          type: string
          format: date-time
        isIncluded:
          type: boolean
        section:
          type: string
        equipment:
          type: string
        description:
          type: string
        comments:
          type: string
      required:
        - startDateLocal
        - endDateLocal
        - isIncluded
        - section
        - equipment
        - description
        - comments
      type: object
      additionalProperties: false
    AlarmUpdateDto:
      properties:
        id:
          type: string
        startDateLocal:
          type: string
          format: date-time
        endDateLocal:
          type: string
          format: date-time
        isIncluded:
          type: boolean
        comments:
          type: string
        excludeReason:
          type: string
      required:
        - id
        - isIncluded
        - comments
      type: object
      additionalProperties: false
    MovementByTypeValue:
      description: Movement by type data response structure
      properties:
        totalMovements:
          anyOf:
            - type: number
              format: double
            - type: string
        percentage:
          anyOf:
            - type: number
              format: double
            - type: string
      type: object
      additionalProperties: false
    MovementByTypeData:
      properties:
        name:
          type: string
        value:
          $ref: '#/components/schemas/MovementByTypeValue'
      required:
        - name
        - value
      type: object
      additionalProperties: false
    DmsId:
      type: string
      enum:
        - inventory
        - shipping
    MovementUnitType:
      type: string
      enum:
        - loadunit
        - sku
    LocationValue:
      properties:
        name:
          type: string
        value:
          type: number
          format: double
      required:
        - name
        - value
      type: object
      description: |-
        Transformed movement location data response structure
        Used for the new locationData format
    MovementLocationData:
      properties:
        locationData:
          items:
            properties: {}
            additionalProperties:
              items:
                $ref: '#/components/schemas/LocationValue'
              type: array
            type: object
          type: array
      required:
        - locationData
      type: object
      additionalProperties: false
    MovementLocationScope:
      type: string
      enum:
        - source_location
        - destination_location
        - source_type
        - destination_type
      description: Type definition for the scope parameter
    TimeSeriesValue:
      properties:
        name:
          type: string
        value:
          type: number
          format: double
      required:
        - name
        - value
      type: object
      description: |-
        Movement time-series data response structure
        Unified interface for both daily and hourly movement aggregations
        Following the same structure as other movement endpoints
    MovementTimeSeriesData:
      properties:
        timeSeriesData:
          items:
            properties: {}
            additionalProperties:
              items:
                $ref: '#/components/schemas/TimeSeriesValue'
              type: array
            type: object
          type: array
      required:
        - timeSeriesData
      type: object
      additionalProperties: false
    MovementTimeSeriesGranularity:
      type: string
      enum:
        - day
        - hour
      description: Granularity options for time-series movement data
    MovementLoadUnitsEntry:
      properties:
        name:
          type: string
        value:
          type: number
          format: double
      required:
        - name
        - value
      type: object
      description: |-
        Movement load units data response structure
        Unified endpoint for load unit and SKU based movements
        Following the same structure as other movement endpoints
    MovementLoadUnitsData:
      properties: {}
      additionalProperties:
        items:
          $ref: '#/components/schemas/MovementLoadUnitsEntry'
        type: array
      type: object
    ApiResponseArray_MovementLoadUnitsData-Array._page-number--limit-number--totalResults-number__:
      properties:
        metadata:
          properties:
            totalResults:
              type: number
              format: double
            limit:
              type: number
              format: double
            page:
              type: number
              format: double
          required:
            - totalResults
            - limit
            - page
          type: object
        data:
          items:
            $ref: '#/components/schemas/MovementLoadUnitsData'
          type: array
      required:
        - metadata
        - data
      type: object
  responses: {}
  parameters: {}
  examples: {}
  requestBodies: {}
  headers: {}
  securitySchemes:
    jwt:
      type: http
      scheme: bearer
      bearerFormat: JWT
    auth0:
      type: oauth2
      flows:
        implicit:
          authorizationUrl: 'https://dev-zmogq9vd.us.auth0.com/authorize'
          scopes: {}
