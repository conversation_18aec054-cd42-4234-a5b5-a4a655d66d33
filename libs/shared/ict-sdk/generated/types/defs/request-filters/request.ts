import {SortField} from './sort-fields';

export interface NonPaginatedRequest {
  /**
   * @isDateTime
   */
  start_date: Date;
  /**
   * @isDateTime
   */
  end_date: Date;
  filters?: unknown;
  /**
   * @isArray
   */
  sortFields?: SortField[];
  /**
   * @isString
   */
  searchString?: string;
  /**
   * @isBoolean
   */
  exportColumns?: {[key: string]: boolean};
  /**
   * @isInt
   * @minimum 1
   * @maximum 1000
   */
  limit?: number;
  /**
   * @isString
   */
  id?: string;
}
